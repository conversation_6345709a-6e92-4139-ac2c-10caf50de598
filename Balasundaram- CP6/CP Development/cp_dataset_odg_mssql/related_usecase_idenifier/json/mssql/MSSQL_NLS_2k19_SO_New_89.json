[{"Name": "MSSQL_NLS_2k19_SO_New", "actions": [{"action_name": "LSSQL_Check_Primary_SecondaryLogshipping_Exist", "description": "Check if primary and secondary log shipping exists."}, {"action_name": "LSSQL_Check_DBEntry_OnSecondaryServer_Exist", "description": "Verify if the database entry exists on the secondary server."}, {"action_name": "LSSQL_Check_PrimaryLogshipping_Exist", "description": "Verify if the primary log shipping exists."}, {"action_name": "LSSQL_Check_DbEntry_OnPrimaryServer_Exist", "description": "Verify if the database entry exists on the primary server."}, {"action_name": "LSSQL_DisableJob", "description": "Disable the specified job on the SQL server."}, {"action_name": "LSSQL_DisableJob", "description": "Disable the specified job on the SQL server."}, {"action_name": "LSSQL_DisableJob", "description": "Disable the specified job on the SQL server."}, {"action_name": "LSSQL_RunJob", "description": "Execute the specified job on the SQL server."}, {"action_name": "LSSQL_RunJob", "description": "Execute the specified job on the SQL server."}, {"action_name": "LSSQL_VerifyLogFileSequence", "description": "Verify the sequence of the log files in the SQL server."}, {"action_name": "LSSQL_KillDBSessionWithTimeOut", "description": "Terminate database sessions with a specified timeout."}, {"action_name": "LSSQL_SetDBSingleUserAcessMode", "description": "Set the database to single-user access mode."}, {"action_name": "LSSQL_VerifyPRDBSingleUserAcessMode", "description": "Verify if the primary database is in single-user access mode."}, {"action_name": "LSSQL_VerifyLogFileSequence", "description": "Verify the sequence of the log files in the SQL server."}, {"action_name": "LSSQL_KillDBSessionWithTimeOut", "description": "Terminate database sessions with a specified timeout."}, {"action_name": "LSSQL_GenerateLastLogBackupWithNoRecovery", "description": "Generate the last log backup with no recovery mode."}, {"action_name": "LSSQL_SetDBMultiUserAccessMode", "description": "Set the database to multi-user access mode."}, {"action_name": "LSSQL_KillDBSessionWithTimeOut", "description": "Terminate database sessions with a specified timeout."}, {"action_name": "LSSQL_SetDBSingleUserAcessMode", "description": "Set the database to single-user access mode."}, {"action_name": "LSSQL_KillDBSessionWithTimeOut", "description": "Terminate database sessions with a specified timeout."}, {"action_name": "LSSQL_RestoreLastBackupLogWithRecovery", "description": "<PERSON>ore the last log backup with recovery."}, {"action_name": "LSSQL_VerifyLogFileSequence", "description": "Verify the sequence of the log files in the SQL server."}, {"action_name": "LSSQL_RemovePrimarySecondaryLogShipping", "description": "Remove both primary and secondary log shipping configurations."}, {"action_name": "LSSQL_Verify_Primary_SecondarylogShipping_Exist", "description": "Verify if both primary and secondary log shipping exist."}, {"action_name": "LSSQL_RemovePrimaryLogShipping", "description": "Remove primary log shipping configuration."}, {"action_name": "LSSQL_Verify_DBEntry_OnSecondaryServer_Exist", "description": "Verify if the database entry exists on the secondary server."}, {"action_name": "LSSQL_Verify_Primarylogshipping_Exist", "description": "Verify if primary log shipping exists."}, {"action_name": "LSSQL_RemoveSecondaryLogShipping", "description": "Remove secondary log shipping configuration."}, {"action_name": "LSSQL_Verify_DBEntry_OnPrimaryServer_Exist", "description": "Verify if the database entry exists on the primary server."}, {"action_name": "LSSQL_RemoveSecondaryJob", "description": "Remove the secondary job configuration."}, {"action_name": "LSSQL_RemoveSecondaryJob", "description": "Remove the secondary job configuration."}, {"action_name": "LSSQL_RemoveSecondaryJob", "description": "Remove the secondary job configuration."}, {"action_name": "LSSQL_SetDBMultiUserAccessMode", "description": "Set the database to multi-user access mode."}, {"action_name": "LSSQL_ExecutePrimaryLogShipping", "description": "Execute the primary log shipping operation."}, {"action_name": "LSSQL_ExecuteSecondaryLogShipping", "description": "Execute the secondary log shipping operation."}, {"action_name": "LSSQL_UpdatingBackupJobwithPRIPaddress", "description": "Update the backup job with the primary IP address."}, {"action_name": "LSSQL_UpdatingcopyjobwithDRIpaddress", "description": "Update the copy job with the disaster recovery IP address."}, {"action_name": "LSSQL_UpdatingRestoreJobwithDRIPAddress", "description": "Update the restore job with the disaster recovery IP address."}, {"action_name": "LSSQL_fixorphanusers", "description": "Fix orphaned users in the SQL database."}]}]