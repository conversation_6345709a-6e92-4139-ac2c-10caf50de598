[{"Name": "MSSQL_NLS_SO", "actions": [{"action_name": "LSSQL_Check_Primary_SecondaryLogshipping_Exist", "description": "Verifies if the log shipping exists between the primary and secondary servers."}, {"action_name": "LSSQL_Check_DBEntry_OnSecondaryServer_Exist", "description": "Checks if the database entry exists on the secondary server."}, {"action_name": "LSSQL_Check_PrimaryLogshipping_Exist", "description": "Verifies if log shipping exists on the primary server."}, {"action_name": "LSSQL_Check_DbEntry_OnPrimaryServer_Exist", "description": "Checks if the database entry exists on the primary server."}, {"action_name": "LSSQL_DisableJob", "description": "Disables a specific SQL Server job to prevent its execution."}, {"action_name": "LSSQL_DisableJob", "description": "Disables a specific SQL Server job to prevent its execution."}, {"action_name": "LSSQL_DisableJob", "description": "Disables a specific SQL Server job to prevent its execution."}, {"action_name": "LSSQL_RunJob", "description": "Runs a specified SQL Server job manually."}, {"action_name": "LSSQL_RunJob", "description": "Runs a specified SQL Server job manually."}, {"action_name": "LSSQL_VerifyLogFileSequence", "description": "Verifies the sequence of log files in a SQL Server database."}, {"action_name": "LSSQL_KillDBSessionWithTimeOut", "description": "Kills a database session if it exceeds the specified timeout."}, {"action_name": "LSSQL_SetDBSingleUserAcessMode", "description": "Sets the database to single-user access mode to restrict access to one user."}, {"action_name": "LSSQL_VerifyPRDBSingleUserAcessMode", "description": "Verifies that the database is in single-user access mode on the primary server."}, {"action_name": "LSSQL_VerifyLogFileSequence", "description": "Verifies the sequence of log files in a SQL Server database."}, {"action_name": "LSSQL_KillDBSessionWithTimeOut", "description": "Kills a database session if it exceeds the specified timeout."}, {"action_name": "LSSQL_GenerateLastBackUpLog", "description": "Generates the last backup log for the database."}, {"action_name": "LSSQL_SetDBMultiUserAccessMode", "description": "Sets the database to multi-user access mode to allow multiple users to connect."}, {"action_name": "LSSQL_KillDBSessionWithTimeOut", "description": "Kills a database session if it exceeds the specified timeout."}, {"action_name": "LSSQL_SetDBSingleUserAcessMode", "description": "Sets the database to single-user access mode to restrict access to one user."}, {"action_name": "LSSQL_KillDBSessionWithTimeOut", "description": "Kills a database session if it exceeds the specified timeout."}, {"action_name": "LSSQL_RestoreLastBackupLogWithRecovery", "description": "Restores the last backup log with recovery to ensure the database is fully recovered."}, {"action_name": "LSSQL_VerifyLogFileSequence", "description": "Verifies the sequence of log files in a SQL Server database."}, {"action_name": "LSSQL_RemovePrimarySecondaryLogShipping", "description": "Removes log shipping configuration between the primary and secondary servers."}, {"action_name": "LSSQL_Verify_Primary_SecondarylogShipping_Exist", "description": "Verifies if log shipping exists between the primary and secondary servers."}, {"action_name": "LSSQL_RemovePrimaryLogShipping", "description": "Removes the log shipping configuration from the primary server."}, {"action_name": "LSSQL_Verify_DBEntry_OnSecondaryServer_Exist", "description": "Checks if the database entry exists on the secondary server."}, {"action_name": "LSSQL_Verify_Primarylogshipping_Exist", "description": "Verifies if log shipping exists on the primary server."}, {"action_name": "LSSQL_RemoveSecondaryLogShipping", "description": "Removes the log shipping configuration from the secondary server."}, {"action_name": "LSSQL_Verify_DBEntry_OnPrimaryServer_Exist", "description": "Checks if the database entry exists on the primary server."}, {"action_name": "LSSQL_RemoveSecondaryJob", "description": "Removes the secondary job associated with log shipping."}, {"action_name": "LSSQL_RemoveSecondaryJob", "description": "Removes the secondary job associated with log shipping."}, {"action_name": "LSSQL_RemoveSecondaryJob", "description": "Removes the secondary job associated with log shipping."}, {"action_name": "LSSQL_SetDBMultiUserAccessMode", "description": "Sets the database to multi-user access mode to allow multiple users to connect."}, {"action_name": "LSSQL_ExecutePrimaryLogShipping", "description": "Executes log shipping on the primary server."}, {"action_name": "LSSQL_ExecuteSecondaryLogShipping", "description": "Executes log shipping on the secondary server."}, {"action_name": "LSSQL_UpdatingBackupJobwithPRIPaddress", "description": "Updates the backup job with the primary IP address."}, {"action_name": "LSSQL_UpdatingcopyjobwithDRIpaddress", "description": "Updates the copy job with the disaster recovery IP address."}, {"action_name": "LSSQL_UpdatingRestoreJobwithDRIPAddress", "description": "Updates the restore job with the disaster recovery IP address."}, {"action_name": "LSSQL_fixorphanusers", "description": "Fixes orphaned users in the SQL Server database."}]}]