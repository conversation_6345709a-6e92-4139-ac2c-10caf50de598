[{"actions": [{"action_name": "DGVerifyDBModeAndRole", "description": "Verifies the database mode and role in the Data Guard environment."}, {"action_name": "DGVerifyDBModeAndRole", "description": "Verifies the database mode and role in the Data Guard environment."}, {"action_name": "JobStatusInPR", "description": "Checks the job status in the primary region."}, {"action_name": "VerifyArchiveLogSequence", "description": "Verifies the sequence of archive log files."}, {"action_name": "DGSwitchLogFile", "description": "Switches the log file in the Data Guard environment."}, {"action_name": "DGSwitchLogFile", "description": "Switches the log file in the Data Guard environment."}, {"action_name": "DGSwitchLogFileCurrent", "description": "Switches the current log file in the Data Guard environment."}, {"action_name": "VerifyArchiveLogSequence", "description": "Verifies the sequence of archive log files."}, {"action_name": "VerifySwitchoverStatus", "description": "Verifies the status of the switchover process."}, {"action_name": "srvctl_StopInstance", "description": "Stops the instance using the srvctl utility."}, {"action_name": "srvctl_StopInstance", "description": "Stops the instance using the srvctl utility."}, {"action_name": "VerifyAlterDBSO", "description": "Verifies the database alter switchover operation."}, {"action_name": "AlterDataBaseSwitchOver", "description": "Performs the database alter switchover operation."}, {"action_name": "AlterDatabaseOpen", "description": "Opens the database after the switchover operation."}, {"action_name": "srvctl_StartInstance_OMount", "description": "Starts the instance with mounting in the srvctl utility."}, {"action_name": "srvctl_StartInstance_OOpen", "description": "Starts the instance and opens the database using the srvctl utility."}, {"action_name": "srvctl_StartInstance_OMount", "description": "Starts the instance with mounting in the srvctl utility."}, {"action_name": "RecoveryStartInNewDR", "description": "Starts the recovery process in the new disaster recovery environment."}, {"action_name": "DGSwitchLogFileCurrent", "description": "Switches the current log file in the Data Guard environment."}, {"action_name": "VerifyArchiveLogSequence", "description": "Verifies the sequence of archive log files."}, {"action_name": "DGVerifyDBModeAndRole", "description": "Verifies the database mode and role in the Data Guard environment."}], "Name": "Oracle_Rac_SB_20241216_11_18_temp"}]