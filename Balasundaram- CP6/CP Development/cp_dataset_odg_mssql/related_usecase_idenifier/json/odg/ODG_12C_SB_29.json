[{"Name": "ODG_12C_SB", "actions": [{"action_name": "DGVerifyDBModeAndRole", "description": "Verifies the current database mode and role (primary or standby) to ensure that the Data Guard system is operating correctly."}, {"action_name": "DGCheckUserStatus", "description": "Checks the status of users in the database to ensure that no user-related issues are preventing Data Guard operations."}, {"action_name": "DGJobStatus", "description": "Monitors the status of the Data Guard jobs to ensure that there are no issues or errors in the system."}, {"action_name": "DGVerifyMaxSequenceNumber", "description": "Verifies that the maximum sequence number is correct, ensuring that the redo logs are synchronized between the primary and standby databases."}, {"action_name": "DGSwitchPrimaryToStandBy", "description": "Switches the primary database to standby mode, typically for maintenance or during a failover."}, {"action_name": "DGMountStandBy", "description": "Mounts the standby database, preparing it for role transition or recovery operations."}, {"action_name": "DGSwitchStandByToPrimary", "description": "Switches the standby database to the primary role during a switchover or failover process."}, {"action_name": "ShutDB", "description": "Shuts down the database, either for maintenance or to facilitate role transitions in the Data Guard setup."}, {"action_name": "OpenDatabase", "description": "Opens the database after it has been shut down or mounted, making it available for normal operations."}, {"action_name": "DGSwitchLogFile", "description": "Switches the redo log files to ensure that the latest changes are applied in the Data Guard configuration."}, {"action_name": "DGRecoverStandBy", "description": "Performs recovery operations on the standby database to ensure it is synchronized with the primary database."}]}]