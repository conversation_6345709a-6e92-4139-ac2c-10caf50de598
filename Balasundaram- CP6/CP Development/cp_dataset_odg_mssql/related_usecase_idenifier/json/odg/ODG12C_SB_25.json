[{"Name": "ODG12C_SB", "actions": [{"action_name": "DGVerifyDBModeAndRole", "description": "Verifies the current database mode (primary or standby) and its associated role in the Oracle Data Guard configuration."}, {"action_name": "DGCheckUserStatus", "description": "Checks the status of the user in the Oracle Data Guard environment to ensure proper connectivity and access."}, {"action_name": "DGJobStatus", "description": "Checks the status of ongoing Data Guard jobs to ensure they are running correctly."}, {"action_name": "DGVerifyMaxSequenceNumber", "description": "Verifies that the maximum sequence number in the archive logs is accurate and synchronized between the primary and standby databases."}, {"action_name": "DGSwitchPrimaryToStandBy", "description": "Switches the primary database to standby mode for maintenance or disaster recovery purposes."}, {"action_name": "DGMountStandBy", "description": "Mounts the standby database to prepare it for failover or recovery operations."}, {"action_name": "DGSwitchStandByToPrimary", "description": "Switches the standby database to primary mode to take over the production workload."}, {"action_name": "ShutDB", "description": "Shuts down the database safely to allow for maintenance or backup operations."}, {"action_name": "OpenDatabase", "description": "Opens the database after a shutdown, allowing it to resume operations."}, {"action_name": "DGSwitchLogFile", "description": "Switches the log file in the Oracle Data Guard configuration to ensure that logs are properly archived and transferred."}, {"action_name": "DGRecoverStandBy", "description": "Recovers the standby database to synchronize with the primary database, ensuring data consistency."}]}]