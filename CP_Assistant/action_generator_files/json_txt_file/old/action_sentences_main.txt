Execute a Power shell (powershell) command and verify its output,
Verify if SSH is installed,
Automate UI interactions using a password,
Automate UI interactions,
Disable or stop or kill User Account Control (UAC),
Test SRM (Site Recovery Manager) connectivity,
Stop the Microsoft Cluster or kill ms cluster,
Start a scheduled task or job,
Start the Microsoft Cluster service,
Kill or stop a service or process in unix or linux,
Check if the cluster status is online ,
Check if the cluster status is offline,
Install OpenSSH (new method),
Install OpenSSH,
Compare two folders for differences,
Compare two files for differences,
Execute a script with user credentials,
Execute a script with a password,
Execute a script using environment variables for password,
Execute a script along with the password,
Execute a script,
Run an SSH command in a single session,
Execute ZFS commands on the operating system,
Execute an OS command using a password,
Run a local process on the operating system,
Check OS command output using a password,
Compare files or folders,
Remove a dependency from a cluster resource,
Add a dependency to a cluster resource,
Change the IP address of a node,
Monitor the status of an application service,
Add a user to the administrator group,
Run a Power shell (powershell) command,
Enable folder target referral namespace,
Disable folder target referral namespace,
Check the status of folder target referral namespace,
Pause execution for a specified time,
Kill a Windows process by its name,
Check if a file exists in Windows,
Rename a file in Windows,
Change text in a file in Windows,
Wait for a ping response from a server,
Stop a scheduled task,
Start a scheduled task,
Shut down a remote server,
Shut down the local server,
Transfer files using SCP,
Replicate the standby control file,
Replicate and sync data folders on Windows,
Replicate and sync a single file on Windows,
Replace text within a file,
Replace a file with another,
Remove NT AUTHORITY SYSTEM account from DNS record with allow or deny permission,
Remove a computer account from DNS record with allow or deny permission,
Reboot a remote server,
Replicate data for disaster recovery,
Modify DNS record value of A-type without TTL,
Modify DNS record value of A-type with TTL,
Verify if Microsoft SQL Server and SSMS are installed,
Verify if Microsoft SQL Server is installed,
Execute a Windows process,
Run a Symcli command,
Run an SSH command,
Run an OS command,
Verify Symcli command output,
Execute a batch file,
Enable a scheduled task,
Disable a scheduled task,
Delete a DNS record,
Replicate a file for data synchronization,
Sync redo archive folders for data,
Sync folders for data,
Sync archive folders for data,
Set TTL value for DNS SOA record on a DNS server,
Check if a string exists in a file,
Check the status of a service,
Check the status of a scheduled task,
Verify if a file exists,
Check if a DNS record without TTL exists for A-type,
Check if a DNS record with TTL exists for A-type,
Change the start mode of a service,
Update the DNS configuration,
Execute a batch file,
Stop an application,
Start an application,
Add a new record to a text file,
Add NT AUTHORITY SYSTEM account to DNS record with deny permission,
Add NT AUTHORITY SYSTEM account to DNS record with allow permission,
Add a DNS record,
Add a computer account to a DNS record with deny permission,
Add a computer account to a DNS record with allow permission,
Verify if a Windows service is running,
Verify if a Windows service is down,
Stop a Windows service,
Stop a service,
Start a service,
Start a Windows service,
Enable a NetScaler load balancing server,
Disable a NetScaler load balancing server,
Check the status of the NetScaler load balancer,
Verify if a DNS CNAME record exists,
Transfer or seize the FSMO (Flexible Single Master Operation) roles,
Remove SPN (Service Principal Name) from an Active Directory computer object,
Modify a DNS CNAME record with TTL settings,
Modify a DNS A record with TTL settings,
Verify DC (Domain Controller) server status,
Verify DNS configuration and settings,
Modify DNS records,
Check the replication sync status across all Active Directory (AD) objects,
Check the replication status of Active Directory,
Verify the FSMO roles for an Active Directory forest,
Verify the FSMO roles for an Active Directory domain,
Change the DNS glue record,
Add SPN (Service Principal Name) to an Active Directory computer object,
Add a new user to Active Directory,
Check Active Directory replication sync status across all objects,
Check the replication status of Active Directory,
Verify the state of cluster resources in Windows Server 2008,
Stop a cluster resource in Windows Server 2008,
Start a cluster resource in Windows Server 2008,
Bring a resource online,
Take a resource offline,
Import a configuration or resource,
Rescan disks for new or updated configurations,
Bring a disk online with an assigned drive letter,
Check the status of a disk,
Set a disk to read-write mode,
Bring a disk online,
Take a disk offline,
Verify drive letter assignment after bringing a disk online,
Check disk details using LUN or WWN number,
Change the drive letter of a disk,
Execute a 5250 transaction and check the popup count,
Execute a 5250 process,
Monitor AS/400 operations,
Check the status of a client LPAR (Logical Partition),
Verify the boot disk mapping of a client LPAR,
Activate a client LPAR,
Wait for the LPAR state to update,
Shut down a client LPAR,
Check the status of Ethernet connections,
Bring volume groups online (vary on),
Take volume groups offline (vary off),
Unmount volume groups,
Unmount volume groups (shorthand - VGS),
Unmount a specific volume group (VG),
Unmount an NFS volume,
Replicate a standby trace file,
Sync data folders in a POSIX environment,
Sync data folders for disaster recovery,
Sync a single data file in a POSIX environment,
Remove an HDisk device,
Remove volume groups in a specific configuration,
Mount volume groups,
Mount volume groups (shorthand - VGS),
Mount a specific volume group (VG),
Mount an NFS volume,
Kill a process by its ID or name,
Unmount volume groups in HP systems,
Unmount volume groups in parallel on HP systems,
Deactivate a volume group in HP-UX,
Activate a volume group in HP-UX,
Unmount a file system in HP-UX,
Mount a file system in HP-UX,
Verify if a volume group is active in HP-UX,
Import a volume group using a map file in HP-UX,
Export a volume group to a map file in HP-UX,
Mount volume groups in HP systems,
Mount volume groups in parallel on HP systems,
Export volume groups configuration,
Execute a command using nohup (no hang-up),
Execute a CPSL process,
Enable SLB (Server Load Balancer) real services,
Disable SLB (Server Load Balancer) real services,
Check the status of a specific SLB real service,
Mount a file system in AIX,
Dismount a file system in AIX,
Change text in a file on AIX,
Execute a CPL process,
Run an OS command,
Verify output of an OS command,
Replicate folders using rsync in POSIX environments,
Replicate a file using rsync in POSIX environments,
Replicate a standby control file using Oracle's rsync,
Replicate a standby trace file using Oracle's rsync,
Verify the output of an XCLI command,
Verify the status of a virtual machine (VM),
Check if a failover has completed successfully,
Verify the current state of a VM or resource,
Check reverse replication connection configuration,
Verify the replication mode,
Verify the state of a replication process,
Check if the system is prepared for failover,
Verify if a Hyper-V VM is connected to the specified VLAN ID,
Verify if a Hyper-V VM is disconnected,
Verify if a Hyper-V VM is connected,
Check forward replication connection configuration,
Start a virtual machine (VM),
Initiate a failover process,
Retrieve the IP address of a single-network Hyper-V VM using its MAC address,
Shut down a virtual machine (VM),
Set a VLAN ID to a virtual switch on a Hyper-V VM,
Resume replication for Hyper-V,
Fail over a replica VM,
Disconnect from a Hyper-V VM,
Connect to a virtual switch on a Hyper-V VM,
Verify the primary replication mode in a cluster,
Change the replication mode,
Change the DNS IP address,
Change the IP address of a single-network Hyper-V VM without using a MAC address,
Change the IP address of a cluster Hyper-V VM using its MAC address,
Cancel a failover for a cluster Hyper-V VM,
Retrieve the IP address of a single-network Hyper-V VM with incorrect MAC address handling,
Monitor actions in Hyper-V,
Remove a cluster resource dependency from a dependent resource,
Add a cluster resource dependency to a dependent resource,
Verify a cluster resource dependency in a dependent resource,
Remove a cluster resource from the cluster owner group,
Add a cluster resource to the cluster owner group,
Verify the disk status on a cluster shared volume,
Check the status of a cluster shared volume disk in a failover cluster,
Remove a virtual sharing hard disk from a duplicate VM,
Remove a virtual sharing hard disk,
Enable the virtual hard disk sharing option for a duplicate VM,
Enable the virtual hard disk sharing option,
Disable the virtual hard disk sharing option for a duplicate VM,
Disable the virtual hard disk sharing option,
Check the status of the virtual hard disk sharing option for a duplicate VM,
Verify the status of the virtual hard disk sharing option,
Add a new virtual hard disk to a duplicate VM,
Add a new virtual hard disk,
Monitor Hyper-V operations,
Verify the power-off status of a replica protected resource (PR),
Check the running status of the primary disaster recovery (DR) resource,
Start a primary Hyper-V VM for disaster recovery,
Verify the replication mode of a replica protected resource in Hyper-V,
Check the Hyper-V replication state for a protected resource,
Verify the primary replication mode in Hyper-V,
Confirm the Hyper-V replication state,
Switch to primary replication mode and start replication,
Verify the failover waiting completion status,
Failover a replica to a target host for disaster recovery,
Verify the prepared failover status of a protected resource,
Start failover for a primary replica with pending tasks,
Shut down a primary Hyper-V server in an off state,
Shut down a primary Hyper-V server,
Verify the reverse replication connection configuration for disaster recovery,
Check the forward replication connection configuration for a protected resource,
Verify the primary replication mode for a protected resource in Hyper-V,
Confirm the replica replication mode for disaster recovery in Hyper-V,
Check the Hyper-V replication state for disaster recovery,
Verify the existence status of a virtual switch,
Confirm the reverse replication connection configuration,
Check the forward replication connection configuration,
Verify the completion status of a failed-over resource,
Verify the status of a VM's network adapter,
Check the VLAN ID status of a VM's network adapter,
Stop a virtual machine (VM),
Start a VM failover,
Retrieve the IP address of a single-network Hyper-V VM without using a MAC address,
Retrieve the IP address of a single-network Hyper-V VM using its MAC address,
Set reverse replication for a VM,
Set a VLAN ID to a virtual switch,
Connect a network adapter to a VM,
Prepare a VM for failover,
Execute a command to check Hyper-V status,
Disconnect a network adapter from a VM,
Check the state of a virtual machine (VM),
Verify the replication state of a virtual machine,
Check the replication mode of a VM,
Confirm the Hyper-V replication status,
Change the IP address of a Hyper-V VM without using a MAC address,
Change the IP address of a Hyper-V VM using its MAC address,
Cancel a failover for a standalone Hyper-V VM,
Verify the availability of the target local zone for Leap recovery,
Verify the availability of the source failed zone for Leap recovery,
Check the name of a Nutanix Leap recovery plan,
Assign an IP address to a network interface card in Nutanix,
Validate the recovery plan's target site for Nutanix Leap recovery,
Execute an unplanned failover to a target site for Nutanix Leap recovery,
Perform a planned failover to a target site for Nutanix Leap recovery,
Verify that the recovery point entity VM does not exist at the target local availability zone,
Check if the recovery point entity VM exists at the source failed availability zone,
Verify the existence of a network adapter using its NIC IP address,
Check the existence of a network adapter using its MAC address,
Assign a DNS IP address randomly to a Windows server,
Assign a DNS IP address to a Windows server using its MAC address,
Assign a DNS IP address to a Windows server using its IP address,
Add a network interface card to a virtual machine,
Verify no replication is pending for the protection domain in the inactive site,
Ensure no replication is pending for the protection domain in the active site,
Migrate a protection domain,
Execute a power-on operation for a virtual machine (VM),
Execute a power-off operation for a virtual machine (VM),
Check the state of a virtual machine,
Verify if a VM exists in a consistency group under the protection domain at an inactive site,
Check if a VM exists in a consistency group under the protection domain at an active site,
Verify the status of the protection domain,
Confirm if a consistency group exists under the protection domain in the inactive site,
Verify if a consistency group exists under the protection domain in the active site,
Unmount a datastore,
Unmount a datastore from all ESXi hosts,
Unregister a virtual machine (VM),
Unregister a datastore,
Stop the vSphere HA agent service,
Start the vSphere HA agent service,
Rescan HBA to find a new storage LUN,
Register a virtual machine without associating it with an ESXi host,
Register a virtual machine (VM),
Rescan VMFS to locate storage LUNs,
Power on a virtual machine (VM),
Power on a datastore,
Power off a virtual machine (VM),
Power off a datastore,
Mount a datastore on all ESXi hosts,
Mount a datastore,
Detach a LUN,
Check if a datastore can be unmounted,
Attach a LUN to multiple ESXi hosts,
Attach a LUN,
Update failover network settings for vApp,
Set reverse replication for vApp,
Power on a vApp,
Power off a vApp,
Execute failover for a vApp,
Delete a vApp,
Check the replication state of a vApp,
Check the recovery state of a vApp,
Check the overall health of a vApp,
Verify failover network settings between source and target for vApp,
Check failover network settings for vApp,
Execute vApp synchronization in VMware Cloud Availability (VCAV),
Execute recovery to production for RecoverPoint for VMs,
Perform recovery activities for RecoverPoint for VMs during recovery to production,
Stop test copy activity for RecoverPoint for VMs during recovery to production,
Start test copy for RecoverPoint for VMs during recovery to production,
Execute failover role change for RecoverPoint for VMs,
Perform failover role change activities for RecoverPoint for VMs,
Stop test copy activity for RecoverPoint for VMs during failover,
Start test copy for RecoverPoint for VMs during failover,
Stop a test copy for RecoverPoint for VMs,
Start a test copy for RecoverPoint for VMs,
Check the transfer status of an active consistency group,
Check the recovery activities status of a consistency group,
Verify the details of the production replica in an RPA cluster for a consistency group,
Monitor replication for virtualization using RecoverPoint for VMs,
Rollback a VPG before committing a move,
Commit a VPG move with no policy,
Commit a VPG move without reverse protection,
Commit a VPG move with reverse protection,
Execute VPG failover without committing and shut down the source VM,
Check the initial synchronization state of a volume in VPG,
Verify the moving state before committing in VPG,
Commit a VPG failover with reverse protection,
Stop a VPG failover test,
Rollback a VPG before committing a failover,
Check the protection status of a VPG meeting SLA and state is none,
Execute VPG failover without committing,
Perform a failover test for VPG,
Check the delta synchronization state in VPG,
Verify the recovery site for disaster recovery in VPG,
Check the protection state of VPG during failing over or rolling back,
Check the protection state of VPG during failover before committing,
Verify the failing-over protection status of VPG,
Failover using predefined network for RecoverPoint for VMs,
Enable the latest image in RecoverPoint for VMs,
Start a replica transfer in RecoverPoint for VMs,
Add an additional disk to a VM,
Verify the volume mount status,
Check the unmount status of a volume,
Verify a storage LUN,
Check the mount status of a volume,
Verify a disk,
Check the device associated with a disk,
Verify the attach status of a disk,
Power on a virtual machine in vCenter,
Power off a virtual machine in vCenter,
Check the running state of a VM in vCenter,
Remove a virtual machine in vCenter,
Remove a snapshot in vCenter,
Provision a new VM in vCenter,
Power on a VM in vCenter,
Power off a VM in vCenter,
Execute a command on a VM in vCenter,
Create a linked clone in vCenter,
Check the VM tools status in vCenter,
Verify the power state of a VM in vCenter,
Check if a VM exists in vCenter,
Unmount a VM,
Unregister a VM,
Remove a snapshot from a VM,
Register a virtual machine,
Mount a VM,
Check if a VM is available,
Create a snapshot for a VM,
Verify if a VM is running,
Update the hostname of a Windows server,
Update network information for a VM,
Rescan all adaptors,
Replicate a virtual machine.
Remove a virtual machine (VM),
Remove a LUN from an ESXi host,
Remove a guest VM system from a domain,
Remove an additional disk from a virtual machine,
Power on a machine,
Power off a machine,
Unmount a NepApp VM,
Mount using a VMFS UUID,
Join a guest VM system to a domain,
Check if a machine is running,
Execute a VMware PowerCLI command,
Run a PowerCLI script,
Execute a specialized VMware PowerCLI command for NCheOp,
Execute a command to check VM status,
Detach a LUN from an ESXi host,
Create a new virtual machine (VM),
Create a linked clone of a virtual machine,
Check the VM tools status,
Verify if a VM exists,
Change the hostname of a guest VM,
Attach LUNs to an ESXi host,
Re-protect a recovery plan using VMware SRM,
Perform a planned migration failover using VMware SRM,
Execute disaster recovery failover using VMware SRM,
Initiate a recovery plan using VMware SRM,
Execute a test recovery plan using VMware SRM,
Clean up a recovery plan after execution in VMware SRM,
Check the state of a recovery plan in VMware SRM,
Verify the state of a protection group in VMware SRM,
Reproduce a recovery plan,
Check the state of a recovery plan,
Monitor recovery actions in VMware SRM,
Verify the target local availability zone for Leap recovery plan,
Verify the source failed availability zone for Leap recovery plan,
Validate the recovery plan name for Nutanix Leap,
Assign an IP address to a network interface card in Nutanix,
Validate the target site state for Nutanix Leap recovery plan,
Perform an unplanned failover to the target site using Nutanix Leap,
Execute a planned failover to the target site using Nutanix Leap,
Check if the recovery point entity VM does not exist at the target local AZ,
Check if the recovery point entity VM exists at the source failed AZ,
Verify the existence of a network adapter using its NIC IP address,
Verify the existence of a network adapter using its MAC address,
Randomly assign a DNS IP address to a Windows server,
Assign a DNS IP address to a Windows server using a MAC address,
Assign a DNS IP address to a Windows server using its IP address,
Add a network interface card to a VM,
Verify no pending replication for an inactive site under a protection domain,
Verify no pending replication for an active site under a protection domain,
Migrate a protection domain,
Execute power-on operation for a virtual machine,
Execute power-off operation for a virtual machine,
Check the state of a virtual machine,
Verify if a VM exists in a consistency group under a protection domain for an inactive site,
Verify if a VM exists in a consistency group under a protection domain for an active site,
Check the status of a protection domain,
Verify if a consistency group exists in a protection domain for an inactive site,
Verify if a consistency group exists in a protection domain for an active site,
Verify no replication pending for a protection domain in an inactive site,
Verify no replication is pending for the protection domain in an active site,
Activate a protection domain failover from the disaster recovery site,
Check if a virtual machine exists in the consistency group under the protection domain in an active site,
Verify if a VM exists in the consistency group under the protection domain in the active state,
Check the protection domain status for a specific site,
Verify the member status of the consistency group under the protection domain in the active state,
Check the consistency group member status under the protection domain in the active state,
Acknowledge and resolve recent informative alerts for a protection domain,
Verify the disk status on a cluster shared volume,
Check the cluster shared volume disk status in a failover cluster,
Remove a duplicate virtual sharing hard disk from a virtual machine,
Remove a virtual sharing hard disk,
Enable the virtual hard disk sharing option for a duplicate virtual machine,
Enable the virtual hard disk sharing option,
Disable the virtual hard disk sharing option for a duplicate virtual machine,
Disable the virtual hard disk sharing option,
Check the status of the virtual hard disk sharing option for a duplicate virtual machine,
Check the status of the virtual hard disk sharing option,
Add a new virtual hard disk to a duplicate virtual machine,
Add a new virtual hard disk,
Monitor operations related to Hyper-V,
Verify the power-off status of a replica PR,
Verify the running status of the primary disaster recovery site,
Start a primary Hyper-V virtual machine in the disaster recovery site,
Verify the replication mode of the replica in the primary region,
Check the Hyper-V replication state for the primary region,
Verify the replication mode of the primary site,
Check the replication state of Hyper-V,
Switch the replication mode to primary replication and start replication,
Verify the failover wait commit status of the replication,
Fail over the replica on the target host in the disaster recovery site,
Check the prepared failover status for the primary region,
Start failover for a primary replication in a pending state,
Shut down the primary Hyper-V virtual machine,
Shut down the primary Hyper-V in an off state,
Verify the reverse replication connection configuration for the disaster recovery site,
Verify the forward replication connection configuration for the primary region,
Check the primary replication mode for the primary region,
Verify the replication mode of the replica in the disaster recovery site,
Check the replication state of Hyper-V in the disaster recovery site,
Verify the existence status of a virtual switch.
Verify the reverse replication connection status,
Verify the forward replication connection status,
Verify if the failover is waiting for completion status,
Verify the status of the virtual machine network adapter,
Verify the VLAN ID status of the virtual machine network adapter,
Stop the virtual machine,
Start the virtual machine failover,
Configure the Hyper-V VM's IP address without the MAC address in a single network,
Configure the Hyper-V VM's IP address with the MAC address in a single network,
Set the virtual machine reverse replication configuration,
Set the VLAN ID for the virtual switch,
Set the network adapter connection for the virtual machine,
Prepare the virtual machine for failover,
Execute the check Hyper-V command,
Disconnect the network adapter connection,
Check the virtual machine state,
Check the virtual machine replication state,
Check the virtual machine replication mode,
Check the Hyper-V replication status,
Change the Hyper-V virtual machine's IP address without the MAC address,
Change the Hyper-V virtual machine's IP address with the MAC address,
Cancel failover for a standalone Hyper-V virtual machine,
Shutdown the zone,
Detach the zone,
Check if the zone is up,
Check the status of the zone,
Check if the zone is down,
Boot the zone,
Attach the zone,
Verify the power-on status of an LDOM (Logical Domain),
Verify the power-off status of an LDOM,
Unbind the domain from LDOM,
Stop the LDOM domain,
Start the LDOM domain,
Remove the LDOM domain,
Bind the domain to LDOM,
Add memory to the LDOM,
Add a domain to the LDOM,
Add CPU to the LDOM,
Power on the AIX LPAR (Logical Partition),
Power off the AIX LPAR,
Check if the AIX LPAR is powered on,
Check if the AIX LPAR is powered off,
Check if the Redis array is empty in DR (Disaster Recovery) environment,
Check if the Redis array is empty in PR (Production) environment,
Verify the row count of a single table in Redis DR environment,
Verify the row count of a single table in Redis PR environment,
Start the Postgres service,
Check the content for availability,
Add content recovery to the system,
Check the recovery file in the primary server,
Verify if the recovery process is complete,
Restart the service to ensure proper functioning,
Create a trigger file to initiate processes,
Check the recovery file for consistency,
Stop the Postgres service for maintenance,
Verify the status of the recovery process,
Verify if the server is running correctly,
Verify the PostgreSQL trigger file name and path for accuracy,
Verify the DR replication status of the PostgreSQL server,
Verify the PR replication status of the PostgreSQL server,
Verify the state of the PostgreSQL database cluster,
Verify that the PostgreSQL XLog LSN (Log Sequence Number) is matching,
Start the PostgreSQL server to bring it online,
Restart the PostgreSQL server to resolve issues or apply updates,
Execute a DR (Disaster Recovery) failover to a standby server,
Verify the DR trigger file path for accuracy,
Verify the shutdown status of the PR database cluster,
Stop the PostgreSQL server for maintenance or troubleshooting,
Create a recovery configuration file at PR (Production Recovery),
Verify that the WAL (Write-Ahead Logging) LSN matches between PR and DR,
Verify the status of the database cluster,
Execute a Postgres SQL command for troubleshooting or updates,
Perform a cluster changeover for maintenance or failover purposes,
Promote the cluster state to ensure high availability,
Verify the DR replication status for proper failover operation,
Verify the status of the cluster for health and availability,
Verify the PostgreSQL service status to ensure it's running,
Verify the current WAL location in PostgreSQL,
Verify the PostgreSQL recovery status to ensure data integrity,
Verify the PR replication status for proper database syncing,
Verify the cluster state to ensure smooth operation,
Monitor PostgreSQL performance and health,
Execute PostgreSQL recovery process for critical data,
Start the PostgreSQL service to bring it online,
Execute PostgreSQL recovery from a catalog file,
Stop the PostgreSQL service for scheduled maintenance,
Synchronize two clusters using PostgreSQL’s PG Rewind feature,
Create a recovery configuration for a MV (Multi-Version) PostgreSQL setup,
Test the PG Rewind process to ensure data synchronization,
Create a standby file in the secondary server for failover purposes,
Test a custom action for process validation,
Test a Power shell (powershell) script for system automation,
Test an action to ensure it works as expected,
Start the Sybase server for database operations,
Verify if the Sybase server is up and running,
Verify if the Sybase server is down and troubleshoot if necessary,
Shut down the Sybase database for maintenance,
Perform a checkpoint operation on the Sybase database to ensure consistency,
Replicate a Sybase BCP (Bulk Copy Program) table for backup or recovery,
Dump the transaction log file for standby access purposes,
Enable Sybase database for standby access during replication,
Bring the Sybase database online after maintenance or issues,
Load a transaction log file into Sybase for recovery or auditing,
Execute a Sybase ISQL command for administrative tasks,
Generate the last transaction log for Sybase for disaster recovery,
Execute the Sybase BCP in command to import data into Sybase,
Execute the Sybase BCP out command to export data from Sybase,
Verify the status of the database to ensure proper operation,
Verify the status of the backup server to confirm functionality,
Verify the status of the Sybase data server for availability,
Copy the last transaction log for Sybase for disaster recovery,
Execute Sybase ISQL command checks for validation,
Resume the Sybase replication connection to continue data syncing,
Start the Sybase replication agent on the primary server for data syncing,
Check the Sybase replication role switch status to ensure smooth failover,
Check the Sybase replication switchover status to confirm the primary role change,
Failover Sybase from the primary to the standby server for high availability,
Verify if the Sybase replication agent is down during troubleshooting,
Stop the Sybase replication agent on the primary server for maintenance,
Verify the status of the Sybase standby database,
Verify the status of the Sybase primary database for health checks,
Test a command for database copy verification (e.g., Copy 9011),
Test the WW (Worldwide) replication process for database consistency (e.g., Copy 8328),
Verify the status of a database copy (e.g., Copy 3563),
Restore the last backup log with no recovery in LSSQL with pre-check (e.g., Copy 1761),
Enable log shipping with target database restoring in LSSQL (e.g., Copy 2143),
Restore the last log with standby DR in LSSQL (e.g., Copy 1751),
Restore the last log with recovery DR in LSSQL (e.g., Copy 6317),
Validate the backup with primary and secondary databases in LSSQL (e.g., Copy 8706),
Copy the tail log backup for DR in LSSQL (e.g., Copy 4404),
Verify that the primary backup transaction log exists in LSSQL (e.g., Copy 4304),
Execute secondary log shipping in LSSQL for data replication (e.g., Copy 6983),
Execute primary log shipping reverse in LSSQL for replication consistency (e.g., Copy 6024),
Verify the job name associated with log shipping in LSSQL (e.g., Copy 9987),
Verify the source and destination backup directory in LSSQL (e.g., Copy 626),
Verify the backup directory and shared path in LSSQL (e.g., Copy 2758),
Execute 2 DR site primary log shipping in LSSQL (e.g., Copy 3220),
Restore the last backup log with recovery in LSSQL (e.g., Copy 8040),
Execute 3-site secondary log shipping in LSSQL (e.g., Copy 7757),
Execute 3 DR site primary log shipping in LSSQL (e.g., Copy 2643),
Restore the log with standby in LSSQL (e.g., Copy 1265),
Verify the 3 DR site log file sequence in LSSQL (e.g., Copy 9302),
Make the database writable after the last restored file failover in LSSQL (e.g., Copy 1308),
Execute the secondary log shipping job schedule in LSSQL (e.g., Copy 8777),
Execute the primary log shipping job schedule in LSSQL (e.g., Copy 504),
Check the database mode in SQL NLS for configuration (e.g., Copy 5504),
Check the database state in SQL NLS for consistency (e.g., Copy 5936),
Verify the database mirroring status to ensure synchronization (e.g., Copy 8342),
Perform a failover of database mirroring (e.g., Copy 1092),
Kill the MSSQL process by its name for troubleshooting or termination (e.g., Copy 4082),
Remove the restore job in ILSSQL to clean up unnecessary jobs (e.g., Copy 4676),
Remove the copy job in ILSSQL to clean up unnecessary jobs (e.g., Copy 8019),
Attach a database in SQL Server for use (e.g., Copy 9781),
Set the database option to single-user mode (e.g., Copy 2035),
Take the database offline in SQL Server for maintenance (e.g., Copy 5634),
Migrate database roles in LSSQL (e.g., Copy 5841),
Migrate logins in ILSSQL (e.g., Copy 6748),
Execute the secondary log shipping in ILSSQL (e.g., Copy 8481),
Execute the primary log shipping in ILSSQL (e.g., Copy 1013),
Remove the backup job in ILSSQL (e.g., Copy 8703),
Run the backup job in ILSSQL (e.g., Copy 1312),
Dismount the Linux or unmount a service in Linux (e.g., Copy 8956),
Mount the Linux (e.g., Copy 1579),
Migrate database roles in ILSSQL (e.g., Copy 6694),
Migrate logins in LSSQL (e.g., Copy 2225),
Detach the SQL database for maintenance or troubleshooting (e.g., Copy 6169),
Attach the SQL database for restoration or access (e.g., Copy 4977),
Test the NLS (National Language Support) configuration (e.g., Copy 697),
Remove the secondary job in LSSQL (e.g., Copy 5188),
Check if the database entry exists on the primary server in LSSQL (e.g., Copy 6825),
Check if the database entry exists on the secondary server in LSSQL (e.g., Copy 3771),
Check if the primary log shipping exists in LSSQL (e.g., Copy 1013),
Check if primary and secondary log shipping exist in LSSQL (e.g., Copy 153),
Restore secondary log shipping in LSSQL (e.g., Copy 6109),
Restore primary log shipping in LSSQL (e.g., Copy 8024),
Restore the last backup log with no recovery in LSSQL (e.g., Copy 5534),
Set the database to multi-user access mode in LSSQL (e.g., Copy 3724),
Generate the last backup log in LSSQL (e.g., Copy 8879),
Kill all sessions in LSSQL for troubleshooting or maintenance (e.g., Copy 6006),
Monitor the NLS settings in MSSQL (e.g., Copy 5990),
Fix orphaned users in the SQL database (e.g., Copy 144),
Set the DR database in multi-user access mode in ILSSQL (e.g., Copy 7365),
Update the restore job with the DR IP address in LSSQL (e.g., Copy 7250),
Update the restore job with the DR IP address in ILSSQL (e.g., Copy 3921),
Update the copy job with the DR IP address in LSSQL (e.g., Copy 1697),
Update the copy job with the DR IP address in ILSSQL (e.g., Copy 7043),
Update the backup job with the PR IP address in ILSSQL (e.g., Copy 6952),
Update the backup job with the PR IP address in LSSQL (e.g., Copy 5431),
Remove primary and secondary log shipping in LSSQL (e.g., Copy 3349),
Remove primary log shipping in ILSSQL (e.g., Copy 9087),
Remove secondary log shipping in LSSQL (e.g., Copy 6454),
Remove secondary log shipping in ILSSQL (e.g., Copy 9524),
Remove primary log shipping in LSSQL (e.g., Copy 7851),
Remove primary and secondary log shipping in ILSSQL (e.g., Copy 3414),
Restore the database with recovery in LSSQL (e.g., Copy 8869),
Restore the database with recovery in ILSSQL (e.g., Copy 3308),
Set the database to single-user access mode in ILSSQL for DR (e.g., Copy 2666),
Kill the session in ILSSQL for DR (e.g., Copy 1484),
Set the database to multi-user access mode in ILSSQL for the primary server (e.g., Copy 6753),
Verify the database is in single-user access mode in LSSQL (e.g., Copy 3803),
Verify the PR database is in single-user access mode in ILSSQL (e.g., Copy 4681),
Set the database to single-user access mode in LSSQL (e.g., Copy 9415),
Set the database to single-user access mode on the primary server in ILSSQL (e.g., Copy 4551),
Import login roles on the DR server in ILSSQL (e.g., Copy 5070),
Import logins on the DR server in ILSSQL (e.g., Copy 2279),
Export login roles from the production server in LSSQL (e.g., Copy 951),
Export logins from the production server in LSSQL (e.g., Copy 8248),
Kill the session on the primary server in LSSQL (e.g., Copy 3833),
Fix orphaned users in LSSQL (e.g., Copy 4095),
Enable the job in LSSQL for scheduled operations (e.g., Copy 978),
Generate the last backup log with no recovery in LSSQL (e.g., Copy 9108),
Kill the database session with a timeout in LSSQL (e.g., Copy 8578),
Run a job in LSSQL for execution (e.g., Copy 7046),
Disable the job in LSSQL to pause operations (e.g., Copy 903),
Generate the backup log sequence in LSSQL (e.g., Copy 2486),
Run the restore job in LSSQL for database recovery (e.g., Copy 5153),
Run the copy job in LSSQL for database replication (e.g., Copy 7219),
Disable the restore job in LSSQL (e.g., Copy 9208),
Disable the copy job in LSSQL (e.g., Copy 5245),
Disable the backup job in LSSQL (e.g., Copy 5566),
Check the backup job status in LSSQL (e.g., Copy 9896),
Verify the transaction log shipping state in LSSQL (e.g., Copy 5868),
Check the database access mode in LSSQL (e.g., Copy 5213),
Check the database recovery model in LSSQL (e.g., Copy 1968),
Verify the database recovery mode in LSSQL (e.g., Copy 4754),
Verify the updateability state in DR in LSSQL (e.g., Copy 9100),
Verify the updateability state in PR in LSSQL (e.g., Copy 8009),
Verify the database status in LSSQL (e.g., Copy 2448),
Execute an MSSQL command for database operations,
Execute a check command for MSSQL configurations,
Compare the row count in a SQL Server table in DR (e.g., Copy 1308),
Compare the row count in a SQL Server table in PR (e.g., Copy 504),
Unjoin a database from the availability group for maintenance,
Restore the database with recovery only in DR in LSSQL,
Remove the primary database from the availability group for changes,
Modify the AG mode for the availability group,
Join the secondary database to the availability group for syncing,
Check if a database is joined or unjoined from the availability group,
Add the primary database to the availability group for high availability,
Test the health of all availability replicas in production,
Test the health of all availability replicas in DR,
Synchronize AlwaysOn failover with a specific secondary replica,
Synchronize AlwaysOn failover with a random secondary replica,
Suspend replication for all databases in an availability group for maintenance,
Resume replication in an availability group after suspension,
Perform a manual planned failover for a controlled switch over to a secondary replica,
Forcefully failover to a secondary replica in an emergency situation,
Check the suspended state of replication for availability groups,
Check if replication has been resumed after suspension,
Check the status of the availability group in the production environment,
Enable or disable the availability group in the production environment,
Check the status of the availability group in the DR environment,
Verify if all connections are allowed in both production and DR environments,
Check the state of all databases in an availability group,
Synchronize AlwaysOn failover with a specific secondary replica,
Synchronize AlwaysOn failover with a random secondary replica for redundancy,
Check the running states of SQL Server instances,
Verify the database role and mirror state in SQL mirroring,
Execute a database cluster changeover operation,
Monitor SQL Server mirroring state and health,
Change the cluster node weight for multiple nodes in a failover cluster,
Change the cluster node weight for a single node in a failover cluster,
Check the cluster node weight for multiple nodes in a failover cluster,
Check the cluster node weight for a single node in a failover cluster,
Associate a cluster name with a group in SQL Server failover clustering,
Restore the last log in SQL DSM for a specific database,
Migrate server roles to the DR server in SQL DSM,
Restore a log in SQL DSM for database recovery,
Scan a file from an application server for processing or replication,
Migrate server roles to the PR server in SQL DSM,
Migrate the logging roles to the PR server in SQL DSM,
Migrate the logging roles to the DR server in SQL DSM,
Restore the database with recovery in SQL DSM for both primary and secondary servers,
Kill the process on the secondary SQL Server instance in SQL DSM,
Kill the process on the primary SQL Server instance in SQL DSM,
Generate the last log for failover control in SQL DSM,
Set the database option in SQL DSM for optimized performance,
Sync SQL Server 2000 data across platforms in SQL DSM,
Move a CSV file to the database server for integration,
Replicate files from the application server to the database server for backup or processing,
Download the response file from the production server for analysis,
Create a CSV file from response data for integration or reporting,
Sync application data with the database server for consistency,
Monitor the NLS (National Language Support) settings in MSSQL for compatibility,
Fix orphaned users in the SQL database to restore integrity,
Set the DR database to multi-user access mode in ILSSQL for general use,
Update the restore job with the DR IP address in LSSQL for disaster recovery scenarios,
Update the restore job with the DR IP address in ILSSQL for consistency,
Update the copy job with the DR IP address in LSSQL for redundancy,
Update the copy job with the DR IP address in ILSSQL for replication consistency,
Update the backup job with the PR IP address in ILSSQL for primary operations,
Update the backup job with the PR IP address in LSSQL for primary server operations,
Remove primary and secondary log shipping in LSSQL to reset configuration,
Remove primary log shipping in ILSSQL for decommissioning,
Remove secondary log shipping in LSSQL to stop replication,
Remove secondary log shipping in ILSSQL for consistency management,
Remove primary log shipping in LSSQL for reset or changes,
Remove primary and secondary log shipping in ILSSQL for cleanup,
Restore the database with recovery in LSSQL for both primary and secondary servers,
Restore the database with recovery in ILSSQL for production or DR restoration,
Set the database to single-user access mode for DR purposes in ILSSQL,
Kill the session on the DR server in ILSSQL for troubleshooting,
Set the database to multi-user access mode on the primary server in ILSSQL,
Verify the database is in single-user access mode in LSSQL for recovery,
Verify the PR database is in single-user access mode in ILSSQL for maintenance,
Set the database to single-user access mode in LSSQL for maintenance or recovery,
Set the database to single-user access mode on the primary server in ILSSQL,
Import login roles to the DR server in ILSSQL for replication,
Import logins to the DR server in ILSSQL for synchronization,
Export login roles from the production server for migration or backup,
Export logins from the production server for replication or backup,
Kill the session on the primary server in LSSQL for troubleshooting,
Fix orphaned users in LSSQL for restoring user mappings,
Enable the job in LSSQL for scheduled operations to run,
Generate the last backup log with no recovery in LSSQL for failover preparation,
Kill the database session with a timeout in LSSQL for resource management,
Run a job in LSSQL for scheduled database operations,
Disable a job in LSSQL to stop scheduled operations temporarily,
Verify the log file sequence in LSSQL for consistency checking,
Run the restore job in LSSQL for database recovery from backup,
Run the copy job in LSSQL for database replication or migration,
Disable the restore job in LSSQL for maintenance or troubleshooting,
Disable the copy job in LSSQL for maintenance or troubleshooting,
Disable the backup job in LSSQL for scheduled operations pause,
Check the backup job status in LSSQL for monitoring progress,
Verify the transaction log shipping state in LSSQL for replication monitoring,
Check the database access mode in LSSQL for operation validation,
Check the database recovery model in LSSQL for consistency,
Restore secondary log shipping in LSSQL for failover management,
Restore primary log shipping in LSSQL for failover management,
Generate the last backup log in LSSQL for database recovery preparation,
Kill all sessions in LSSQL for system cleanup or troubleshooting,
Restore the last backup log with no recovery in LSSQL for failover,
Set the database to multi-user access mode in LSSQL after maintenance,
Remove the secondary job in LSSQL for replication cleanup,
Check if the database entry exists on the primary server in LSSQL,
Check if the database entry exists on the secondary server in LSSQL,
Check if the primary log shipping exists in LSSQL for configuration verification,
Check if primary and secondary log shipping exist in LSSQL for redundancy,
Verify the database recovery mode in LSSQL for consistency checks,
Verify the updateability state in DR in LSSQL for operational readiness,
Verify the updateability state in PR in LSSQL for operational readiness,
Verify the status of the database to ensure it is online and operational,
Test the National Language Support (NLS) compatibility in SQL Server,
Restore the last backup log with no recovery in LSSQL with prechecks,
Enable log shipping with the target database in restoring mode in LSSQL,
Restore the last transaction log with standby mode in DR using LSSQL,
Restore the last transaction log with recovery mode in DR using LSSQL,
Validate backup consistency between primary and secondary servers in LSSQL,
Copy the tail-log backup to DR using LSSQL,
Verify if the primary backup transaction log exists in LSSQL,
Execute the secondary log shipping process in LSSQL,
Execute reverse primary log shipping in LSSQL for failback,
Verify if the correct job name is associated with log shipping,
Verify the source and destination backup directory for log shipping,
Check the backup directory and shared path for consistency,
Execute the primary log shipping process for a secondary DR site,
Restore the last backup log with recovery in LSSQL for database recovery,
Execute secondary log shipping for a three-site configuration,
Execute primary log shipping for a three-DR site configuration,
Restore the transaction log in standby mode,
Verify the log file sequence for a three-DR site setup,
Make the database writable after failover of the last restored file in LSSQL,
Execute the secondary log shipping job schedule in LSSQL,
Execute the primary log shipping job schedule in LSSQL,
Check the database mode for SQL NLS compatibility,
Check the database state for SQL NLS validation,
Verify the database mirroring status for consistency,
Perform a mirroring failover for database redundancy,
Kill a specific SQL Server process by name for troubleshooting,
Remove the restore job in ILSSQL for cleanup,
Remove the copy job in ILSSQL for cleanup,
Attach a database to the SQL Server instance,
Set the database option to single-user mode for maintenance,
Take the database offline for maintenance or troubleshooting,
Migrate server roles in LSSQL for role consistency,
Migrate logins in ILSSQL for login synchronization,
Execute secondary log shipping in ILSSQL for redundancy,
Execute primary log shipping in ILSSQL for DR operations,
Remove the backup job in ILSSQL for cleanup,
Run the backup job in ILSSQL for scheduled operations,
Dismount the Linux process or unmount a service in linux,
Mount the Linux,
Migrate roles in ILSSQL for role management,
Migrate logins in LSSQL for login consistency,
Detach a SQL database for relocation or maintenance,
Attach a SQL database after detachment or migration,
Verify if the database entry exists on the primary server in LSSQL,
Verify if the database entry exists on the secondary server in LSSQL,
Check if primary log shipping exists in LSSQL,
Check if primary and secondary log shipping exist in LSSQL,
Verify the database mode in SQL NLS for compatibility,
Change the availability mode for AlwaysOn Availability Groups,
Execute a normal failover in AlwaysOn for redundancy,
Perform a pre-flight check for database sync state in AlwaysOn,
Monitor the AlwaysOn Availability Group status,
Resume data movement in AlwaysOn Availability Groups,
Execute a forced failover in AlwaysOn for disaster recovery,
Check the availability mode of an AlwaysOn Availability Group,
Check the role of the server in an AlwaysOn setup,
Verify the custom priority state of an AlwaysOn replica,
Check the health status of an AlwaysOn setup,
Monitor MongoDB status and operations,
Check the replication lag status in MongoDB,
Set the priority of a replica set in MongoDB,
Verify the primary state of a MongoDB replica set,
Verify the secondary state of a MongoDB replica set,
Perform a DB2 HADR takeover for disaster recovery,
Start the DB2 database,
Deactivate and start the DB2 database,
Activate the DB2 database for operations,
Switch back to the primary DB2 database,
Perform a switchover operation in DB2 for planned maintenance,
Verify if the DB2 database is online,
Verify if DB2 HADR is active,
Stop the DB2 HADR process,
Start the DB2 HADR primary instance,
Deactivate and terminate the DB2 database,
Start the DB2 HADR standby instance,
Deactivate the DB2 database for maintenance,
Terminate the DB2 instance,
Verify if the DB2 database is active,
Unquiesce the DB2 database for operations,
Check if the DB2 database is quiesced,
Quiesce the DB2 database for maintenance,
Verify the log gap in DB2 HADR,
Verify the log position in DB2 HADR,
Check if DB2 HADR state is in PEER mode,
Check if DB2 HADR role is standby,
Check if DB2 HADR role is primary,
Verify if the DB2 database is in standby mode,
Verify if the DB2 database is in primary mode,
Monitor DB2 HADR status for health and consistency,
Verify the status of a virtual machine (VM),
Check if the VM failover is in a waiting completion status,
Verify the state of a VM or server,
Check the reverse replication connection configuration,
Verify the replication mode for consistency,
Verify the replication state for health,
Check if the system is prepared for failover status,
Verify the VLAN ID connectivity status for Hyper-V VMs,
Verify the disconnected status for Hyper-V VMs,
Verify the connection status of a Hyper-V virtual machine (VM),
Check the forward replication connection configuration for Hyper-V,
Start a virtual machine on Hyper-V,
Initiate a failover process for a VM,
Assign a single network cluster Hyper-V VM with an IP address and its MAC address,
Shut down a virtual machine on Hyper-V,
Set a VLAN ID to the virtual switch associated with a Hyper-V VM,
Resume replication for a Hyper-V VM,
Failover a replica virtual machine on Hyper-V,
Disconnect a Hyper-V VM from the network or switch,
Connect a Hyper-V VM to a virtual switch,
Verify the primary replication mode of a Hyper-V cluster,
Change the replication mode for a Hyper-V setup,
Change the DNS IP address configuration for a system,
Change the cluster Hyper-V VM IP address without modifying the MAC address,
Change the cluster Hyper-V VM IP address along with the MAC address,
Cancel an ongoing failover process for a Hyper-V VM in a cluster,
Assign a single network cluster Hyper-V VM with an incorrect MAC address,
Perform monitoring actions on a Hyper-V setup,
Remove cluster resource dependencies from dependent resources,
Add a cluster resource dependency to dependent resources,
Verify the cluster resource dependencies in dependent resources,
Remove a cluster resource from the cluster owner group,
Add a cluster resource to the cluster owner group,
Verify the master log file and its position on master and slave servers,
Stop the slave process in a database replication setup,
Check the running status of the slave SQL process,
Verify the state of the slave I/O process,
Verify the running status of the slave I/O process,
Read the relay master log file in a database replication setup,
Read the master log position for replication,
Check the slave status in a MySQL replication setup,
Check the status of the MySQL service,
Read the relay master log file on the master server,
Read the master log position on the master server,
Verify the master log file in a replication setup,
Execute a MySQL database command,
Execute a MySQL database check command,
Verify the master log position in a replication setup,
Check the connection status of the database,
Change the master host and log file in a replication setup,
Change the master host in a replication setup,
Set the global read-only mode to OFF,
Set the global read-only mode to ON,
Flush logs in a MySQL database,
Flush tables in a MySQL database,
Verify read/write operations on the master instance,
Enable read/write mode on the master instance,
Show the master status in a MySQL replication setup,
Start the slave process on the master server,
Check if the slave process has stopped,
Monitor the MySQL replication setup,
Stop replication on a slave server,
Set the master instance to read-only mode,
Verify the read-only status of a slave instance,
Verify replication user connectivity from slave to master,
Verify the read-only status on the master instance,
Verify that SQL binary logging is enabled on the master,
Verify the read-only status of the master instance,
Verify the SQL log position on both master and slave servers,
Recover a standby database in a disaster recovery setup,
Prepare a database for shutdown,
Execute a pre-shutdown redo control script for PR,
Mount a standby database for recovery,
Create a control script for database management,
Create a control file from a trace file for recovery,
Backup the redo control file for database recovery,
Add a temporary tablespace to the database,
Restore the standby control file using ASM,
Recover a database using ASM,
Open the database using ASM,
Create a standby control file using ASM,
Start up a database in no-mount mode,
Start up a database in mount mode,
Shut down a standby database,
Shut down a primary database,
Shut down a disaster recovery (DR) database,
Replicate folders using rsync in a POSIX environment,
Replicate files using rsync in a POSIX environment,
Replicate a standby control file using Oracle rsync,
Replicate a standby trace file using Oracle rsync,
Update the disaster recovery operation status,
Verify the state of the database,
Start a MaxDB database instance,
Stop a MaxDB database instance,
Execute a database check command,
Validate the state of a Data Guard Broker (DGB) setup,
Switch over in a Data Guard Broker setup,
Perform a failover in a Data Guard Broker setup,
Convert a snapshot standby database in DGB,
Convert a physical standby database in DGB,
Verify the configuration of a Data Guard Broker setup,
Start recovery for Oracle Data Guard 12c,
Start the database for Oracle Data Guard 12c,
Shut down the database for Oracle Data Guard 12c,
Mount the database for Oracle Data Guard 12c,
Verify a switchover in Oracle Data Guard 12c,
Perform a switchover in Oracle Data Guard 12c,
Stop the Node Manager service in Oracle WebLogic,
Stop a managed server in Oracle WebLogic,
Stop the HTTP server,
Stop the administration server,
Start the Node Manager service,
Start a managed server,
Start the HTTP server,
Start the administration server,
Verify the database mode after reverting a snapshot,
Verify the database role after reverting a snapshot,
Verify the database role before reverting a snapshot,
Verify the database role before taking a snapshot,
Check if the flashback feature is turned off,
Convert a snapshot standby database to a physical standby database,
Verify the database role after converting a snapshot,
Verify the database mode after converting a snapshot,
Verify the current system change number (SCN),
Convert a database to a snapshot standby,
Verify the flashback retention target for the database,
Verify the recovery file destination size for the database,
Verify the recovery file destination for the database,
Verify the Data Guard status,
Verify the database mode before taking a snapshot,
Verify the database mode before reverting a snapshot,
Verify the maximum sequence number in Data Guard for Windows,
Open the database in Data Guard,
Shut down the primary database in Data Guard,
Establish a Data Guard connector,
Execute an SQL command,
Perform a while-check operation,
Mount a standby database in Data Guard,
Check the job status in Data Guard,
Test the chatbot functionality,
Test role and mode actions in Data Guard,
Start an Oracle instance in open mode using SRVCTL,
Monitor Oracle Data Guard actions,
Perform a database switch over using the `ALTER DATABASE` command,
Verify the `ALTER DATABASE` switchover operation,
Stop a database using SRVCTL,
Start a database using SRVCTL,
Stop an Oracle instance using SRVCTL,
Start an Oracle instance in mount mode using SRVCTL,
Check the open mode of a database,
Mount a database using the `ALTER DATABASE` command,
Start the database in no-mount mode,
Check the switchover status in Data Guard,
Execute a SQL command for verification,
Switch a Data Guard standby database to primary,
Switch a Data Guard primary database to standby,
Switch the log file in Data Guard,
Recover a standby database in Data Guard,
Verify the user status in Data Guard,
Verify the maximum sequence number in Data Guard,
Verify the database mode and role in Data Guard,
Switch to the current log file in Data Guard,
Check the job status in PR,
Start the recovery process in a new disaster recovery setup,
Shut down the disaster recovery environment,
Mount a new disaster recovery standby database,
Shut down the primary database,
Verify the archive log sequence,
Verify the switchover status in Data Guard,
Monitor Oracle RAC actions,
Switch a database in Oracle Data Guard using API,
Check database synchronization status using Oracle Data Guard API,
Verify the log sequence in Oracle Data Guard,
Shut down the primary database for switching,
Shut down the PR database for Windows disaster recovery,
Shut down the PR database for Windows,
Stop the Oracle listener,
Stop the listener with a password,
Start the database in mount mode with force,
Start the Oracle listener,
Start the listener with a password,
Start the database in no-mount mode,
Start the database in mount mode,
Start the database,
Start the database in read-only mode,
Start the database standby for Windows disaster recovery,
Start the database standby for Windows,
Start the database standby,
Start the database in read-write mode for Windows disaster recovery,
Start the database in read-write mode for Windows,
Start the database in read-write mode,
Shut down the standby database for Windows disaster recovery,
Shut down the database,
Shut down the database instance,
Restore a standby control file,
Replicate the standby control file,
Recover the standby database for Windows disaster recovery,
Recover the standby database file,
Recover the database,
Execute a pre-shutdown redo control script,
Open the database,
Terminate Oracle sessions,
Check if the checkpoint count is one,
Generate a redo control backup script for Windows disaster recovery,
Generate a redo control backup script for Windows,
Flashback to a restore point,
Execute an SQL script with a password,
Execute an SQL script with an environment password,
Execute a redo control backup for Windows disaster recovery,
Execute a redo control backup for Windows,
Execute a database command,
Drop a restore point,
Switch the database log for Windows disaster recovery,
Create a temporary tablespace file,
Create a temporary file,
Create a standby control file,
Create a restore point,
Generate a control file creation script,
Create a control file using a script,
Copy the redo control file,
Compare table counts between PR and DR,
Compare table counts between primary and standby databases,
Check for no-logging operations in the database,
Check if the flashback feature is turned on,
Backup the control file,
Open the database using the `ALTER DATABASE` command,
Apply incremental logs to the database,
Enable log archive destination using the `ALTER SYSTEM` command,
Disable log archive destination using the `ALTER SYSTEM` command,
Log system operations using the `ALTER SYSTEM` command,
Set the standby database to maximum performance using `ALTER DATABASE`,
Mount the database using the `ALTER DATABASE` command,
Enable flashback on the database using `ALTER DATABASE`,
Disable flashback on the database using `ALTER DATABASE`,
Convert the database to a physical standby using `ALTER DATABASE`,
Activate the standby database using `ALTER DATABASE`,
Cancel the managed recovery mode for a standby database using `ALTER DATABASE`,
Activate the database in read-write mode,
Force-enable the node state of a virtual server,
Force-disable the node state of a virtual server,
Enable a Local Traffic Manager (LTM) virtual server,
Disable a Local Traffic Manager (LTM) virtual server,
Check the state of the Local Traffic Manager (LTM) virtual server,
Check if the default route pools name exists in the F5 Load Balancer,
Check if the F5 Load Balancer name exists and is enabled,
Check the API token expiry date,
Migrate the F5 Load Balancer default route pools name change,
Modify NS1 DNS records with multiple A-type IP addresses,
Modify NS1 DNS record of CNAME type,
Modify NS1 DNS record of A-type with a single IP address,
Check if NS1 DNS record is of CNAME type,
Check if NS1 DNS record is of A-type with a single IP address,
Check if NS1 DNS record is of A-type with multiple IP addresses,
Query DNS records using INFOBLOX,
Modify DNS records using INFOBLOX,
Delete DNS records using INFOBLOX,
View DNS query statistics with INFOBLOX,
Add DNS records using INFOBLOX,
Delete a CNAME record,
Enable or disable DNS record mapping,
Check the DNS record status,
Check if a DNS record exists in a specific view and zone,
Check if a DNS zone exists in a DNS view,
Check if a DNS view exists,
Modify a CNAME record,
Add a CNAME record,
Check VLAN configuration,
Check static route configuration,
Update firewall policies,
Monitor OpenShift clusters,
Scale up or down OpenShift pods and deployment counts,
Check the pod deployment count with all pods in ready status,
Scale up or down OpenShift pods in replica sets,
Check the pod replica set count with all pods in ready status,
Scale up or down OpenShift pods in stateful sets,
Check the pod stateful set count with all pods in ready status,
Scale up or down OpenShift machine sets machine count,
Check the OpenShift machine set machine count,
Stop a virtual machine,
Start a virtual machine,
Check the status of a specific virtual machine,
Test pod deployment in OpenShift,
Update the AirGap action in IM (Incident Management),
Enable a port in IM (Incident Management),
Verify if a port is enabled in IM (Incident Management),
Verify if a port is disabled in IM (Incident Management),
Disable a port in IM (Incident Management),
Add a route in Nexus,
Unmount the volume,
Mount the volume,
Verify the status of LUNs (Logical Unit Numbers),
Restrict the SnapMirror volume,
Bring the SnapMirror volume online,
Update the SnapMirror configuration,
Resync the SnapMirror volume,
Check the volume status of SnapMirror,
Break the SnapMirror replication,
Initialize the SnapMirror,
Verify the status of SnapMirror replication,
Monitor SnapMirror replication for NetApp,
Synchronize replication and verify,
Synchronize replication,
Delete the Mtree (multi-volume tree),
Create an Mtree replication pair,
Create an Mtree,
Check Mtree replication pair status,
Verify the protect status of the site,
Unprotect the site,
Switch the site,
Check the split status,
Protect the site,
Isolate the site,
Halt the consistency group (CG),
Enable the Star configuration,
Disconnect the site,
Disable the Star configuration,
Connect the site,
Perform PPDM backup,
Perform cross vCenter VM migration,
Check if the vCenter exists on the PPDM server asset source,
Restore VM to an alternate location (multiple new locations),
Perform VM instant access restore,
Restore VM to an alternate location (multiple new locations),
Restore VM to an alternate location (multiple),
Restore VM to an alternate location (proxy),
Restore VM to an alternate location (new),
Restore VM to an alternate location,
Restore virtual machine to a new location,
Restore virtual machine to the original location,
Replicate all backups for a specific client,
Replicate all backups for a specific domain,
Restore virtual machine from history to a new location,
Restore virtual machine from history to the original location,
Check if the domain exists after replication,
Replicate backup,
Check if the client exists,
Check if the client exists after replication,
Check if the latest backup image exists for the virtual machine,
Check the virtual machine if the latest backup image exists after replica,
Check if the VM exists and protect it (new),
Check if the VM exists and protect it,
Check the replication activity status,
Check if the latest backup image exists of the target VM for restore operation,
Check if the target VM exists for restore,
Check if the target vCenter name exists for restore,
Check if the target exists for restore operation,
Check if the domain name exists,
Add vCenter to the PPDM asset source,
Add credentials to PPDM type vCenter,
Perform VM instant access restore (multiple),
Execute sync copy,
Execute secure copy analyze,
Execute secure copy,
Execute recovery check,
Execute policy application recovery,
Execute copy analyze,
Check if the latest copy is available for a particular policy,
Check the last analysis status,
Check the recovery status of the latest selected check,
Perform EMC switchover for the device group (DG),
Perform EMC switchback for the device group (DG),
Swap EMC personality,
Split EMC device group,
Set EMC device group to async mode,
Set EMC device group to ACP device mode,
Resume EMC device group,
Check if EMC device group is split,
Check if EMC device group is recent,
Check if EMC device group is consistent,
Perform EMC failover for device group,
Establish EMC device group,
Enable EMC device group,
Disable EMC device group,
Check EMC device group tracks zero,
Disable group image access,
Perform failover protection,
Enable group image access,
Set failover protection group,
Verify logged access,
Verify group statistics,
Verify group state,
Verify direct access,
Verify data transfer,
Start group data transfer,
Set production copy,
Pause group data transfer,
Check if the group is synchronized,
Allow writes to the sync policy.
Run sync job,
Modify sync policy schedule,
Failover sync policy,
Monitor disk global mirror,
Update the target storage group,
Perform switch over,
Perform data synchronization,
Pause global mirror,
Check if CG (consistency group) is formed,
Perform switch back,
Resume global mirror,
Execute storage command,
Execute DS wait for no flash copy,
Execute DS pause GMIR,
Execute DS make flash,
Execute DS make session,
Execute DS make GMIR,
Execute DS make flash,
Execute DS failover PPRC,
Execute DS failback PPRC,
Execute DS commit flash,
Execute DS check no flash copy,
Execute DS check flash zero tracks,
Execute DS change session remove,
Execute DS change session add,
Execute DS change volume group remove,
Execute DS change volume group add,
Execute DS command,
Execute check DS command,
Execute DS wait for flash tracks zero,
Execute DS set flash revertible,
Execute DS revert flash,
Execute DS reverse flash,
Execute DS resync flash,
Execute DS remove GMIR,
Execute DS remove flash,
Execute DS pause PPRC,
Check storage mailbox path,
Check DS tracks for LSPPRC,
Execute DS resume PPRC,
Execute DS resume GMIR,
Perform Hitachi pair swap,
Display Hitachi pair for DR,
Display Hitachi pair for PR,
Swap application node replication,
Resume application node replication,
Pause application node replication,
Check the replication paused state,
Check the replication state OK,
Check the replication swapped state,
HUR monitor,
Hitachi pair split,
Hitachi pair resync,
Check if Hitachi volume is suspended,
Perform Hitachi HORCC takeover,
Monitor HP3PAR storage,
Stop RCopy group,
Set RCopy group for restore,
Set RCopy group for failover,
Execute sync for RCopy,
Check RCopy group sync status,
Synchronize replication for Huawei,
Swap replica role,
Split replication,
Enable secondary resource access for read-write,
Enable secondary resource access for read-only,
Check secondary resource access for read-write,
Check secondary resource access for read-only,
Check replication running status,
Check LUN split state,
Check consistency group time value,
Check if consistency group synchronization begins,
Check consistency group replica role,
Change consistency group time value,
Promote pod,
Demote pod,
Check replication data lag,
Check pod replication direction,
Check pod promotion status,
Check pod replication status,
Unmount file system,
Stop VxVMDG,
Start VxVMDG,
Mount file system,
Check if VxVMDG volume is enabled,
Check if VxVMDG volume is disabled,
Check if file system is unmounted,
Check if file system is mounted,
Import VxVMDG,
Deport VxVMDG,
Check if all volumes in the DG are enabled,
Check if all volumes in the DG are disabled,
Rename folder,
Rename file,
Copy folder,
Copy file,
Stop the service,
Start the service,
Resume the service,
Pause the service,
Cancel the service,
Stop GoldenGate group,
Start GoldenGate group,
Check if GoldenGate group is running,
Check GoldenGate group RBASync,
GoldenGate replication,
Stop Rsync replication,
Execute Rsync job,
Verify sync status,
Verify connectivity,
Rsync replication,
Monitor Rsync application,
Rsync application replication,
Monitor Robocopy,
Robocopy replication,
Execute failover,
Verify failover,
Check status of execute start,
Verify job start,
Execute failback,
Verify failback,
Execute restore job,
Verify job restore status,
Verify job status,
Verify job reverse,
Execute reverse,
Verify DT replication status,
Verify DT replication queue,
Verify DT job name,
Stop DT job,
Start DT job,
DT job restore,
DT job perform failover,
DT job perform failback,
Check if DT job is not in error,
Check DT job mirror state,
Check DT job mirror permillage,
Check DT job mirror bytes remaining,
Check if DT job is in error,
Check DT job high-level state,
Check DT job health,
Check DT job disk queue bytes,
Check if DT job can stop,
Check if DT job can start,
Check if DT job can restore,
Check if DT job can failover,
Check if DT job can failback,
Check status of execute reverse,
Execute VCS resource online,
Execute VCS resource offline,
Check VCS resource state,
Check VVR service group state of cluster node,
Check if VVR service group node is offline,
Check if VVR service group cluster node is online,
Check if VVR SG string value exists in file,
Execute VVR SG batch file,
Execute VVR SG online on any one server group,
Execute VVR SG offline on any one server group,
Check status of VVR SG service group cluster name,
Check VVR link state up-to-date status,
Check VVR link state in replication status,
Switch VVR node state in cluster,
Get VVR service group state on specified node.
Verify the test replication health passed statistics except for database availability,
Verify that the server components are in an active state for all components,
Verify the replay queue status for all mailbox databases,
Verify the state of the primary active manager (PAM) at the primary site,
Verify that mailbox databases are mounted at the primary site level,
Verify that mailbox databases are mounted at the primary server level,
Verify the health status of mailbox databases at the disaster recovery site level,
Verify the health status of mailbox databases at the disaster recovery server level,
Verify that the database copy activation policy settings are correctly configured for mailbox servers,
Verify that the content index state is healthy for all mailbox databases,
Stop the Database Availability Group (DAG) at the primary site without configuration changes,
Stop the DAG at the primary site with configuration changes,
Start the DAG service,
Set the database copy auto-activation policy,
Enable the send and receive connector,
Restore the DAG to its original state,
Redirect the mailbox server message queue to another server,
Move the primary active manager to a target server,
Move a database without skipping any steps,
Move a database with specific skip options,
Move the active mailbox database with skip options,
Mount the database,
Verify that the DAG witness server and path are properly configured,
Verify the alternate witness server and directory for the DAG,
Set the witness server and directory,
Set the alternate witness server and directory for the DAG,
Enable the send connector,
Enable mailbox database circular logging,
Dismount the database,
Disable the send connector,
Disable mailbox database circular logging,
Check the transport server queue status,
Check the site-level mailbox server status under the DAG,
Check the full or incremental backup status of mailbox databases,
Check the mailbox server message queue count,
Check the mailbox database copy status,
Check the database copy auto-activation policy,
Check the mounted status of all mailbox databases,
Verify the passive mailbox replay queue length,
Verify the status of the passive mailbox database copy,
Verify the error message for the passive mailbox database copy,
Verify the content of the passive mailbox database copy,
Verify the queue length for the passive mailbox database copy,
Verify that the DAG status is online,
Verify the status of the active mailbox database copy,
Verify the error message for the active mailbox database copy,
Verify the content of the active mailbox database copy,
Verify the "suspend when ready to complete" mode status,
Suspend the process with "ready to complete" mode,
Stop mailbox servers,
Start mailbox servers,
Resume a move mailbox request,
Remove a user mailbox move request if it exists,
Move the active mailbox database,
Get the move request status,
Get the move request statistics,
Check if a user move request can be created,
Check if the user mailbox exists,
Set the SCR prerequisite,
Check SCR status and copy queue length,
Resume SCR,
Move the mailbox configuration,
Mount the database,
Get the mailbox list count before switching,
Get the mailbox list count,
Enable SCR,
Dismount the mailbox database,
Disable SCR and restore logs,
Create a storage group,
Compare the new storage group mailbox path,
Check the target database status,
Check the target database file status,
Allow file restore to the mailbox database,
Verify the DAG parameter values,
Stop the DAG on the mailbox server,
Stop the DAG active directory site configuration only,
Start the DAG on the mailbox server,
Set the preferred active directory server,
Set the database availability group,
Restore the DAG active directory site,
Move the cluster group,
Move the active mailbox database,
Check the mailbox database status parameter values,
Verify the replication service on PAM,
Verify mailbox server entries,
Stop the DAG on a mailbox server,
Stop the database availability group,
Set the public folder database,
Set the database copy auto-activation policy to blocked,
Set the database copy auto-activation policy,
Restore the database availability group,
Move the mailbox database to the disaster recovery server,
Move the default offline address book,
Execute a Power shell (powershell) command,
Verify the started mailbox server for Exchange DAG,
Verify the current site name associated with the mailbox server,
Verify and stop the cluster service for Exchange DAG,
Stop the DAG at the primary mailbox server,
Start the DAG at the primary mailbox server,
Set the DAG to seed all changes,
Resume the mailbox database copy,
Mount the mailbox database on the active primary mailbox server,
Verify the replication health on the Exchange mailbox server,
Check the primary and alternate file share witness in use,
Check the replication health status,
Check the status of mailbox server role services,
Check the details of healthy mailbox databases,
Check the mounted status of the database,
Check the Database Availability Group (DAG) membership status,
Stop the IIS website,
Stop the application pool,
Start the IIS website,
Start the application pool,
Access the HTTP URL,
Access and check the HTTP URL,
Execute and check the REST API command,
No replication action,
Mark the system as disaster recovery ready,
Determine the disaster recovery operation type,
Wait for the workflow action to complete,
Wait for the parallel action to complete,
Display an alert,
Stop the workflow for recovery time objective (RTO),
Start the workflow for RTO,
Execute and analyze a secure copy for cyber recovery,
Execute a secure copy for cyber recovery,
Execute a recovery application process using PPDM,
Check if the latest copy is available for a particular policy,
Check the status of the cyber recovery process,
Execute a secure copy analysis for cyber recovery,
Execute a protected single VM process using PPDM,
Restore a VM to an alternate location using PPDM Version 1,
Check if an MSSQL database backup copy exists (Version 1.2),
Check if a VM is protected (Version 4) using PPDM,
Execute a backup process for multiple unprotected VMs using PPDM,
Execute a backup process for a single unprotected VM using PPDM,
Restore a VM to an alternate location with the latest backup copy using PPDM,
Restore multiple MSSQL databases to an alternate location with the default version (Version 1.1),
Restore multiple databases for Dell EMC PPDM MSSQL (Version 1.1),
Execute a protected multiple VM process using PPDM,
Remove the probe health from the load balancer,
Remove a load balancer rule from the load balancer,
Remove an inbound NAT rule from the load balancer,
Remove the load balancer from a virtual machine,
Add probe health to the load balancer,
Add a load balancer to a virtual machine,
Add a rule to the load balancer,
Add an inbound NAT rule to the load balancer,
Execute reverse replication from Azure to on-premises,
Execute reprotection from on-premises to Azure,
Execute reprotection from VMware on-premises to Azure,
Execute reprotection from Azure to on-premises,
Execute a planned failover and commit from on-premises to Azure,
Execute a planned failover from Azure to on-premises,
Execute a commit failover,
Check the replication state of protected VMs,
Check the allowed operations from Azure to on-premises,
Power on a TCL cloud VM instance,
Power off a TCL cloud VM instance,
Check the status of a TCL cloud VM instance,
Start a wave in the Rackware RMM server,
Execute a wave failover in Rackware,
Execute a wave failback in Rackware,
Check the wave status in Rackware,
Check the disaster recovery policy status in Rackware,
Perform a host sync when the target exists without waiting,
Perform a host sync when the target exists with waiting,
Create a wave with host auto-provisioning in OCI using parameters,
Create a host sync with an auto-target and static IP without waiting,
Create a host sync with an auto-target and static IP with waiting,
Create a host sync with an auto-target and dynamic IP without waiting,
Perform an Oracle Cloud VM instance action,
Detach all NSGs from an instance VNIC,
Create an NSG and add a security rule,
Check if an NSG is attached to an instance VNIC,
Check the status of an Oracle Cloud VM instance,
Add a new NSG to an instance VNIC, replacing existing NSGs,
Add a new NSG to an instance VNIC, appending to existing NSGs,
Upgrade the memory of a virtual machine in SoftLayer,
Upgrade the CPU of a virtual machine in SoftLayer,
Upgrade a virtual machine by IDs in SoftLayer,
Upgrade a virtual guest in SoftLayer,
Provision a virtual machine by IDs in SoftLayer,
Provision a virtual guest in SoftLayer,
Power on a virtual guest in SoftLayer,
Power off a virtual guest in SoftLayer,
Check if a virtual guest is powered on in SoftLayer,
Check if a virtual guest is powered off in SoftLayer,
Remove a routing rule,
Remove a path-based rule from a backend pool rule,
Check a listener with its associated rule,
Check if an application gateway rule path exists,
Check the operational state of the application gateway,
Add a routing rule,
Add an HTTP-type listener,
Add a backend pool rule path to an application gateway,
Delete an A-type DNS record in OCI,
Check if an A-type DNS record exists in OCI,
Check if an A-type DNS record does not exist in OCI,
Check the existing TTL value of an A-type DNS record in OCI,
Add a DNS record of type A in OCI,
Execute stop and start service operations on Azure MySQL,
Check the start and stop status of Azure MySQL service,
Promote a MySQL replica server to primary,
Create a MySQL replica server,
Delete a standalone MySQL server,
Delete a MySQL replication replica server,
Monitor Azure MySQL services,
Delete the MySQL replication source server,
Check the availability status of MySQL server,
Check the role of the Azure MySQL server,
Check if the Azure MySQL server exists in the region,
Execute stop and start service operations on Azure PostgreSQL,
Check the stop and start status of Azure PostgreSQL service,
Monitor Azure PostgreSQL services,
Stop all instances in the virtual machine scale set,
Stop a specific virtual machine scale set instance,
Stop MySQL replication,
Start all instances in the virtual machine scale set,
Start a specific virtual machine scale set instance,
Remove a security rule from the NSG,
Execute an unplanned failover,
Execute an unplanned failover from on-premise to Azure,
Execute a re-protect operation,
Execute a planned failover,
Execute a planned failover to Azure,
Execute a force failover to Azure,
Execute a failover for Azure Cosmos DB,
Commit an unplanned failover,
Enable or disable ATM endpoint,
Dissociate a Network Security Group (NSG) from a VM,
Dissociate a Network Security Group (NSG) from a network interface,
Delete the replication source server,
Delete the replication replica server,
Create a replication in Azure MySQL,
Check the status of a virtual machine scale set,
Check the status of an unplanned failover completion,
Check the status of an unplanned failover commit,
Check the status of a specific virtual machine scale set,
Check the protection state of the re-protect operation,
Check the re-protect operation,
Check the status of a public IP address,
Check the status of a planned failover,
Check if the NSG name exists,
Check if the NSG name is associated with a VM,
Check the enable or disable ATM endpoint status,
Check the commit status of failover,
Check the write location of Azure Cosmos,
Check the role of Azure SQL DB,
Check the replication state in Azure,
Check the read location of Azure Cosmos,
Check Azure DB activity provisioning statistics for Cosmos,
Check if the operation is allowed,
Check the availability status of Azure MySQL server,
Check the role of Azure MySQL server,
Execute test failover for Azure,
Clean up failover in Azure,
Associate a public IP address,
Associate a specific NSG to a VM network interface,
Associate the default NSG to a VM network interface,
Associate an NSG to a network interface,
Assign an NSG to a VM and replace the existing NSG,
Add a security rule to NSG,
Verify the status of VCD,
Power on a VCD,
Power off a VCD,
Stop an Azure VM,
Start an Azure VM,
Check the status of an Azure VM,
Check the power state of an Azure VM,
Change the size of an Azure VM,
Monitor Azure MSSQL pass,
Execute a forced failover for Azure SQL DB,
Execute a planned failover for Azure SQL DB,
Check the replication state in Azure SQL DB,
Check the PaaS role for Azure SQL DB,
Check the storage replication data lag with input,
Set storage account replication,
Execute a storage account failover,
Check the storage account type,
Monitor Azure storage replication,
Execute a commit failover for Azure Site Recovery (ASR),
Execute an unplanned failover for ASR,
Execute a re-protect operation for ASR,
Execute a test failover cleanup for ASR,
Check the status of ASR test failover,
Execute a test failover for ASR,
Check the status of ASR test failover cleanup,
Check the status of unplanned failover,
Check the status of commit failover,
Check the status of ASR re-protect,
Check the protection status of ASR VM,
Mount a virtual machine using Rubrik,
Migrate a datastore after mounting,
Stop an EC2 instance,
Start an EC2 instance,
Modify the size of an EC2 instance,
Check if an EC2 instance is up,
Check if an EC2 instance is down,
Check the state of an EC2 instance,
Upload a file to an S3 bucket,
Upload multiple files to an S3 bucket,
Download a file from an S3 bucket,
Download multiple files from an S3 bucket,
Execute CyberRecovery copy analyze operation,
Execute CyberRecovery secure copy operation,
Execute recovery application for PPDM,
Check if the latest copy is available for a particular policy in CyberRecovery,
Check the status of CyberRecovery,
Execute secure copy analyze operation in CyberRecovery,
Execute recovery of a protected single VM in PPDM,
Copy a VM to an alternate location in PPDM,
Check if an MSSQL DB backup copy exists in PPDM,
Check if a VM is protected in PPDM,
Execute backup for multiple unprotected VMs in PPDM,
Execute backup for a single unprotected VM in PPDM,
Restore a VM to an alternate location with the latest backup copy in PPDM,
Restore an MSSQL DB to an alternate location from multiple DBs in PPDM,
Restore multiple DBs from a backup in PPDM,
Execute protected backup for multiple VMs in PPDM,
Execute planned failover for VMware replica in VB,
Undo VMware replica failover in VBR,
Undo VMware replica failback in VBR,
Execute permanent VMware replica failover in VBR,
Execute disaster recovery-only VMware replica failover in VBR,
Fail back VMware replica to the original VM in VBR,
Execute failover replication job,
Execute failback replication job,
Commit failback replication job,
Create replication job using the production VM state,
Create replication job from backup files,
Check the last state of a replication job,
Check the last result of a replication job,
Check the status of a replication job,
Check the status of replica monitoring,
Execute eBDR operations,
Update an asset,
Unmanage an asset,
Remove an asset from maintenance mode,
Refresh an asset,
Reboot an asset,
Put an asset in maintenance mode,
Power on an asset,
Power off an asset,
Manage an asset,
Execute a plan,
Execute an OpsCenter command,
Execute check command in OpsCenter,
Power on a blade server in HP,
Power off a blade server in HP,
Check the power status of HP is on,
Check the power status of HP is off,
Check the power status of an asset,
Power on an asset,
Power off an asset,
Check the link status for MSSQL,
Check transaction queue in MSSQL,
Check the status of a node in MSSQL,
Check SAF count in MSSQL,
Check the status of the DRNet connection in MSSQL,
Check the records in DRNet,
Check the status of the distributor file map in DRNet,
Check the mode of the collector file map in DRNet,
Check the audit mode status in DRNet,
Execute Base24 command,
Execute Base24 check command,