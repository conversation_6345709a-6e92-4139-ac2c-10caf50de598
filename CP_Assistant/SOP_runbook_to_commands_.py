import re
import pandas as pd
import json

class SOPProcessor:
    def __init__(self, file_path):
        self.file_path = file_path
        self.df = pd.read_excel(file_path)
        self.command_dict = {}
        self.sql_steps = {}
        self.action_code_checkstring_list = []

    def get_the_actions(self, row, description_details):
        actions = []
        for line in description_details:
            command_with_check_string = []
            if len(description_details) > 1:
                line = line.strip()
                if line:
                    if len(line.split()) > 1:
                        if line not in self.command_dict:
                            command_cell = row["Reference For Stop command"].splitlines()
                            check_output_value = row["Success Criteria in the output"]
                            for i, command_line in enumerate(command_cell):
                                if command_line.lower().startswith("sample command"):
                                    if i + 1 < len(command_cell):
                                        command_with_check_string.append(command_cell[i + 1])
                                        command_with_check_string.append(check_output_value)
                                        self.command_dict[line] = command_with_check_string
                                    else:
                                        self.command_dict[line] = []
                        actions.append(line)
                    else:
                        actions.append(line)
            elif len(description_details) == 1:
                line = line.strip()
                if line:
                    if len(line.split()) > 1:
                        if line not in self.command_dict:
                            command_cell = row["Reference For Stop command"].splitlines()
                            check_output_value = row["Success Criteria in the output"]
                            for i, command_line in enumerate(command_cell):
                                command_with_check_string.append(command_cell[i + 1])
                                command_with_check_string.append(check_output_value)
                                self.command_dict[line] = command_with_check_string
                                break
                        actions.append(line)
                    else:
                        actions.append(line)

        return actions

    def process_windows_command(self, row, action_list):
        if len(action_list) > 1:
            command_and_checkstring_for_actions = []
            for action in action_list:
                result_dict = {
                    "action": "ExecuteCheckOSCommand",
                    "code": "",
                    "job": "",
                    "return flag": "",
                    "check output": "",
                    "parallel/sequential": ""
                }
                if len(action.split()) > 1:
                    if not command_and_checkstring_for_actions:
                        command_and_checkstring_for_actions.append(self.command_dict.get(action, None))
                else:
                    if command_and_checkstring_for_actions:
                        command = command_and_checkstring_for_actions[0][0]
                        check_string = command_and_checkstring_for_actions[0][1]
                        updated_command = re.sub(r'".*?"', f'"{action}"', command)
                        result_dict["code"] = updated_command
                        parallel_sequel = row["Parallel/Sequel"]
                        result_dict["parallel/sequential"] = parallel_sequel
                        if pd.notna(check_string) and len(check_string) > 1:
                            updated_check_string = re.sub(r'".*?"', f'"{action}"', check_string)
                            result_dict["return flag"] = "True"
                            result_dict["check output"] = updated_check_string
                        elif pd.isna(check_string):
                            result_dict["return flag"] = "False"
                            result_dict["check output"] = ""
                        else:
                            result_dict["return flag"] = "False"
                            result_dict["check output"] = check_string
                        self.action_code_checkstring_list.append(result_dict)
                    else:
                        print(f"No command found to replace '{action}'")

        elif len(action_list) == 1:
            command_and_checkstring_for_actions = []
            for action in action_list:
                result_dict = {
                    "action": "ExecuteCheckOSCommand",
                    "code": "",
                    "job": "",
                    "return flag": "",
                    "check output": "",
                    "parallel/sequential": ""
                }
                if not command_and_checkstring_for_actions:
                    command_and_checkstring_for_actions.append(self.command_dict.get(action, None))

                if command_and_checkstring_for_actions:
                    command = command_and_checkstring_for_actions[0][0]
                    check_string = command_and_checkstring_for_actions[0][1]
                    result_dict["code"] = command
                    parallel_sequel = row["Parallel/Sequel"]
                    result_dict["parallel/sequential"] = parallel_sequel
                    if pd.notna(check_string) and len(check_string) > 1:
                        updated_check_string = re.sub(r'".*?"', f'"{action}"', check_string)
                        result_dict["return flag"] = "True"
                        result_dict["check output"] = updated_check_string
                    elif pd.isna(check_string):
                        result_dict["return flag"] = "False"
                        result_dict["check output"] = ""
                    else:
                        result_dict["return flag"] = "False"
                        result_dict["check output"] = check_string

                    self.action_code_checkstring_list.append(result_dict)
                else:
                    print(f"No command found to replace '{action}'")

    def process_mssql_database(self, row, description_details):
        job_name = row["Preferred Action Type"]
        if job_name:
            action_list = [description_details]
            if description_details[0] not in self.sql_steps:
                command_cell = row["Reference For Stop command"].splitlines()

                processed_lines = []
                for line in command_cell:
                    line = line.strip()
                    match = re.match(r"^[a-dA-D]\)(\S+)", line)
                    if match:
                        processed_lines.append(match.group(1))

                self.sql_steps[description_details[0]] = processed_lines

            return_flag = "False"
            if description_details[0] in self.sql_steps:
                for process_line in self.sql_steps[description_details[0]]:
                    result_dict = {
                        "action": job_name,
                        "code": "",
                        "job": process_line,
                        "return flag": return_flag,
                        "check output": "",
                        "parallel/sequential": row.get("Parallel/Sequel", "")
                    }
                    self.action_code_checkstring_list.append(result_dict)

    def process(self):
        for index, row in self.df.iterrows():
            if isinstance(row["Server/Database Type"], str):
                if row["Server/Database Type"].lower() == "windows":
                    if row["Preferred Action Type"] == "ExecuteCheckOSCommand":
                        description_details = row["Description"].splitlines()
                        action_list = self.get_the_actions(row, description_details)
                        if action_list:
                            self.process_windows_command(row, action_list)

                elif row["Server/Database Type"].lower() == "mssql database":
                    description_details = row["Description"].splitlines()
                    self.process_mssql_database(row, description_details)

    def save_output(self, output_file='action_code_checkstring_list.json'):
        with open(output_file, 'w') as f:
            json.dump(self.action_code_checkstring_list, f, indent=4)


# Example usage
if __name__ == "__main__":
    processor = SOPProcessor('input_files/SOP_runbook_to_commands_template/IndusDirect Manual SOP.xlsx')
    processor.process()
    processor.save_output()