# import os
# from mistralai import Mistral, UserMessage
#
# api_key = "NHiBpO4OcT7tclBe3lM7YCMebXF8mWhd"
# model = "mistral-large-latest"
#
# client = Mistral(api_key=api_key)
#
# messages = [
#     {
#         "role": "user",
#         "content": "What is the best French cheese?",
#     },
# ]
# chat_response = client.chat.complete(
#     model=model,
#     messages=messages,
# )
#
# print(chat_response.choices[0].message.content)


#!/usr/bin/env python

import os

from mistralai import Mistral


def main():
    api_key = "NHiBpO4OcT7tclBe3lM7YCMebXF8mWhd"

    client = Mistral(api_key=api_key)

    list_models_response = client.models.list()
    for idx, model in enumerate(list_models_response.data):
        print(f"{idx} : {model.aliases}")
        print(f"      : {model.name}")
        print(f"____________________________________")


if __name__ == "__main__":
    main()
