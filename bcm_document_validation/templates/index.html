<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
    <title>BCM Validation Assistant</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 40px;
        }

        .upload-section {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #3498db;
            background: #e3f2fd;
        }

        .upload-section.dragover {
            border-color: #2196f3;
            background: #e3f2fd;
            transform: scale(1.02);
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            margin-top: 15px;
        }

        .file-input {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .file-input-button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .file-input-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }

        .file-info {
            margin-top: 15px;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 8px;
            display: none;
        }

        .question-section {
            margin-bottom: 30px;
        }

        .question-section label {
            display: block;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .question-input {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            transition: all 0.3s ease;
        }

        .question-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .submit-button {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
            width: 100%;
        }

        .submit-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
        }

        .submit-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .response-section {
            margin-top: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #3498db;
        }

        .response-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .response-header h3 {
            color: #2c3e50;
            margin-left: 10px;
        }

        .response-content {
            white-space: pre-wrap;
            line-height: 1.6;
            color: #34495e;
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e1e8ed;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e1e8ed;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .examples {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .examples h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .examples ul {
            color: #856404;
            padding-left: 20px;
        }

        .examples li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .content {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 BCM Validation Assistant</h1>
            <p>Upload your Business Continuity Management documents and get intelligent insights</p>
        </div>

        <div class="content">
            <form method="POST" enctype="multipart/form-data" id="bcm-form">
                <div class="upload-section" id="upload-area">
                    <div style="font-size: 48px; margin-bottom: 15px;">📁</div>
                    <h3>Upload your BCM document</h3>
                    <p style="color: #6c757d; margin: 10px 0;">Drag and drop your file here or click to browse</p>
                    <p style="color: #6c757d; font-size: 14px;">Supported formats: PDF, TXT (Max 10MB)</p>

                    <div class="file-input-wrapper">
                        <input type="file" name="bcm_doc" class="file-input" accept=".txt,.pdf" required id="file-input">
                        <button type="button" class="file-input-button">Choose File</button>
                    </div>

                    <div class="file-info" id="file-info">
                        <span id="file-name"></span>
                        <span id="file-size"></span>
                    </div>
                </div>

                <div class="question-section">
                    <label for="question">What would you like to know about your BCM document?</label>
                    <textarea name="question" id="question" class="question-input"
                              placeholder="Enter your question here... (e.g., 'What are the key risk mitigation strategies?', 'Summarize the business impact analysis', 'What are the recovery time objectives?')"
                              required></textarea>
                </div>

                <button type="submit" class="submit-button" id="submit-btn">
                    🤖 Ask BCM Assistant
                </button>

                <div class="loading" id="loading">
                    <div class="loading-spinner"></div>
                    <p>Analyzing your document...</p>
                </div>
            </form>

            <div class="examples">
                <h4>💡 Example Questions:</h4>
                <ul>
                    <li>What are the main business continuity risks identified?</li>
                    <li>Summarize the disaster recovery procedures</li>
                    <li>What are the key stakeholder contact details?</li>
                    <li>List the critical business processes and their priorities</li>
                    <li>What are the backup and recovery strategies?</li>
                </ul>
            </div>

            {% if response %}
            <div class="response-section">
                <div class="response-header">
                    <div style="font-size: 24px;">🤖</div>
                    <h3>BCM Assistant Response</h3>
                </div>
                <div class="response-content">{{ response }}</div>
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        // File upload enhancements
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        const form = document.getElementById('bcm-form');
        const submitBtn = document.getElementById('submit-btn');
        const loading = document.getElementById('loading');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                updateFileInfo(files[0]);
            }
        });

        // File input change handler
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                updateFileInfo(e.target.files[0]);
            }
        });

        // Update file info display
        function updateFileInfo(file) {
            const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
            fileName.textContent = file.name;
            fileSize.textContent = `(${sizeInMB} MB)`;
            fileInfo.style.display = 'block';
        }

        // Form submission with loading state
        form.addEventListener('submit', () => {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Processing...';
            loading.style.display = 'block';
        });

        // Auto-resize textarea
        const textarea = document.getElementById('question');
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    </script>
</body>
</html>