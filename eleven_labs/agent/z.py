from datetime import datetime, timedelta

now = datetime.now()
post_time = now - timedelta(minutes=2)

diff = now - post_time
minutes = diff.total_seconds() // 60
print(f"{int(minutes)} minutes ago")



seats = ["A1", "A2", "A3", "A4"]
booked = []

while True:
    choice = input("Select Seat : ")
    if choice not in booked:
        booked.append(choice)
        print(f"Seat {choice} booked ✅")
    else:
        print(f"Seat {choice} already taken ❌")
