{"agent_id": "0TP5bFyDLLxVqINQh8B5", "name": "Support agent", "conversation_config": {"asr": {"quality": "high", "provider": "elevenlabs", "user_input_audio_format": "pcm_16000", "keywords": []}, "turn": {"turn_timeout": 7, "silence_end_call_timeout": 20, "mode": "turn"}, "tts": {"model_id": "eleven_turbo_v2_5", "voice_id": "21m00Tcm4TlvDq8ikWAM", "supported_voices": [{"label": "Tamil", "voice_id": "21m00Tcm4TlvDq8ikWAM", "description": "use this voice when speaking tamil words", "language": null, "model_family": "multilingual", "optimize_streaming_latency": null, "stability": null, "speed": null, "similarity_boost": null}], "agent_output_audio_format": "pcm_16000", "optimize_streaming_latency": 3, "stability": 0.5, "speed": 1, "similarity_boost": 0.8, "pronunciation_dictionary_locators": []}, "conversation": {"text_only": false, "max_duration_seconds": 1800, "client_events": ["audio", "interruption", "user_transcript", "agent_response", "agent_response_correction"]}, "language_presets": {"hi": {"overrides": {"tts": null, "conversation": null, "agent": {"first_message": "नमस्ते, मैं पर्पेचुइटी सपोर्ट से सुसान हूँ। मैं आज आपकी कैसे मदद कर सकती हूँ?", "language": null, "prompt": null}}, "first_message_translation": {"source_hash": "{\"firstMessage\":\"Hey there, I'm <PERSON> from Perpetuuiti support. How can I help you today?\",\"language\":\"en\"}", "text": "नमस्ते, मैं पर्पेचुइटी सपोर्ट से सुसान हूँ। मैं आज आपकी कैसे मदद कर सकती हूँ?"}}, "ta": {"overrides": {"tts": null, "conversation": null, "agent": {"first_message": "வணக்கம், நான் பெர்பெச்சுவிட்டி ஆதரவில் இருந்து சுசன். இன்று உங்களுக்கு எப்படி உதவலாம்?", "language": null, "prompt": null}}, "first_message_translation": {"source_hash": "{\"firstMessage\":\"Hey there, I'm <PERSON> from Perpetuuiti support. How can I help you today?\",\"language\":\"en\"}", "text": "வணக்கம், நான் பெர்பெச்சுவிட்டி ஆதரவில் இருந்து சுசன். இன்று உங்களுக்கு எப்படி உதவலாம்?"}}}, "agent": {"first_message": "Hey there, I'm <PERSON> from Perpetuuiti support. How can I help you today?", "language": "bg", "dynamic_variables": {"dynamic_variable_placeholders": {}}, "prompt": {"prompt": "You are an English-only agent. Do not process or respond to any message that is not in English.\n\n1. First, detect the language of the input.\n2. If the input is in English → respond normally.\n3. If the input is not in English → do NOT translate or answer. Trigger a transfer to the appropriate language-specific agent.\n\nNever translate other languages into English.\nNever respond in English to inputs in Tamil, Hindi, or any other language.", "llm": "gemini-2.0-flash-lite", "temperature": 0, "max_tokens": -1, "tool_ids": [], "built_in_tools": {"end_call": {"name": "end_call", "description": "", "response_timeout_secs": 20, "type": "system", "params": {"system_tool_type": "end_call"}}, "language_detection": {"name": "language_detection", "description": "You are a language-specific assistant that ONLY responds to users who speak English.\n\n1. Before responding, ALWAYS detect the language of the user's message.\n2. IF the detected language is English → continue the conversation.\n3. IF the detected language is NOT English → do NOT respond. Instead, trigger a transfer to the Tamil agent.\n\nYou MUST NOT answer or translate messages that are not in English.  \nYou MUST NOT assume the user's intent if language is unclear.\n\nIf language cannot be confidently identified as English, treat it as a non-English message and transfer.\n\nRespond ONLY if the user's most recent message is confidently in English.", "response_timeout_secs": 20, "type": "system", "params": {"system_tool_type": "language_detection"}}, "transfer_to_agent": null, "transfer_to_number": null, "skip_turn": null}, "mcp_server_ids": [], "native_mcp_server_ids": [], "knowledge_base": [{"type": "file", "name": "cv_data.docx", "id": "jCKlYpLEmKkPf4xqtEJq", "usage_mode": "auto"}, {"type": "file", "name": "BCM Plan - Ptech - 19082024_2.pdf", "id": "l7TVaniBPVwxiirHWrGd", "usage_mode": "auto"}], "custom_llm": null, "ignore_default_personality": false, "rag": {"enabled": false, "embedding_model": "e5_mistral_7b_instruct", "max_vector_distance": 0.6, "max_documents_length": 50000, "max_retrieved_rag_chunks_count": 20}, "tools": [{"name": "end_call", "description": "", "response_timeout_secs": 20, "type": "system", "params": {"system_tool_type": "end_call"}}, {"name": "language_detection", "description": "You are a language-specific assistant that ONLY responds to users who speak English.\n\n1. Before responding, ALWAYS detect the language of the user's message.\n2. IF the detected language is English → continue the conversation.\n3. IF the detected language is NOT English → do NOT respond. Instead, trigger a transfer to the Tamil agent.\n\nYou MUST NOT answer or translate messages that are not in English.  \nYou MUST NOT assume the user's intent if language is unclear.\n\nIf language cannot be confidently identified as English, treat it as a non-English message and transfer.\n\nRespond ONLY if the user's most recent message is confidently in English.", "response_timeout_secs": 20, "type": "system", "params": {"system_tool_type": "language_detection"}}]}}}, "metadata": {"created_at_unix_secs": 1746610820}, "platform_settings": {"auth": {"enable_auth": false, "allowlist": [], "shareable_token": null}, "evaluation": {"criteria": []}, "widget": {"variant": "full", "placement": "bottom-right", "expandable": "never", "avatar": {"type": "image", "url": "https://storage.googleapis.com/eleven-public-cdn/convai/0TP5bFyDLLxVqINQh8B5/avatar.png"}, "feedback_mode": "during", "bg_color": "#ffffff", "text_color": "#000000", "btn_color": "#000000", "btn_text_color": "#ffffff", "border_color": "#e1e1e1", "focus_color": "#000000", "border_radius": null, "btn_radius": null, "action_text": null, "start_call_text": null, "end_call_text": null, "expand_text": null, "listening_text": null, "speaking_text": null, "shareable_page_text": null, "shareable_page_show_terms": true, "terms_text": "#### Terms and conditions\n\nBy clicking \"Agree,\" and each time I interact with this AI agent, I consent to the recording, storage, and sharing of my communications with third-party service providers, and as described in the Privacy Policy.\nIf you do not wish to have your conversations recorded, please refrain from using this service.", "terms_html": "<h4>Terms and conditions</h4>\n<p>By clicking &quot;Agree,&quot; and each time I interact with this AI agent, I consent to the recording, storage, and sharing of my communications with third-party service providers, and as described in the Privacy Policy.\nIf you do not wish to have your conversations recorded, please refrain from using this service.</p>\n", "terms_key": null, "show_avatar_when_collapsed": true, "disable_banner": false, "override_link": null, "mic_muting_enabled": false, "transcript_enabled": false, "text_input_enabled": false, "text_contents": {"main_label": null, "start_call": null, "new_call": null, "end_call": null, "mute_microphone": null, "change_language": null, "collapse": null, "expand": null, "copied": null, "accept_terms": null, "dismiss_terms": null, "listening_status": null, "speaking_status": null, "connecting_status": null, "input_label": null, "input_placeholder": null, "user_ended_conversation": null, "agent_ended_conversation": null, "conversation_id": null, "error_occurred": null, "copy_id": null}, "styles": {"base": null, "base_hover": null, "base_active": null, "base_border": null, "base_subtle": null, "base_primary": null, "base_error": null, "accent": null, "accent_hover": null, "accent_active": null, "accent_border": null, "accent_subtle": null, "accent_primary": null, "overlay_padding": null, "button_radius": null, "input_radius": null, "bubble_radius": null, "sheet_radius": null, "compact_sheet_radius": null, "dropdown_sheet_radius": null}, "language_selector": false, "supports_text_only": false, "custom_avatar_path": "convai/0TP5bFyDLLxVqINQh8B5/avatar.png", "language_presets": {}}, "data_collection": {}, "overrides": {"conversation_config_override": {"tts": {"voice_id": true}, "conversation": {"text_only": false}, "agent": {"first_message": false, "language": true, "prompt": {"prompt": false}}}, "custom_llm_extra_body": false, "enable_conversation_initiation_client_data_from_webhook": false}, "call_limits": {"agent_concurrency_limit": -1, "daily_limit": 100000, "bursting_enabled": true}, "ban": null, "privacy": {"record_voice": true, "retention_days": 730, "delete_transcript_and_pii": true, "delete_audio": true, "apply_to_existing_conversations": false, "zero_retention_mode": false}, "workspace_overrides": {"conversation_initiation_client_data_webhook": null, "webhooks": {"post_call_webhook_id": null}}, "safety": {"is_blocked_ivc": false, "is_blocked_non_ivc": false, "ignore_safety_evaluation": false}}, "phone_numbers": [], "access_info": null, "tags": []}