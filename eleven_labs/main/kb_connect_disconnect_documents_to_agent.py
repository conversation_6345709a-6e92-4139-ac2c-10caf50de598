from flask import Flask, request, jsonify
from elevenlabs import Eleven<PERSON>abs

app = Flask(__name__)

# {
#     "currentDocs": [
#         {
#             "id": "8kC52p4xEiXVY6wA31oo",
#             "name": "BCM Plan (6).docx"
#         },
#         {
#             "id": "oHOXKCXtXKdZ9lE3KCOj",
#             "name": "BCM Plan (4).docx"
#         }
#     ],
#     "connectStatus":
#         {
#             "id": "oHOXKCXtXKdZ9lE3KCOj",
#             "name": "BCM Plan (4).docx",
#             "status": "connect"
#         }
#
# }

client = ElevenLabs(api_key="***************************************************")

@app.route("/knowledge_base_connect_disconnect_documents_to_agent", methods=["POST"])
def knowledge_base_connect_disconnect_documents_to_agent():
    try:
        data = request.get_json()
        connect_documents_list = data.get("currentDocs")
        connectStatus = data.get("connectStatus")
        status = connectStatus.get("status")


        new_knowledge_base = []
        for doc in connect_documents_list:
            new_document = {
                "type": "file",
                "name": doc["name"],
                "id": doc["id"],
                "usage_mode": "auto"
            }
            new_knowledge_base.append(new_document)

        client.conversational_ai.update_agent(
            agent_id="0TP5bFyDLLxVqINQh8B5",
            conversation_config={
                "agent": {
                    "prompt": {
                        "knowledge_base": new_knowledge_base
                    }
                }
            }
        )

        if status == "connect":
            return jsonify({
                "status": True,
                "message": "Document connected successfully",
                "connectStatus": connectStatus,
            })
        else:
            return jsonify({
                "status": True,
                "message": "Document disconnected successfully",
                "connectStatus": connectStatus,
            })


    except Exception as e:

        return jsonify({
            "status": False,
            "message": f"Document could not be {status}ed: {str(e)}"
        }), 500


if __name__ == "__main__":
    app.run(debug=False, host="0.0.0.0", port=8887)
