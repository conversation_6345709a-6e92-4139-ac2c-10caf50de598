

# from elevenlabs import ElevenLabs
#
# client = ElevenLabs(api_key="***************************************************")
#
# agent_data = client.conversational_ai.get_agent(agent_id="0TP5bFyDLLxVqINQh8B5")
#
# print(agent_data)



from elevenlabs import ElevenLabs

# Step 1: Initialize client
client = ElevenLabs(api_key="***************************************************")

new_knowledge_base = []

new_document = {
    "type": "file",
    "name": "cv_data.docx",
    "id": "tOviruZ5nj0WxQP1XRg6",
    "usage_mode": "auto"
}

new_knowledge_base.append(new_document)

client.conversational_ai.update_agent(
    agent_id="0TP5bFyDLLxVqINQh8B5",
    conversation_config={
        "agent": {
            "prompt": {
                "knowledge_base": new_knowledge_base
            }
        }
    }
)

