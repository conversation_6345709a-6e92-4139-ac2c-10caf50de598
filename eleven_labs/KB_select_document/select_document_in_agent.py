from elevenlabs import ElevenLabs

client = ElevenLabs(
    api_key="***************************************************",
)
result = client.conversational_ai.get_knowledge_base_list()
print(result)


# from elevenlabs import ElevenLabs
#
# # Step 1: Initialize client
# client = ElevenLabs(api_key="***************************************************")
#
# # Step 2: Define new knowledge base entry
# new_knowledge_base_entry = {
#     "type": "file",
#     "name": "cv_data.docx",
#     "id": "qzYf6SLzOjIikC7nL0cr",
#     "usage_mode": "auto"
# }
#
# # Step 3: Update only the knowledge base
# client.conversational_ai.update_agent(
#     agent_id="0TP5bFyDLLxVqINQh8B5",
#     conversation_config={
#         "agent": {
#             "prompt": {
#                 "knowledge_base": [new_knowledge_base_entry]
#             }
#         }
#     }
# )
