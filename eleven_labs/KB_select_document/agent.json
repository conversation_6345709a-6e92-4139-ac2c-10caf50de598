{
  "API_KEY" : "***************************************************",
},
{
  "agent_id": "J3Pbu5gP6NNKBscdCdwB",
  "name": "My Agent",
  "conversation_config": {
    "asr": {
      "quality": "high",
      "provider": "elevenlabs",
      "user_input_audio_format": "pcm_16000",
      "keywords": [
        "hello",
        "world"
      ]
    },
    "turn": {
      "turn_timeout": 7,
      "silence_end_call_timeout": -1,
      "mode": "turn"
    },
    "tts": {
      "model_id": "eleven_turbo_v2",
      "voice_id": "cjVigY5qzO86Huf0OWal",
      "agent_output_audio_format": "pcm_16000",
      "optimize_streaming_latency": 3,
      "stability": 0.5,
      "speed": 1,
      "similarity_boost": 0.8,
      "pronunciation_dictionary_locators": [
        {
          "pronunciation_dictionary_id": "pronunciation_dictionary_id"
        }
      ]
    },
    "conversation": {
      "text_only": true,
      "max_duration_seconds": 600,
      "client_events": [
        "audio",
        "interruption"
      ]
    },
    "language_presets": {
      "key": {
        "overrides": {}
      }
    },
    "agent": {
      "first_message": "Hello, how can I help you today?",
      "language": "en",
      "dynamic_variables": {
        "dynamic_variable_placeholders": {
          "user_name": "John Doe"
        }
      },
      "prompt": {
        "prompt": "You are a helpful assistant that can answer questions about the topic of the conversation.",
        "llm": "gemini-2.0-flash-001",
        "temperature": 0,
        "max_tokens": -1,
        "tools": [
          {
            "type": "client",
            "description": "description",
            "name": "name",
            "dynamic_variables": {
              "dynamic_variable_placeholders": {
                "user_name": "John Doe"
              }
            },
            "expects_response": false
          }
        ],
        "tool_ids": [
          "tool_ids"
        ],
        "knowledge_base": [
          {
            "type": "file",
            "name": "My Knowledge Base",
            "id": "123",
            "usage_mode": "auto"
          }
        ]
      }
    }
  },
  "metadata": {
    "created_at_unix_secs": 1
  },
  "platform_settings": {
    "auth": {
      "enable_auth": true,
      "allowlist": [
        {
          "hostname": "https://example.com"
        }
      ],
      "shareable_token": "1234567890"
    },
    "evaluation": {
      "criteria": [
        {
          "id": "1234567890",
          "name": "name",
          "conversation_goal_prompt": "You are a helpful assistant that can answer questions about the topic of the conversation.",
          "use_knowledge_base": false
        }
      ]
    },
    "widget": {
      "variant": "compact",
      "expandable": "never",
      "avatar": {
        "type": "orb",
        "color_1": "#2792dc",
        "color_2": "#9ce6e6"
      },
      "feedback_mode": "none",
      "bg_color": "bg_color",
      "text_color": "text_color",
      "btn_color": "btn_color",
      "btn_text_color": "btn_text_color",
      "border_color": "border_color",
      "focus_color": "focus_color",
      "border_radius": 1,
      "btn_radius": 1,
      "action_text": "action_text",
      "start_call_text": "start_call_text",
      "end_call_text": "end_call_text",
      "expand_text": "expand_text",
      "listening_text": "listening_text",
      "speaking_text": "speaking_text",
      "shareable_page_text": "shareable_page_text",
      "shareable_page_show_terms": true,
      "terms_text": "terms_text",
      "terms_html": "terms_html",
      "terms_key": "terms_key",
      "show_avatar_when_collapsed": true,
      "disable_banner": true,
      "override_link": "override_link",
      "mic_muting_enabled": true,
      "language_selector": false,
      "custom_avatar_path": "https://example.com/avatar.png"
    },
    "data_collection": {
      "key": {
        "type": "boolean",
        "description": "My property",
        "dynamic_variable": "Dynamic variable",
        "constant_value": "Constant value"
      }
    },
    "overrides": {
      "conversation_config_override": {
        "agent": {
          "prompt": {
            "prompt": false
          },
          "first_message": false,
          "language": false
        },
        "tts": {
          "voice_id": false
        },
        "conversation": {
          "text_only": false
        }
      },
      "custom_llm_extra_body": true,
      "enable_conversation_initiation_client_data_from_webhook": true
    },
    "call_limits": {
      "agent_concurrency_limit": -1,
      "daily_limit": 100000
    },
    "privacy": {
      "record_voice": true,
      "retention_days": -1,
      "delete_transcript_and_pii": false,
      "delete_audio": false,
      "apply_to_existing_conversations": false,
      "zero_retention_mode": false
    },
    "workspace_overrides": {
      "conversation_initiation_client_data_webhook": {
        "url": "https://example.com/webhook",
        "request_headers": {
          "Content-Type": "application/json"
        }
      }
    },
    "safety": {
      "is_blocked_ivc": true,
      "is_blocked_non_ivc": true,
      "ignore_safety_evaluation": true
    }
  },
  "phone_numbers": [
    {
      "provider": "sip_trunk",
      "label": "label",
      "phone_number": "phone_number",
      "phone_number_id": "X3Pbu5gP6NNKBscdCdwB",
      "assigned_agent": {
        "agent_id": "F3Pbu5gP6NNKBscdCdwB",
        "agent_name": "My Agent"
      },
      "provider_config": {
        "address": "address",
        "transport": "auto",
        "media_encryption": "disabled",
        "has_auth_credentials": true
      }
    }
  ],
  "access_info": {
    "is_creator": true,
    "creator_name": "John Doe",
    "creator_email": "<EMAIL>",
    "role": "admin"
  },
  "tags": [
    "tags"
  ]
}