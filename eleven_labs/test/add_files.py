from flask import Flask, request, jsonify
from elevenlabs import Eleven<PERSON>abs
import os
import json
import base64
import time
from datetime import datetime

app = Flask(__name__)

# ElevenLabs API setup
client = ElevenLabs(api_key="***************************************************")

# Configuration
SAVE_FOLDER = 'knowledge_base_documents'
JSON_PATH = 'knowledge_base.json'
os.makedirs(SAVE_FOLDER, exist_ok=True)

if not os.path.exists(JSON_PATH):
    with open(JSON_PATH, 'w') as f:
        json.dump([], f, indent=4)

def get_unique_filename(folder, base_name, ext):
    filename = f"{base_name}.{ext}"
    counter = 1
    while os.path.exists(os.path.join(folder, filename)):
        filename = f"{base_name}_{counter}.{ext}"
        counter += 1
    return filename

def load_json():
    if not os.path.exists(JSON_PATH) or os.path.getsize(JSON_PATH) == 0:
        return []  # Return empty list if file doesn't exist or is empty
    try:
        with open(JSON_PATH, 'r') as f:
            return json.load(f)
    except json.JSONDecodeError:
        return []  # Return empty list if JSON is invalid

def write_json(data):
    with open(JSON_PATH, 'w') as f:
        json.dump(data, f, indent=4)

def save_file_from_base64(base64_str, original_name):
    ext = original_name.split('.')[-1]
    base_name = os.path.splitext(original_name)[0]

    file_data = base64.b64decode(base64_str)
    unique_filename = get_unique_filename(SAVE_FOLDER, base_name, ext)
    filepath = os.path.join(SAVE_FOLDER, unique_filename)

    with open(filepath, 'wb') as f:
        f.write(file_data)

    print(f"File saved as: {unique_filename}")
    return filepath, unique_filename

def upload_files_to_knowledge_base(file_paths, client):
    supported_extensions = ['.pdf', '.docx', '.doc', '.txt']
    knowledge_base_items = []

    for file_path in file_paths:
        file_name = os.path.basename(file_path)
        file_extension = os.path.splitext(file_name)[1].lower()

        if os.path.isfile(file_path) and file_extension in supported_extensions:
            try:
                print(f"Uploading {file_name}...")
                with open(file_path, "rb") as file:
                    response = client.conversational_ai.add_to_knowledge_base(
                        file=file,
                        name=file_name
                    )

                document_id = response.id if hasattr(response, 'id') else None

                if document_id:
                    print(f"Uploaded document ID: {document_id} for {file_name}")
                    knowledge_base_items.append({
                        "type": "file",
                        "id": document_id,
                        "name": file_name,
                        "usage_mode": "auto"
                    })
                    time.sleep(1)
                else:
                    print(f"Failed to get document ID for {file_name}")
            except Exception as e:
                print(f"Error uploading {file_name}: {str(e)}")
        else:
            print(f"Skipping unsupported or non-existent file: {file_path}")

    return knowledge_base_items

@app.route("/knowledge_base_add_file_path", methods=["POST"])
def knowledge_base_add_file_path():
    try:
        data = request.get_json()
        file_name = data.get("filename")
        base64_file_data = data.get("file")

        if not file_name or not base64_file_data:
            return jsonify({"error": "file_name and base64_file_data are required"}), 400

        # Save file and get full path
        saved_path, unique_filename = save_file_from_base64(base64_file_data, file_name)

        # Upload to knowledge base
        upload_response = upload_files_to_knowledge_base([saved_path], client)
        if not upload_response:
            return jsonify({"error": "File not uploaded to knowledge base"}), 500

        # Use the first document's ID
        kb_doc_id = upload_response[0]["id"]

        uploaded_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        entry = {
            "kb_doc_name": unique_filename,
            "kb_doc_id": kb_doc_id,
            "kb_doc_status": "removed",
            "kb_doc_delete": "no",
            "kb_doc_uploaded_time": uploaded_time
        }

        existing_data = load_json()
        existing_data.append(entry)
        write_json(existing_data)

        return jsonify({
            "status": True,
            "message": "Document added successfully"
        })

    except Exception as e:
        print(f"Exception: {e}")
        return jsonify({
            "status": False,
            "message": "Document could not be added"
        })

if __name__ == "__main__":
    app.run(debug=False, host="0.0.0.0", port=8889)
