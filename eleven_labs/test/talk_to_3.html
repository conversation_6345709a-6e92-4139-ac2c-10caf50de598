<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Custom Agent Page</title>
    <style>
        /* IMMEDIATE HIDING - Prevents flash on page load */
        elevenlabs-convai {
            opacity: 0 !important;
            transition: opacity 0.3s ease-in-out;
        }

        /* Show widget only after attribution is hidden */
        elevenlabs-convai.ready {
            opacity: 1 !important;
        }

        /* Aggressive immediate hiding of potential attribution */
        elevenlabs-convai * {
            visibility: hidden !important;
        }

        /* Show only the main widget content */
        elevenlabs-convai .widget-container,
        elevenlabs-convai .chat-container,
        elevenlabs-convai .conversation-container,
        elevenlabs-convai [class*="widget"],
        elevenlabs-convai [class*="chat"],
        elevenlabs-convai [class*="conversation"] {
            visibility: visible !important;
        }

        /* Method 1: Hide by text content (most reliable) */
        elevenlabs-convai *:has-text("Powered by ElevenLabs"),
        elevenlabs-convai *[text*="Powered by"],
        elevenlabs-convai *[aria-label*="Powered by"] {
            display: none !important;
            visibility: hidden !important;
        }

        /* Method 2: Hide common attribution classes/IDs */
        elevenlabs-convai .attribution,
        elevenlabs-convai .powered-by,
        elevenlabs-convai .branding,
        elevenlabs-convai .footer-text,
        elevenlabs-convai [class*="attribution"],
        elevenlabs-convai [class*="powered"],
        elevenlabs-convai [class*="branding"] {
            display: none !important;
            visibility: hidden !important;
        }

        /* Method 3: Hide small text at bottom (common pattern) */
        elevenlabs-convai div:last-child {
            font-size: 0 !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            visibility: hidden !important;
        }

        /* Method 4: Hide any links that might contain attribution */
        elevenlabs-convai a[href*="elevenlabs"],
        elevenlabs-convai a[href*="conversational"] {
            display: none !important;
            visibility: hidden !important;
        }

        /* Method 5: Hide small text elements immediately */
        elevenlabs-convai [style*="font-size: 12px"],
        elevenlabs-convai [style*="font-size: 10px"],
        elevenlabs-convai [style*="font-size: 11px"],
        elevenlabs-convai [style*="opacity: 0.7"],
        elevenlabs-convai [style*="opacity: 0.8"],
        elevenlabs-convai small,
        elevenlabs-convai .small-text {
            display: none !important;
            visibility: hidden !important;
        }

        /* Hide any text that might be attribution */
        elevenlabs-convai *:not(button):not(input):not(textarea) {
            color: transparent !important;
        }

        /* Show essential interactive elements */
        elevenlabs-convai button,
        elevenlabs-convai input,
        elevenlabs-convai textarea,
        elevenlabs-convai [role="button"],
        elevenlabs-convai .chat-message,
        elevenlabs-convai .message {
            color: initial !important;
            visibility: visible !important;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; font-family: sans-serif;">Custom Agent</h1>

    <!-- Embed the Agent -->
    <elevenlabs-convai agent-id="0TP5bFyDLLxVqINQh8B5"></elevenlabs-convai>
    <script src="https://unpkg.com/@elevenlabs/convai-widget-embed" async type="text/javascript"></script>

    <script>
        // IMMEDIATE hiding function - runs as soon as possible
        function immediateHide() {
            const widget = document.querySelector('elevenlabs-convai');
            if (widget) {
                // Hide the entire widget initially
                widget.style.opacity = '0';
                widget.style.transition = 'opacity 0.3s ease-in-out';

                // Function to clean up attribution and show widget
                function cleanAndShow() {
                    // Remove attribution from regular DOM
                    const allElements = widget.querySelectorAll('*');
                    allElements.forEach(el => {
                        if (el.textContent && el.textContent.toLowerCase().includes('powered by')) {
                            el.remove();
                        }
                        if (el.textContent && el.textContent.toLowerCase().includes('elevenlabs')) {
                            el.remove();
                        }
                    });

                    // Handle shadow DOM if present
                    if (widget.shadowRoot) {
                        const shadowElements = widget.shadowRoot.querySelectorAll('*');
                        shadowElements.forEach(el => {
                            if (el.textContent && el.textContent.toLowerCase().includes('powered by')) {
                                el.remove();
                            }
                            if (el.textContent && el.textContent.toLowerCase().includes('elevenlabs')) {
                                el.remove();
                            }
                        });
                    }

                    // Show the widget after cleanup
                    widget.style.opacity = '1';
                    widget.classList.add('ready');
                }

                // Try to clean immediately
                cleanAndShow();

                // Also run after short delays to catch dynamic content
                setTimeout(cleanAndShow, 100);
                setTimeout(cleanAndShow, 500);
                setTimeout(cleanAndShow, 1000);

                // Set up observer for any new elements
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                if (node.textContent && node.textContent.toLowerCase().includes('powered by')) {
                                    node.remove();
                                }
                                if (node.textContent && node.textContent.toLowerCase().includes('elevenlabs')) {
                                    node.remove();
                                }
                            }
                        });
                    });
                });

                observer.observe(widget, {
                    childList: true,
                    subtree: true
                });

                // Also observe shadow root if it exists
                if (widget.shadowRoot) {
                    observer.observe(widget.shadowRoot, {
                        childList: true,
                        subtree: true
                    });
                }
            }
        }

        // Run immediately when script loads
        immediateHide();

        // Run when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', immediateHide);
        }

        // Run when page is fully loaded
        window.addEventListener('load', immediateHide);

        // Backup - run periodically for any dynamic content
        const cleanupInterval = setInterval(() => {
            immediateHide();
        }, 2000);

        // Stop the interval after 30 seconds
        setTimeout(() => {
            clearInterval(cleanupInterval);
        }, 30000);
    </script>
</body>
</html>
