# 1163, 554
# 1920 , 1080

import cv2
import numpy as np

# Load the original image
original_image_path = 'car_parking.png'
image = cv2.imread(original_image_path)

# Original frame size
original_height, original_width = image.shape[:2]

# UI frame size (from which coordinates were taken)
ui_width, ui_height = 1163, 554

# Scale factors
scale_x = original_width / ui_width
scale_y = original_height / ui_height

# Original coordinates taken from UI
ui_coordinates = [[410, 153], [747, 148], [746, 362], [395, 347], [407, 154]]

# Adjust coordinates for the original frame size
adjusted_coordinates = [(int(x * scale_x), int(y * scale_y)) for x, y in ui_coordinates]

# Convert to numpy array of type int32
target_zone = np.array(adjusted_coordinates, np.int32)

# Draw the target zone on the original image (in green color with a thickness of 4)
image_with_polygon = image.copy()
cv2.polylines(image_with_polygon, [target_zone], True, (0, 220, 0), 4)

# Display the image with the drawn polygon
cv2.imshow('Image with Adjusted Polygon', image_with_polygon)
cv2.waitKey(0)

# Save the modified image
cv2.imwrite('adjusted_cropped_and_pasted_image.jpg', image_with_polygon)

cv2.destroyAllWindows()
