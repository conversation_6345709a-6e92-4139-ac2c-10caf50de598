[{"statement": "Verify the Database Mode and Role on Primary & DR Nodes", "command": "`select name, open_mode, database_role from v$database;`", "output": "On Primary: - Mode - READ WRITE, ROLE – PRIMARY On Standby: - Mode – MOUNTED, ROLE – STANDBY"}, {"statement": "Verify the Job Status on Primary Node", "command": "`select name, value from v$parameter where name='job_queue_processes';`", "output": "0"}, {"statement": "Switch the log on database on Primary Node2 (If Available)", "command": "`alter system switch logfile;`", "output": "System altered"}, {"statement": "Shut down the database on Primary Node 2 (If Available)", "command": "`shutdown immediate;`", "output": "ORACLE instance shut down"}, {"statement": "Switch the log on database on Primary Node1", "command": "`alter system switch logfile;`", "output": "System altered"}, {"statement": "Verify the Log Sequence Number on Primary & DR Node", "command": "`select thread#, max(sequence#) from v$log_history group by thread#;`", "output": "Success"}, {"statement": "Verify the Switchover status for database is TO_STANDBY or SESSIONS ACTIVE on Primary Node1", "command": "`select switchover_status from v$database;`", "output": "TO_STANDBY"}, {"statement": "Switch the role of the Database from Primary to Standby on Primary Node 1", "command": "`alter database commit to switchover to physical standby with session shutdown;`", "output": "Database altered"}, {"statement": "Start the database in mount state on the New DR Node 1", "command": "`startup mount;`", "output": "Database mounted"}, {"statement": "Verify the Switchover status for database is TO_PRIMARY or SESSIONS ACTIVE on Original DR Node", "command": "`select switchover_status from v$database;`", "output": "TOPRIMARY"}, {"statement": "Switch the role of the Database from Standby to Primary on Original DR Node", "command": "`alter database commit to switchover to primary;`", "output": "Database altered"}, {"statement": "Shutdown the database post switching the role from Standby to Primary on Original DR Node", "command": "`shutdown immediate;`", "output": "ORACLE instance shut down"}, {"statement": "Start the database in Read Write mode on New Primary Node", "command": "`startup;`", "output": "Database opened"}, {"statement": "Start the database in standby mode on New DR Node 2 (If Available)", "command": "`startup mount;`", "output": "Database mounted"}, {"statement": "Verify the Database Mode and Role on DR & Primary & Nodes post Switchover", "command": "`select name, open_mode, database_role from v$database;`", "output": "On New Primary: - Mode - READ WRITE, ROLE – PRIMARY On New Standby: - Mode – MOUNTED, ROLE - STANDBY"}, {"statement": "Switch the log on database on New Primary Node 1", "command": "`alter system switch logfile;`", "output": "System altered"}, {"statement": "Start the recovery for database from DR to Primary on New DR Node", "command": "`alter database recover managed standby database disconnect from session;`", "output": "Database altered"}, {"statement": "Verify the Log Sequence Number on New DR & Primary Nodes", "command": "`select thread#, max(sequence#) from v$log_history group by thread#;`", "output": "Success"}]