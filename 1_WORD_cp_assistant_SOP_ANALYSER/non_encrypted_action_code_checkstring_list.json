[{"action": "DGVerifyDBModeAndRole", "script": "", "command": "select name, open_mode, database_role from v$database;", "job": "", "return flag": "True", "check output": "On Primary: - Mode - READ WRITE, ROLE – PRIMARYOn Standby: - Mode – MOUNTED, ROLE – STANDBY", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGVerifyDBModeAndRole"}, {"action": "DGJobStatus", "script": "", "command": "select name, value from v$parameter where name='job_queue_processes';", "job": "", "return flag": "True", "check output": "0", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGJobStatus"}, {"action": "VerifyLogSequence", "script": "", "command": "select thread#, max(sequence#) from v$log_history group by thread#;", "job": "", "return flag": "True", "check output": "Success", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "VerifyLogSequence"}, {"action": "DGSwitchStandByToPrimary", "script": "", "command": "alter system switch logfile;", "job": "", "return flag": "True", "check output": "System altered", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGSwitchStandByToPrimary"}, {"action": "DGShutDownPrimary", "script": "", "command": "shutdown immediate;", "job": "", "return flag": "True", "check output": "ORACLE instance shut down", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGShutDownPrimary"}, {"action": "DGSwitchLogFile", "script": "", "command": "alter system switch logfile;", "job": "", "return flag": "True", "check output": "System altered", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGSwitchLogFile"}, {"action": "VerifyLogSequence", "script": "", "command": "select thread#, max(sequence#) from v$log_history group by thread#;", "job": "", "return flag": "True", "check output": "Success", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "VerifyLogSequence"}, {"action": "VerifySwitchoverStatus", "script": "", "command": "select switchover_status from v$database;", "job": "", "return flag": "True", "check output": "TO_STANDBY", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "VerifySwitchoverStatus"}, {"action": "DGSwitchStandByToPrimary", "script": "", "command": "alter database commit to switchover to physical standby with session shutdown;", "job": "", "return flag": "True", "check output": "Database altered", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGSwitchStandByToPrimary"}, {"action": "DGMountStandby", "script": "", "command": "startup mount;", "job": "", "return flag": "True", "check output": "Database mounted", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGMountStandby"}, {"action": "VerifySwitchoverStatus", "script": "", "command": "select switchover_status from v$database;", "job": "", "return flag": "True", "check output": "TO PRIMARY", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "VerifySwitchoverStatus"}, {"action": "DGSwitchStandByToPrimary", "script": "", "command": "alter database commit to switchover to primary;", "job": "", "return flag": "True", "check output": "Database altered", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGSwitchStandByToPrimary"}, {"action": "DGSwitchPrimaryToStandBy", "script": "", "command": "shutdown immediate;", "job": "", "return flag": "True", "check output": "ORACLE instance shut down", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGSwitchPrimaryToStandBy"}, {"action": "DGSwitchStandByToPrimary", "script": "", "command": "startup;", "job": "", "return flag": "True", "check output": "Database opened", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGSwitchStandByToPrimary"}, {"action": "DGSwitchPrimaryToStandBy", "script": "", "command": "startup mount;", "job": "", "return flag": "True", "check output": "Database mounted", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGSwitchPrimaryToStandBy"}, {"action": "DGVerifyDBModeAndRole", "script": "", "command": "select name, open_mode, database_role from v$database;", "job": "", "return flag": "True", "check output": "On New Primary: - Mode - READ WRITE, ROLE – PRIMARYOn New Standby: - Mode – MOUNTED, ROLE - STANDBY", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGVerifyDBModeAndRole"}, {"action": "DGSwitchLogFile", "script": "", "command": "alter system switch logfile;", "job": "", "return flag": "True", "check output": "System altered", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "DGSwitchLogFile"}, {"action": "OracleDG12C_StartRecovery", "script": "", "command": "alter database recover managed standby database disconnect from session;", "job": "", "return flag": "True", "check output": "Database altered", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "OracleDG12C_StartRecovery"}, {"action": "VerifyLogSequence", "script": "", "command": "select thread#, max(sequence#) from v$log_history group by thread#;", "job": "", "return flag": "True", "check output": "Success", "type": "Sequence", "id": 0, "server": "************", "solution_name": "", "solution_actions": [], "display_name": "VerifyLogSequence"}]