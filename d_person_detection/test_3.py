from ultralytics import YOLO
import cv2
import numpy as np
import os

model = YOLO("yo_models/yolov8n-pose.pt")


def calculate_distance(point1, point2):
    return np.sqrt((point1[0] - point2[0]) ** 2 + (point1[1] - point2[1]) ** 2)


def detect_hand_to_face(keypoints, threshold_distance):
    left_shoulder = keypoints[5][:2]
    right_shoulder = keypoints[6][:2]
    left_wrist = keypoints[9][:2]
    right_wrist = keypoints[10][:2]

    left_hand_to_face = calculate_distance(left_wrist, left_shoulder) < threshold_distance
    right_hand_to_face = calculate_distance(right_wrist, right_shoulder) < threshold_distance

    return left_hand_to_face, right_hand_to_face


def draw_pose_points(frame, results):
    for result in results:
        if result.keypoints is not None:
            keypoints = result.keypoints.data

            for person_keypoints in keypoints:
                # Detect hand to face
                threshold_distance = frame.shape[0] * 0.15  # 15% of frame height
                left_hand_to_face, right_hand_to_face = detect_hand_to_face(person_keypoints, threshold_distance)

                for i, keypoint in enumerate(person_keypoints):
                    x, y, conf = keypoint
                    if conf > 0.5:  # You can adjust this threshold
                        cv2.circle(frame, (int(x), int(y)), 5, (0, 255, 0), -1)
                        cv2.putText(frame, str(i), (int(x), int(y) - 10),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)


                if left_hand_to_face and right_hand_to_face:
                    alert_text = "GREETINGS DETECTED"
                    cv2.putText(frame, alert_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                    print(alert_text)

    return frame

image_path = "frame_18061.jpg"
frame = cv2.imread(image_path)

if frame is not None:
    results = model.track(frame, classes=0, tracker="bytetrack.yaml")

    base_name = os.path.splitext(os.path.basename(image_path))[0]
    output_filename = f"{base_name}_output.png"

    frame = draw_pose_points(frame, results)
    cv2.imwrite(output_filename, frame)
else:
    print("Error: Image not found or path is incorrect")

