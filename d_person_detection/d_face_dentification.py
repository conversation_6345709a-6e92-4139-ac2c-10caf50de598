from datetime import datetime
import cv2
import numpy as np
import torch
import onnxruntime as ort
from insightface.app import FaceAnalysis
import os
import pickle
import time
from collections import deque
import threading
import math

class AdvancedFaceRecognitionApp:
    def __init__(self):
        print("Initializing AdvancedFaceRecognitionApp...")
        self.face_database = {}
        self.database_file = "face_database.pkl"

        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device: {self.device}")

        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if self.device.type == 'cuda' else ['CPUExecutionProvider']
        self.face_analyzer = FaceAnalysis(providers=providers)
        self.face_analyzer.prepare(ctx_id=0, det_size=(640, 640))

        self.recognition_threshold = 0.5
        self.load_face_database()

        self.streams = {}
        self.frame_queues = {}
        self.stop_capture = threading.Event()

        self.max_queue_size = 150  # 5 seconds at 30 fps
        self.max_delay = 5  # Maximum delay in seconds

        self.grid_size = (0, 0)
        self.selected_stream = None
        self.window_name = "Face Recognition Streams"

        print("AdvancedFaceRecognitionApp initialized.")

    def load_face_database(self):
        if os.path.exists(self.database_file):
            with open(self.database_file, 'rb') as f:
                self.face_database = pickle.load(f)
            print(f"Loaded {len(self.face_database)} faces from database.")
        else:
            print("No existing face database found. Starting with an empty database.")

    def save_face_database(self):
        with open(self.database_file, 'wb') as f:
            pickle.dump(self.face_database, f)
        print(f"Saved {len(self.face_database)} faces to database.")

    def add_face(self):
        name = input("Enter the person's name: ")
        cap = cv2.VideoCapture(0)

        if not cap.isOpened():
            print("Error: Could not open camera.")
            return

        os.makedirs("face_database", exist_ok=True)
        person_dir = os.path.join("face_database", name)
        os.makedirs(person_dir, exist_ok=True)

        embeddings = []
        image_files = []
        poses = ["straight", "left", "right"]

        for pose in poses:
            images_captured = 0
            print(f"\nCapturing {pose} pose. Press 'c' to capture an image (10 required).")
            print("Press 'q' to quit.")

            while images_captured < 10:
                ret, frame = cap.read()
                if not ret:
                    print("Failed to capture image")
                    continue

                cv2.putText(frame, f"Pose: {pose} ({images_captured}/10)", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1,
                            (0, 255, 0), 2)
                cv2.imshow("Capture Face", frame)

                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('c'):
                    faces = self.face_analyzer.get(frame)
                    if len(faces) > 0:
                        face = faces[0]
                        bbox = face.bbox.astype(int)

                        # Ensure bbox coordinates are within frame boundaries
                        bbox[0] = max(0, bbox[0])
                        bbox[1] = max(0, bbox[1])
                        bbox[2] = min(frame.shape[1], bbox[2])
                        bbox[3] = min(frame.shape[0], bbox[3])

                        if bbox[2] > bbox[0] and bbox[3] > bbox[1]:
                            face_img = frame[bbox[1]:bbox[3], bbox[0]:bbox[2]]

                            if face_img.size > 0:
                                filename = f"{name}_{pose}_{images_captured}.jpg"
                                filepath = os.path.join(person_dir, filename)

                                try:
                                    cv2.imwrite(filepath, face_img)
                                    embeddings.append(face.embedding)
                                    image_files.append(filename)
                                    images_captured += 1
                                    print(f"Captured and saved image {images_captured} for {pose} pose")
                                except Exception as e:
                                    print(f"Error saving image: {e}")
                            else:
                                print("Invalid face image, please try again")
                        else:
                            print("Invalid bounding box, please try again")
                    else:
                        print("No face detected, please try again")

            if images_captured < 10:
                print(f"Capturing for {pose} pose interrupted. Moving to the next pose.")

        cap.release()
        cv2.destroyAllWindows()

        if len(embeddings) > 0:
            self.face_database[name] = {
                'embeddings': embeddings,
                'image_files': image_files
            }
            print(f"Added {name} to the recognition database with {len(embeddings)} images.")
            self.save_face_database()
        else:
            print(f"Failed to capture any valid faces for {name}. Please try again.")

    def cosine_similarity(self, a, b):
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

    # def play_video_file(self, video_path):
    #     if not os.path.exists(video_path):
    #         print(f"Error: Video file '{video_path}' not found.")
    #         return
    #
    #     cap = cv2.VideoCapture(video_path)
    #     if not cap.isOpened():
    #         print("Error: Could not open video file.")
    #         return
    #     frame_count = 0
    #     while True:
    #         ret, frame_main = cap.read()
    #         if not ret:
    #             break
    #         frame = cv2.resize(frame_main, (640, 480))
    #         frame_count += 1
    #         faces = self.face_analyzer.get(frame)
    #
    #         for face in faces:
    #             bbox = face.bbox.astype(int)
    #             recognized_name = self.recognize_face(face.embedding)
    #             cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
    #             cv2.putText(frame, recognized_name, (bbox[0], bbox[1] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0),
    #                         2)
    #             print(f"Frame {frame_count}: Detected face: {recognized_name}")
    #
    #         cv2.imshow("Video Playback", frame)
    #         if cv2.waitKey(1) & 0xFF == ord('q'):
    #             break
    #
    #     cap.release()
    #     cv2.destroyAllWindows()

    def play_video_file(self, video_path):
        if not os.path.exists(video_path):
            print(f"Error: Video file '{video_path}' not found.")
            return

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print("Error: Could not open video file.")
            return


        frame_count = 0
        # start_time = time.time()
        start_time = datetime.now()
        print(f"Start Time : {start_time}")

        while True:
            ret, frame = cap.read()

            if not ret:
                print(f"End of video or error reading frame at frame count {frame_count}")
                break

            if frame is None:
                print(f"Warning: Empty frame encountered at frame count {frame_count}")
                continue

            # frame = cv2.resize(frame_main, (640, 480))
            #
            frame_count += 1

            # Process every third frame
            if frame_count % 1 == 5:
                faces = self.face_analyzer.get(frame)

                for face in faces:
                    bbox = face.bbox.astype(int)
                    recognized_name = self.recognize_face(face.embedding)
                    cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
                    cv2.putText(frame, recognized_name, (bbox[0], bbox[1] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9,
                                (0, 255, 0), 2)
                    print(f"Frame {frame_count}: Detected face: {recognized_name}")

                cv2.imshow("Video Playback (2x speed)", frame)

                # Wait for a fixed time to maintain desired FPS
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            else:
                # Skip display for non-processed frames, but check for 'q' key
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break

        cap.release()
        cv2.destroyAllWindows()
        end_time = datetime.now()
        print(f"End Time : {end_time}")
        print(f"Total Time : {end_time - start_time}")

    def recognize_face(self, face_embedding):
        if len(self.face_database) == 0:
            return "Unknown"

        max_similarity = -1
        recognized_name = "Unknown"
        for name, data in self.face_database.items():
            for embedding in data['embeddings']:
                similarity = self.cosine_similarity(face_embedding, embedding)
                if similarity > max_similarity:
                    max_similarity = similarity
                    recognized_name = name

        return recognized_name if max_similarity > self.recognition_threshold else "Unknown"

    def capture_frames(self, stream_id, video_source):
        cap = cv2.VideoCapture(video_source)
        if not cap.isOpened():
            print(f"Error: Could not open video source for stream {stream_id}.")
            return

        while not self.stop_capture.is_set():
            ret, frame = cap.read()
            if not ret:
                print(f"Failed to capture frame from stream {stream_id}")
                break

            if len(self.frame_queues[stream_id]) >= self.max_queue_size:
                self.frame_queues[stream_id].popleft()
            self.frame_queues[stream_id].append((time.time(), frame))

        cap.release()

    def process_frames(self):
        cv2.namedWindow(self.window_name)
        cv2.setMouseCallback(self.window_name, self.on_mouse_click)

        while not self.stop_capture.is_set():
            grid_frame = self.create_grid_frame()

            for i, stream_id in enumerate(self.streams):
                if len(self.frame_queues[stream_id]) > 0:
                    timestamp, frame = self.frame_queues[stream_id][-1]
                    if time.time() - timestamp <= self.max_delay:
                        faces = self.face_analyzer.get(frame)

                        for face in faces:
                            bbox = face.bbox.astype(int)
                            recognized_name = self.recognize_face(face.embedding)
                            cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
                            cv2.putText(frame, recognized_name, (bbox[0], bbox[1] - 10), cv2.FONT_HERSHEY_SIMPLEX,
                                        0.9, (0, 255, 0), 2)
                            print(f"Stream {stream_id} - Detected face: {recognized_name}")

                        if self.selected_stream == stream_id:
                            cv2.imshow(self.window_name, frame)
                        else:
                            row = i // self.grid_size[1]
                            col = i % self.grid_size[1]
                            frame_h, frame_w = 360, 640  # Match this with create_grid_frame
                            resized_frame = cv2.resize(frame, (frame_w, frame_h))
                            grid_frame[row * frame_h:(row + 1) * frame_h,
                            col * frame_w:(col + 1) * frame_w] = resized_frame

            if self.selected_stream is None:
                cv2.imshow(self.window_name, grid_frame)

            if cv2.waitKey(1) & 0xFF == ord('q'):
                self.stop_capture.set()
                break

        cv2.destroyAllWindows()

    def create_grid_frame(self):
        rows, cols = self.grid_size
        frame_h, frame_w = 360, 640  # Smaller frame size
        grid_h, grid_w = frame_h * rows, frame_w * cols
        return np.zeros((grid_h, grid_w, 3), dtype=np.uint8)

    def on_mouse_click(self, event, x, y, flags, param):
        if event == cv2.EVENT_LBUTTONDOWN:
            if self.selected_stream is None:
                rows, cols = self.grid_size
                frame_h, frame_w = 480, 640  # Assuming a default frame size
                grid_h, grid_w = frame_h // rows, frame_w // cols
                row = y // grid_h
                col = x // grid_w
                index = row * cols + col
                if index < len(self.streams):
                    self.selected_stream = list(self.streams.keys())[index]
            else:
                self.selected_stream = None

    def on_mouse_click(self, event, x, y, flags, param):
        if event == cv2.EVENT_LBUTTONDOWN:
            if self.selected_stream is None:
                rows, cols = self.grid_size
                frame_h, frame_w = 360, 640  # Match this with create_grid_frame
                row = y // frame_h
                col = x // frame_w
                index = row * cols + col
                if index < len(self.streams):
                    self.selected_stream = list(self.streams.keys())[index]
            else:
                self.selected_stream = None

    def calculate_grid_size(self, n):
        cols = math.ceil(math.sqrt(n))
        rows = math.ceil(n / cols)
        return (rows, cols)

    def list_known_faces(self):
        if len(self.face_database) == 0:
            print("No faces in the database.")
        else:
            print("Known faces:")
            for name, data in self.face_database.items():
                print(f"- {name} ({len(data['image_files'])} images)")

    def delete_face(self):
        if len(self.face_database) == 0:
            print("No faces in the database to delete.")
            return

        print("Current faces in the database:")
        for i, name in enumerate(self.face_database.keys(), 1):
            print(f"{i}. {name}")

        choice = input("Enter the number of the face you want to delete (or 'q' to cancel): ")
        if choice.lower() == 'q':
            return

        try:
            index = int(choice) - 1
            if 0 <= index < len(self.face_database):
                name = list(self.face_database.keys())[index]
                del self.face_database[name]
                print(f"Deleted {name} from the database.")
                self.save_face_database()

                person_dir = os.path.join("face_database", name)
                if os.path.exists(person_dir):
                    for file in os.listdir(person_dir):
                        os.remove(os.path.join(person_dir, file))
                    os.rmdir(person_dir)
                    print(f"Removed {name}'s directory from face_database folder.")
            else:
                print("Invalid selection. Please try again.")
        except ValueError:
            print("Invalid input. Please enter a number or 'q'.")

    def delete_specific_image(self):
        if len(self.face_database) == 0:
            print("No faces in the database.")
            return

        print("Current faces in the database:")
        for i, name in enumerate(self.face_database.keys(), 1):
            print(f"{i}. {name}")

        choice = input("Enter the number of the person whose image you want to delete (or 'q' to cancel): ")
        if choice.lower() == 'q':
            return

        try:
            index = int(choice) - 1
            if 0 <= index < len(self.face_database):
                name = list(self.face_database.keys())[index]
                person_data = self.face_database[name]
                person_dir = os.path.join("face_database", name)

                if os.path.exists(person_dir):
                    images = person_data['image_files']

                    if not images:
                        print(f"No images found for {name}.")
                        return

                    print(f"Images for {name}:")
                    for i, image in enumerate(images, 1):
                        print(f"{i}. {image}")

                    image_choice = input("Enter the number of the image you want to delete (or 'q' to cancel): ")
                    if image_choice.lower() == 'q':
                        return

                    try:
                        image_index = int(image_choice) - 1
                        if 0 <= image_index < len(images):
                            image_to_delete = images[image_index]
                            os.remove(os.path.join(person_dir, image_to_delete))
                            print(f"Deleted image: {image_to_delete}")

                            del person_data['image_files'][image_index]
                            del person_data['embeddings'][image_index]

                            if person_data['image_files']:
                                self.save_face_database()
                                print(f"Updated database for {name}")
                            else:
                                del self.face_database[name]
                                self.save_face_database()
                                os.rmdir(person_dir)
                                print(f"Removed {name} from the database as no images remain.")
                        else:
                            print("Invalid image selection. Please try again.")
                    except ValueError:
                        print("Invalid input. Please enter a number or 'q'.")
                else:
                    print(f"No directory found for {name}.")
            else:
                print("Invalid selection. Please try again.")
        except ValueError:
            print("Invalid input. Please enter a number or 'q'.")

    def run(self):
        while True:
            choice = input(
                "Choose an option:\n1. Add face\n2. Real-time recognition\n3. List known faces\n4. Delete face\n5. Delete specific image\n6. Video File Process\n7. Exit")
            if choice == '1':
                self.add_face()
            elif choice == '2':
                num_feeds = int(input("Enter the number of video feeds: "))
                video_sources = []
                for i in range(num_feeds):
                    source = input(f"Enter RTSP URL for feed {i + 1}: ")
                    video_sources.append(source)
                if video_sources:
                    self.process_video_streams(video_sources)  # Changed from recognize_faces to process_video_streams
                else:
                    print("No video sources provided.")
            elif choice == '3':
                self.list_known_faces()
            elif choice == '4':
                self.delete_face()
            elif choice == '5':
                self.delete_specific_image()
            elif choice == '6':
                video_path = input("Enter the path to your video file (mp4 or avi): ")
                self.play_video_file(video_path)
            elif choice == '7':
                break
            else:
                print("Invalid choice. Please try again.")

    def process_video_streams(self, video_sources):
        self.stop_capture.clear()
        self.streams = {}
        self.frame_queues = {}

        for i, source in enumerate(video_sources):
            stream_id = f"stream_{i}"
            self.streams[stream_id] = source
            self.frame_queues[stream_id] = deque(maxlen=self.max_queue_size)

            capture_thread = threading.Thread(target=self.capture_frames, args=(stream_id, source))
            capture_thread.start()

        self.grid_size = self.calculate_grid_size(len(video_sources))
        process_thread = threading.Thread(target=self.process_frames)
        process_thread.start()

        process_thread.join()

if __name__ == "__main__":
    app = AdvancedFaceRecognitionApp()
    app.run()
