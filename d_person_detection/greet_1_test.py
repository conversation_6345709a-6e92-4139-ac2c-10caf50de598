from ultralytics import YOLO
import cv2
import numpy as np
import os

model = Y<PERSON><PERSON>("yo_models/yolov8n-pose.pt")


def calculate_distance(point1, point2):
    return np.sqrt((point1[0] - point2[0]) ** 2 + (point1[1] - point2[1]) ** 2)


def display_greeting(frame, person_count, alert_text, position, text_color):
    full_text = f"{alert_text}"
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 1
    thickness = 4
    bg_color = (255, 255, 255)

    (text_width, text_height), _ = cv2.getTextSize(full_text, font, font_scale, thickness)

    text_org = position
    top_left = (text_org[0] - 10, text_org[1] - text_height - 10)
    bottom_right = (text_org[0] + text_width + 10, text_org[1] + 10)

    cv2.rectangle(frame, top_left, bottom_right, bg_color, -1)

    cv2.putText(frame, full_text, text_org, font, font_scale, text_color, thickness)


def check_greetings(frame, results):
    person_count = 0
    text_down = 60
    for result in results:
        if result.keypoints is not None:
            keypoints = result.keypoints.data

            for person_keypoints in keypoints:
                person_count += 1
                valid_keypoints = [kp for kp in person_keypoints if kp[2] > 0.5]
                if valid_keypoints:
                    avg_x = np.mean([kp[0] for kp in valid_keypoints])
                    avg_y = np.mean([kp[1] for kp in valid_keypoints])

                    cv2.putText(frame, f"Person {person_count}", (int(avg_x), int(avg_y) - 30),
                                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)
                points_dict = {}
                left_hand = False
                right_hand = False
                greet_flag = False
                for i, keypoint in enumerate(person_keypoints):
                    x, y, conf = keypoint
                    if conf > 0.5:
                        points_dict[i] = [x, y]
                        # cv2.circle(frame, (int(x), int(y)), 5, (0, 255, 0), -1)
                        # cv2.putText(frame, str(i), (int(x), int(y) - 10),
                        #             cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

                if 0 in points_dict:
                    if (7 in points_dict) and (9 in points_dict):
                        y_7 = points_dict[7][1].item()
                        y_9 = points_dict[9][1].item()
                        if y_9 <= y_7:
                            left_hand = True
                    if (8 in points_dict) and (10 in points_dict):
                        y_8 = points_dict[8][1].item()
                        y_10 = points_dict[10][1].item()
                        if y_10 <= y_8:
                            right_hand = True

                    if (7 in points_dict) and (9 in points_dict) and (8 in points_dict) and (10 in points_dict):
                        if left_hand and right_hand:
                            print(f"Person - {person_count} Both")
                            alert_text = f"Person - {person_count} Greeted "
                            greet_flag = True
                            display_greeting(frame, person_count, alert_text, position=(20, text_down), text_color=(0, 255, 0))
                            text_down += 100

                    elif (7 in points_dict) and (9 in points_dict):
                        if ((8 not in points_dict) and (10 not in points_dict)) or (
                                (8 in points_dict) or (10 in points_dict)):
                            if left_hand:
                                print(f"Person - {person_count} left")
                                alert_text = f"Person - {person_count} Greeted "
                                greet_flag = True
                                display_greeting(frame, person_count, alert_text, position=(20, text_down),
                                                 text_color=(0, 255, 0))
                                text_down += 100

                    elif (8 in points_dict) and (10 in points_dict):
                        if ((7 not in points_dict) and (9 not in points_dict)) or (
                                (7 in points_dict) or (9 in points_dict)):
                            if right_hand:
                                print(f"Person - {person_count} right")
                                alert_text = f"Person - {person_count} Greeted "
                                greet_flag = True
                                display_greeting(frame, person_count, alert_text, position=(20, text_down),
                                                 text_color=(0, 255, 0))
                                text_down += 100

                    if not greet_flag:
                        alert_text = f"Person - {person_count} Not Greeted "
                        display_greeting(frame, person_count, alert_text, position=(20, text_down), text_color=(0, 0, 255))
                        text_down += 100

    return frame


cap = cv2.VideoCapture("g_test_3.mp4")

frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
fps = cap.get(cv2.CAP_PROP_FPS)

# Define codec and create VideoWriter object to save the output video
fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # Codec for mp4
out = cv2.VideoWriter('greeting_output_3.mp4', fourcc, fps, (frame_width, frame_height))

while cap.isOpened():
    success, frame = cap.read()
    if success:
        results = model.track(frame, classes=0, tracker="bytetrack.yaml", persist=True)

        frame = check_greetings(frame, results)

        cv2.imshow("YOLOv8 Tracking", frame)
        out.write(frame)

        if cv2.waitKey(1) & 0xFF == ord("q"):
            break
    else:
        break

cap.release()
out.release()
cv2.destroyAllWindows()
