""" Done """

from ultralytics import YOLO
model = YOLO("yo_models/yolov8n-pose.pt")

import cv2
import numpy as np


def print_pose_points(results):
    for result in results:
        if result.keypoints is not None:
            keypoints = result.keypoints
            print(f"Person detected:")
            for i, keypoint in enumerate(keypoints.data[0]):
                x, y, conf = keypoint
                print(f"  Point {i}: x={x:.2f}, y={y:.2f}, confidence={conf:.2f}")


def calculate_distance(point1, point2):
    return np.sqrt((point1[0] - point2[0]) ** 2 + (point1[1] - point2[1]) ** 2)


def detect_hand_to_face(keypoints, threshold_distance):
    left_shoulder = keypoints[5][:2]
    right_shoulder = keypoints[6][:2]
    left_wrist = keypoints[9][:2]
    right_wrist = keypoints[10][:2]

    left_hand_to_face = calculate_distance(left_wrist, left_shoulder) < threshold_distance
    right_hand_to_face = calculate_distance(right_wrist, right_shoulder) < threshold_distance

    return left_hand_to_face, right_hand_to_face


def draw_pose_points(frame, results):
    for result in results:
        if result.keypoints is not None:
            keypoints = result.keypoints.data[0]

            # Detect hand to face
            threshold_distance = frame.shape[0] * 0.15  # 15% of frame height
            left_hand_to_face, right_hand_to_face = detect_hand_to_face(keypoints, threshold_distance)

            for i, keypoint in enumerate(keypoints):
                x, y, conf = keypoint
                if conf > 0.5:  # You can adjust this threshold
                    cv2.circle(frame, (int(x), int(y)), 5, (0, 255, 0), -1)
                    cv2.putText(frame, str(i), (int(x), int(y) - 10),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

            # Alert if hands are near face
            if left_hand_to_face or right_hand_to_face:
                alert_text = "GREETINGS DETECTED"
                cv2.putText(frame, alert_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                print(alert_text)

    return frame


cap = cv2.VideoCapture(0)

while cap.isOpened():
    success, frame = cap.read()
    if success:
        results = model.track(frame, classes=0, tracker="bytetrack.yaml")

        # Print pose points and their coordinates
        print_pose_points(results)

        # Draw pose points and add text labels
        frame = draw_pose_points(frame, results)

        cv2.imshow("YOLOv8 Tracking", frame)

        if cv2.waitKey(1) & 0xFF == ord("q"):
            break
    else:
        break

cap.release()
cv2.destroyAllWindows()