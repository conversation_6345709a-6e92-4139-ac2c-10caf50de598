import cv2
import numpy as np
from sort.sort import Sort


# Load YOLO
net = cv2.dnn.readNet(r"yo_3/yolov3_2000.weights", r"yo_3/yolov3.cfg")
layer_names = net.getLayerNames()
output_layers = [layer_names[i - 1] for i in net.getUnconnectedOutLayers()]

# Initialize SORT tracker
mot_tracker = Sort()

# Open webcam or RTSP stream
cap = cv2.VideoCapture("rtsp://admin:Admin123$@10.11.25.64:554/stream1")

while True:
    ret, frame = cap.read()
    if not ret:
        break

    height, width, channels = frame.shape

    # Detecting objects
    blob = cv2.dnn.blobFromImage(frame, 0.00392, (416, 416), (0, 0, 0), True, crop=False)
    net.setInput(blob)
    outs = net.forward(output_layers)

    # Information to be displayed
    class_ids = []
    confidences = []
    boxes = []

    for out in outs:
        for detection in out:
            scores = detection[5:]
            class_id = np.argmax(scores)
            confidence = scores[class_id]
            if confidence > 0.5 and class_id == 0:  # 0 is the class ID for 'person'
                # Object detected
                center_x = int(detection[0] * width)
                center_y = int(detection[1] * height)
                w = int(detection[2] * width)
                h = int(detection[3] * height)

                # Rectangle coordinates
                x = int(center_x - w / 2)
                y = int(center_y - h / 2)

                boxes.append([x, y, w, h])
                confidences.append(float(confidence))
                class_ids.append(class_id)

    indexes = cv2.dnn.NMSBoxes(boxes, confidences, 0.5, 0.4)

    # Prepare detections for SORT tracker: [x1, y1, x2, y2, score]
    detections = []
    if len(indexes) > 0:
        for i in indexes.flatten():
            x, y, w, h = boxes[i]
            x2, y2 = x + w, y + h  # Bottom-right corner
            detections.append([x, y, x2, y2, confidences[i]])

    detections = np.array(detections)

    # Update SORT tracker with the current detections
    tracked_objects = mot_tracker.update(detections)

    # Draw bounding boxes and labels for tracked objects
    for i in range(len(tracked_objects)):
        x1, y1, x2, y2, obj_id = tracked_objects[i]
        label = f"Person {int(obj_id)}"
        cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
        cv2.putText(frame, label, (int(x1), int(y1) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    # Display the resulting frame
    cv2.imshow("YOLO Person Detection with Tracking", frame)

    # Break the loop if 'q' is pressed
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# Release the capture and close windows
cap.release()
cv2.destroyAllWindows()
