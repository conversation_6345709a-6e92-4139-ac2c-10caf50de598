# Ultralytics YOLO 🚀, AGPL-3.0 license
# Optimized YOLO tracker settings for ByteTrack tracker

tracker_type: bytetrack  # tracker type, ['botsort', 'bytetrack']
track_high_thresh: 0.5  # threshold for the first association
track_low_thresh: 0.3   # increased threshold for second association to reduce tracking weak objects
new_track_thresh: 0.6   # threshold for init new track if the detection does not match any tracks
track_buffer: 15        # reduced buffer size for faster removal of inactive tracks
match_thresh: 0.6       # reduced IoU threshold for matching tracks, increases speed
fuse_score: False       # disabled score fusion to reduce complexity
# min_box_area: 10      # keeping this commented out as it's not used for now
