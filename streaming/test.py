import vlc
import time
import tkinter as tk
import math
import os
import subprocess
import re
from datetime import datetime
from threading import Thread
from tkinter import filedialog, messagebox

rtsp_urls = [
    "rtsp://admin:Admin123$@10.11.25.51:554/stream1"
]

# Create main "videos" folder
main_folder = "5_video"
os.makedirs(main_folder, exist_ok=True)

# Create subfolders for each stream;'
stream_folders = []
for url in rtsp_urls:
    folder_name = re.sub(r'[^\w\-_]', '_', url.split('/')[-2])
    folder_path = os.path.join(main_folder, folder_name)
    os.makedirs(folder_path, exist_ok=True)
    stream_folders.append(folder_path)

# Function to record streams using FFmpeg with re-encoding
def record_stream(url, output_folder):
    while True:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(output_folder, f"{timestamp}.mp4")

        ffmpeg_cmd = [
            "ffmpeg",
            "-rtsp_transport", "tcp",
            "-i", url,
            "-vf", "scale=640:360",
            "-r", "10",
            "-t", "00:01:00",
            "-c:v", "libx265",
            "-preset", "ultrafast",
            "-crf", "28",
            "-y",
            output_file
        ]

        subprocess.run(ffmpeg_cmd)

# Start recording processes in separate threads
recording_threads = []
for url, folder in zip(rtsp_urls, stream_folders):
    thread = Thread(target=record_stream, args=(url, folder))
    thread.start()
    recording_threads.append(thread)

# GUI setup
root = tk.Tk()
root.title("RTSP Streams")
root.attributes('-fullscreen', True)

screen_width = root.winfo_screenwidth()
screen_height = root.winfo_screenheight()

stream_count = len(rtsp_urls)
grid_size = math.ceil(math.sqrt(stream_count))
frame_width = screen_width // grid_size
frame_height = screen_height // grid_size

instances = []
players = []
rtsp_frames = []
full_screen_mode = None

button_width = int(screen_width * 0.01)
button_height = int(screen_height * 0.001)

def enter_full_screen(frame, canvas, player):
    global full_screen_mode
    full_screen_mode = (frame.grid_info(), canvas)
    for widget in root.winfo_children():
        widget.grid_remove()
    frame.grid(row=0, column=0, sticky="nsew")
    frame.config(width=screen_width, height=screen_height)
    canvas.config(width=screen_width, height=screen_height)
    player.set_hwnd(canvas.winfo_id())

    # Create a "Back to Grid" button as a small rectangular bar
    back_button = tk.Button(frame, text="Back to Grid", command=exit_full_screen, width=button_width,
                            height=button_height)
    back_button.place(relx=1.0, rely=0.0, anchor='ne')

def exit_full_screen():
    global full_screen_mode
    if full_screen_mode:
        original_grid_info, original_canvas = full_screen_mode
        frame = original_canvas.master
        frame.grid_forget()
        frame.grid(row=original_grid_info['row'], column=original_grid_info['column'])
        frame.config(width=frame_width, height=frame_height)
        original_canvas.config(width=frame_width, height=frame_height)
        player = [p for p in players if p.get_hwnd() == original_canvas.winfo_id()][0]
        player.set_hwnd(original_canvas.winfo_id())
        full_screen_mode = None

        for i, frame in enumerate(rtsp_frames):
            frame.grid(row=i // grid_size, column=i % grid_size)

        for widget in frame.winfo_children():
            if isinstance(widget, tk.Button) and widget.cget("text") == "Back to Grid":
                widget.destroy()

        for i, frame in enumerate(rtsp_frames):
            canvas = frame.winfo_children()[0]
            player = players[i]
            fs_button = tk.Button(frame, text="Full Screen", width=button_width, height=button_height,
                                  command=lambda f=frame, c=canvas, p=player: enter_full_screen(f, c, p))
            fs_button.place(relx=1.0, rely=0.0, anchor='ne')

def get_video_files(folder):
    video_files = []
    for root, dirs, files in os.walk(folder):
        for file in files:
            if file.endswith('.mp4'):
                video_files.append(os.path.join(root, file))
    return video_files

def play_video(video_path):
    playback_window = tk.Toplevel(root)
    playback_window.title("Video Playback")
    playback_window.geometry("800x600")

    canvas = tk.Canvas(playback_window, bg="black")
    canvas.pack(fill=tk.BOTH, expand=True)

    instance = vlc.Instance()
    player = instance.media_player_new()
    player.set_hwnd(canvas.winfo_id())

    media = instance.media_new(video_path)
    player.set_media(media)
    player.play()

    def on_closing_playback():
        player.stop()
        playback_window.destroy()

    playback_window.protocol("WM_DELETE_WINDOW", on_closing_playback)

def open_playback_window():
    playback_window = tk.Toplevel(root)
    playback_window.title("Video Playback")
    playback_window.geometry("400x300")

    video_listbox = tk.Listbox(playback_window, width=50)
    video_listbox.pack(pady=10)

    videos = get_video_files(main_folder)
    for video in videos:
        video_listbox.insert(tk.END, os.path.basename(video))

    def play_selected_video():
        selection = video_listbox.curselection()
        if selection:
            video_path = os.path.join(main_folder, video_listbox.get(selection[0]))
            play_video(video_path)
        else:
            messagebox.showwarning("No Selection", "Please select a video to play.")

    play_button = tk.Button(playback_window, text="Play", command=play_selected_video)
    play_button.pack(pady=5)

for i, url in enumerate(rtsp_urls):
    frame = tk.Frame(root, width=frame_width, height=frame_height)
    frame.grid(row=i // grid_size, column=i % grid_size)
    frame.pack_propagate(0)

    canvas = tk.Canvas(frame, bg="black", width=frame_width, height=frame_height)
    canvas.pack(fill=tk.BOTH, expand=True)

    instance = vlc.Instance()
    player = instance.media_player_new()
    player.set_hwnd(canvas.winfo_id())

    media = instance.media_new(url)
    player.set_media(media)
    player.play()

    fs_button = tk.Button(frame, text="Full Screen", width=button_width, height=button_height,
                          command=lambda f=frame, c=canvas, p=player: enter_full_screen(f, c, p))
    fs_button.place(relx=1.0, rely=0.0, anchor='ne')

    pb_button = tk.Button(frame, text="Playback", width=button_width, height=button_height,
                          command=open_playback_window)
    pb_button.place(relx=1.0, rely=0.05, anchor='ne')

    instances.append(instance)
    players.append(player)
    rtsp_frames.append(frame)

    time.sleep(0.5)

def on_closing():
    for player in players:
        player.stop()
    root.destroy()

root.protocol("WM_DELETE_WINDOW", on_closing)
root.mainloop()