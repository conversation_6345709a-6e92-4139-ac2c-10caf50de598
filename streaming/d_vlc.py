import vlc
import time
import tkinter as tk
import math

rtsp_urls = [
    "rtsp://admin:Admin123$@10.11.25.62:554/stream1",  # lobby
    "rtsp://admin:Admin123$@10.11.25.59:554/stream1",  # entrance
    "rtsp://admin:Admin123$@10.11.25.53:554/stream1",  # centre
]

root = tk.Tk()
root.title("RTSP Streams")
root.attributes('-fullscreen', True)

screen_width = root.winfo_screenwidth()
screen_height = root.winfo_screenheight()

stream_count = len(rtsp_urls)
grid_size = math.ceil(math.sqrt(stream_count))
frame_width = screen_width // grid_size
frame_height = screen_height // grid_size

instances = []
players = []
rtsp_frames = []
full_screen_mode = None

button_width = int(screen_width * 0.01)
button_height = int(screen_height * 0.001)


def enter_full_screen(frame, canvas, player):
    global full_screen_mode
    full_screen_mode = (frame.grid_info(), canvas)
    for widget in root.winfo_children():
        widget.grid_remove()
    frame.grid(row=0, column=0, sticky="nsew")
    frame.config(width=screen_width, height=screen_height)
    canvas.config(width=screen_width, height=screen_height)
    player.set_hwnd(canvas.winfo_id())

    # Create a "Back to Grid" button as a small rectangular bar
    back_button = tk.Button(frame, text="Back to Grid", command=exit_full_screen, width=button_width,
                            height=button_height)
    back_button.place(relx=1.0, rely=0.0, anchor='ne')


def exit_full_screen():
    global full_screen_mode
    if full_screen_mode:
        original_grid_info, original_canvas = full_screen_mode
        frame = original_canvas.master
        frame.grid_forget()
        frame.grid(row=original_grid_info['row'], column=original_grid_info['column'])
        frame.config(width=frame_width, height=frame_height)
        original_canvas.config(width=frame_width, height=frame_height)
        player = [p for p in players if p.get_hwnd() == original_canvas.winfo_id()][0]
        player.set_hwnd(original_canvas.winfo_id())
        full_screen_mode = None

        for i, frame in enumerate(rtsp_frames):
            frame.grid(row=i // grid_size, column=i % grid_size)

        for widget in frame.winfo_children():
            if isinstance(widget, tk.Button) and widget.cget("text") == "Back to Grid":
                widget.destroy()

        for i, frame in enumerate(rtsp_frames):
            canvas = frame.winfo_children()[0]
            player = players[i]
            fs_button = tk.Button(frame, text="Full Screen", width=button_width, height=button_height,
                                  command=lambda f=frame, c=canvas, p=player: enter_full_screen(f, c, p))
            fs_button.place(relx=1.0, rely=0.0, anchor='ne')


for i, url in enumerate(rtsp_urls):
    frame = tk.Frame(root, width=frame_width, height=frame_height)
    frame.grid(row=i // grid_size, column=i % grid_size)
    frame.pack_propagate(0)

    canvas = tk.Canvas(frame, bg="black", width=frame_width, height=frame_height)
    canvas.pack(fill=tk.BOTH, expand=True)

    instance = vlc.Instance()
    player = instance.media_player_new()
    player.set_hwnd(canvas.winfo_id())

    media = instance.media_new(url)
    player.set_media(media)
    player.play()

    fs_button = tk.Button(frame, text="Full Screen", width=button_width, height=button_height,
                          command=lambda f=frame, c=canvas, p=player: enter_full_screen(f, c, p))
    fs_button.place(relx=1.0, rely=0.0, anchor='ne')

    instances.append(instance)
    players.append(player)
    rtsp_frames.append(frame)

    time.sleep(0.5)

def on_closing():
    for player in players:
        player.stop()
    root.destroy()


root.protocol("WM_DELETE_WINDOW", on_closing)
root.mainloop()
