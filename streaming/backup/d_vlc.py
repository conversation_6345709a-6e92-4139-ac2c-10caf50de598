import vlc
import time
import tkinter as tk
import math


rtsp_urls = [
    "rtsp://admin:Admin123$@10.11.25.51:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.52:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.53:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.54:554/stream1",
    # "rtsp://admin:Admin123$@10.11.25.55:554/stream1",
    # "rtsp://admin:Admin123$@10.11.25.56:554/stream1",
    # "rtsp://admin:Admin123$@10.11.25.57:554/stream1",
    # "rtsp://admin:Admin123$@10.11.25.58:554/stream1",
    # "rtsp://admin:Admin123$@10.11.25.59:554/stream1",
    # "rtsp://admin:Admin123$@10.11.25.60:554/stream1",
    # "rtsp://admin:Admin123$@10.11.25.61:554/stream1",
    # "rtsp://admin:Admin123$@10.11.25.62:554/stream1",
    # "rtsp://admin:Admin123$@10.11.25.63:554/stream1",
    # "rtsp://admin:Admin123$@10.11.25.64:554/stream1",
    # "rtsp://admin:Admin123$@10.11.25.65:554/stream1",
]

# Set up the main window
root = tk.Tk()
root.title("RTSP Streams")
root.attributes('-fullscreen', True)

# Get screen dimensions
screen_width = root.winfo_screenwidth()
screen_height = root.winfo_screenheight()

# Calculate the number of rows and columns for the grid based on the number of streams
stream_count = len(rtsp_urls)
grid_size = math.ceil(math.sqrt(stream_count))
frame_width = screen_width // grid_size
frame_height = screen_height // grid_size

instances = []
players = []
full_screen_mode = None

def toggle_full_screen(event, frame, canvas, player):
    global full_screen_mode
    if full_screen_mode is None:
        # Enter full-screen mode
        full_screen_mode = (frame.grid_info(), canvas)
        for widget in root.winfo_children():
            widget.grid_remove()
        frame.grid(row=0, column=0, sticky="nsew")
        frame.config(width=screen_width, height=screen_height)
        canvas.config(width=screen_width, height=screen_height)
        player.set_hwnd(canvas.winfo_id())
    else:
        # Exit full-screen mode
        original_grid_info, original_canvas = full_screen_mode
        frame.grid_forget()
        frame.grid(row=original_grid_info['row'], column=original_grid_info['column'])
        frame.config(width=frame_width, height=frame_height)
        canvas.config(width=frame_width, height=frame_height)
        player.set_hwnd(canvas.winfo_id())
        full_screen_mode = None

for i, url in enumerate(rtsp_urls):
    # Create a frame for each video
    frame = tk.Frame(root, width=frame_width, height=frame_height)
    frame.grid(row=i // grid_size, column=i % grid_size)
    frame.pack_propagate(0)

    # Create a canvas within the frame
    canvas = tk.Canvas(frame, bg="black", width=frame_width, height=frame_height)
    canvas.pack(fill=tk.BOTH, expand=True)

    # Create VLC instance and media player
    instance = vlc.Instance()
    player = instance.media_player_new()
    player.set_hwnd(canvas.winfo_id())

    # Create media and play
    media = instance.media_new(url)
    player.set_media(media)
    player.play()

    # Bind click event to toggle full-screen mode
    canvas.bind("<Button-1>", lambda event, f=frame, c=canvas, p=player: toggle_full_screen(event, f, c, p))

    # Keep references to instances and players
    instances.append(instance)
    players.append(player)

    time.sleep(0.5)  # Give each instance time to start

def on_closing():
    for player in players:
        player.stop()
    root.destroy()

root.protocol("WM_DELETE_WINDOW", on_closing)
root.mainloop()
