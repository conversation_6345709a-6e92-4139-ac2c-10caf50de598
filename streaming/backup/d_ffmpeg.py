import subprocess
import tkinter as tk
from PIL import Image, ImageTk
import cv2
import numpy as np
import threading

rtsp_urls = [
    "rtsp://admin:Admin123$@10.11.25.51:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.52:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.53:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.54:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.55:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.56:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.57:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.58:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.59:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.60:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.61:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.62:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.63:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.64:554/stream1",
    "rtsp://admin:Admin123$@10.11.25.65:554/stream1",
]

# Set up the main window
root = tk.Tk()
root.title("RTSP Streams")
root.attributes('-fullscreen', True)

# Get screen dimensions
screen_width = root.winfo_screenwidth()
screen_height = root.winfo_screenheight()

# Create a canvas to display the video
canvas = tk.Canvas(root, width=screen_width, height=screen_height)
canvas.pack()

# FFmpeg command to create a 4x4 grid
ffmpeg_command = [
    "ffmpeg",
    *sum([["-i", url] for url in rtsp_urls], []),
    "-filter_complex",
    "nullsrc=size=1920x1080 [base];" +
    ";".join([f"[{i}:v] setpts=PTS-STARTPTS, scale=480x270 [v{i}]" for i in range(16)]) + ";" +
    ";".join([f"[base][v{i}] overlay=x={i%4*480}:y={i//4*270}" + (":shortest=1" if i == 15 else "") for i in range(16)]),
    "-c:v", "libx264", "-preset", "ultrafast", "-f", "rawvideo", "-pix_fmt", "bgr24", "pipe:"
]

# Function to read frames from FFmpeg
def read_frames():
    process = subprocess.Popen(ffmpeg_command, stdout=subprocess.PIPE, bufsize=10**8)
    while True:
        frame_size = 1920 * 1080 * 3  # 1920x1080 BGR
        in_bytes = process.stdout.read(frame_size)
        if not in_bytes:
            break
        yield np.frombuffer(in_bytes, np.uint8).reshape(1080, 1920, 3)

# Function to update the image on the canvas
def update_image():
    for frame in read_frames():
        image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        photo = ImageTk.PhotoImage(image)
        canvas.create_image(0, 0, anchor=tk.NW, image=photo)
        canvas.image = photo
        root.update()

# Start the video update in a separate thread
threading.Thread(target=update_image, daemon=True).start()

def on_closing():
    root.destroy()

root.protocol("WM_DELETE_WINDOW", on_closing)
root.mainloop()