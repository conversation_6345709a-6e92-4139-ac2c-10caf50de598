import cv2
import numpy as np
from datetime import datetime


# def put_text_centered(frame, text, font_scale, thickness, color):
#     try:
#         frame_height, frame_width = frame.shape[:2]
#         font = cv2.FONT_HERSHEY_SIMPLEX
#         text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
#
#         text_x = (frame_width - text_size[0]) // 2
#         text_y = (frame_height + text_size[1]) // 2
#
#         cv2.putText(frame, text, (text_x, text_y), font, font_scale, color, thickness, cv2.LINE_AA)
#     except Exception as e:
#         print(f"Error in put_text_centered: {e}")


def put_text_centered(frame, text, color):
    try:
        frame_height, frame_width = frame.shape[:2]
        print(f"{frame_height} : {frame_width}")

        # Set the desired width of the text to be 70% of the frame width
        desired_text_width = int(0.7 * frame_width)

        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1  # Initial guess for font scale

        # Adjust the font scale dynamically until the text width is close to the desired width
        text_size = cv2.getTextSize(text, font, font_scale, 1)[0]  # Set thickness to 1 initially for scaling
        while text_size[0] < desired_text_width:
            font_scale += 0.1  # Increment font scale
            text_size = cv2.getTextSize(text, font, font_scale, 1)[0]

        # Dynamically calculate thickness based on frame size (e.g., 1% of frame height)
        thickness = max(1, int(0.01 * frame_height))  # Ensure thickness is at least 1

        print(f" Font_Scale : {font_scale}")
        print(f" Thickness : {thickness}")
        # Calculate position to center the text
        text_x = (frame_width - text_size[0]) // 2
        text_y = (frame_height + text_size[1]) // 2

        # Put the text in the center of the frame
        cv2.putText(frame, text, (text_x, text_y), font, font_scale, color, thickness, cv2.LINE_AA)

    except Exception as e:
        print(e)


def detect_tampering(edge_threshold=50, complexity_threshold=0.1):
    cap = cv2.VideoCapture(0)

    # Read first frame
    ret, frame = cap.read()
    if not ret:
        print("Failed to grab frame")
        return

    # height, width = frame.shape[:2]
    # new_width, new_height = width // 2, height // 2
    #
    # # Resize the first frame
    # frame = cv2.resize(frame, (new_width, new_height))
    prev_edges = cv2.Canny(frame, 100, 200)
    prev_complexity = np.sum(prev_edges > 0) / prev_edges.size

    while True:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame")
            break

        # Resize the frame
        # frame = cv2.resize(frame, (new_width, new_height))

        # Detect edges
        edges = cv2.Canny(frame, 100, 200)

        # Calculate scene complexity (percentage of edge pixels)
        complexity = np.sum(edges > 0) / edges.size

        # Calculate the change in complexity
        complexity_change = abs(complexity - prev_complexity)

        if complexity_change > 0.0020:
            print(f"Complexity: {complexity:.4f}, Change: {complexity_change:.4f}")
            put_text_centered(frame, "Tampering Detected", (0, 0, 255))
            print("Tampering detected! (Sudden Change)")
        elif complexity_change == 0:
            put_text_centered(frame, "Screen Covered Completely", (0, 0, 255))
            print("Tampering detected! (Sudden Change)")

        # if complexity < complexity_threshold:
        #     # put_text_centered(frame, "Tampering Detected", (0, 0, 255))
        #     # print("Tampering detected! (Low Complexity)")
        #     pass
        # elif complexity_change > edge_threshold / 100:
        #     put_text_centered(frame, "Tampering Detected", (0, 0, 255))
        #     print("Tampering detected! (Sudden Change)")

        # frame = cv2.resize(frame, (700,500))
        # frame = cv2.resize(frame, (int(frame.shape[:2][1] / 2), int(frame.shape[:2][0] / 2)))

        cv2.imshow('Camera Feed', frame)
        # cv2.imshow('Edges', edges)

        # Update previous complexity
        prev_complexity = complexity

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()


detect_tampering()