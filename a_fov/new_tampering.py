import cv2
import numpy as np
from datetime import datetime


def put_text_centered(frame, text, font_scale, thickness, color):
    try:
        frame_height, frame_width = frame.shape[:2]
        font = cv2.FONT_HERSHEY_SIMPLEX
        text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]

        text_x = (frame_width - text_size[0]) // 2
        text_y = (frame_height + text_size[1]) // 2

        cv2.putText(frame, text, (text_x, text_y), font, font_scale, color, thickness, cv2.LINE_AA)
    except Exception as e:
        print(f"Error in put_text_centered: {e}")


def detect_tampering(threshold=0.1):
    cap = cv2.VideoCapture(0)
    black_screen_count = 0
    black_screen_limit = 3

    # Read first frame
    ret, frame = cap.read()
    if not ret:
        print("Failed to grab frame")
        return

    height, width = frame.shape[:2]
    new_width, new_height = width // 2, height // 2

    # Resize the first frame
    frame = cv2.resize(frame, (new_width, new_height))
    prev_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    while True:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame")
            break

        # Resize the frame
        frame = cv2.resize(frame, (new_width, new_height))

        # Convert frame to grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Calculate the difference between current and previous frame
        frame_diff = cv2.absdiff(gray, prev_frame)

        # Calculate the percentage of changed pixels
        change_percent = np.sum(frame_diff > 25) / frame_diff.size
        print(f"Change percent: {change_percent}")

        # Check if the change exceeds the threshold
        if change_percent == 0:
            black_screen_count += 1
            if black_screen_count > black_screen_limit:
                put_text_centered(frame, "BLACK SCREEN DETECTED", 1, 2, (0, 0, 255))
                print("Black Screen detected!")

        elif change_percent > threshold:
            put_text_centered(frame, "TAMPERING DETECTED", 1, 2, (0, 0, 255))
            print("Tampering detected!")
            black_screen_count = 0
        else:
            black_screen_count = 0

        cv2.imshow('Camera Feed', frame)

        # Update previous frame
        prev_frame = gray

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()


detect_tampering()