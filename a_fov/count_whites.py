import cv2

# Load the image
image = cv2.imread(r"E:\d_rtsp\a_fov\tampered_frames\frame_0271_a_651447_1.jpg")

# Convert to grayscale
gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# Apply a binary threshold to get a binary image
_, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)

# Find contours
contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

# Count the number of contours
num_white_shapes = len(contours)
print(f"Number of white shapes: {num_white_shapes}")

# Calculate the area for each white shape and add the count number to the image
for i, contour in enumerate(contours):
    area = cv2.contourArea(contour)
    print(f"Area of white shape {i + 1}: {area} pixels")

    # Get the bounding box for the contour
    x, y, w, h = cv2.boundingRect(contour)

    # Draw the bounding box
    cv2.rectangle(image, (x, y), (x + w, y + h), (0, 255, 0), 2)

    # Put the count number on the image
    cv2.putText(image, str(i + 1), (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)

# Display the image with bounding boxes and count numbers
cv2.imshow('White Shapes', image)
cv2.waitKey(0)
cv2.destroyAllWindows()
