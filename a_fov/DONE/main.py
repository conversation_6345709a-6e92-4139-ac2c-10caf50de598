import base64
import io
import subprocess
import platform
import queue
import cv2
import json
import dlib
import psutil
import datetime
import wmi
import ffmpeg
import re
import torch
import shutil
import requests
import face_recognition
from imageio.plugins import ffmpeg
from openvino.inference_engine import IECore
from flask import Flask, Response, request, render_template, send_from_directory, jsonify, session, abort, flash, redirect, url_for
from flask_cors import CORS, cross_origin
from flask_session import Session
from flask.views import MethodView
from PIL import Image
from subprocess import Popen, PIPE
from processor.__DatabaseLayer__ import DataAccess
# from models.Apparel_detection.yolov3_tf2.models import YoloV3
from surveillance_process.TressPass.area_intrusion import Trespass
from surveillance_process.Tampering.camera_tampering import MovementDetector
# from surveillance_process.ObjectDetection.object_identifier import objectdetection_main
from surveillance_process.MotionDetection.movement_detection import movement_detector_main
from surveillance_process.LeftObjectDetection.left_object_detection import left_object_detection_main
# from surveillance_process.Tripwire.area_intrusion import tripwire_main
from surveillance_process.PersonTripwire.linecrossingwithouttracker import Tripwire
from surveillance_process.Loitering.loitering_detection import loitering
from surveillance_process.PersonExit.person_exit_detection import person_exit
from surveillance_process.CarParkedRestricted.car_parked_restricted_area import no_parking_main
from surveillance_process.SmokeDetection.smoke import smoke_main
from surveillance_process.WrongWay_Direction_Person.wrong_way_person import wrongway_person_main
from surveillance_process.FireDetection.fireDetection import fire_detector_main
from surveillance_process.FaceRecognition.face_recognitions import face_identifier_main
from surveillance_process.FaceRecognition.face_recognitions import build_argparser, FrameProcessor
from surveillance_process.CarLicensePlateRecognition.licenseplate_detector import license_plate_detector_main
from surveillance_process.SocialDistance.social_distance_detector import social_distance_main
from surveillance_process.MissingObject.MissingObjectDetection import missing_object_detection_main
from surveillance_process.MissingObjectArea.MissingObjectArea import missing_object_detection_main_area
from surveillance_process.MaskDetection.maskdetection import mask_detection
from surveillance_process.VehicleWrongDirection.VehicleWrongDirectionDetection import vehicleWrongdirection_main
from surveillance_process.VehicleEnteredInRestrictedArea.Vehicle_area_intrusion import Vehicle_tresspass_main
from surveillance_process.VehicleTripwire.linecrossingwithouttracker import VehicleTripwire
# from surveillance_process.FaceRecognition.age_gender import Age_Gender_Detection
from surveillance_process.SignalViolation.main import signal_violation_main
from surveillance_process.wrong_dir_linecross.Wrongway_line_cross import wrong_dir_main
from surveillance_process.vehicleLineCrossingCount.vehicleLineCrossingCount import vehicleLineCrossingCount_dir_main
from surveillance_process.personLineCrossingCount.personLineCrossingCount import personlinecrosscount
from surveillance_process.objectdetectionnew.object_detection import run
from models.object_detection_new.models.common import DetectMultiBackend
from surveillance_process.objectdetectionnew.torch_utils import select_device
from surveillance_process.Rider_helmet_detection.Rider_Helmet_detection import object_detection
from surveillance_process.GasFlameDetection.gas_flame_detection import flame_detection
from torch.backends import cudnn
from threading import Thread
from multiprocessing import Queue
from processor import analytics_detector, third_party_api
from processor.analytics_search import SearchEngine
from processor.common import FolderView
from multiprocessing.pool import ThreadPool
from alerts.email.email_attachment import email_image_notification
from alerts.whats_app.whats_app_attachment import whats_image_notification
from collections import defaultdict
from faceswap import face_swap
import numpy as np
import coloredlogs
import logging
from processor.color_identifier import color_detection
from alerts.alarm_sounds.audioplaybackbg import *
import moviepy.editor as moviepy
from processor.entity_extraction import EntityExtractor
from video_summary import main
from functools import wraps
import warnings
from apscheduler.schedulers.background import BackgroundScheduler
warnings.filterwarnings(action='ignore')


def login_required(f):
    @wraps(f)
    def wrap(*args, **kwargs):
        if 'logged_in' in session:
            return f(*args, **kwargs)
        else:
            flash("You need to login first")
            return redirect(url_for('login'))

    return wrap


coloredlogs.install()
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'

DataAccess.db_details()
with open('config.json', 'r') as f:
    js = json.load(f)
    app_host = js['app']['host']
    app_port = js['app']['port']
    dat_back = js['app']['Av3ar_gviewer']
    if dat_back == "ON":
        Av3ar_gviewer = True
    else:
        Av3ar_gviewer = False

image_flag = None
elasticsearch_flag = None
Db_image_flag = None
email_flag = None
audio_enable_flag = None
time_date_flag = None
whatsapp_flag = None
face_image_name = None
trigger = False
trigger1 = False
face_image_name = None
startTime = None
video_cap_details = []

# region system information
computer = wmi.WMI()
computer_info = computer.Win32_ComputerSystem()[0]
os_info = computer.Win32_OperatingSystem()[0]
proc_info = computer.Win32_Processor()[0]
gpu_info = computer.Win32_VideoController()[0]

os_name = os_info.Name.encode('utf-8').split(b'|')[0]
os_version = ' '.join([os_info.Version, os_info.BuildNumber])
system_ram = float(os_info.TotalVisibleMemorySize) / 1048576  # KB to GB
trace.info("==================== getting system information ===============================")
trace.info('OS Name: {0}'.format(os_name))
trace.info('OS Version: {0}'.format(os_version))
trace.info('CPU: {0}'.format(proc_info.Name))
trace.info('RAM: {0} GB'.format(system_ram))
trace.info('Graphics Card: {0}'.format(gpu_info.Name))
if str(gpu_info.Name).__contains__('Intel'):
    Gpu = True
else:
    Gpu = False


# endregion


# region loading models
# region tresspass
# Load Tresspass model
class boundaryLine:
    def __init__(self, line=(0, 0, 0, 0)):
        self.p0 = (line[0], line[1])
        self.p1 = (line[2], line[3])
        self.color = (0, 255, 255)
        self.lineThinkness = 4
        self.textColor = (0, 255, 255)
        self.textSize = 4
        self.textThinkness = 2
        self.count1 = 0
        self.count2 = 0


global boundaryLines, model_det, model_reid

model_det = 'models/TressPass_model/{0}/FP16/{0}'.format('pedestrian-detection-adas-0002')
model_reid = 'models/TressPass_model/{0}/FP16/{0}'.format('person-reidentification-retail-0277')

# boundary lines
boundaryLines = [
    boundaryLine([300, 40, 20, 400]),
    boundaryLine([440, 40, 700, 400])
]

_N, _C, _H, _W = 0, 1, 2, 3
trace.info('================Loading Models for Tresspass detection========================')
ie = IECore()
gpu_config = {'CACHE_DIR': './cache'}
# Prep for face/pedestrian detection
net_det = ie.read_network(model_det + '.xml', model_det + '.bin')  # faceswap_model=pedestrian-detection-adas-0002
input_name_det = next(iter(net_det.input_info))  # Input blob name "data"
input_shape_det = net_det.input_info[input_name_det].tensor_desc.dims  # [1,3,384,672]
out_name_det = next(iter(net_det.outputs))  # Output blob name "detection_out"
out_shape_det = net_det.outputs[out_name_det].shape  # [ image_id, label, conf, xmin, ymin, xmax, ymax ]
print('Loading', model_det, '...', end='', flush=True)
if Gpu:
    exec_net_det = ie.load_network(net_det, 'GPU', gpu_config)
else:
    exec_net_det = ie.load_network(net_det, 'CPU', num_requests=10)

print('Completed')

# Preparation for face/pedestrian re-identification
net_reid = ie.read_network(model_reid + ".xml", model_reid + ".bin")  # person-reidentificaton-retail-0079
input_name_reid = next(iter(net_reid.input_info))  # Input blob name "data"
input_shape_reid = net_reid.input_info[input_name_reid].tensor_desc.dims  # [1,3,160,64]
out_name_reid = next(iter(net_reid.outputs))  # Output blob name "embd/dim_red/conv"
out_shape_reid = net_reid.outputs[out_name_reid].shape  # [1,256,1,1]
print('Loading', model_reid, '...', end='', flush=True)
if Gpu:
    exec_net_reid = ie.load_network(net_reid, 'GPU', gpu_config)
else:
    exec_net_reid = ie.load_network(net_reid, 'CPU', num_requests=10)
print('Completed')

# endregion
# region ObjectDetection
# Load ObjectDetection model
trace.info('===============Loading Models for Object detection=======================')
obj_model_det = root + '/models/ObjectDetection_model/yolo-v4-tiny-tf/FP16/yolo-v4-tiny-tf.xml'
obj_bin = root + '/models/ObjectDetection_model/yolo-v4-tiny-tf/FP16/yolo-v4-tiny-tf.bin'
obj_text = root + '/models/ObjectDetection_model/yolo-v4-tiny-tf/coco_80cl.txt'
obj_ie = IECore()
obj_gpu_config = {'CACHE_DIR': './cache'}
# Prep for face/pedestrian detection
obj_net_det = obj_ie.read_network(obj_model_det, obj_bin)
print('Loading', obj_model_det, '...', end='', flush=True)
if Gpu:
    obj_exec_net_det = obj_ie.load_network(net_det, 'GPU', gpu_config)
else:
    obj_exec_net_det = obj_ie.load_network(obj_net_det, 'CPU')

print('Completed')
input_blob = next(iter(obj_net_det.input_info))
obj_net_det.batch_size = 1
# Read and pre-process input images
obj_n, obj_c, obj_h, obj_w = obj_net_det.input_info[input_blob].input_data.shape

if obj_text:
    with open(obj_text, 'r') as f:
        labels_map = [x.strip() for x in f]
        # trace.info(labels_map)
else:
    labels_map = None

if Gpu:
    obj_exec_net = obj_ie.load_network(network=obj_net_det, num_requests=2, device_name="GPU")
else:
    obj_exec_net = obj_ie.load_network(network=obj_net_det, num_requests=2, device_name="CPU")

# endregion
# region loitering
# Load loitering model
trace.info('====================Loading Models for loitering detection================')
loitering_model_det = 'pedestrian-detection-adas-0002'
loitering_model_reid = 'person-reidentification-retail-0277'
loitering_model_det = 'models/Loitering_model/{0}/FP16/{0}'.format(loitering_model_det)
loitering_model_reid = 'models/Loitering_model/{0}/FP16/{0}'.format(loitering_model_reid)

loitering_N, loitering_C, loitering_H, loitering_W = 0, 1, 2, 3
loitering_ie = IECore()
gpu_config = {'CACHE_DIR': './cache'}
# Prep for face/pedestrian detection
loitering_net_det = loitering_ie.read_network(loitering_model_det + '.xml', loitering_model_det + '.bin')  # faceswap_model=pedestrian-detection-adas-0002
loitering_input_name_det = next(iter(loitering_net_det.input_info))  # Input blob name "data"
loitering_input_shape_det = loitering_net_det.input_info[loitering_input_name_det].tensor_desc.dims  # [1,3,384,672]
loitering_out_name_det = next(iter(loitering_net_det.outputs))  # Output blob name "detection_out"
loitering_out_shape_det = loitering_net_det.outputs[loitering_out_name_det].shape  # [ image_id, label, conf, xmin, ymin, xmax, ymax ]
print('Loading', loitering_model_det, '...', end='', flush=True)
if Gpu:
    loitering_exec_net_det = loitering_ie.load_network(loitering_net_det, 'GPU', gpu_config)
else:
    loitering_exec_net_det = loitering_ie.load_network(loitering_net_det, 'CPU')
print('Completed')

# Preparation for face/pedestrian re-identification
loitering_net_reid = loitering_ie.read_network(loitering_model_reid + ".xml", loitering_model_reid + ".bin")  # person-reidentificaton-retail-0079
loitering_input_name_reid = next(iter(loitering_net_reid.input_info))  # Input blob name "data"
loitering_input_shape_reid = loitering_net_reid.input_info[loitering_input_name_reid].tensor_desc.dims  # [1,3,160,64]
loitering_out_name_reid = next(iter(loitering_net_reid.outputs))  # Output blob name "embd/dim_red/conv"
loitering_out_shape_reid = loitering_net_reid.outputs[loitering_out_name_reid].shape  # [1,256,1,1]
print('Loading', loitering_model_reid, '...', end='', flush=True)
if Gpu:
    loitering_exec_net_reid = loitering_ie.load_network(loitering_net_reid, 'GPU', gpu_config)
else:
    loitering_exec_net_reid = loitering_ie.load_network(loitering_net_reid, 'CPU')
print('Completed')
# endregion
# region person exist
# Load person exist model
logging.info(" ================loading model for Person exist======================== ")
person_exist_model_det = 'pedestrian-detection-adas-0002'
person_exist_model_reid = 'person-reidentification-retail-0277'
person_exist_model_det = 'models/PersonExit_model/{0}/FP16/{0}'.format(person_exist_model_det)
person_exist_model_reid = 'models/PersonExit_model/{0}/FP16/{0}'.format(person_exist_model_reid)

person_exist_N, person_exist_C, person_exist_H, person_exist_W = 0, 1, 2, 3
person_exist_ie = IECore()
gpu_config = {'CACHE_DIR': './cache'}
# Prep for face/pedestrian detection
person_exist_net_det = person_exist_ie.read_network(person_exist_model_det + '.xml', person_exist_model_det + '.bin')  # faceswap_model=pedestrian-detection-adas-0002
person_exist_input_name_det = next(iter(person_exist_net_det.input_info))  # Input blob name "data"
person_exist_input_shape_det = person_exist_net_det.input_info[person_exist_input_name_det].tensor_desc.dims  # [1,3,384,672]
person_exist_out_name_det = next(iter(person_exist_net_det.outputs))  # Output blob name "detection_out"
person_exist_out_shape_det = person_exist_net_det.outputs[person_exist_out_name_det].shape  # [ image_id, label, conf, xmin, ymin, xmax, ymax ]
print('Loading', person_exist_model_det, '...', end='', flush=True)
if Gpu:
    person_exist_exec_net_det = person_exist_ie.load_network(person_exist_net_det, 'GPU', gpu_config)
else:
    person_exist_exec_net_det = person_exist_ie.load_network(person_exist_net_det, 'CPU')
print('Completed')

# Preparation for face/pedestrian re-identification
person_exist_net_reid = person_exist_ie.read_network(person_exist_model_reid + ".xml", person_exist_model_reid + ".bin")  # person-reidentificaton-retail-0079
person_exist_input_name_reid = next(iter(person_exist_net_reid.input_info))  # Input blob name "data"
person_exist_input_shape_reid = person_exist_net_reid.input_info[person_exist_input_name_reid].tensor_desc.dims  # [1,3,160,64]
person_exist_out_name_reid = next(iter(person_exist_net_reid.outputs))  # Output blob name "embd/dim_red/conv"
person_exist_out_shape_reid = person_exist_net_reid.outputs[person_exist_out_name_reid].shape  # [1,256,1,1]
print('Loading', person_exist_model_reid, '...', end='', flush=True)
if Gpu:
    person_exist_exec_net_reid = person_exist_ie.load_network(person_exist_net_reid, 'GPU', gpu_config)
else:
    person_exist_exec_net_reid = person_exist_ie.load_network(person_exist_net_reid, 'CPU')
print('Completed')
# endregion
# region No parking
# Load No parking model
logging.info(" ================loading model for No parking ======================== ")
no_parking_model_det = 'vehicle-detection-0200'
no_parking_model_reid = 'person-reidentification-retail-0277'
no_parking_model_det = 'models/NoParking_model/{0}/FP16/{0}'.format(no_parking_model_det)
no_parking_model_reid = 'models/NoParking_model/{0}/FP16/{0}'.format(no_parking_model_reid)

no_parking_N, no_parking_C, no_parking_H, no_parking_W = 0, 1, 2, 3
no_parking_ie = IECore()
gpu_config = {'CACHE_DIR': './cache'}
# Prep for face/pedestrian detection
no_parking_net_det = no_parking_ie.read_network(no_parking_model_det + '.xml', no_parking_model_det + '.bin')  # faceswap_model=pedestrian-detection-adas-0002
no_parking_input_name_det = next(iter(no_parking_net_det.input_info))  # Input blob name "data"
no_parking_input_shape_det = no_parking_net_det.input_info[no_parking_input_name_det].tensor_desc.dims  # [1,3,384,672]
no_parking_out_name_det = next(iter(no_parking_net_det.outputs))  # Output blob name "detection_out"
no_parking_out_shape_det = no_parking_net_det.outputs[no_parking_out_name_det].shape  # [ image_id, label, conf, xmin, ymin, xmax, ymax ]
print('Loading', no_parking_model_det, '...', end='', flush=True)
if Gpu:
    no_parking_exec_net_det = no_parking_ie.load_network(no_parking_net_det, 'GPU', gpu_config)
else:
    no_parking_exec_net_det = no_parking_ie.load_network(no_parking_net_det, 'CPU')
print('Completed')

# Preparation for face/pedestrian re-identification
no_parking_net_reid = no_parking_ie.read_network(no_parking_model_reid + ".xml", no_parking_model_reid + ".bin")  # person-reidentificaton-retail-0079
no_parking_input_name_reid = next(iter(no_parking_net_reid.input_info))  # Input blob name "data"
no_parking_input_shape_reid = no_parking_net_reid.input_info[no_parking_input_name_reid].tensor_desc.dims  # [1,3,160,64]
no_parking_out_name_reid = next(iter(no_parking_net_reid.outputs))  # Output blob name "embd/dim_red/conv"
no_parking_out_shape_reid = no_parking_net_reid.outputs[no_parking_out_name_reid].shape  # [1,256,1,1]
print('Loading', no_parking_model_reid, '...', end='', flush=True)
if Gpu:
    no_parking_exec_net_reid = no_parking_ie.load_network(no_parking_net_reid, 'GPU', gpu_config)
else:
    no_parking_exec_net_reid = no_parking_ie.load_network(no_parking_net_reid, 'CPU')
print('Completed')
# endregion
# region smoke
# load smoke model
smoke_model_det = 'models/Smoke_model/model_final.pth'
smoke_model = torch.load(smoke_model_det, map_location=torch.device('cpu'))
smoke_class_names = ['Fire', 'Neutral', 'Smoke']
# endregion
# region fire
# load fire model
fire_cascade_model = cv2.CascadeClassifier('models/FireDetection_model/fire_detection.xml')
# endregion
# region face detection
# load face detection model
face_detector_model = 'models/FaceRecognition_model/face-detection-adas-0001/FP16/face-detection-adas-0001.xml'
face_recognition_model = 'models/FaceRecognition_model/landmarks-regression-retail-0009/FP16/landmarks-regression-retail-0009.xml'
face_validator_model = 'models/FaceRecognition_model/face-reidentification-retail-0095/FP16/face-reidentification-retail-0095.xml'
face_trained_images = 'models/FaceRecognition_model/Images'
age_gender_model = 'models/FaceRecognition_model/age-gender-recognition-retail-0013/FP16/age-gender-recognition-retail-0013.xml'
face_args = build_argparser(None, face_detector_model, face_recognition_model, face_validator_model, face_trained_images, age_gender_model).parse_args()
face_frame_processor = FrameProcessor(face_args)
# endregion
# region license plate detection
# load license plate detection model
license_plate_detector = cv2.CascadeClassifier(cv2.data.haarcascades + "haarcascade_russian_plate_number.xml")
# endregion
# region signal violation
# signal violation model
signal_violation_cascade = cv2.CascadeClassifier('models/SignalViolation_model/cars.xml')
signal_violation_net = cv2.dnn.readNetFromCaffe('models/SignalViolation_model/MobileNetSSD_deploy.prototxt.txt', 'models/SignalViolation_model/MobileNetSSD_deploy.caffemodel')
signal_violation_class = ["background", "aeroplane", "bicycle", "bird", "boat", "bottle", "bus", "car", "cat", "chair", "cow", "diningtable", "dog", "horse", "motorbike", "person", "pottedplant", "sheep",	"sofa", "train", "tvmonitor"]
signal_violation_colors = np.random.uniform(0, 255, size=(len(signal_violation_class), 3))
# endregion
# region Apparel_detection
# Load Apparel_detection
# fashion_model = YoloV3(classes=13)
# fashion_model.load_weights('models/Apparel_detection/weights/built_model/deepfashion2_yolov3')
# endregion
# region Vehicle Wrong direction
# load Vehicle Wrong direction
logging.info(" ================loading VehicleWrongDirection ======================== ")
vehicle_wrong_direction_model_det = "vehicle-detection-0200"
vehicle_wrong_direction_model_reid = "person-reidentification-retail-0277"
vehicle_wrong_direction_model_det = 'models/VehicleWrongDirection/{0}/FP16/{0}'.format(vehicle_wrong_direction_model_det)
vehicle_wrong_direction_model_reid = 'models/VehicleWrongDirection/{0}/FP16/{0}'.format(vehicle_wrong_direction_model_reid)
vehicleWrongdirection_N, vehicleWrongdirection_C, vehicleWrongdirection_H, vehicleWrongdirection_W = 0, 1, 2, 3
vehicleWrongdirection_ie = IECore()
gpu_config = {'CACHE_DIR': './cache'}
vehicleWrongdirection_net_det = vehicleWrongdirection_ie.read_network(vehicle_wrong_direction_model_det + '.xml', vehicle_wrong_direction_model_det + '.bin')  # VehicleWrongDirection
vehicleWrongdirection__input_name_det = next(iter(vehicleWrongdirection_net_det.input_info))  # Input blob name "data"
vehicleWrongdirection_input_shape_det = vehicleWrongdirection_net_det.input_info[vehicleWrongdirection__input_name_det].tensor_desc.dims  # [1,3,384,672]
vehicleWrongdirection_out_name_det = next(iter(vehicleWrongdirection_net_det.outputs))  # Output blob name "detection_out"
vehicleWrongdirection_out_shape_det = vehicleWrongdirection_net_det.outputs[vehicleWrongdirection_out_name_det].shape  # [ image_id, label, conf, xmin, ymin, xmax, ymax ]
print('Loading', vehicle_wrong_direction_model_det, '...', end='', flush=True)
if Gpu:
    vehicleWrongdirection_exec_net_det = vehicleWrongdirection_ie.load_network(vehicleWrongdirection_net_det, 'GPU', gpu_config)
else:
    vehicleWrongdirection_exec_net_det = vehicleWrongdirection_ie.load_network(vehicleWrongdirection_net_det, 'CPU')
print('Completed')
# Preparation for VehicleWrongDirection re-identification
vehicleWrongdirection_net_reid = vehicleWrongdirection_ie.read_network(vehicle_wrong_direction_model_reid + '.xml', vehicle_wrong_direction_model_reid + '.bin')  # VehicleWrongDirection
vehicleWrongdirection_input_name_reid = next(iter(vehicleWrongdirection_net_reid.input_info))  # Input blob name "data"
vehicleWrongdirection_input_shape_reid = vehicleWrongdirection_net_reid.input_info[vehicleWrongdirection_input_name_reid].tensor_desc.dims  # [1,3,384,672]
vehicleWrongdirection_out_name_reid = next(iter(vehicleWrongdirection_net_reid.outputs))  # Output blob name "detection_out"
vehicleWrongdirection_out_shape_reid = vehicleWrongdirection_net_reid.outputs[vehicleWrongdirection_out_name_reid].shape  # [ image_id, label, conf, xmin, ymin, xmax, ymax ]
print('Loading', vehicle_wrong_direction_model_reid, '...', end='', flush=True)
if Gpu:
    vehicleWrongdirection_exec_net_reid = vehicleWrongdirection_ie.load_network(vehicleWrongdirection_net_reid, 'GPU', gpu_config)
else:
    vehicleWrongdirection_exec_net_reid = vehicleWrongdirection_ie.load_network(vehicleWrongdirection_net_reid, 'CPU')
print('Completed')
# endregion


# region Vehicle Tresspass
# Load Vehicle Tresspass model
class boundaryLine:
    def __init__(self, line=(0, 0, 0, 0)):
        self.p0 = (line[0], line[1])
        self.p1 = (line[2], line[3])
        self.color = (0, 255, 255)
        self.lineThinkness = 4
        self.textColor = (0, 255, 255)
        self.textSize = 4
        self.textThinkness = 2
        self.count1 = 0
        self.count2 = 0


vehicle_tresspass_model_det = 'vehicle-detection-0200'
vehicle_tresspass_model_reid = 'person-reidentification-retail-0277'
vehicle_tresspass_model_det = 'models/VehicleEnteredInRestrictedArea/{0}/FP16/{0}'.format(vehicle_tresspass_model_det)
vehicle_tresspass_model_reid = 'models/VehicleEnteredInRestrictedArea/{0}/FP16/{0}'.format(vehicle_tresspass_model_reid)

_N, _C, _H, _W = 0, 1, 2, 3
trace.info('================Loading Models for Tresspass detection========================')
ie = IECore()
gpu_config = {'CACHE_DIR': './cache'}
# Prep for face/pedestrian detection
vehicle_tresspass_net_det = ie.read_network(vehicle_tresspass_model_det + '.xml', vehicle_tresspass_model_det + '.bin')  # faceswap_model=pedestrian-detection-adas-0002
vehicle_tresspass_input_name_det = next(iter(vehicle_tresspass_net_det.input_info))  # Input blob name "data"
vehicle_tresspass_input_shape_det = vehicle_tresspass_net_det.input_info[vehicle_tresspass_input_name_det].tensor_desc.dims  # [1,3,384,672]
vehicle_tresspass_out_name_det = next(iter(vehicle_tresspass_net_det.outputs))  # Output blob name "detection_out"
vehicle_tresspass_out_shape_det = vehicle_tresspass_net_det.outputs[vehicle_tresspass_out_name_det].shape  # [ image_id, label, conf, xmin, ymin, xmax, ymax ]
print('Loading', model_det, '...', end='', flush=True)
if Gpu:
    vehicle_tresspass_exec_net_det = ie.load_network(vehicle_tresspass_net_det, 'GPU', gpu_config)
else:
    vehicle_tresspass_exec_net_det = ie.load_network(vehicle_tresspass_net_det, 'CPU', num_requests=10)

print('Completed')
# Preparation for face/pedestrian re-identification
vehicleTresspass_net_reid = ie.read_network(vehicle_tresspass_model_reid + ".xml", vehicle_tresspass_model_reid + ".bin")  # person-reidentificaton-retail-0079
vehicleTresspass_input_name_reid = next(iter(vehicleTresspass_net_reid.input_info))  # Input blob name "data"
vehicleTresspass_input_shape_reid = vehicleTresspass_net_reid.input_info[vehicleTresspass_input_name_reid].tensor_desc.dims  # [1,3,160,64]
vehicleTresspass_out_name_reid = next(iter(vehicleTresspass_net_reid.outputs))  # Output blob name "embd/dim_red/conv"
vehicleTresspass_out_shape_reid = vehicleTresspass_net_reid.outputs[vehicleTresspass_out_name_reid].shape  # [1,256,1,1]
print('Loading', model_reid, '...', end='', flush=True)
if Gpu:
    vehicleTresspass_exec_net_reid = ie.load_network(vehicleTresspass_net_reid, 'GPU', gpu_config)
else:
    vehicleTresspass_exec_net_reid = ie.load_network(vehicleTresspass_net_reid, 'CPU', num_requests=10)
print('Completed')
# endregion
# region object detection with yolov5m
# load model for object detection with yolov5m pytroch
trace.info('===============Loading Models for object_detection_new=======================')
obj_model_det1 = root+'/models/object_detection_new/weights/v61_yolov5m.pt'
obj_text = root+'/models/object_detection_new/coco128.yaml'
# checkpoint=torch.load(obj_model_det1,map_location="cpu")
device = ''
# hide confidences
half = False
device = select_device(device)
model = DetectMultiBackend(obj_model_det1, device=device, dnn=False, data=obj_text)
stride, names, pt, jit, onnx, engine = model.stride, model.names, model.pt, model.jit, model.onnx, model.engine
half &= (pt or jit or onnx or engine) and device.type != 'cpu'
if pt or jit:
    model.model.half() if half else model.model.float()
# Dataloader
cudnn.benchmark = True
# endregion
# endregion

app = Flask(__name__, static_url_path='', static_folder='web/static', template_folder='web/templates',
            instance_path="/instance")
app.secret_key = b'!9m@S-dThyIlW[pHQbN^'
CORS(app, support_credentials=True)
root = os.getcwd()

SESSION_TYPE = 'filesystem'
app.config.from_object(__name__)
Session(app)

global os_env, video_queue
os_env = platform.system()
trace.info(os_env)
trace.info("=================== completed on getting system information =========================")

pool = ThreadPool(processes=100)
openvino_pool = ThreadPool(processes=1)
live_pool = ThreadPool(processes=100)
database_pool = ThreadPool(processes=100)
object_count = ThreadPool(processes=100)
whatsapp_pool = ThreadPool(processes=100)
if os_env.lower() == 'linux':
    import gi
    gi.require_version("Gst", "1.0")
    from gi.repository import Gst, GLib
    Gst.init()
    main_loop = GLib.MainLoop()
    thread = Thread(target=main_loop.run)
    thread.start()

# video_queue = queue.Queue()
db_res = None


class GstreamerProcess:
    def __init__(self):
        # self.windows_cmd = ' ffmpeg -rtsp_transport tcp -i {} -c:v copy -hls_time 5 -hls_segment_filename {}/segment_%03d.ts -hls_list_size 0 {}/playlist.m3u8'
        # self.windows_cmd = 'ffmpeg -rtsp_transport tcp -i {} -c:v libx264 -vf "scale=480:640" -c:a copy -hls_time 5 -hls_segment_filename {}/segment_%03d.ts -hls_list_size 0 {}/playlist.m3u8'
        self.windows_cmd = 'gst-launch-1.0 -v rtspsrc location={} ! rtph264depay ! avdec_h264 ! clockoverlay ! videoconvert ! videoscale ! video/x-raw,width=640, height=360 ! x264enc bitrate=1000 ! video/x-h264,profile=\"high\" ! mpegtsmux ! hlssink max-files=10 location="{}/segment_%05d.webm" playlist-location="{}/playlist.m3u8" target-duration=10'
        self.linux_cmd = 'rtspsrc location={} ! rtph264depay ! avdec_h264 ! clockoverlay ! videoconvert ! videoscale ! video/x-raw,width=640, height=360 ! x264enc bitrate=1000 ! video/x-h264,profile=\"high\" ! mpegtsmux ! hlssink  max-files=20 location="{}/segment_%05d.webm" playlist-location="{}/playlist.m3u8" target-duration=5'
        self.camera_json = root + "/cam_details.json"

    def gstreamer_new_pipeline(self, cam_url, path):
        """

        ex: cam_url rtsp url , path : "data/video/3/camera1"

        """
        try:
            trace.info('Initiating gstreamer pipeline')
            if os_env.lower() == "windows":
                # cmd = self.windows_cmd.format(cam_url, path)
                cmd = self.windows_cmd.format(cam_url, path, path)
                # cmd = 'gst-launch-1.0 -v rtspsrc location={} ! rtph264depay ! avdec_h264 ! clockoverlay ! videoconvert ! videoscale ! video/x-raw,width=640, height=360 ! x264enc ! video/x-h264,profile=\"high\" ! mpegtsmux ! hlssink max-files=0 location="{}/segment_%05d.webm" playlist-location="{}/playlist.m3u8" target-duration=5'.format(cam_url, path, path)
                # cmd = 'gst-launch-1.0 -v rtspsrc location={} ! rtph264depay ! avdec_h264 ! clockoverlay ! videoconvert ! x264enc ! mpegtsmux ! hlssink max-files=0 location="{}/segment_%05d.webm" playlist-location="{}/playlist.m3u8" target-duration=5'.format(cam_url, path, path)
                pro = subprocess.Popen(cmd, stdout=subprocess.PIPE, shell=True)
                process_id = pro.pid
            elif os_env.lower() == "linux":
                # pipeline_string = 'rtspsrc location={} ! rtph264depay ! avdec_h264 ! clockoverlay ! videoconvert ! videoscale ! video/x-raw,width=640, height=360 ! x264enc ! video/x-h264,profile=\"high\" ! mpegtsmux ! hlssink  max-files=20 location="{}/segment_%05d.webm" playlist-location="{}/playlist.m3u8" target-duration=5'.format(
                pipeline_string = self.linux_cmd.format(cam_url, path, path)
                pipeline = Gst.parse_launch(pipeline_string)
                pipeline.set_state(Gst.State.PLAYING)
                process_id = 12345
            else:
                print("Please check OS and update Environment")
                process_id = 0
            return process_id
        except Exception as e:
            exc.exception('Error occurred while gstreamer pipeline {}'.format(e))
            print(e)

    def reload_gstreamer(self):
        """
        creating and writing the json file
        """
        try:
            trace.warning('Starting to Reload gstreamer ')
            with open(self.camera_json) as json_file:
                json_ob = json.load(json_file)
            count = 0
            evaluate = []
            for i, stream_details in enumerate(json_ob):
                for key, value in stream_details.items():
                    if key == 'process_id':
                        try:
                            p = psutil.Process(stream_details['process_id'])
                            p.terminate()
                        except BaseException:
                            pass
                    else:
                        if key != 'status':
                            FolderView(key).removefolder()
                            FolderView(key).createfolder()
                            re_process_id = GstreamerProcess().gstreamer_new_pipeline(value, key)
                            cap = cv2.VideoCapture(value)
                            ret, frame = cap.read()
                            evaluate.append({"process_id": re_process_id, key: value, "status": ret})
                count = count + 1
            with open(self.camera_json, "w") as json_file:
                json.dump(evaluate, json_file, ensure_ascii=False, indent=4)
        except Exception as e:
            exc.exception('Error while reloading gstreamer {}'.format(e))


def reset_stream_details():
    """
    Gets user's stream details
    """
    try:
        trace.info('Getting users streaming details')
        global db_res
        db_res = DataAccess.get_user_stream_details()
    except Exception as e:
        exc.exception('Error while streaming users details {}'.format(e))
        print(e)


videocapture1_list = []
process_list = []


# bufferless VideoCapture
class WebcamStream:
    def __init__(self, stream_id):
        # self.q = queue.Queue()
        self.stream_id = stream_id  # default is 0 for primary camera
        self.cam_flag = False
        self._kill = threading.Event()
        self._interval = 0.0001
        if stream_id == str(0):
            self.stream_id = int(stream_id)
        # opening video capture stream
        self.vcap = cv2.VideoCapture(self.stream_id)
        if self.vcap.isOpened() is False:
            print("[Exiting]: Error accessing webcam stream.")
            # exit(0)
            self.vcap = cv2.VideoCapture(root + '/test/bot_loader.gif')
            self.cam_flag = True
        fps_input_stream = int(self.vcap.get(5))
        print("FPS of webcam hardware/input stream: {}".format(fps_input_stream))

        # reading a single frame from vcap stream for initializing
        self.grabbed, self.frame = self.vcap.read()
        if self.grabbed is False:
            print('[Exiting] No more frames to read')
            # exit(0)

        # self.stopped is set to False when frames are being read from self.vcap stream
        self.stopped = True
        # reference to the thread for reading next available frame from input stream
        self.t = Thread(target=self.update, args=())
        self.t.name = str(self.stream_id)
        process_list.append(self.t.name)
        self.t.daemon = True  # daemon threads keep running in the background while the program is executing

    # method for starting the thread for grabbing next available frame in input stream
    def start(self):
        self.stopped = False
        self.t.start()

        # method for reading next frame

    def update(self):
        while True:
            is_killed = self._kill.wait(self._interval)
            if is_killed:
                break
            if self.stopped is True:
                break
            # try:  #TODO
            if self.vcap.isOpened():
                self.grabbed, self.frame = self.vcap.read()
            # except :
                # self.grabbed = False
            if self.grabbed is False:
                print('[Exiting] No more frames to read')
                self.stopped = True
                break
        self.vcap.release()

    # method for returning latest read frame
    def read(self):
        return self.cam_flag, self.frame

    # method called to stop reading frames
    def stop(self):
        self.stopped = True

    def killing(self):
        self._kill.set()
        self.t.join()


class VideoCapture1:

    def __init__(self, name):
        self.name = name
        self.cap = cv2.VideoCapture(self.name)
        self.q = Queue()
        self._kill = threading.Event()
        self._interval = 0.0001
        self.t = threading.Thread(target=self._reader)
        self.t.daemon = True
        self.t.start()
        videocapture1_list.append(self)

    # read frames as soon as they are available, keeping only most recent one
    def _reader(self):
        while True:
            is_killed = self._kill.wait(self._interval)
            if is_killed:
                break
            self.ret, self.frame = self.cap.read()
            if not self.ret:
                print('[Exiting] No more frames to read')
                self.cap.release()
                break
            if not self.q.empty():
                try:
                    self.q.get_nowait()  # discard previous (unprocessed) frame
                except queue.Empty:
                    pass
            self.q.put(self.frame)
        self.cap.release()

    def read(self):
        try:
            # if self.q.qsize()>0:
            if self.frame is not None:
                return self.q.get()
            else:
                return self.frame
        except AttributeError:
            return None

    def killing(self):
        self._kill.set()
        self.t.join()


video_processes = []


# reconnect videocapture
class VideoCapture:
    def __init__(self, cam_address, cam_force_address=None, blocking=False):
        """
        cam_address: ip address of the camera feed
        cam_force_address: ip address to disconnect other clients (forcefully take over)
        blocking: if true read() and connect_camera() methods blocked until ip camera is reconnected
        """
        self.cam_address = cam_address
        self.cam_force_address = cam_force_address
        self.blocking = blocking
        self.capture = None

        # NOTE: Can be increased to reduce printing
        self.RECONNECTION_PERIOD = 0.5
        # Calls the connect method
        self.connect_camera()

    def connect_camera(self):
        print("Connecting...")
        while True:
            try:
                if self.cam_force_address is not None:
                    requests.get(self.cam_force_address)
                # self.cap = cv2.VideoCapture(self.cam_address)
                self.capture = WebcamStream(self.cam_address)
                self.capture.start()
                # self.capture = VideoCapture1(self.cam_address)
                video_processes.append(self.capture)
                img = self.capture.read()
                # _,img = self.cap.read()
                if img is None:
                    time.sleep(self.RECONNECTION_PERIOD)
                    raise Exception("Could not connect to a camera: {0}".format(self.cam_address))
                print("Connected to a camera: {}".format(self.cam_address))
                break
            except Exception as e:
                print(e)
                if self.blocking is False:
                    break
                time.sleep(self.RECONNECTION_PERIOD)

    def getstream(self):
        """
        Reads frames and if frame is not received tries to reconnect the camera

        :return: ret - bool witch specifies if frame was read successfully
                 frame - opencv image from the camera
        """
        # if self.capture.isOpened():
        cam_flag, frame = self.capture.read()
        # If feed goes down we try to reconnect
        if frame is None:
            self.connect_camera()
        return cam_flag, frame


fgbg = cv2.createBackgroundSubtractorMOG2()


class Survillence:
    def __init__(self, usr_id, cam_name, rule, url):
        try:
            trace.info('Starting surveillance process')
            self.usr_id = usr_id
            self.cam_name = cam_name
            self.rule = rule
            self.url = url
            self.firstframe = None
            self.areas = None
            self.cap_line = None
            self.main_queue = queue.Queue()
            self.main_back = []
            self.output_user_path = root + "/data/output/" + usr_id
            self.output_user_cam_path = self.output_user_path + "/" + cam_name
            self.tresspass_user_cam_path = self.output_user_cam_path + "/Trespass"
            self.trip_user_cam_path = self.output_user_cam_path + "/Tripwire"
            self.tamper_user_cam_path = self.output_user_cam_path + "/Tampering"
            self.object_user_cam_path = self.output_user_cam_path + "/ObjectDetection"
            self.movement_user_cam_path = self.output_user_cam_path + "/MovementDetection"
            self.leftobject_user_cam_path = self.output_user_cam_path + "/LeftObjectDetection"
            self.loitering_user_cam_path = self.output_user_cam_path + "/LoiteringDetection"
            self.person_exit_user_cam_path = self.output_user_cam_path + "/PersonExit"
            self.no_parking_user_cam_path = self.output_user_cam_path + "/NoParking"
            self.smoke_user_cam_path = self.output_user_cam_path + "/Smoke"
            self.wrongway_person_user_cam_path = self.output_user_cam_path + "/WrongWayPerson"
            self.fire_user_cam_path = self.output_user_cam_path + "/Fire"
            self.face_user_cam_path = self.output_user_cam_path + "/FaceDetection"
            self.license_plate_user_cam_path = self.output_user_cam_path + "/LicensePlate"
            self.social_dictance_user_cam_path = self.output_user_cam_path + "/SocialDistance"
            self.msd_object_user_cam_path = self.output_user_cam_path + '/MissingObjectDetection'
            self.msd_object_area_user_cam_path = self.output_user_cam_path + '/MissingObjectDetectionArea'
            self.signal_violation_user_cam_path = self.output_user_cam_path + '/SignalViolation'
            # self.mask_detection_user_cam_path = self.output_user_cam_path + '/MaskDetection'
            self.Mask_Detection_user_cam_path = self.output_user_cam_path + '/MaskDetection'
            self.wrong_user_cam_path = self.output_user_cam_path + "/WrongDirection"
            self.VehicleWrongdirection_user_cam_path = self.output_user_cam_path + "/VehicleWrongdirection"
            self.vehicle_trip_user_cam_path = self.output_user_cam_path + "/VehicleTripwire"
            self.vehicleTresspass_user_cam_path = self.output_user_cam_path + "/VehicleTresspass"
            self.vehicleLineCrossingCount_user_cam_path = self.output_user_cam_path + "/VehicleLineCrossCount"
            self.personLineCrossingCount_user_cam_path = self.output_user_cam_path + "/PersonLineCrossCount"
            self.Rider_helmet_licenseplate_detection_user_cam_path = self.output_user_cam_path + "/HelmetDetection"
            self.cur_time = None
            self.start_time_1min = None
            self.left_object_first_frame = 0
            self.missing_object_first_frame = 0
            self.missing_object_area_first_frame = 0
            self.face_frame_num = 0
            self.face_presenter = None
            self.face_output_transform = None
            self.Vehicle_dict_cnt = {}
            self.Person_dict_cnt = {}
            self.social_distance_queue = queue.Queue()
            self.tresspass_queue = queue.Queue()
            self.tripwire_queue = queue.Queue()
            self.camera_tamper_queue = queue.Queue()
            self.object_detection_queue = queue.Queue()
            self.loitering_queue = queue.Queue()
            self.no_parking_queue = queue.Queue()
            self.person_exit_queue = queue.Queue()
            self.smoke_queue = queue.Queue()
            self.fire_queue = queue.Queue()
            self.wrong_way_det_queue = queue.Queue()
            self.face_rec_queue = queue.Queue()
            self.signal_queue = queue.Queue()
            self.Movement_det_queue = queue.Queue()
            self.Movement_det_queue1 = queue.Queue()
            self.PersonLineCrossCount_queue = queue.Queue()
            self.VehicleLineCrossCount_queue = queue.Queue()
            self.VehicleTresspass_queue = queue.Queue()
            self.VehicleTripwire_queue = queue.Queue()
            self.VehicleWrongDirection_queue = queue.Queue()
            self.WrongDirection_queue = queue.Queue()
            self.LicensePlate_queue = queue.Queue()
            self.Mask_Detection_queue = queue.Queue()
            self.Rider_helmet_licenseplate_detection_queue = queue.Queue()
            self.flame_queue = queue.Queue()
            self.camera_tamp_detector = MovementDetector()
        except Exception as e:
            exc.exception('Failed to start surveillance process {}'.format(e))

    def time_comp(self, start_date, end_date, start_time, end_time):
        try:
            current_time = datetime.datetime.now()
            start_time = datetime.datetime.combine(
                datetime.datetime.strptime(start_date, "%m/%d/%Y"),
                datetime.datetime.strptime(start_time, "%H:%M").time())
            end_time = datetime.datetime.combine(
                datetime.datetime.strptime(end_date, "%m/%d/%Y"),
                datetime.datetime.strptime(end_time, "%H:%M").time())
            return current_time, start_time, end_time
        except Exception as e:
            exc.exception('Could not process {}'.format(e))

    def detect_image_save(self, tress_image_path, image):
        d = threading.Thread(target=self.detection_image_save1, args=(tress_image_path, image))
        d.start()

    def detection_image_save1(self, tress_image_path, image):
        cv2.imwrite(tress_image_path, image, [cv2.IMWRITE_JPEG_QUALITY, 50])

    def original_image_save(self,tress_plain_image_path, image):
        d = threading.Thread(target=self.original_image_save1, args=(tress_plain_image_path, image))
        d.start()

    def original_image_save1(self, tress_plain_image_path, pl_image):
        cv2.imwrite(tress_plain_image_path, pl_image)

    def detect_image_save_quality(self, tress_image_path, image, image_quality):
        d = threading.Thread(target=self.detection_image_save_quality1, args=(tress_image_path, image, image_quality))
        d.start()

    def detection_image_save_quality1(self, detect_image_path, image,image_quality):
        cv2.imwrite(detect_image_path, image, [cv2.IMWRITE_JPEG_QUALITY, image_quality])

    def original_image_save_quality(self, plain_image_path, image, image_quality):
        d = threading.Thread(target=self.original_image_save_quality1, args=(plain_image_path, image, image_quality))
        d.start()

    def original_image_save_quality1(self, plain_image_path, pl_image,image_quality):
        cv2.imwrite(plain_image_path, pl_image, [cv2.IMWRITE_JPEG_QUALITY, image_quality])

    def analytics_process(self, out_img, out_img1, alert_alarm):
        try:
            if 'Left Object Detection' in self.rule['rule_type']:
                try:
                    if self.left_object_first_frame == 0:
                        self.firstframe = out_img
                        self.left_object_first_frame = +1
                    self.cur_time = time.time()
                    elapsed_time_1min = self.cur_time - self.start_time_1min
                    if elapsed_time_1min >= 60:
                        self.start_time_1min = self.cur_time
                        self.firstframe = out_img
                except Exception as e:
                    exc.error("Error while processing Left object detection {}".format(e))
                    exc.exception('Could not process Left object detection {}'.format(e))
            if 'Missing Object Detection' in self.rule['rule_type']:
                try:
                    if self.missing_object_first_frame == 0:
                        self.firstframe = out_img
                        self.missing_object_first_frame = +1
                    self.cur_time = time.time()
                    elapsed_time_1min = self.cur_time - self.start_time_1min
                    if elapsed_time_1min >= 60:
                        self.start_time_1min = self.cur_time
                        self.firstframe = out_img
                except Exception as e:
                    exc.error("Error while processing Missing object detection {}".format(e))
                    exc.exception('Could not process Missing object detection {}'.format(e))
            if 'Missing Object Detection Area' in self.rule['rule_type']:
                try:
                    if self.missing_object_area_first_frame == 0:
                        self.firstframe = out_img
                        self.missing_object_area_first_frame = +1
                    self.cur_time = time.time()
                    elapsed_time_1min = self.cur_time - self.start_time_1min
                    if elapsed_time_1min >= 60:
                        self.start_time_1min = self.cur_time
                        self.firstframe = out_img
                except Exception as e:
                    exc.error("Error while processing Missing object area detection {}".format(e))
                    exc.exception('Could not process Missing object area detection {}'.format(e))
            if 'Trespass' in self.rule['rule_type']:
                try:
                    self.tresspass_queue.put(out_img)
                    current_time, start_time, end_time = self.time_comp(self.rule['Trespass_start_date'],
                                                                        self.rule['Trespass_end_date'],
                                                                        self.rule['Trespass_start_time'],
                                                                        self.rule['Trespass_end_time'])
                    tresspass_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            tresspass_time_status_flag = True
                        else:
                            tresspass_time_status_flag = False
                    if tresspass_time_status_flag:
                        zones = self.rule['Trespass_zones']
                        height = self.rule['Trespass_img_h']
                        width = self.rule['Trespass_img_w']
                        image_shape = (input_shape_det[_W], input_shape_det[_H])
                        image_re_shape = (input_shape_reid[_W], input_shape_reid[_H])
                        tress_res = openvino_pool.apply_async(Trespass(
                            self.tresspass_queue, zones, image_shape, input_shape_det, exec_net_det, input_name_det,
                            out_name_det, image_re_shape, input_shape_reid, exec_net_reid, input_name_reid,
                            out_name_reid, height, width).trespass_main, ())
                        tress_res_return_val = tress_res.get()
                        elastic_list = tress_res_return_val[3]
                        pl_image = tress_res_return_val[2]
                        writer_val = tress_res_return_val[1]
                        out_img = tress_res_return_val[0]
                        if text_flag:
                            cv2.putText(out_img, "Person Inside Restricted Area", (10, 25),
                                        cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
                        if writer_val:
                            alert_alarm = True
                        if writer_val and image_flag:
                            tress_image_path = self.tresspass_user_cam_path + "/Trespass{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(tress_image_path):
                                FolderView(self.tresspass_user_cam_path).createfolder()
                                tress_plain_image_fld = self.tresspass_user_cam_path + "/original"
                                FolderView(tress_plain_image_fld).createfolder()
                                tress_plain_image_path = tress_plain_image_fld + "/Trespass{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(tress_image_path, out_img)
                                self.original_image_save(tress_plain_image_path, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Person", "Trespass",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      tress_image_path, "", "person", tress_plain_image_path, "",
                                                      "False",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")), elastic_list))

                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(
                                        self.usr_id, self.cam_name, "Trespass", tress_image_path, "Person")
                                if email_flag and writer_val and self.rule['Trespass_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        tress_image_path, "Trespass", self.rule['Trespass_email_alarm_mode']["from"],
                                        self.rule['Trespass_email_alarm_mode']["to"],
                                        self.rule['Trespass_email_alarm_mode']["password"]))
                                if whatsapp_flag and writer_val and self.rule['Trespass_whatsapp_alarm_mode']:
                                    pool.apply_async(whats_image_notification, (tress_image_path,
                                                             self.rule['Trespass_whatsapp_alarm_mode']["token"],
                                                             self.rule['Trespass_whatsapp_alarm_mode']['whatsapp'],
                                                             self.rule['Trespass_whatsapp_alarm_mode'][
                                                                 "instanceId"], self.cam_name, "Trespass"))
                    print("end time of tresspass:", datetime.datetime.now().strftime("%H:%M:%S"))
                except Exception as e:
                    exc.exception('Could not process Tresspass detection {}'.format(e))
            if 'Tripwire' in self.rule['rule_type']:
                try:
                    self.tripwire_queue.put(out_img)
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Tripwire_start_date'], self.rule['Tripwire_end_date'],
                        self.rule['Tripwire_start_time'], self.rule['Tripwire_end_time'])
                    tripwire_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            tripwire_time_status_flag = True
                        else:
                            tripwire_time_status_flag = False
                    if tripwire_time_status_flag:
                        zones = self.rule['Tripwire_zones'][0] + self.rule['Tripwire_zones'][1]
                        height = self.rule['Tripwire_img_h']
                        width = self.rule['Tripwire_img_w']
                        # trip_res = pool.apply_async(tripwire_main, (out_img, zones, height, width))
                        trip_res = openvino_pool.apply_async(
                            Tripwire(self.tripwire_queue, zones, height, width).person_tripwire, ())
                        trip_res_return_val = trip_res.get()
                        pl_image = trip_res_return_val[2]
                        writer_val = trip_res_return_val[1]
                        out_img = trip_res_return_val[0]
                        elastic_trip_list = trip_res_return_val[3]
                        if text_flag:
                            cv2.putText(out_img, "Person crossing the restricted line", (10, 25),
                                        cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
                        if writer_val and image_flag:
                            trip_image_path = self.trip_user_cam_path + "/Tripwire{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(trip_image_path):
                                FolderView(self.trip_user_cam_path).createfolder()
                                trip_plain_image_fld = self.trip_user_cam_path + "/original"
                                FolderView(trip_plain_image_fld).createfolder()
                                trip_plain_image_path = trip_plain_image_fld + "/Tripwire{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(trip_image_path, out_img)
                                self.original_image_save(trip_plain_image_path, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Person", "Tripwire",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      trip_image_path, "", "person", trip_plain_image_path, "",
                                                      "False",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      elastic_trip_list))

                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(
                                        self.usr_id, self.cam_name, "Tripwire", trip_image_path, "Person")
                                if email_flag and writer_val and self.rule['Tripwire_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        trip_image_path, "Tripwire", self.rule['Tripwire_email_alarm_mode']["from"],
                                        self.rule['Tripwire_email_alarm_mode']["to"],
                                        self.rule['Tripwire_email_alarm_mode']["password"]))
                                if whatsapp_flag and writer_val and self.rule['Tripwire_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (
                                        trip_image_path, self.rule['Tripwire_whatsapp_alarm_mode']["token"],
                                        self.rule['Tripwire_whatsapp_alarm_mode']['whatsapp'],
                                        self.rule['Tripwire_whatsapp_alarm_mode']["instanceId"], self.cam_name,
                                        "Tripwire"))

                except Exception as e:
                    exc.exception('Could not process Tripwire detection {}'.format(e))
            if 'Camera Tampering' in self.rule['rule_type']:
                try:
                    # fgbg = cv2.createBackgroundSubtractorMOG2()
                    # if out_img is not None:
                    #     fgmask = fgbg.apply(out_img)
                    # fgmask = fgbg.apply(out_img)
                    self.camera_tamper_queue.put(out_img)
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Camera Tampering_start_date'], self.rule['Camera Tampering_end_date'],
                        self.rule['Camera Tampering_start_time'], self.rule['Camera Tampering_end_time'])
                    ct_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            ct_time_status_flag = True
                        else:
                            ct_time_status_flag = False
                    if ct_time_status_flag:
                        tamper_res = pool.apply_async(self.camera_tamp_detector.process_frame, (out_img,))
                        tamper_res_return_val = tamper_res.get()
                        pl_image = tamper_res_return_val[2]
                        cam_tampering_flag = tamper_res_return_val[1]
                        out_img = tamper_res_return_val[0]
                        if text_flag:
                            cv2.putText(out_img, "Camera Tampering Detection", (10, 25), cv2.FONT_HERSHEY_SIMPLEX,
                                        1.0, (0, 0, 0), 2)
                        if cam_tampering_flag and image_flag:
                            tamp_image_path = self.tamper_user_cam_path + "/Tampering{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(tamp_image_path):
                                FolderView(self.tamper_user_cam_path).createfolder()
                                tamp_plain_image_fld = self.tamper_user_cam_path + "/original"
                                FolderView(tamp_plain_image_fld).createfolder()
                                tamp_plain_image_path = tamp_plain_image_fld + "/Tampering{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(tamp_image_path, out_img)
                                self.original_image_save(tamp_plain_image_path, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Person", "Tampering",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      tamp_image_path, "", "", tamp_plain_image_path, "",
                                                      "True",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(
                                        self.usr_id, self.cam_name, "Tampering", tamp_image_path, "Person")
                                if email_flag and cam_tampering_flag and self.rule['Camera Tampering_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (tamp_image_path, "Tampering", self.rule[
                                        'Camera Tampering_email_alarm_mode']["from"], self.rule[
                                                                                    'Camera Tampering_email_alarm_mode'][
                                                                                    "to"], self.rule[
                                                                                    'Camera Tampering_email_alarm_mode'][
                                                                                    "password"]))
                                if whatsapp_flag and cam_tampering_flag and self.rule[
                                    'Camera Tampering_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (
                                        tamp_image_path, self.rule['Camera Tampering_whatsapp_alarm_mode']["token"],
                                        self.rule['Camera Tampering_whatsapp_alarm_mode']['whatsapp'],
                                        self.rule['Camera Tampering_whatsapp_alarm_mode']["instanceId"], self.cam_name,
                                        "Tampering"))
                except Exception as e:
                    exc.exception('Could not process Camera tampering detection {}'.format(e))
            # if 'Object Detection' in self.rule['rule_type']:
            #     try:
            #         current_time, start_time, end_time = self.time_comp(
            #             self.rule['Object Detection_start_date'], self.rule['Object Detection_end_date'],
            #             self.rule['Object Detection_start_time'], self.rule['Object Detection_end_time'])
            #
            #         obj_det_time_status_flag = True
            #         if time_date_flag:
            #             if (current_time >= start_time) and (current_time <= end_time):
            #                 obj_det_time_status_flag = True
            #             else:
            #                 obj_det_time_status_flag = False
            #         if obj_det_time_status_flag:
            #             object = self.rule['object']
            #             object_height_width = (obj_w, obj_h)
            #             object_n_c_h_w = (obj_n, obj_c, obj_h, obj_w)
            #             object_res = pool.apply_async(objectdetection_main,
            #                                           (out_img, obj_exec_net, labels_map, input_blob,
            #                                            object_height_width,
            #                                            object_n_c_h_w, self.object_user_cam_path, Db_image_flag,
            #                                            self.usr_id, self.cam_name, image_flag,
            #                                            elasticsearch_flag,object,fashion_model))
            #             object_res_return_val = object_res.get()
            #             object_count = object_res_return_val[1]
            #             out_img = object_res_return_val[0]
            #             position_obj = (10, 25)
            #             cv2.putText(out_img, "Object Detection", position_obj, cv2.FONT_HERSHEY_SIMPLEX, 1.0,
            #                         (0, 0, 0), 2)
            #             object_color = object_res_return_val[2]
            #             detected_objects = object_res_return_val[3]
            #             object_image_path = object_res_return_val[4]
            #             image_path_original = object_res_return_val[5]
            #             frame1 = object_res_return_val[6]
            #             if image_flag:
            #                 # object_image_path = self.object_user_cam_path + "/ObjectDetection{}.jpg".format(
            #                 #     str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
            #                 FolderView(self.object_user_cam_path).createfolder()
            #                 # cv2.imwrite(object_image_path, out_img)
            #                 # cv2.imwrite(image_path_original, frame1)
            #                 # background thread for writing image
            #
            #                 bbox_imae_thread = threading.Thread(target=object_detection_image_save,
            #                                                     args=(object_image_path, out_img))
            #                 origional_image_thread = threading.Thread(target=object_detection_image_save,
            #                                                           args=(image_path_original, frame1))
            #                 bbox_imae_thread.start()
            #                 origional_image_thread.start()
            #
            #                 # if elasticsearch_flag:
            #                 #     pool.apply_async(SearchEngine().engine_data,(self.usr_id,self.cam_name,"Object","ObjectDetection", str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")),"","","","","",object_image_path,""))
            #                 if Db_image_flag:
            #                     analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
            #                                                          "ObjectDetection",
            #                                                          object_image_path, "Object")
            #                 if email_flag and self.rule['Object Detection_alarm_mode']:
            #                     pool.apply_async(email_image_notification, (object_image_path, "Object Detection",self.rule['Object Detection_alarm_mode']["from"],self.rule['Object Detection_alarm_mode']["to"],self.rule['Object Detection_alarm_mode']["password"]))
            #     except Exception as ex:
            #         exc.exception('Could not process Object detection {}'.format(ex))
            if 'Object Detection' in self.rule['rule_type']:
                try:
                    self.object_detection_queue.put(out_img)
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Object Detection_start_date'], self.rule['Object Detection_end_date'],
                        self.rule['Object Detection_start_time'], self.rule['Object Detection_end_time'])
                    obj_det_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            obj_det_time_status_flag = True
                        else:
                            obj_det_time_status_flag = False
                    if obj_det_time_status_flag:
                        img = self.object_detection_queue.get()
                        cv2.resize(img, (450, 450))
                        imgs = [None] * 1
                        imgs[0] = img
                        img0 = imgs.copy()
                        frame_det = pool.apply_async(run, (
                            device, model, names, img, img0, (640, 640), stride, pt, half, self.object_user_cam_path,
                            self.usr_id, self.cam_name, image_flag, elasticsearch_flag, Db_image_flag,
                            self.rule['Object Detection_object']))
                        object_res_return_val = frame_det.get()
                        frame = object_res_return_val[1]
                        out_img = object_res_return_val[0]
                        obj_flag = object_res_return_val[4]
                        image_path_original = object_res_return_val[3]
                        object_image_path = object_res_return_val[2]
                        object_elastic_lst = object_res_return_val[5]
                        object_db_lst = object_res_return_val[6]
                        if text_flag:
                            cv2.putText(out_img, "Object Detection", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 1.0,
                                        (0, 0, 0), 2)
                        if image_flag and obj_flag:
                            if not os.path.exists(object_image_path):
                                FolderView(self.object_user_cam_path).createfolder()
                                self.detect_image_save_quality(object_image_path, out_img, 60)
                                self.original_image_save_quality(image_path_original, frame, 60)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Object", "ObjectDetection",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      object_image_path, "", "", image_path_original, "",
                                                      "True",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      []))
                                    pool.apply_async(SearchEngine().bulk_insert(object_elastic_lst, ))
                                if Db_image_flag:
                                    database_pool.apply_async(analytics_detector.insert_predicted_details, (
                                        self.usr_id, self.cam_name, "ObjectDetection", object_image_path, "Object"))
                                    database_pool.apply_async(analytics_detector.insert_bulk_data, (object_db_lst,))
                                if email_flag and self.rule['Object Detection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        object_image_path, "Object Detection",
                                        self.rule['Object Detection_email_alarm_mode']["from"],
                                        self.rule['Object Detection_email_alarm_mode']["to"],
                                        self.rule['Object Detection_email_alarm_mode']["password"]))
                                if whatsapp_flag and self.rule['Object Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (
                                        object_image_path, self.rule['Object Detection_whatsapp_alarm_mode']["token"],
                                        self.rule['Object Detection_whatsapp_alarm_mode']['whatsapp'],
                                        self.rule['Object Detection_whatsapp_alarm_mode']["instanceId"], self.cam_name,
                                        "ObjectDetection"))
                    print("end time of Object Detection:", datetime.datetime.now().strftime("%H:%M:%S"))
                except Exception as e:
                    exc.exception('Could not process Object detection {}'.format(e))
            # Need to put Alarm sound
            if 'Movement Detection' in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Movement Detection_start_date'], self.rule['Movement Detection_end_date'],
                        self.rule['Movement Detection_start_time'], self.rule['Movement Detection_end_time'])
                    mov_det_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            mov_det_time_status_flag = True
                        else:
                            mov_det_time_status_flag = False
                    if mov_det_time_status_flag:
                        frame = out_img[84:2000, 0:1700]
                        frame1 = out_img1[84:2000, 0:1700]
                        self.Movement_det_queue.put(frame)
                        self.Movement_det_queue1.put(frame1)
                        movement_detect_res = pool.apply_async(movement_detector_main, (
                            self.Movement_det_queue.get(), self.Movement_det_queue1.get()))
                        # movement_detect_res = pool.apply_async(movement_detector_main, (
                        # out_img, out_img1))
                        movement_detect_res_return_val = movement_detect_res.get()
                        pl_image = movement_detect_res_return_val[2]
                        movement_flag = movement_detect_res_return_val[1]
                        out_img = movement_detect_res_return_val[0]
                        if text_flag:
                            cv2.putText(out_img, "Movement Detection", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 1.3,
                                        (0, 0, 0), 2)

                        if movement_flag:
                            alert_alarm = True

                        if movement_flag and image_flag:

                            movement_image_path = self.movement_user_cam_path + "/MovementDetection{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(movement_image_path):
                                FolderView(self.movement_user_cam_path).createfolder()
                                movement_plain_image_fld = self.movement_user_cam_path + "/original"
                                FolderView(movement_plain_image_fld).createfolder()
                                movement_plain_image_path = movement_plain_image_fld + "/MovementDetection{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(movement_image_path, out_img)
                                self.original_image_save(movement_plain_image_path, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name,  "Person", "MovementDetection",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      movement_image_path, "","", movement_plain_image_path, "",
                                                      "True",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(
                                        self.usr_id, self.cam_name, "MovementDetection", movement_image_path, "Person")
                                if email_flag and movement_flag and self.rule['Movement Detection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        movement_image_path, "MovementDetection",
                                        self.rule['Movement Detection_email_alarm_mode']["from"],
                                        self.rule['Movement Detection_email_alarm_mode']["to"],
                                        self.rule['Movement Detection_email_alarm_mode']["password"]))
                                if whatsapp_flag and movement_flag and self.rule[
                                    'Movement Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (
                                        movement_image_path,
                                        self.rule['Movement Detection_whatsapp_alarm_mode']["token"],
                                        self.rule['Movement Detection_whatsapp_alarm_mode']['whatsapp'],
                                        self.rule['Movement Detection_whatsapp_alarm_mode']["instanceId"],
                                        self.cam_name, "MovementDetection"))
                except Exception as e:
                    exc.exception('could not process Movement detection {}'.format(e))
            if 'Left Object Detection' in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Left Object Detection_start_date'], self.rule['Left Object Detection_end_date'],
                        self.rule['Left Object Detection_start_time'], self.rule['Left Object Detection_end_time'])
                    leftobj_det_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            leftobj_det_time_status_flag = True
                        else:
                            leftobj_det_time_status_flag = False
                    if leftobj_det_time_status_flag:
                        frame = self.firstframe[84:2000, 0:1700]
                        frame1 = out_img1[84:2000, 0:1700]
                        pl_image = frame.copy()
                        left_object_res = pool.apply_async(left_object_detection_main,
                                                           (frame, frame1))
                        left_object_res_return_val = left_object_res.get()
                        left_object = left_object_res_return_val[1]
                        out_img = left_object_res_return_val[0]
                        if text_flag:
                            cv2.putText(out_img, "Left Object Detection", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 1.0,
                                        (0, 0, 0), 2)
                        if left_object:
                            alert_alarm = True
                        if left_object and image_flag:
                            leftobject_image_path = self.leftobject_user_cam_path + "/LeftObjectDetection{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(leftobject_image_path):
                                FolderView(self.leftobject_user_cam_path).createfolder()
                                LeftObjectDetection_plain_image_fld = self.leftobject_user_cam_path + "/original"
                                FolderView(LeftObjectDetection_plain_image_fld).createfolder()
                                LeftObjectDetection_plain_image_path = LeftObjectDetection_plain_image_fld + "/LeftObjectDetection{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(leftobject_image_path, out_img)
                                self.original_image_save(LeftObjectDetection_plain_image_path, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Object", "LeftObjectDetection",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      leftobject_image_path, "", "", LeftObjectDetection_plain_image_path, "",
                                                      "True",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      []))
                                if Db_image_flag:
                                    database_pool.apply_async(analytics_detector.insert_predicted_details, (
                                        self.usr_id, self.cam_name, "LeftObjectDetection", leftobject_image_path,
                                        "Object"))
                                if email_flag and left_object and self.rule['Left Object Detection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        leftobject_image_path, "LeftObjectDetection",
                                        self.rule['Left Object Detection_email_alarm_mode']["from"],
                                        self.rule['Left Object Detection_email_alarm_mode']["to"],
                                        self.rule['Left Object Detection_email_alarm_mode']["password"]))
                                if whatsapp_flag and left_object and self.rule[
                                    'Left Object Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (
                                        leftobject_image_path,
                                        self.rule['Left Object Detection_whatsapp_alarm_mode']["token"],
                                        self.rule['Left Object Detection_whatsapp_alarm_mode']['whatsapp'],
                                        self.rule['Left Object Detection_whatsapp_alarm_mode']["instanceId"],
                                        self.cam_name, "LeftObjectDetection"))
                except Exception as e:
                    exc.exception('Could not process left object detection {}'.format(e))
            if 'Loitering Detection' in self.rule['rule_type']:
                try:
                    self.loitering_queue.put(out_img)
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Loitering Detection_start_date'], self.rule['Loitering Detection_end_date'],
                        self.rule['Loitering Detection_start_time'], self.rule['Loitering Detection_end_time'])

                    loitering_det_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            loitering_det_time_status_flag = True
                        else:
                            loitering_det_time_status_flag = False
                    if loitering_det_time_status_flag:
                        loitering_zones = self.rule['Loitering Detection_zones']
                        loitering_height = self.rule['Loitering Detection_img_h']
                        loitering_width = self.rule['Loitering Detection_img_w']
                        loitering_time_limit = self.rule['Loitering Detection_intrusion_time_limit']
                        loitering_image_shape = (
                            loitering_input_shape_det[loitering_W], loitering_input_shape_det[loitering_H])
                        loitering_image_re_shape = (
                            loitering_input_shape_reid[loitering_W], loitering_input_shape_reid[loitering_H])
                        pl_image = out_img.copy()
                        loitering_res = openvino_pool.apply_async(loitering().loitering_main, (
                            self.loitering_queue, loitering_zones, loitering_image_shape, loitering_input_shape_det,
                            loitering_exec_net_det, loitering_input_name_det, loitering_out_name_det,
                            loitering_image_re_shape, loitering_input_shape_reid, loitering_exec_net_reid,
                            loitering_input_name_reid, loitering_out_name_reid,
                            loitering_height, loitering_width,))
                        loitering_res_return_val = loitering_res.get()
                        loitering_writer_val = loitering_res_return_val[1]
                        out_img = loitering_res_return_val[0]
                        elastic_loit_list = loitering_res_return_val[2]
                        if text_flag:
                            cv2.putText(out_img, "Person waiting long time", (10, 25), cv2.FONT_HERSHEY_SIMPLEX,
                                        1.0, (0, 0, 0), 2)
                        if loitering_writer_val:
                            alert_alarm = True

                        if loitering_writer_val and image_flag:

                            loitering_image_path = self.loitering_user_cam_path + "/Loitering{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(loitering_image_path):
                                FolderView(self.loitering_user_cam_path).createfolder()
                                loitering_user_cam_path_original = self.loitering_user_cam_path + '/Original'
                                FolderView(loitering_user_cam_path_original).createfolder()
                                loitering_image_path_original = loitering_user_cam_path_original + "/Loitering{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(loitering_image_path, out_img)
                                self.original_image_save(loitering_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Person", "LoiteringDetection",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      loitering_image_path, "", "person", loitering_image_path_original,
                                                      "",
                                                      "False",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      elastic_loit_list))

                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "LoiteringDetection",
                                                                                loitering_image_path,
                                                                                "Person")
                                if email_flag and self.rule['Loitering Detection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (loitering_image_path, "Loitering",
                                                                                self.rule[
                                                                                    'Loitering Detection_email_alarm_mode'][
                                                                                    "from"], self.rule[
                                                                                    'Loitering Detection_email_alarm_mode'][
                                                                                    "to"], self.rule[
                                                                                    'Loitering Detection_email_alarm_mode'][
                                                                                    "password"]))
                                if whatsapp_flag and image_flag and self.rule[
                                    'Loitering Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (loitering_image_path,
                                                                                         self.rule[
                                                                                             'Loitering Detection_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'Loitering Detection_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'Loitering Detection_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         "LoiteringDetection"))

                except Exception as e:
                    exc.exception('Could not process Loitering detection {}'.format(e))
            if 'Person Exit' in self.rule['rule_type']:
                try:
                    self.person_exit_queue.put(out_img)
                    pl_image = out_img.copy()
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Person Exit_start_date'], self.rule['Person Exit_end_date'],
                        self.rule['Person Exit_start_time'], self.rule['Person Exit_end_time'])
                    person_exit_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            person_exit_time_status_flag = True
                        else:
                            person_exit_time_status_flag = False
                    if person_exit_time_status_flag:
                        person_exist_zones = self.rule['Person Exit_zones']
                        person_exist_height = self.rule['Person Exit_img_h']
                        person_exist_width = self.rule['Person Exit_img_w']
                        person_exist_time_limit = self.rule['Person Exit_intrusion_time_limit']
                        person_exist_image_shape = (person_exist_input_shape_det[person_exist_W],
                                                    person_exist_input_shape_det[person_exist_H])
                        person_exist_image_re_shape = (person_exist_input_shape_reid[person_exist_W],
                                                       person_exist_input_shape_reid[person_exist_H])
                        person_exist_res = openvino_pool.apply_async(person_exit().person_exit_main, (
                            self.person_exit_queue, person_exist_zones, person_exist_image_shape,
                            person_exist_input_shape_det,
                            person_exist_exec_net_det, person_exist_input_name_det, person_exist_out_name_det,
                            person_exist_image_re_shape, person_exist_input_shape_reid,
                            person_exist_exec_net_reid, person_exist_input_name_reid,
                            person_exist_out_name_reid,
                            person_exist_height, person_exist_width, person_exist_time_limit))
                        person_exist_res_return_val = person_exist_res.get()
                        person_exist_writer_val = person_exist_res_return_val[1]
                        out_img = person_exist_res_return_val[0]
                        elastic_list = person_exist_res_return_val[2]
                        if text_flag:
                            cv2.putText(out_img, "Person Exit Detection", (10, 25), cv2.FONT_HERSHEY_SIMPLEX,
                                        1.0, (0, 0, 0), 2)

                        if person_exist_writer_val:
                            alert_alarm = True
                        if person_exist_writer_val and image_flag:
                            person_exist_image_path = self.person_exit_user_cam_path + "/PersonExist{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(person_exist_image_path):
                                FolderView(self.person_exit_user_cam_path).createfolder()
                                person_exit_user_cam_path_original = self.person_exit_user_cam_path + '/Original'
                                FolderView(person_exit_user_cam_path_original).createfolder()
                                person_exit_path_original = person_exit_user_cam_path_original + "/PersonExist{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(person_exist_image_path, out_img)
                                self.original_image_save(person_exit_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Person", "PersonExit",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      person_exist_image_path, "", "person", person_exit_path_original,
                                                      "",
                                                      "False",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")), elastic_list))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "PersonExit", person_exist_image_path,
                                                                                "Person")
                                if email_flag and self.rule['Person Exit_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (person_exist_image_path, "PersonExist",
                                                                                self.rule[
                                                                                    'Person Exit_email_alarm_mode'][
                                                                                    "from"], self.rule[
                                                                                    'Person Exit_email_alarm_mode'][
                                                                                    "to"], self.rule[
                                                                                    'Person Exit_email_alarm_mode'][
                                                                                    "password"]))
                                if whatsapp_flag and self.rule['Person Exit_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (person_exist_image_path,
                                                                                         self.rule[
                                                                                             'Person Exit_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'Person Exit_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'Person Exit_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name, "PersonExit"))
                except Exception as e:
                    exc.exception('Could not process PersonExist detection {}'.format(e))
            if 'No Parking' in self.rule['rule_type']:
                try:
                    self.no_parking_queue.put(out_img)
                    pl_image = out_img.copy()
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['No Parking_start_date'], self.rule['No Parking_end_date'],
                        self.rule['No Parking_start_time'], self.rule['No Parking_end_time'])

                    no_parking_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            no_parking_time_status_flag = True
                        else:
                            no_parking_time_status_flag = False
                    if no_parking_time_status_flag:
                        no_parking_zones = self.rule['No Parking_zones']
                        no_parking_height = self.rule['No Parking_img_h']
                        no_parking_width = self.rule['No Parking_img_w']
                        no_parking_time_limit = self.rule['No Parking_intrusion_time_limit']
                        no_parking_image_shape = (
                            no_parking_input_shape_det[no_parking_W], no_parking_input_shape_det[no_parking_H])
                        no_parking_image_re_shape = (
                            no_parking_input_shape_reid[no_parking_W],
                            no_parking_input_shape_reid[no_parking_H])
                        no_parking_res = openvino_pool.apply_async(no_parking_main, (
                            self.no_parking_queue, no_parking_zones, no_parking_image_shape, no_parking_input_shape_det,
                            no_parking_exec_net_det, no_parking_input_name_det, no_parking_out_name_det,
                            no_parking_image_re_shape, no_parking_input_shape_reid, no_parking_exec_net_reid,
                            no_parking_input_name_reid, no_parking_out_name_reid,
                            no_parking_height, no_parking_width, no_parking_time_limit))
                        no_parking_res_return_val = no_parking_res.get()
                        no_parking_writer_val = no_parking_res_return_val[1]
                        out_img = no_parking_res_return_val[0]
                        if text_flag:
                            cv2.putText(out_img, "No Parking Detection", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 1.0,
                                        (0, 0, 0), 2)
                        if no_parking_writer_val:
                            alert_alarm = True

                        if no_parking_writer_val and image_flag:
                            no_parking_image_path = self.no_parking_user_cam_path + "/NoParking{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(no_parking_image_path):
                                FolderView(self.no_parking_user_cam_path).createfolder()
                                no_parking_user_cam_path_original = self.no_parking_user_cam_path + '/Original'
                                FolderView(no_parking_user_cam_path_original).createfolder()
                                no_parking_image_path_original = no_parking_user_cam_path_original + "/NoParking{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(no_parking_image_path, out_img)
                                self.original_image_save(no_parking_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Object", "NoParking",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      no_parking_image_path, "", "",
                                                      no_parking_image_path_original, "",
                                                      "True",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name, "NoParking",
                                                                                no_parking_image_path, "Object")
                                if email_flag and self.rule['No Parking_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (no_parking_image_path, "NoParking",
                                                                                self.rule[
                                                                                    'No Parking_email_alarm_mode'][
                                                                                    "from"], self.rule[
                                                                                    'No Parking_email_alarm_mode'][
                                                                                    "to"], self.rule[
                                                                                    'No Parking_email_alarm_mode'][
                                                                                    "password"]))
                                if whatsapp_flag and image_flag and self.rule['No Parking_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (no_parking_image_path,
                                                                                         self.rule[
                                                                                             'No Parking_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'No Parking_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'No Parking_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name, "NoParking"))
                except Exception as e:
                    exc.exception('Could not process No parking detection {}'.format(e))
            if 'Smoke Detection' in self.rule['rule_type']:
                try:
                    self.smoke_queue.put(out_img)
                    pl_image = out_img.copy()
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Smoke Detection_start_date'], self.rule['Smoke Detection_end_date'],
                        self.rule['Smoke Detection_start_time'], self.rule['Smoke Detection_end_time'])

                    smoke_detection_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            smoke_detection_time_status_flag = True
                        else:
                            smoke_detection_time_status_flag = False
                    if smoke_detection_time_status_flag:
                        smoke_res = pool.apply_async(smoke_main, (self.smoke_queue, smoke_model, smoke_class_names))
                        smoke_res_return_val = smoke_res.get()
                        smoke_flag = smoke_res_return_val[1]
                        out_img = smoke_res_return_val[0]
                        if text_flag:
                            cv2.putText(out_img, "Smoke Detection", (545, 25), cv2.FONT_HERSHEY_SIMPLEX, 1.0,
                                        (0, 0, 0), 2)
                        if smoke_flag:
                            alert_alarm = True
                        if smoke_flag and image_flag:
                            smoke_image_path = self.smoke_user_cam_path + "/Smoke{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(smoke_image_path):
                                FolderView(self.smoke_user_cam_path).createfolder()
                                smoke_user_cam_path_original = self.smoke_user_cam_path + '/Original'
                                FolderView(smoke_user_cam_path_original).createfolder()
                                smoke_user_image_path_original = smoke_user_cam_path_original + "/Smoke{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(smoke_image_path, out_img)
                                self.original_image_save(smoke_user_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Object", "SmokeDetection",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      smoke_image_path, "", "",
                                                      smoke_user_image_path_original, "",
                                                      "True",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "SmokeDetection",
                                                                                smoke_image_path, "Person")
                                if email_flag and self.rule['Smoke Detection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (smoke_image_path, "SmokeDetection",
                                                                                self.rule[
                                                                                    'Smoke Detection_email_alarm_mode'][
                                                                                    "from"], self.rule[
                                                                                    'Smoke Detection_email_alarm_mode'][
                                                                                    "to"], self.rule[
                                                                                    'Smoke Detection_email_alarm_mode'][
                                                                                    "password"]))
                                if whatsapp_flag and self.rule['Smoke Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (smoke_image_path,
                                                                                         self.rule[
                                                                                             'Smoke Detection_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'Smoke Detection_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'Smoke Detection_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         "Smoke Detection"))
                except Exception as e:
                    exc.exception('Could not process Smoke detection {}'.format(e))
            if 'Wrong Way Detection' in self.rule['rule_type']:
                try:
                    self.wrong_way_det_queue.put(out_img)
                    pl_image = out_img.copy()
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Wrong Way Detection_start_date'], self.rule['Wrong Way Detection_end_date'],
                        self.rule['Wrong Way Detection_start_time'], self.rule['Wrong Way Detection_end_time'])

                    wrongway_detection_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            wrongway_detection_time_status_flag = True
                        else:
                            wrongway_detection_time_status_flag = False
                    if wrongway_detection_time_status_flag:
                        direction_target = self.rule['Wrong Way Detection_direction']
                        wwp_res = openvino_pool.apply_async(wrongway_person_main,
                                                            (self.wrong_way_det_queue, direction_target))
                        wwp_res_return_val = wwp_res.get()
                        writer_val = wwp_res_return_val[1]
                        out_img = wwp_res_return_val[0]
                        worng_elastic_list = wwp_res_return_val[2]

                        if text_flag:
                            cv2.putText(out_img, "Going Wrong Way Detection", (10, 25), cv2.FONT_HERSHEY_SIMPLEX,
                                        1.0, (0, 0, 0), 2)

                        if writer_val:
                            alert_alarm = True

                        if writer_val and image_flag:
                            wwp_image_path = self.wrongway_person_user_cam_path + "/WrongWayPerson{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(wwp_image_path):
                                FolderView(self.wrongway_person_user_cam_path).createfolder()
                                wrongway_person_user_cam_path_original = self.wrongway_person_user_cam_path + '/Original'
                                FolderView(wrongway_person_user_cam_path_original).createfolder()
                                wrongway_person_user_image_path_original = wrongway_person_user_cam_path_original + "/WrongWayPerson{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(wwp_image_path, out_img)
                                self.original_image_save(wrongway_person_user_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name,"Person", "WrongWayDetection",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      wwp_image_path, "", "person",
                                                      wrongway_person_user_image_path_original, "",
                                                      "False",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      worng_elastic_list))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "WrongWayDetection", wwp_image_path,
                                                                                "Person")
                                if email_flag and writer_val and self.rule['Wrong Way Detection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (wwp_image_path, "WrongWayPerson",
                                                                                self.rule[
                                                                                    'Wrong Way Detection_email_alarm_mode'][
                                                                                    "from"],
                                                                                self.rule[
                                                                                    'Wrong Way Detection_email_alarm_mode'][
                                                                                    "to"], self.rule[
                                                                                    'Wrong Way Detection_email_alarm_mode'][
                                                                                    "password"]))
                                if whatsapp_flag and writer_val and self.rule[
                                    'Wrong Way Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (
                                        wwp_image_path, self.rule['Wrong Way Detection_whatsapp_alarm_mode']["token"],
                                        self.rule['Wrong Way Detection_whatsapp_alarm_mode']['whatsapp'],
                                        self.rule['Wrong Way Detection_whatsapp_alarm_mode']["instanceId"],
                                        self.cam_name,
                                        "Wrong Way Detection"))
                except Exception as e:
                    exc.exception('Could not process Wrongway person detection {}'.format(e))
            if 'Fire Detection' in self.rule['rule_type']:
                try:
                    self.fire_queue.put(out_img)
                    pl_image = out_img.copy()
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Fire Detection_start_date'], self.rule['Fire Detection_end_date'],
                        self.rule['Fire Detection_start_time'], self.rule['Fire Detection_end_time'])

                    fire_detection_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            fire_detection_time_status_flag = True
                        else:
                            fire_detection_time_status_flag = False
                    if fire_detection_time_status_flag:
                        fire_res = pool.apply_async(fire_detector_main, (self.fire_queue, fire_cascade_model))
                        fire_res_return_val = fire_res.get()
                        fire_flag = fire_res_return_val[1]
                        out_img = fire_res_return_val[0]
                        if text_flag :
                            cv2.putText(out_img, "Fire Detection", (545, 25), cv2.FONT_HERSHEY_SIMPLEX, 1.0,
                                        (0, 0, 0), 2)
                        if fire_flag:
                            alert_alarm = True
                        if fire_flag and image_flag:
                            fire_image_path = self.fire_user_cam_path + "/Fire{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(fire_image_path):
                                FolderView(self.fire_user_cam_path).createfolder()
                                fire_user_cam_path_original = self.fire_user_cam_path + '/Original'
                                FolderView(fire_user_cam_path_original).createfolder()
                                fire_user_image_path_original = fire_user_cam_path_original + "/Fire{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(fire_image_path, out_img)
                                self.original_image_save(fire_user_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Person", "FireDetection",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      fire_image_path, "", "",
                                                      fire_user_image_path_original, "",
                                                      "True",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "FireDetection",
                                                                                fire_image_path, "Person")
                                if email_flag and self.rule['Fire Detection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (fire_image_path, "FireDetection",
                                                                                self.rule[
                                                                                    'Fire Detection_email_alarm_mode'][
                                                                                    "from"], self.rule[
                                                                                    'Fire Detection_email_alarm_mode'][
                                                                                    "to"], self.rule[
                                                                                    'Fire Detection_email_alarm_mode'][
                                                                                    "password"]))
                                if whatsapp_flag and self.rule['Wrong Way Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (fire_image_path,
                                                                                         self.rule[
                                                                                             'Fire Detection_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'Fire Detection_whatsapp_alarm_mode'][
                                                                                             'whatsapp'],
                                                                                         self.rule[
                                                                                             'Fire Detection_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         "Fire Detection"))
                except Exception as e:
                    exc.exception('Could not process Fire detection {}'.format(e))
            # Need to put Alarm sound
            if 'Face Detection' in self.rule['rule_type']:
                try:
                    self.face_rec_queue.put(out_img)
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Face Detection_start_date'], self.rule['Face Detection_end_date'],
                        self.rule['Face Detection_start_time'], self.rule['Face Detection_end_time'])
                    face_detection_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            face_detection_time_status_flag = True
                        else:
                            face_detection_time_status_flag = False
                    if face_detection_time_status_flag:
                        face_res = openvino_pool.apply_async(face_identifier_main,
                                                             (self.face_rec_queue, self.face_frame_num,
                                                              face_frame_processor, self.face_presenter,
                                                              self.face_output_transform, face_args,
                                                              self.face_user_cam_path,
                                                              Db_image_flag, self.usr_id,
                                                              self.cam_name, image_flag,
                                                              elasticsearch_flag))
                        face_res_return_val = face_res.get()
                        frame = face_res_return_val[6]
                        face_image_original_path = face_res_return_val[5]
                        face_flag = face_res_return_val[3]
                        self.face_output_transform = face_res_return_val[2]
                        self.face_presenter = face_res_return_val[1]
                        out_img = face_res_return_val[0]
                        if text_flag:
                            cv2.putText(out_img, "Face Recognition", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 1.0,
                                        (0, 0, 0), 2)
                        face_image_path = face_res_return_val[4]
                        self.face_frame_num += 1
                        if image_flag and face_flag:

                            if not os.path.exists(face_image_path):
                                FolderView(self.face_user_cam_path).createfolder()
                                self.detect_image_save(face_image_path, out_img)
                                self.original_image_save_quality(face_image_original_path, frame, 50)
                                # if elasticsearch_flag:
                                #     pool.apply_async(SearchEngine().engine_data,(self.usr_id,self.cam_name,"Person","FaceDetection",str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")),"","","","","",face_image_path,""))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "FaceDetection",
                                                                                face_image_path, "Person")
                                if email_flag and self.rule['Face Detection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (face_image_path, "FaceDetection",
                                                                                self.rule[
                                                                                    'Face Detection_email_alarm_mode'][
                                                                                    "from"], self.rule[
                                                                                    'Face Detection_email_alarm_mode'][
                                                                                    "to"], self.rule[
                                                                                    'Face Detection_email_alarm_mode'][
                                                                                    "password"]))
                                if whatsapp_flag and self.rule['Face Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (face_image_path,
                                                                                         self.rule[
                                                                                             'Face Detection_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'Face Detection_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'Face Detection_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         "Face Detection"))

                except Exception as e:
                    exc.exception('Could not process Face detection {}'.format(e))
            # Need to put Alarm sound
            if 'License Plate Detection' in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['License Plate Detection_start_date'], self.rule['License Plate Detection_end_date'],
                        self.rule['License Plate Detection_start_time'], self.rule['License Plate Detection_end_time'])

                    lic_plate_detection_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            lic_plate_detection_time_status_flag = True
                        else:
                            lic_plate_detection_time_status_flag = False
                    if lic_plate_detection_time_status_flag:
                        self.LicensePlate_queue.put(out_img)
                        pl_image = out_img.copy()
                        lic_dd_res = openvino_pool.apply_async(license_plate_detector_main,
                                                               (self.LicensePlate_queue, license_plate_detector))
                        lic_dd_res_return_val = lic_dd_res.get()
                        lic_dd_flag = lic_dd_res_return_val[1]
                        out_img = lic_dd_res_return_val[0]
                        if text_flag:
                            cv2.putText(out_img, "Licence plate Detection", (10, 25), cv2.FONT_HERSHEY_SIMPLEX,
                                        1.0, (0, 0, 0), 2)
                        if image_flag and lic_dd_flag:
                            lic_dd_image_path = self.license_plate_user_cam_path + "/LicensePlateDetection{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(lic_dd_image_path):
                                FolderView(self.license_plate_user_cam_path).createfolder()
                                license_plate_cam_path_original = self.license_plate_user_cam_path + '/Original'
                                FolderView(license_plate_cam_path_original).createfolder()
                                license_plate_path_original = license_plate_cam_path_original + "/LicensePlateDetection{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(lic_dd_image_path, out_img)
                                self.original_image_save(license_plate_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Object", "LicensePlateDetection",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      lic_dd_image_path, "", "",
                                                      license_plate_path_original, "",
                                                      "True",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "LicensePlateDetection",
                                                                                lic_dd_image_path, "Object")
                                if email_flag and self.rule['License Plate Detection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        lic_dd_image_path, "LicensePlateDetection",
                                        self.rule['License Plate Detection_email_alarm_mode']["from"],
                                        self.rule['License Plate Detection_email_alarm_mode']["to"],
                                        self.rule['License Plate Detection_email_alarm_mode']["password"]))
                                if whatsapp_flag and self.rule['License Plate Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (lic_dd_image_path,
                                                                                         self.rule[
                                                                                             'License Plate Detection_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'License Plate Detection_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'License Plate Detection_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         "Licence plate Detection"))
                except Exception as e:
                    exc.exception('Could not process Licence Plate detection {}'.format(e))
            if 'Social Distance' in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Social Distance_start_date'], self.rule['Social Distance_end_date'],
                        self.rule['Social Distance_start_time'], self.rule['Social Distance_end_time'])
                    social_dis_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            social_dis_time_status_flag = True
                        else:
                            social_dis_time_status_flag = False
                    if social_dis_time_status_flag:
                        self.social_distance_queue.put(out_img)
                        # pl_image=out_img.copy()
                        social_dis_res = pool.apply_async(social_distance_main, (self.social_distance_queue,))
                        social_dis_res_return_val = social_dis_res.get()
                        pl_image = social_dis_res_return_val[2]
                        social_val = social_dis_res_return_val[1]
                        out_img = social_dis_res_return_val[0]
                        social_elastic_list = social_dis_res_return_val[3]
                        if text_flag:
                            cv2.putText(out_img, "Social Distance Detection", (10, 25), cv2.FONT_HERSHEY_SIMPLEX,
                                        1.0, (0, 0, 0), 2)
                        if social_val:
                            alert_alarm = True
                        if image_flag and social_val:

                            social_dis_image_path = self.social_dictance_user_cam_path + "/SocialDistance{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(social_dis_image_path):
                                FolderView(self.social_dictance_user_cam_path).createfolder()
                                social_dictance_cam_path_original = self.social_dictance_user_cam_path + '/Original'
                                FolderView(social_dictance_cam_path_original).createfolder()
                                social_dictance_path_original = social_dictance_cam_path_original + "/SocialDistance{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(social_dis_image_path, out_img)
                                self.original_image_save(social_dictance_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Person", "SocialDistance",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      social_dis_image_path, "", "person",
                                                      social_dictance_path_original, "",
                                                      "False",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      social_elastic_list))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "SocialDistance",
                                                                                social_dis_image_path, "Person")
                                if email_flag and self.rule['Social Distance_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        social_dis_image_path, "Social Distance",
                                        self.rule['Social Distance_email_alarm_mode']["from"],
                                        self.rule['Social Distance_email_alarm_mode']["to"],
                                        self.rule['Social Distance_email_alarm_mode']["password"]))
                                if whatsapp_flag and self.rule['Social Distance_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (social_dis_image_path,
                                                                                         self.rule[
                                                                                             'Social Distance_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'Social Distance_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'Social Detection_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         'Social Distance'))
                except Exception as e:
                    trace.error()
                    exc.exception('Could not process social distance detection {}'.format(e))
            if 'Signal Violation' in self.rule['rule_type']:
                self.signal_queue.put(out_img)
                pl_image = out_img.copy()
                current_time, start_time, end_time = self.time_comp(
                    self.rule['Signal Violation_start_date'], self.rule['Signal Violation_end_date'],
                    self.rule['Signal Violation_start_time'], self.rule['Signal Violation_end_time'])
                sig_vio_time_status_flag = True
                if time_date_flag:
                    if (current_time >= start_time) and (current_time <= end_time):
                        sig_vio_time_status_flag = True
                    else:
                        sig_vio_time_status_flag = False
                if sig_vio_time_status_flag:
                    point1 = (1, 1)
                    point2 = (426, 233)
                    mouse_count = True
                    drawing = False
                    signal_violation_res = pool.apply_async(signal_violation_main, (
                        self.signal_queue, point1, point2, mouse_count, drawing, signal_violation_cascade,
                        signal_violation_net, signal_violation_class, signal_violation_colors, image_flag,
                        elasticsearch_flag, Db_image_flag, email_flag, self.signal_violation_user_cam_path, self.usr_id,
                        self.cam_name))
                    signal_violation_val = signal_violation_res.get()
                    writer_val = signal_violation_val[1]
                    out_img = signal_violation_val[0]
                    if writer_val:
                        alert_alarm = True
                    if writer_val and image_flag:
                        signal_violation_image_path = self.signal_violation_user_cam_path + "/Signalviolation{}.jpeg".format(
                            str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                        if not os.path.exists(signal_violation_image_path):
                            FolderView(self.signal_violation_user_cam_path).createfolder()
                            signal_violation_cam_path_original = self.signal_violation_user_cam_path + '/Original'
                            FolderView(signal_violation_cam_path_original).createfolder()
                            signal_violation_path_original = signal_violation_cam_path_original + "/Signalviolation{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            self.detect_image_save(signal_violation_image_path, out_img)
                            self.original_image_save(signal_violation_path_original, pl_image)
                            if elasticsearch_flag:
                                pool.apply_async(SearchEngine().engine_data,
                                                 (self.usr_id, self.cam_name, "Object", "SignalViolation",
                                                  str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                  "", "",
                                                  signal_violation_image_path, "", "",
                                                  signal_violation_path_original, "",
                                                  "True",
                                                  str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                  []))
                            if Db_image_flag:
                                analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                            "SignalViolation",
                                                                            signal_violation_image_path, "Object")
                            if email_flag and writer_val and self.rule['Signal Violation_email_alarm_mode']:
                                pool.apply_async(email_image_notification, (
                                    signal_violation_image_path, "SignalViolation",
                                    self.rule['Signal Violation_email_alarm_mode']["from"],
                                    self.rule['Signal Violation_email_alarm_mode']["to"],
                                    self.rule['Signal Violation_email_alarm_mode']["password"]))
                            if whatsapp_flag and self.rule['Signal Violation_whatsapp_alarm_mode']:
                                whatsapp_pool.apply_async(whats_image_notification, (signal_violation_image_path,
                                                                                     self.rule[
                                                                                         'Signal Violation_whatsapp_alarm_mode'][
                                                                                         "token"],
                                                                                     self.rule[
                                                                                         'Signal Violation_whatsapp_alarm_mode'][
                                                                                         'whatsapp'], self.rule[
                                                                                         'Signal Violation_whatsapp_alarm_mode'][
                                                                                         "instanceId"], self.cam_name,
                                                                                     'Signal Violation'))
            if 'Missing Object Detection' in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Missing Object Detection_start_date'],
                        self.rule['Missing Object Detection_end_date'],
                        self.rule['Missing Object Detection_start_time'],
                        self.rule['Missing Object Detection_end_time'])
                    miss_obj_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            miss_obj_time_status_flag = True
                        else:
                            miss_obj_time_status_flag = False
                    if miss_obj_time_status_flag:
                        msd_object_zones = self.rule['Missing Object Detection_zones']
                        frame = self.firstframe[84:2000, 0:1700]
                        frame1 = out_img1[84:2000, 0:1700]
                        pl_image = frame.copy()
                        msd_object_res = pool.apply_async(missing_object_detection_main,
                                                          (msd_object_zones, frame, frame1))
                        msd_object_res_return_val = msd_object_res.get()
                        msd_object = msd_object_res_return_val[1]
                        out_img = msd_object_res_return_val[0]
                        if text_flag:
                            cv2.putText(out_img, "Missing Object Detection", (10, 25), cv2.FONT_HERSHEY_SIMPLEX,
                                        1.0, (0, 0, 0), 2)
                        if msd_object:
                            alert_alarm = True

                        if msd_object and image_flag:
                            msd_object_image_path = self.msd_object_user_cam_path + "/MissingObjectDetection{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(msd_object_image_path):
                                FolderView(self.msd_object_user_cam_path).createfolder()
                                msd_object_cam_path_original = self.msd_object_user_cam_path + '/Original'
                                FolderView(msd_object_cam_path_original).createfolder()
                                msd_object_path_original = msd_object_cam_path_original + "/MissingObjectDetection{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save_quality(msd_object_image_path, out_img, 50)
                                self.original_image_save(msd_object_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Object", "MissingObjectDetection",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      msd_object_image_path, "", "",
                                                      msd_object_path_original, "",
                                                      "True",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "MissingObjectDetection",
                                                                                msd_object_image_path, "Object")
                                if email_flag and msd_object and self.rule['Missing Object Detection_alarm_mode']:
                                    pool.apply_async(email_image_notification,
                                                     (msd_object_image_path, "MissingObjectDetection",
                                                      self.rule['Missing Object Detection_alarm_mode']["from"],
                                                      self.rule['Missing Object Detection_alarm_mode']["to"],
                                                      self.rule['Missing Object Detection_alarm_mode']["password"]))
                                if whatsapp_flag and msd_object and self.rule[
                                    'Missing Object Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (msd_object_image_path,
                                                                                         self.rule[
                                                                                             'Missing Object Detection_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'Missing Object Detection_whatsapp_alarm_mode'][
                                                                                             'whatsapp'],
                                                                                         self.rule[
                                                                                             'Missing Object Detection_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         'Missing Object Detection'))
                except Exception as e:
                    exc.exception('Could not process left object detection {}'.format(e))
            if 'Mask Detection' in self.rule['rule_type']:
                try:
                    self.Mask_Detection_queue.put(out_img)
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Mask Detection_start_date'], self.rule['Mask Detection_end_date'],
                        self.rule['Mask Detection_start_time'], self.rule['Mask Detection_end_time'])
                    mask_detection_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            mask_detection_time_status_flag = True
                        else:
                            mask_detection_time_status_flag = False
                    if mask_detection_time_status_flag:
                        mask_res = pool.apply_async(mask_detection().mask_detection_main, (
                            out_img, self.Mask_Detection_queue))
                        pl_image = out_img.copy()
                        mask_res_return_val = mask_res.get()
                        mask_val = mask_res_return_val[1]
                        out_img = mask_res_return_val[0]
                        if text_flag:
                            cv2.putText(out_img, "Mask Detection", (10, 25), cv2.FONT_HERSHEY_SIMPLEX,
                                        1.0, (0, 0, 0), 2)
                        if mask_val:
                            alert_alarm = True
                        if image_flag and mask_val:
                            mask_detection_image_path = self.Mask_Detection_user_cam_path + "/Mask_Detection{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(mask_detection_image_path):
                                FolderView(self.Mask_Detection_user_cam_path).createfolder()
                                Mask_Detection_cam_path_original = self.Mask_Detection_user_cam_path + '/Original'
                                FolderView(Mask_Detection_cam_path_original).createfolder()
                                Mask_Detection_path_original = Mask_Detection_cam_path_original + "/Mask_Detection{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(mask_detection_image_path, out_img)
                                self.original_image_save(Mask_Detection_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Person", "MaskDetection",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      mask_detection_image_path, "", "",
                                                      Mask_Detection_path_original, "",
                                                      "True",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "MaskDetection",
                                                                                mask_detection_image_path, "Person")
                                if email_flag and self.rule['Mask Detection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        mask_detection_image_path, "Mask Detection",
                                        self.rule['Mask Detection_email_alarm_mode']["from"],
                                        self.rule['Mask Detection_email_alarm_mode']["to"],
                                        self.rule['Mask Detection_email_alarm_mode']["password"]))
                                if whatsapp_flag and self.rule['Mask Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (mask_detection_image_path,
                                                                                         self.rule[
                                                                                             'Mask Detection_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'Mask Detection_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'Mask Detection_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         'Mask Detection'))
                except Exception as e:
                    trace.error()
                    exc.exception('Could not process Mask detection {}'.format(e))
            if 'Wrong Direction' in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Wrong Direction_start_date'],
                        self.rule['Wrong Direction_end_date'],
                        self.rule['Wrong Direction_start_time'],
                        self.rule['Wrong Direction_end_time'])
                    wrong_detect_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            wrong_detect_time_status_flag = True
                        else:
                            wrong_detect_time_status_flag = False
                    if wrong_detect_time_status_flag:

                        # line coordiantes getting from the UI
                        # wrongdir_zones = self.rule['WrongDirection_zones'][0] + self.rule['WrongDirection_zones'][1]
                        wrongdir_zones = self.rule['Wrong Direction_zones']
                        wrongdir_height = self.rule['Wrong Direction_img_h']
                        wrongdir_width = self.rule['Wrong Direction_img_w']
                        # Mention the direction which you wants to allow
                        target = self.rule['Wrong Direction_direction']
                        if target.lower() == 'forward':
                            target = "up"
                        if target.lower() == 'backward':
                            target = "down"
                        self.WrongDirection_queue.put(out_img)
                        pl_image = out_img.copy()
                        wrong_res = openvino_pool.apply_async(wrong_dir_main, (
                            self.WrongDirection_queue, wrongdir_zones, target, wrongdir_height, wrongdir_width))
                        wrong_res_return_val = wrong_res.get()
                        wrong_img = wrong_res_return_val[1]
                        out_img = wrong_res_return_val[0]
                        if text_flag:
                            cv2.putText(out_img, "Person crossing the restricted line", (10, 25),
                                        cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
                        if wrong_img and image_flag:
                            wrong_image_path = self.wrong_user_cam_path + "/WrongDirection{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(wrong_image_path):
                                FolderView(self.wrong_user_cam_path).createfolder()
                                wrong_user_cam_path_original = self.wrong_user_cam_path + '/Original'
                                FolderView(wrong_user_cam_path_original).createfolder()
                                wrong_user_image_path_original = wrong_user_cam_path_original + "/wrong{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                self.detect_image_save(wrong_image_path, out_img)
                                self.original_image_save(wrong_user_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Person", "WrongDirection",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      wrong_image_path, "", "",
                                                      wrong_user_image_path_original, "",
                                                      "True",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                      []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "WrongDirection",
                                                                                wrong_image_path, "Person")
                                if email_flag and wrong_img and self.rule['Wrong Direction_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        wrong_image_path, "WrongDetection",
                                        self.rule['Wrong Direction_email_alarm_mode']["from"],
                                        self.rule['Wrong Direction_email_alarm_mode']["to"],
                                        self.rule['Wrong Direction_email_alarm_mode']["password"]))
                                if whatsapp_flag and self.rule['Wrong Direction_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (wrong_image_path,
                                                                                         self.rule[
                                                                                             'Wrong Direction_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'Wrong Direction_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'Wrong Direction_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         'Wrong Direction'))

                except Exception as e:
                    exc.exception('Could not process Wrong Direction detection {}'.format(e))
            if 'Missing Object Detection Area' in self.rule['rule_type']:
                try:
                    msd_object_zones = self.rule['Missing Object Detection Area_zones']
                    img_h = self.rule['Missing Object Detection Area_img_h']
                    img_w = self.rule['Missing Object Detection Area_img_w']
                    frame = cv2.resize(self.firstframe, (img_w, img_h))
                    frame1 = cv2.resize(out_img1, (img_w, img_h))
                    pl_image = frame.copy()
                    msd_object_res = pool.apply_async(missing_object_detection_main_area,
                                                      (msd_object_zones, frame, frame1))
                    msd_object_res_return_val = msd_object_res.get()
                    msd_object = msd_object_res_return_val[1]
                    out_img = msd_object_res_return_val[0]
                    if text_flag:
                        cv2.putText(out_img, "Missing Object Detection Area", (10, 25), cv2.FONT_HERSHEY_SIMPLEX,
                                    1.0, (0, 0, 0), 2)
                    if msd_object:
                        alert_alarm = True

                    if msd_object and image_flag:
                        msd_object_image_path = self.msd_object_area_user_cam_path + "/MissingObjectDetectionArea{}.jpeg".format(
                            str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                        if not os.path.exists(msd_object_image_path):
                            FolderView(self.msd_object_area_user_cam_path).createfolder()
                            cv2.imwrite(msd_object_image_path, out_img, [cv2.IMWRITE_JPEG_QUALITY, 50])
                            msd_object_area_user_cam_path_original = self.msd_object_area_user_cam_path + '/Original'
                            FolderView(msd_object_area_user_cam_path_original).createfolder()
                            msd_object_area_user_image_path_original = msd_object_area_user_cam_path_original + "/MissingObjectDetectionArea{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            cv2.imwrite(msd_object_area_user_image_path_original, pl_image)
                            if elasticsearch_flag:
                                pool.apply_async(SearchEngine().engine_data,
                                                 (self.usr_id, self.cam_name, "Object", "MissingObjectDetectionArea",
                                                  str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                  "", "",
                                                  msd_object_image_path, "", "",
                                                  msd_object_area_user_image_path_original, "",
                                                  "True",
                                                  str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                  []))
                            if Db_image_flag:
                                analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                            "MissingObjectDetectionArea",
                                                                            msd_object_image_path, "Object")
                            if email_flag and msd_object and self.rule[
                                'Missing Object Detection Area_email_alarm_mode']:
                                pool.apply_async(email_image_notification,
                                                 (msd_object_image_path, "MissingObjectDetectionArea",
                                                  self.rule[
                                                      'Missing Object Detection Area_email_alarm_mode'][
                                                      "from"], self.rule[
                                                      'Missing Object Detection Area_email_alarm_mode'][
                                                      "to"], self.rule[
                                                      'Missing Object Detection Area_email_alarm_mode'][
                                                      "password"]))
                            if whatsapp_flag and self.rule['Missing Object Detection Area_whatsapp_alarm_mode']:
                                whatsapp_pool.apply_async(whats_image_notification, (msd_object_image_path,
                                                                                     self.rule[
                                                                                         'Missing Object Detection Area_whatsapp_alarm_mode'][
                                                                                         "token"],
                                                                                     self.rule[
                                                                                         'Missing Object Detection Area_whatsapp_alarm_mode'][
                                                                                         'whatsapp'], self.rule[
                                                                                         'Missing Object Detection Area_whatsapp_alarm_mode'][
                                                                                         "instanceId"], self.cam_name,
                                                                                     'Missing Object Detection Area'))
                except Exception as e:
                    exc.exception('Could not process left object detection {}'.format(e))
            if 'VehicleWrongDirection' in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['VehicleWrongDirection_start_date'], self.rule['Vehiclewrongdirection_end_date'],
                        self.rule['VehicleWrongDirection_start_time'], self.rule['Vehiclewrongdirection_end_time'])

                    vehiclewrongdirection_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            vehiclewrongdirection_time_status_flag = True
                        else:
                            vehiclewrongdirection_time_status_flag = False
                    if vehiclewrongdirection_time_status_flag:
                        direction = self.rule['VehicleWrongDirection_direction']
                        self.VehicleWrongDirection_queue.put(out_img)
                        pl_image = out_img.copy()
                        wwp_res = openvino_pool.apply_async(vehicleWrongdirection_main,
                                                            (self.VehicleWrongDirection_queue, direction))
                        wwp_res_return_val = wwp_res.get()
                        writer_val = wwp_res_return_val[1]
                        out_img = wwp_res_return_val[0]
                        position_wwp = (10, 25)
                        cv2.putText(out_img, "Going Wrong Way Detection", position_wwp, cv2.FONT_HERSHEY_SIMPLEX,
                                    1.0, (0, 0, 0), 2)

                        if writer_val:
                            alert_alarm = True

                        if writer_val and image_flag:
                            wwp_image_path = self.VehicleWrongdirection_user_cam_path + "/VehicleWrongDirection{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(wwp_image_path):
                                FolderView(self.VehicleWrongdirection_user_cam_path).createfolder()
                                cv2.imwrite(wwp_image_path, out_img, [cv2.IMWRITE_JPEG_QUALITY, 50])
                                VehicleWrongdirection_user_cam_path_original = self.VehicleWrongdirection_user_cam_path + '/Original'
                                FolderView(VehicleWrongdirection_user_cam_path_original).createfolder()
                                VehicleWrongdirection_user_image_path_original = VehicleWrongdirection_user_cam_path_original + "/VehicleWrongDirection{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                cv2.imwrite(VehicleWrongdirection_user_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (
                                                     self.usr_id, self.cam_name, "Object", "VehicleWrongDirection",
                                                     str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                     "", "",
                                                     wwp_image_path, "", "",
                                                     VehicleWrongdirection_user_image_path_original, "",
                                                     "True",
                                                     str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                     []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "VehicleWrongDirection", wwp_image_path,
                                                                                "Object")
                                if email_flag and writer_val and self.rule['VehicleWrongDirection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (wwp_image_path, "VehicleWrongDirection",
                                                                                self.rule[
                                                                                    'VehicleWrongDirection_email_alarm_mode'][
                                                                                    "from"],
                                                                                self.rule[
                                                                                    'VehicleWrongDirection_email_alarm_mode'][
                                                                                    "to"],
                                                                                self.rule[
                                                                                    'VehicleWrongDirection_email_alarm_mode'][
                                                                                    "password"]))
                                if whatsapp_flag and writer_val and self.rule[
                                    'VehicleWrongDirection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (wwp_image_path,
                                                                                         self.rule[
                                                                                             'VehicleWrongDirection_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'VehicleWrongDirection_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'VehicleWrongDirection_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         "VehicleWrongDirection"))
                except Exception as e:
                    exc.exception('Could not process VehicleWrongDirection {}'.format(e))
            if "Vehicle Tripwire" in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Vehicle Tripwire_start_date'], self.rule['Vehicle Tripwire_end_date'],
                        self.rule['Vehicle Tripwire_start_time'], self.rule['Vehicle Tripwire_end_time'])
                    vehicle_tripwire_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            vehicle_tripwire_time_status_flag = True
                        else:
                            vehicle_tripwire_time_status_flag = False
                    if vehicle_tripwire_time_status_flag:
                        zones = self.rule['Vehicle Tripwire_zones'][0] + self.rule['Vehicle Tripwire_zones'][1]
                        height = self.rule['Vehicle Tripwire_img_h']
                        width = self.rule['Vehicle Tripwire_img_w']
                        # trip_res = pool.apply_async(tripwire_main, (out_img, zones, height, width))
                        self.VehicleTripwire_queue.put(out_img)
                        pl_image = out_img.copy()
                        vehicle_trip_res = openvino_pool.apply_async(VehicleTripwire,
                                                                     (self.VehicleTripwire_queue, zones, height, width))
                        vehicle_trip_res_return_val = vehicle_trip_res.get()
                        writer_val = vehicle_trip_res_return_val[1]
                        out_img = vehicle_trip_res_return_val[0]
                        position_vehicle_trip = (10, 25)
                        cv2.putText(out_img, "Vehicle crossing the restricted line", position_vehicle_trip,
                                    cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
                        if writer_val and image_flag:
                            vehicle_trip_image_path = self.vehicle_trip_user_cam_path + "/VehicleTripwire{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(vehicle_trip_image_path):
                                FolderView(self.vehicle_trip_user_cam_path).createfolder()
                                cv2.imwrite(vehicle_trip_image_path, out_img, [cv2.IMWRITE_JPEG_QUALITY, 50])
                                vehicle_trip_user_cam_path_original = self.vehicle_trip_user_cam_path + '/Original'
                                FolderView(vehicle_trip_user_cam_path_original).createfolder()
                                vehicle_trip_user_image_path_original = vehicle_trip_user_cam_path_original + "/VehicleTripwire{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                cv2.imwrite(vehicle_trip_user_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (
                                                         self.usr_id, self.cam_name, "Object", "VehicleTripwire",
                                                         str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                         "", "",
                                                         vehicle_trip_image_path, "", "",
                                                         vehicle_trip_user_image_path_original, "",
                                                         "True",
                                                         str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                         []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "VehicleTripwire",
                                                                                vehicle_trip_image_path, "Object")
                                if email_flag and writer_val and self.rule['Vehicle Tripwire_email_alarm_mode']:
                                    pool.apply_async(email_image_notification,
                                                     (vehicle_trip_image_path, "VehicleTripwire",
                                                      self.rule['Vehicle Tripwire_email_alarm_mode'][
                                                          "from"],
                                                      self.rule['Vehicle Tripwire_email_alarm_mode'][
                                                          "to"],
                                                      self.rule['Vehicle Tripwire_email_alarm_mode'][
                                                          "password"]))
                                if whatsapp_flag and writer_val and self.rule['Vehicle Tripwire_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (vehicle_trip_image_path,
                                                                                         self.rule[
                                                                                             'Vehicle Tripwire_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'Vehicle Tripwire_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'Vehicle Tripwire_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         "Vehicle Tripwire"))

                except Exception as e:
                    exc.exception('Could not process Vehicle Tripwire detection {}'.format(e))
            if 'Vehicle Tresspass' in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(self.rule['Vehicle Tresspass_start_date'],
                                                                        self.rule['Vehicle Tresspass_end_date'],
                                                                        self.rule['Vehicle Tresspass_start_time'],
                                                                        self.rule['Vehicle Tresspass_end_time'])
                    vehicletresspass_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            vehicletresspass_time_status_flag = True
                        else:
                            vehicletresspass_time_status_flag = False
                    if vehicletresspass_time_status_flag:
                        vehicletresspass_zones = self.rule['Vehicle Tresspass_zones']
                        vehicletresspass_height = self.rule['Vehicle Tresspass_img_h']
                        vehicletresspass_width = self.rule['Vehicle Tresspass_img_w']
                        vehicletresspass_image_shape = (
                            vehicle_tresspass_input_shape_det[_W], vehicle_tresspass_input_shape_det[_H])
                        vehicletresspass_image_re_shape = (
                            vehicleTresspass_input_shape_reid[_W], vehicleTresspass_input_shape_reid[_H])
                        self.VehicleTresspass_queue.put(out_img)
                        pl_image = out_img.copy()
                        vehicletresspass_res = openvino_pool.apply_async(Vehicle_tresspass_main, (
                            self.VehicleTresspass_queue, vehicletresspass_zones, vehicletresspass_image_shape,
                            vehicle_tresspass_input_shape_det, vehicle_tresspass_exec_net_det,
                            vehicle_tresspass_input_name_det, vehicle_tresspass_out_name_det,
                            vehicletresspass_image_re_shape, vehicleTresspass_input_shape_reid,
                            vehicleTresspass_exec_net_reid, vehicleTresspass_input_name_reid,
                            vehicleTresspass_out_name_reid, vehicletresspass_height,
                            vehicletresspass_width))
                        vehicletresspass_res_return_val = vehicletresspass_res.get()
                        writer_val = vehicletresspass_res_return_val[1]
                        out_img = vehicletresspass_res_return_val[0]
                        position_vehicletresspass = (10, 25)
                        cv2.putText(out_img, "Vehicle Inside Restricted Area", position_vehicletresspass,
                                    cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
                        if writer_val:
                            alert_alarm = True
                        if writer_val and image_flag:
                            vehicletresspass_image_path = self.vehicleTresspass_user_cam_path + "/VehicleTresspass{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(vehicletresspass_image_path):
                                FolderView(self.vehicleTresspass_user_cam_path).createfolder()
                                cv2.imwrite(vehicletresspass_image_path, out_img, [cv2.IMWRITE_JPEG_QUALITY, 50])
                                vehicleTresspass_user_cam_path_original = self.vehicleTresspass_user_cam_path + '/Original'
                                FolderView(vehicleTresspass_user_cam_path_original).createfolder()
                                vehicleTresspass_user_image_path_original = vehicleTresspass_user_cam_path_original + "/VehicleTresspass{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                cv2.imwrite(vehicleTresspass_user_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (
                                                         self.usr_id, self.cam_name, "Object", "VehicleTresspass",
                                                         str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                         "", "",
                                                         vehicletresspass_image_path, "", "",
                                                         vehicleTresspass_user_image_path_original, "",
                                                         "True",
                                                         str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                         []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "VehicleTresspass",
                                                                                vehicletresspass_image_path, "Object")
                                if email_flag and writer_val and self.rule['Vehicle Tresspass_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        vehicletresspass_image_path, "Vehicle Trespass",
                                        self.rule['Vehicle Tresspass_email_alarm_mode']["from"],
                                        self.rule['Vehicle Tresspass_email_alarm_mode']["to"],
                                        self.rule['Vehicle Tresspass_email_alarm_mode']["password"]))
                                if whatsapp_flag and writer_val and self.rule['Vehicle Tresspass_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification, (vehicletresspass_image_path,
                                                                                         self.rule[
                                                                                             'Vehicle Tresspass_whatsapp_alarm_mode'][
                                                                                             "token"],
                                                                                         self.rule[
                                                                                             'Vehicle Tresspass_whatsapp_alarm_mode'][
                                                                                             'whatsapp'], self.rule[
                                                                                             'Vehicle Tresspass_whatsapp_alarm_mode'][
                                                                                             "instanceId"],
                                                                                         self.cam_name,
                                                                                         'Vehicle Tresspass'))
                except Exception as e:
                    exc.exception('Could not process vehicleTresspass detection {}'.format(e))
            if 'Vehicle Line Cross Count' in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Vehicle Line Cross Count_start_date'],
                        self.rule['Vehicle Line Cross Count_end_date'],
                        self.rule['Vehicle Line Cross Count_start_time'],
                        self.rule['Vehicle Line Cross Count_end_time'])
                    vehiclelinecrossingcount_detect_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            vehiclelinecrossingcount_detect_time_status_flag = True
                        else:
                            vehiclelinecrossingcount_detect_time_status_flag = False
                    if vehiclelinecrossingcount_detect_time_status_flag:

                        # line coordiantes getting from the UI
                        # wrongdir_zones = self.rule['WrongDirection_zones'][0] + self.rule['WrongDirection_zones'][1]
                        vehiclelinecrossingcount_zones = self.rule['Vehicle Line Cross Count_zones']
                        vehiclelinecrossingcount_height = self.rule['Vehicle Line Cross Count_img_h']
                        vehiclelinecrossingcount_width = self.rule['Vehicle Line Cross Count_img_w']
                        # Mention the direction which you wants to allow

                        # target can be get from UI also using the direction dropdown button, Don't forgot to uncomment the below line the get the value from the UI
                        # target = self.rule['WrongDirectioneffection_target']
                        self.VehicleLineCrossCount_queue.put(out_img)
                        pl_image = out_img.copy()
                        vehiclelinecrossingcount_res = openvino_pool.apply_async(vehicleLineCrossingCount_dir_main, (
                            self.VehicleLineCrossCount_queue, vehiclelinecrossingcount_zones,
                            vehiclelinecrossingcount_height, vehiclelinecrossingcount_width, self.Vehicle_dict_cnt))
                        vehiclelinecrossingcount_res_return_val = vehiclelinecrossingcount_res.get()
                        vehiclelinecrossingcount_img = vehiclelinecrossingcount_res_return_val[1]
                        out_img = vehiclelinecrossingcount_res_return_val[0]
                        self.Vehicle_dict_cnt = vehiclelinecrossingcount_res_return_val[2]
                        position_vehiclelinecrossingcount = (10, 25)
                        cv2.putText(out_img, "VehicleLineCrossCount",
                                    position_vehiclelinecrossingcount,
                                    cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
                        if vehiclelinecrossingcount_img and image_flag:
                            vehiclelinecrossingcount_image_path = self.vehicleLineCrossingCount_user_cam_path + "/VehicleLineCrossCount{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(vehiclelinecrossingcount_image_path):
                                FolderView(self.vehicleLineCrossingCount_user_cam_path).createfolder()
                                cv2.imwrite(vehiclelinecrossingcount_image_path, out_img,
                                            [cv2.IMWRITE_JPEG_QUALITY, 50])
                                vehicleLineCrossingCount_user_cam_path_original = self.vehicleLineCrossingCount_user_cam_path + '/Original'
                                FolderView(vehicleLineCrossingCount_user_cam_path_original).createfolder()
                                vehicleLineCrossingCount_user_image_path_original = vehicleLineCrossingCount_user_cam_path_original + "/VehicleLineCrossCount{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                cv2.imwrite(vehicleLineCrossingCount_user_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (
                                                         self.usr_id, self.cam_name, "Object", "VehicleLineCrossCount",
                                                         str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                         "", "",
                                                         vehiclelinecrossingcount_image_path, "", "",
                                                         vehicleLineCrossingCount_user_image_path_original, "",
                                                         "True",
                                                         str(datetime.datetime.now().strftime("%H:%M:%S")),
                                                         []))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "VehicleLineCrossCount",
                                                                                vehiclelinecrossingcount_image_path,
                                                                                "Object")
                                if email_flag and vehiclelinecrossingcount_img and self.rule[
                                    'Vehicle Line Cross Count_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        vehiclelinecrossingcount_image_path, "VehicleLineCrossCount",
                                        self.rule['Vehicle Line Cross Count_email_alarm_mode']["from"],
                                        self.rule['Vehicle Line Cross Count_email_alarm_mode']["to"],
                                        self.rule['Vehicle Line Cross Count_email_alarm_mode']["password"]))
                                if whatsapp_flag and vehiclelinecrossingcount_img and self.rule[
                                    'Vehicle Line Cross Count_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification,
                                                              (vehiclelinecrossingcount_image_path,
                                                               self.rule[
                                                                   'Vehicle Line Cross Count_whatsapp_alarm_mode'][
                                                                   "token"],
                                                               self.rule[
                                                                   'Vehicle Line Cross Count_whatsapp_alarm_mode'][
                                                                   'whatsapp'], self.rule[
                                                                   'Vehicle Line Cross Count_whatsapp_alarm_mode'][
                                                                   "instanceId"], self.cam_name,
                                                               'Vehicle Line Cross Count'))
                except Exception as e:
                    exc.exception('Could not process vehicleLineCrossCount {}'.format(e))
            if 'Person Line Cross Count' in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Person Line Cross Count_start_date'],
                        self.rule['Person Line Cross Count_end_date'],
                        self.rule['Person Line Cross Count_start_time'],
                        self.rule['Person Line Cross Count_end_time'])
                    personlinecrossingcount_detect_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            personlinecrossingcount_detect_time_status_flag = True
                        else:
                            personlinecrossingcount_detect_time_status_flag = False
                    if personlinecrossingcount_detect_time_status_flag:

                        # line coordiantes getting from the UI
                        # wrongdir_zones = self.rule['WrongDirection_zones'][0] + self.rule['WrongDirection_zones'][1]
                        person_line_crossing_count_zones = self.rule['Person Line Cross Count_zones']
                        person_line_crossing_count_height = self.rule['Person Line Cross Count_img_h']
                        person_line_crossing_count_width = self.rule['Person Line Cross Count_img_w']
                        # Mention the direction which you wants to allow

                        # target can be get from UI also using the direction dropdown button, Don't forgot to uncomment the below line the get the value from the UI
                        # target = self.rule['WrongDirectioneffection_target']
                        self.PersonLineCrossCount_queue.put(out_img)
                        # pl_image = out_img.copy()
                        personlinecrossingcount_res = openvino_pool.apply_async(
                            personlinecrosscount().personLineCrossingCount_dir_main,
                            (self.PersonLineCrossCount_queue,
                             person_line_crossing_count_zones,
                             person_line_crossing_count_height,
                             person_line_crossing_count_width,
                             self.Person_dict_cnt))
                        personlinecrossingcount_res_return_val = personlinecrossingcount_res.get()
                        person_line_crossing_count_img = personlinecrossingcount_res_return_val[1]
                        out_img = personlinecrossingcount_res_return_val[0]
                        self.Person_dict_cnt = personlinecrossingcount_res_return_val[2]
                        elastic_list = personlinecrossingcount_res_return_val[3]
                        pl_image = personlinecrossingcount_res_return_val[4]
                        # position_person_line_crossing_count = (10, 25)
                        # cv2.putText(out_img, "PersonLineCrossCount", position_person_line_crossing_count,
                        #             cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
                        if person_line_crossing_count_img and image_flag:
                            personlinecrossingcount_image_path = self.personLineCrossingCount_user_cam_path + "/PersonLineCrossCount{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(personlinecrossingcount_image_path):
                                FolderView(self.personLineCrossingCount_user_cam_path).createfolder()
                                cv2.imwrite(personlinecrossingcount_image_path, out_img, [cv2.IMWRITE_JPEG_QUALITY, 50])
                                personLineCrossingCount_user_cam_path_original = self.personLineCrossingCount_user_cam_path + '/Original'
                                FolderView(personLineCrossingCount_user_cam_path_original).createfolder()
                                personLineCrossingCount_user_image_path_original = personLineCrossingCount_user_cam_path_original + "/PersonLineCrossCount{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                cv2.imwrite(personLineCrossingCount_user_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data,
                                                     (self.usr_id, self.cam_name, "Person", "PersonLineCrossCount",
                                                      str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")), "",
                                                      "", "",
                                                      personlinecrossingcount_image_path, "", "person",
                                                      personLineCrossingCount_user_image_path_original, "",
                                                      "False",
                                                      str(datetime.datetime.now().strftime("%H:%M:%S")), elastic_list))

                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "PersonLineCrossCount",
                                                                                personlinecrossingcount_image_path,
                                                                                "Person")
                                if email_flag and person_line_crossing_count_img and self.rule[
                                    'Person Line Cross Count_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        personlinecrossingcount_image_path, "PersonLineCrossCount",
                                        self.rule['Person Line Cross Count_email_alarm_mode']["from"],
                                        self.rule['Person Line Cross Count_email_alarm_mode']["to"],
                                        self.rule['Person Line Cross Count_email_alarm_mode']["password"]))
                                if whatsapp_flag and person_line_crossing_count_img and self.rule[
                                    'Person Line Cross Count_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification,
                                                              (personlinecrossingcount_image_path,
                                                               self.rule[
                                                                   'Person Line Cross Count_whatsapp_alarm_mode'][
                                                                   "token"],
                                                               self.rule[
                                                                   'Person Line Cross Count_whatsapp_alarm_mode'][
                                                                   'whatsapp'],
                                                               self.rule['Person Line Cross Count_whatsapp_alarm_mode'][
                                                                   "instanceId"], self.cam_name,
                                                               'Person Line Cross Count'))
                except Exception as e:
                    exc.exception('Could not process PersonLineCrossingCount {}'.format(e))
            if 'Helmet Detection' in self.rule['rule_type']:
                try:
                    current_time, start_time, end_time = self.time_comp(
                        self.rule['Helmet Detection_start_date'],
                        self.rule['Helmet Detection_end_date'],
                        self.rule['Helmet Detection_start_time'],
                        self.rule['Helmet Detection_end_time'])

                    rider_helmet__detection_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            rider_helmet__detection_time_status_flag = True
                        else:
                            rider_helmet__detection_time_status_flag = False
                    if rider_helmet__detection_time_status_flag:
                        self.Rider_helmet_licenseplate_detection_queue.put(out_img)
                        pl_image = out_img.copy()
                        helmet_res = pool.apply_async(object_detection,
                                                      (self.Rider_helmet_licenseplate_detection_queue,))
                        helmet_res_return_val = helmet_res.get()
                        helmet_val = helmet_res_return_val[1]
                        out_img = helmet_res_return_val[0]
                        position = (10, 25)
                        cv2.putText(out_img, "Helmet Detection", position, cv2.FONT_HERSHEY_SIMPLEX,
                                    1.0, (0, 0, 0), 2)
                        if helmet_val:
                            alert_alarm = True
                        if image_flag and helmet_val:
                            rider_helmet_detection_image_path = self.Rider_helmet_licenseplate_detection_user_cam_path + "/Helmet_Detection{}.jpeg".format(
                                str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                            if not os.path.exists(rider_helmet_detection_image_path):
                                FolderView(self.Rider_helmet_licenseplate_detection_user_cam_path).createfolder()
                                cv2.imwrite(rider_helmet_detection_image_path, out_img,
                                            [cv2.IMWRITE_JPEG_QUALITY, 50])
                                Rider_helmet_licenseplate_user_cam_path_original = self.Rider_helmet_licenseplate_detection_user_cam_path + '/Original'
                                FolderView(Rider_helmet_licenseplate_user_cam_path_original).createfolder()
                                Rider_helmet_licenseplate_user_image_path_original = Rider_helmet_licenseplate_user_cam_path_original + "/Helmet_Detection{}.jpeg".format(
                                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                                cv2.imwrite(Rider_helmet_licenseplate_user_image_path_original, pl_image)
                                if elasticsearch_flag:
                                    pool.apply_async(SearchEngine().engine_data, (
                                        self.usr_id, self.cam_name, "Person", "Helmet Detection",
                                        str(datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")),
                                        "",
                                        "",
                                        "", rider_helmet_detection_image_path, "",
                                        Rider_helmet_licenseplate_user_image_path_original, "", "",
                                        str(datetime.datetime.now().strftime("%H:%M:%S"))))
                                if Db_image_flag:
                                    analytics_detector.insert_predicted_details(self.usr_id, self.cam_name,
                                                                                "Helmet Detection",
                                                                                rider_helmet_detection_image_path,
                                                                                "Person")
                                if email_flag and self.rule['Helmet Detection_email_alarm_mode']:
                                    pool.apply_async(email_image_notification, (
                                        rider_helmet_detection_image_path,
                                        "Helmet Detection",
                                        self.rule['Helmet Detection_email_alarm_mode']["from"],
                                        self.rule['Helmet Detection_email_alarm_mode']["to"],
                                        self.rule['Helmet Detection_email_alarm_mode']["password"]))
                                if whatsapp_flag and self.rule['Helmet Detection_whatsapp_alarm_mode']:
                                    whatsapp_pool.apply_async(whats_image_notification,
                                                              (rider_helmet_detection_image_path,
                                                               self.rule[
                                                                   'Helmet Detection_whatsapp_alarm_mode'][
                                                                   "token"],
                                                               self.rule[
                                                                   'Helmet Detection_whatsapp_alarm_mode'][
                                                                   'whatsapp'], self.rule[
                                                                   'Helmet Detection_whatsapp_alarm_mode'][
                                                                   "instanceId"],
                                                               self.cam_name,
                                                               'Helmet Detection'))
                except Exception as e:
                    trace.error()
                    exc.exception('Could not process Helmet Detection {}'.format(e))

            if 'Flame Detection' in self.rule['rule_type']:
                try:
                    self.flame_queue.put(out_img)
                    current_time, start_time, end_time = self.time_comp(self.rule['Flame Detection_start_date'],
                                                                        self.rule['Flame Detection_end_date'],
                                                                        self.rule['Flame Detection_start_time'],
                                                                        self.rule['Flame Detection_end_time'])
                    flame_detection_time_status_flag = True
                    if time_date_flag:
                        if (current_time >= start_time) and (current_time <= end_time):
                            flame_detection_time_status_flag = True
                        else:
                            flame_detection_time_status_flag = False
                    if flame_detection_time_status_flag:
                        zones = self.rule['Flame Detection_zones']
                        height = self.rule['Flame Detection_img_h']
                        width = self.rule['Flame Detection_img_w']
                        image_shape = (width, height)
                        flame_res = openvino_pool.apply_async(flame_detection, (self.flame_queue,))
                        flame_res_return_val = flame_res.get()
                        flame_val = flame_res_return_val[1]
                        out_img = flame_res_return_val[0]
                        if flame_val:
                            row, col = out_img.shape[:2]
                            bottom = out_img[row - 2:row, 0:col]
                            bordersize = 10
                            out_img = cv2.copyMakeBorder(
                                out_img,
                                top=bordersize,
                                bottom=bordersize,
                                left=bordersize,
                                right=bordersize,
                                borderType=cv2.BORDER_CONSTANT,
                                value=[0, 0, 255]
                            )
                        alert_alarm = True
                except Exception as e:
                    exc.exception('Could not process flame detection {}'.format(e))
            return out_img, alert_alarm
        except Exception as e:
            print(e)

    def back_ground_process(self):
        try:
            alert_alarm = False
            while True:
                if self.main_queue.empty() is not True:
                    out_img = self.main_queue.get()
                    predict_image = live_pool.apply_async(self.analytics_process, (out_img, out_img, alert_alarm))
                    predict_image_return_val = predict_image.get()
                    out_img1 = predict_image_return_val[0]
                    alert_alarm = predict_image_return_val[1]
                    if alert_alarm and audio_enable_flag:
                        print("alarm playing")
                        t1 = threading.Thread(target=alarm_sd)
                        t1.start()
        except Exception as e:
            print(e)

    def survillence_live_feed(self):
        try:
            trace.info('Starting Live Detection..')
            FolderView(self.output_user_path).createfolder()
            FolderView(self.output_user_cam_path).createfolder()
            if self.url.__contains__('.mp4') and self.rule['rule_type'][0] != 'Object Detection':
                count = 0
                self.cap_line = cv2.VideoCapture(self.url)
            elif self.rule['rule_type'][0] == "Movement Detection" or self.rule['rule_type'][0] == "Left Object Detection" or self.rule['rule_type'][0] == "Missing Object Detection" or self.rule['rule_type'][0] == "Missing Object Detection Area":
                self.cap_line = VideoCapture1(self.url)
            else:
                self.cap_line = VideoCapture(self.url)
            self.cur_time = time.time()  # Get current time
            self.start_time_1min = self.cur_time - 59
            try:
                while True:
                    try:
                        alert_alarm = False
                        cam_flag = False
                        if self.url.__contains__('.mp4') and self.rule['rule_type'][0] != 'Object Detection':
                            # self.cap_line.set(cv2.CAP_PROP_POS_MSEC, (count * 1080))
                            original, out_img = self.cap_line.read()
                            original, out_img1 = self.cap_line.read()
                            # count = count + 1
                        elif self.rule['rule_type'][0] == "Movement Detection" or self.rule['rule_type'][0] == "Left Object Detection" or self.rule['rule_type'][0] == "Missing Object Detection" or self.rule['rule_type'][0] == "Missing Object Detection Area":
                            out_img = self.cap_line.read()
                            out_img1 = self.cap_line.read()
                        else:
                            cam_flag, out_img = self.cap_line.getstream()
                            out_img1 = ''
                            # _, out_img1 = self.cap_line.getstream()
                        if out_img is not None and cam_flag is not True:
                            #     self.main_queue.put(out_img)
                            #     if not self.main_back:
                            #         p1 = threading.Thread(target=self.back_ground_process)
                            #         p1.name = "prediction_process"
                            #         p1.daemon = True
                            #         p1.start()
                            #         self.main_back.append(p1)
                            predict_image = live_pool.apply_async(self.analytics_process,
                                                                  (out_img, out_img1, alert_alarm))
                            predict_image_return_val = predict_image.get()
                            out_img1 = predict_image_return_val[0]
                            alert_alarm = predict_image_return_val[1]
                            if alert_alarm and audio_enable_flag:
                                print("alarm playing")
                                t1 = threading.Thread(target=alarm_sd)
                                t1.start()
                            for rule_name in self.rule['rule_type']:
                                height = self.rule[rule_name + '_img_h']
                                width = self.rule[rule_name + '_img_w']
                                out_img = cv2.resize(out_img, (width, height))
                                if len(self.rule[rule_name + '_zones']) >= 4:
                                    contour = np.array(self.rule[rule_name + '_zones'], dtype=np.int32)
                                    out_img = cv2.polylines(out_img, [contour], True, (255, 0, 0), 4)
                                if len(self.rule[rule_name + '_zones']) is 2:
                                    out_img = cv2.line(out_img, (
                                        int(self.rule[rule_name + '_zones'][0][0]),
                                        int(self.rule[rule_name + '_zones'][0][1])),
                                                       (int(self.rule[rule_name + '_zones'][1][0]),
                                                        int(self.rule[rule_name + '_zones'][1][1])), (255, 0, 0), 2)
                        try:
                            ret, buffer = cv2.imencode('.jpeg', out_img1)
                            frame = buffer.tobytes()
                            yield (b'--frame\r\n'
                                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
                        except:
                            pass
                    except GeneratorExit:
                        return
            except KeyboardInterrupt:
                pass
        except Exception as e:
            exc.exception('Failed to start live detection feed {}'.format(e))


# UI configuration
@app.route('/Registration', methods=['GET', 'POST'])
def signup():
    try:
        trace.info("Checking Registration details of users")
        msg = ""
        if request.method == 'POST':
            account = DataAccess.register_user_details(request.form['user name'], request.form['email_id'],
                                                       request.form['password'])
            if account == '1':
                msg = "Email id already found"
            else:
                return Response()
        return render_template('Login.html', msg=msg)
    except Exception as e:
        exc.exception('Failed to check registration details of users {}'.format(e))
        print(ex)


class LoginView(MethodView):

    def get(self):
        try:
            trace.info('Rendering login page')
            return render_template('Login.html')
        except Exception as e:
            exc.exception('Failed to render login page {}'.format(e))

    def post(self):
        try:
            global image_flag, elasticsearch_flag, Db_image_flag, email_flag, audio_enable_flag, time_date_flag, whatsapp_flag
            trace.info('Checking Username and password')
            username = request.json['user_name']
            password = request.json['password']
            account = DataAccess.get_login_details(username, password)
            if account:
                session['logged_in'] = True
                session['id'] = account[0]
                session['username'] = account[1]
                flags = DataAccess.flag_details(session['id'])
                session['image_flag'] = image_flag = bool(flags['image_flag'])
                session['elasticsearch_flag'] = elasticsearch_flag = bool(flags['elasticsearch_flag'])
                session['Db_image_flag'] = Db_image_flag = bool(flags['db_flag'])
                session['audio_enable_flag'] = audio_enable_flag = bool(flags['audio_enable_flag'])
                session['time_date_flag'] = time_date_flag = bool(flags['time_flag'])
                session['email_flag'] = email_flag = bool(flags['email_flag'])
                session['whatsapp_flag'] = whatsapp_flag = bool(flags['whatsapp_flag'])
                return Response('true')
            else:
                return Response('false')
        except Exception as e:
            exc.exception('Failed to check username and password {}'.format(e))


class DashboardView(MethodView):
    decorators = [login_required]

    def get(self):
        try:
            trace.info('rendering analytics dashboard')
            return render_template('Analytics_Dashboard_new.html')
        except Exception as Ex:
            exc.exception("Failed to render analytics dashboard {}".format(Ex))


class SearchDashboardView(MethodView):
    decorators = [login_required]

    def get(self):
        try:
            trace.info('rendering analytics dashboard')
            return render_template('Search_Analytics.html')
        except Exception as Ex:
            exc.exception("Failed to render analytics dashboard {}".format(Ex))


class ThirdPartySettings(MethodView):
    decorators = [login_required]

    def get(self):
        try:
            trace.info('rendering analytics dashboard')
            return render_template('Integration.html')
        except Exception as Ex:
            exc.exception("Failed to render analytics dashboard {}".format(Ex))


class ReportPageView(MethodView):
    decorators = [login_required]

    def get(self):
        try:
            trace.info('rendering analytics dashboard')
            return render_template('Report.html')
        except Exception as Ex:
            exc.exception("Failed to render analytics dashboard {}".format(Ex))


class CameraView(MethodView):
    decorators = [login_required]

    def get(self):
        return render_template('Add_Camera.html')


class CameraDetailsView(MethodView):
    decorators = [login_required]

    def __init__(self):
        self.cam = []
        self.cam_dict = {}

    def get(self):
        return render_template('Login.html')

    def post(self):
        try:
            trace.info('Camera Details View')
            user_id = session.get('id')
            userflag = False
            if user_id is None:
                user_id = 1
                userflag = True
            check_user = DataAccess.get_camera_by_user(user_id)
            for cam_details in check_user:
                self.cam_dict['Name'] = cam_details[2]
                self.cam_dict['stream_url'] = cam_details[4]
                if userflag:
                    self.cam_dict['play_list'] = cam_details[5]
                else:
                    self.cam_dict['play_list'] = request.host_url + cam_details[5]
                self.cam_dict['stream_type'] = cam_details[3]
                self.cam_dict['groupName'] = cam_details[9]
                self.cam_dict['camera_condition'] = cam_details[10]
                self.cam.append(self.cam_dict)
                self.cam_dict = {}
            return jsonify(self.cam)
        except Exception as e:
            exc.exception('Error in camera view details {}'.format(e))
            return jsonify(self.cam)


class AddCameraView(MethodView):
    decorators = [login_required]

    def __init__(self):
        self.host_url = request.host_url.rstrip('/')
        self.directory = ""
        self.cam_url = ""
        self.input_type = ""
        self.description = ""
        self.dir = ['video', 'process_video']
        self.directory = None
        self.json_path = root + "/cam_details.json"
        self.group_name = ''
        self.description = ''

    def get(self):
        return render_template('Login.html')

    def post(self):
        try:
            trace.info('Adding camera view')
            self.directory = request.json["cameraName"]
            self.cam_url = request.json["cameraUrl"]
            self.input_type = request.json['inputType']
            if 'groupName' in request.json:
                self.group_name = request.json['groupName']
            if 'description' in request.json:
                self.description = request.json['description']
            user_id = session.get('id')
            if user_id is None:
                user_id = 1
            if self.input_type == 'webcam':
                if self.cam_url.__contains__('.mp4'):
                    self.cam_url = self.cam_url.replace(root, "")
                    self.cam_url = self.cam_url.replace('\\', "/")
                DataAccess.camera_details(self.directory, self.input_type, self.cam_url, self.cam_url, user_id,
                                          self.group_name, self.description)
                return self.cam_url
            if self.input_type == 'video':
                self.cam_url = "test/" + self.cam_url
                DataAccess.camera_details(self.directory, self.input_type, self.cam_url, self.cam_url, user_id,
                                          self.group_name, self.description)
            if self.input_type == 'rtsp':
                self.folder = root + os.path.join("/data/video/{}/".format(user_id))
                self.path = os.path.join(self.folder, self.directory)
                FolderView(self.folder).createfolder()
                FolderView(self.path).createfolder()
                self.folder = self.folder.replace(root + "/", "")
                self.path = self.path.replace(root + "/", "")
                process_id = GstreamerProcess().gstreamer_new_pipeline(self.cam_url, self.path)
                playlist_url = self.path + "/playlist.m3u8"
                with open(self.json_path) as json_read_file:
                    json_ob = json.load(json_read_file)
                cap = cv2.VideoCapture(self.cam_url)
                ret, frame = cap.read()
                json_ob.append({"process_id": process_id, self.path: self.cam_url, "status": ret})
                with open(self.json_path, "w") as json_out_file:
                    json.dump(json_ob, json_out_file, ensure_ascii=False, indent=4)

                check_user = DataAccess.camera_details(self.directory, self.input_type, self.cam_url, playlist_url,
                                                       user_id, self.group_name, self.description)
                if check_user == '1':
                    return Response("Name already found")
                else:
                    reset_stream_details()
                    while True:
                        if os.path.isfile(playlist_url):
                            return Response(playlist_url)
                        time.sleep(20)
        except Exception as e:
            exc.exception('Error in Adding camera view {}'.format(e))

    def delete(self):
        try:
            trace.info('Starting to Delete camera details')
            user_id = session.get('id')
            if user_id is None:
                user_id = 1
            self.directory = request.json["camera_name"]
            for fold in self.dir:
                folder = os.path.join("data/" + fold + "/{}/".format(user_id))
                path = os.path.join(folder, self.directory)
                FolderView(path).removefolder()
                DataAccess.delete_camera_details(user_id, self.directory)

                if elasticbackup:
                    SearchEngine().elastic_data_delete(user_id, self.directory)
                    DataAccess.delete_predicted_details(user_id, self.directory)
                    folder = os.path.join("data/" + "output" + "/{}/".format(user_id))
                    path = os.path.join(folder, self.directory)
                    FolderView(path).removefolder()

                with open(self.json_path) as json_read_file:
                    json_ob = json.load(json_read_file)
                    for i, stream_details in enumerate(json_ob):
                        for key, value in stream_details.items():
                            if key == path:
                                json_ob.remove(stream_details)
                with open(self.json_path, "w") as json_out_file:
                    json.dump(json_ob, json_out_file, indent=4)
                reset_stream_details()
                return Response("true")
        except Exception as e:
            exc.exception('Error while deleting camera details'.format(e))
            return Response("true")


@app.route('/data/video/<string:dir_name>/<string:folder_name>/<string:file_name>', methods=['GET', 'POST'])
@cross_origin(origin='*')
def stream(file_name, folder_name, dir_name):
    try:
        trace.info('Video directory')
        video_dir = 'data/video/' + str(dir_name) + '/' + folder_name
        return send_from_directory(directory=video_dir, path=file_name)
    except FileNotFoundError:
        abort(404)


@app.route('/rule_set', methods=['GET', 'POST'])
@login_required
def rule_camera():
    try:
        trace.info('Rendering rule set page')
        return render_template('Rule_Set.html')
    except Exception as e:
        exc.exception("Error rendering rule set page".format(e))


@app.route('/get_rule_for_camera', methods=['GET', 'POST'])
@login_required
def get_camera_rule():
    try:
        trace.info('Getting rule by camera')
        user_id = session.get('id')
        if user_id is None:
            user_id = 1
        camera_name = request.json["camera_name"]
        data = DataAccess.get_camera_rules(user_id, camera_name)
        details = data[0][0]
        if details is None:
            rule_data = []
        else:
            rule_data = details.decode("utf-8")
        return Response(rule_data)
    except Exception as e:
        exc.exception('Failed while getting rules by camera {}'.format(e))


@app.route('/rule_setting', methods=['GET', "POST"])
@login_required
def rules_for_camera():
    try:
        trace.info('Adding rules for camera ')
        user_id = session.get('id')
        if user_id is None:
            user_id = 1
        camera_name = request.json["camera_name"]
        rules = request.json['rule_details']
        curr_rule_lst = request.json['current_rule']
        DataAccess.set_camera_rules(user_id, camera_name, json.dumps(rules))

        for curr_rule in curr_rule_lst:
            if curr_rule['group_name'] != "" and curr_rule['group_applied'] == "All":
                cam_details = DataAccess.get_camera_by_user(user_id)
                for cam_data in cam_details:
                    cam_name = cam_data[2]
                    if cam_name != camera_name:
                        cam_rule_bytes = cam_data[6]
                        if not cam_rule_bytes:
                            cam_rule_lst = []
                        else:
                            cam_rule_lst = json.loads(cam_rule_bytes.decode('utf-8'))
                        cam_rule_cpy = cam_rule_lst.copy()
                        if cam_data[9] == curr_rule['group_name']:
                            cam_rule_cpy.append(curr_rule)
                            DataAccess.set_camera_rules(user_id, cam_name, json.dumps(cam_rule_cpy))

        msg = 'Rules Added Successfully'
        reset_stream_details()
        return Response(msg)
    except Exception as e:
        exc.exception('Failed to add rules to camera {}'.format(e))


@app.route('/process_video')
@cross_origin(origin='*')
def investicated_video():
    try:
        trace.info('Creating Process_video with session id')
        if session.get('logged_in'):
            pro_usr_dir = os.path.join("process_video/{}/".format(session.get('id')))
            predicted_videos1 = [os.path.join(os.path.join(pro_usr_dir, file), x) for file in os.listdir(pro_usr_dir)
                                 for x in
                                 os.listdir(os.path.join(pro_usr_dir, file))]
            for path in predicted_videos1:
                if not path.__contains__('Predicted'):
                    predict_video_path = path.replace('Trespass', 'Predicted')
                    p = Popen(
                        ['ffmpeg', '-y', '-f', 'image2pipe', '-vcodec', 'mjpeg', '-r', '24', '-i', '-', '-vcodec',
                         'h264', '-qscale', '5', '-r', '20',
                         str(predict_video_path).replace("\\", "/").replace('.webm', '.mp4')],
                        stdin=PIPE)

                    video = cv2.VideoCapture(path)
                    while True:
                        ret, frame = video.read()
                        if ret:
                            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                            im = Image.fromarray(frame)
                            im.save(p.stdin, 'JPEG')
                        else:
                            break
                    p.stdin.close()
                    p.wait()
                    video.release()
                    os.remove(path)

            predicted_videos = [request.host_url.rstrip('/') + os.path.join(os.path.join(pro_usr_dir, file), x) for file
                                in
                                os.listdir(pro_usr_dir) for x in os.listdir(os.path.join(pro_usr_dir, file)) if
                                (os.path.join(os.path.join(pro_usr_dir, file), x)).__contains__('Predicted')]
            return jsonify(predicted_videos)
        else:
            return jsonify([])
    except Exception as e:
        exc.exception('Error while Creating Process_video with session id {}'.format(e))


@app.route('/process_video/<string:dir_name>/<string:folder_name>/<string:file_name>', methods=['GET', 'POST'])
@cross_origin(origin='*')
def process_video_stream(dir_name, folder_name, file_name):
    try:
        trace.info("Starting process_video_stream")
        video_dir = "process_video/" + dir_name + "/" + folder_name
        return send_from_directory(directory=video_dir, path=file_name)
    except FileNotFoundError:
        abort(404)


@app.route('/video_feed/<string:usr_id>/<string:cam_name>', methods=["GET", "POST"])
@login_required
def video_feed(usr_id, cam_name):
    try:
        trace.info('Loading video_feed')
        try:
            live_cam = json.loads(session['camera_details'])
        except AttributeError:
            camera_details = [
                {"usr_id": 1, "cam_name": "65", "stream_url": "rtsp://admin:Admin123$@10.11.25.65:554/stream1",
                 "rules": "[{\"rule_type\": [\"Trespass\"], \"Trespass_img_h\": 472, \"Trespass_img_w\": 885, \"Trespass_zones\": [[312, 186.21875], [518, 179.21875], [541, 263.21875], [288, 244.21875], [303, 190.21875]], \"Trespasseffection_target\": \"Any\", \"Trespass_intrusion_time_limit\": \"57\"}]"},
                {"usr_id": 1, "cam_name": "64", "stream_url": "rtsp://admin:Admin123$@10.11.25.64:554/stream1",
                 "rules": "[{\"rule_type\": [\"Camera Tampering\"], \"Camera Tampering_img_h\": 472, \"Camera Tampering_img_w\": 885, \"Camera Tampering_zones\": [[492, 268.21875], [637, 263.21875]], \"Camera Tamperingeffection_target\": \"Any\", \"Camera Tampering_intrusion_time_limit\": \"57\"}]"},
                {"usr_id": 1, "cam_name": "53", "stream_url": "rtsp://admin:Admin123$@***********:554/stream1",
                 "rules": "[{\"rule_type\": [\"Face Detection\"], \"Face Detection_img_h\": 472, \"Face Detection_img_w\": 885, \"Face Detection_zones\": [[297, 380.21875], [538, 393.21875]], \"Face Detectioneffection_target\": \"Any\", \"Face Detection_intrusion_time_limit\": \"58\"}]"},
                {"usr_id": 1, "cam_name": "59", "stream_url": "rtsp://admin:Admin123$@***********:554/stream1",
                 "rules": "[{\"rule_type\": [\"Tripwire\"], \"Tripwire_img_h\": 472, \"Tripwire_img_w\": 885, \"Tripwire_zones\": [[247, 270.21875], [454, 273.21875]], \"Tripwireeffection_target\": \"Any\", \"Tripwire_intrusion_time_limit\": \"59\"}]"}]
            live_cam = json.loads(str(json.dumps(camera_details)))
        for cam_data in live_cam:
            if cam_data['cam_name'] == cam_name:
                if 'rules' in cam_data:
                    rules = cam_data['rules']
                    url = cam_data['stream_url']
                    for rule in json.loads(rules):
                        res = Survillence(usr_id, cam_name, rule, url).survillence_live_feed()
                        social_dis_res_return_val = res
                        return Response(social_dis_res_return_val,
                                        mimetype='multipart/x-mixed-replace; boundary=frame')
                else:
                    rules = None
                    url = cam_data['stream_url']
                    res = Survillence(usr_id, cam_name, rules, url).survillence_live_feed()
                    social_dis_res_return_val = res
                    return Response(social_dis_res_return_val,
                                    mimetype='multipart/x-mixed-replace; boundary=frame')

    except Exception as e:
        exc.exception("Error loading Video feed {}".format(e))
        print(e)
        return Response("There is no frame")


@app.route('/detected_images')
@login_required
def detected_images():
    try:
        trace.info('Starting to render Investigate page')
        usr_id = session.get('id')
        predict_image = analytics_detector.read_image_data(usr_id)
        return render_template('Investigate.html', image_list=predict_image)
    except Exception as e:
        exc.exception('Error rendering Investigate page {}'.format(e))


@app.route('/report_activity_count')
@login_required
def report_activity_count():
    activity_dict = {}
    month_dict = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0, 11: 0, 12: 0}
    try:
        trace.info('Starting report activity count')
        user_id = session.get('id')
        activity_data = DataAccess.get_predicted_video(user_id)
        for data in activity_data:
            if data[4] not in activity_dict:
                activity_dict[data[4]] = month_dict.copy()
            activity_dict[data[4]][data[6].month] += 1
        return activity_dict
    except Exception as e:
        exc.exception('Failed starting report activity count {}'.format(e))
        print(e)


def image_src(image_path, user_id):
    # image_path = (root+'/data/analytics_output/'+str(user_id) + "/test.jpeg")
    # img = Image.open(io.BytesIO(base64.decodebytes(bytes(img, "utf-8"))))
    # img.save(image_path)
    image = face_recognition.load_image_file(image_path)
    face_locations = face_recognition.face_locations(image)
    face_encodings = face_recognition.face_encodings(image, face_locations)
    for face_encoding in face_encodings:
        score, face_name = SearchEngine().name_get(face_encoding)
        if score > 0.93:
            return face_name
        else:
            face_name = ""
            return face_name


@app.route('/search_data', methods=['GET', 'POST'])
@login_required
def search_data():
    try:
        trace.info('Starting to search data')
        if request.method == 'POST':
            is_search_text = False
            user_id = session.get('id')
            url = request.host_url.rstrip('/')
            if user_id is None:
                user_id = 3
            size = 1000
            order = "asc"
            img_list = None
            if 'search_string' in request.json:
                if request.json['search_string'] != "":
                    is_search_text = True
                    img_list = EntityExtractor().process_text(request.json['search_string'], user_id, root, url, size,
                                                              order)

            if not is_search_text:
                object_type = []
                gender_lst = []
                image_path = ""
                cam_name = request.json['camera_name']
                if cam_name == 'All':
                    cam_name = ''
                from_date = request.json['start_date']
                to_date = request.json['end_date']
                event_type = request.json['event_type']
                apparel = request.json['Cloth']
                bag = request.json['Bag']
                if event_type == 'none':
                    event_type = ''
                use_case = request.json['event']
                # object_name = request.json['object_name']
                color = request.json['color']
                match = re.search(r'^#(?:[0-9a-fA-F]{3}){1,2}$', color)
                if match:
                    name = color_detection().hex_colorname(color)
                    color_name_det = name.split('/')[1]
                    color = color_name_det
                people = request.json['People']
                vehicle = request.json['Vehicle']
                animals = request.json['Animals']
                if people:
                    for use in use_case:
                        if use == "FaceDetection":
                            for gender in people:
                                gen = gender.lower()
                                gender_lst.append(gen)
                        # elif use == "ObjectDetection":
                        #     object_type.append('person')
                        else:
                            object_type.append('person')
                elif vehicle:
                    for obj_name in vehicle:
                        object_name = obj_name.lower()
                        object_type.append(object_name)
                elif animals:
                    for obj_name in animals:
                        object_name = obj_name.lower()
                        object_type.append(object_name)
                else:
                    object_type = []
                if bag:
                    for bags in bag:
                        bag_type = bags.lower()
                        object_type.append(bag_type)
                if 'start_time' in request.json:
                    start_time = request.json['start_time'] + ":00"
                else:
                    start_time = "00:00:00"
                if 'end_time' in request.json:
                    end_time = request.json['end_time'] + ":59"
                else:
                    end_time = "23:59:59"
                image = request.json['file']
                if image != "":
                    # image = request.json['file']
                    image_path = (root + '/data/analytics_output/' + str(user_id) + "/test.jpeg")
                    img = Image.open(io.BytesIO(base64.decodebytes(bytes(image.split(',')[1], "utf-8"))))
                    img.save(image_path)

                name = ""
                if "FaceDetection" in use_case and image_path != "":
                    if face_match:
                        image = face_recognition.load_image_file(image_path)
                        face_locations = face_recognition.face_locations(image)
                        face_encodings = face_recognition.face_encodings(image, face_locations)
                        for face_encoding in face_encodings:
                            img_list = SearchEngine().image_search(face_encoding, root, url, user_id, cam_name,
                                                                   from_date, to_date, start_time, end_time)
                    else:
                        name = image_src(image_path, user_id)
                        img_list = SearchEngine().image_search_engine(user_id, cam_name, from_date, to_date, event_type,
                                                                      use_case, color, start_time, end_time, image_path,
                                                                      root, url, object_type, apparel,
                                                                      name, gender_lst, size, order)
                else:
                    img_list = SearchEngine().image_search_engine(user_id, cam_name, from_date, to_date, event_type,
                                                                  use_case, color, start_time, end_time,
                                                                  image_path, root, url, object_type, apparel,
                                                                  name, gender_lst, size, order)

            return Response(json.dumps(img_list))
        else:
            user_id = session.get('id')
            if user_id is None:
                user_id = 1
            url = request.host_url.rstrip('/')
            img_data = SearchEngine().search_analytics_image(user_id, root, url)
            return Response(json.dumps(img_data))

    except Exception as e:
        exc.exception('Failed while searching the data {}'.format(e))
        return Response(json.dumps([]))


@app.route('/chatbot_search', methods=['GET', 'POST'])
def chatbot_search():
    try:
        user_id = session.get('id')
        url = request.host_url.rstrip('/')
        if user_id is None:
            user_id = 3
        size = 1000
        order = "asc"
        img_list = None
        if 'search_string' in request.json:
            if request.json['search_string'] != "":
                img_list = EntityExtractor().process_text(request.json['search_string'], user_id, root, url, size,
                                                          order)
            return Response(json.dumps(img_list))
    except Exception as ex:
        print(ex)


@app.route('/report_custom_date', methods=['GET', 'POST'])
@login_required
def get_predicted_data_date():
    global res_key_obj, search_item, dates
    person_events = {"Tampering": 0,
                     "FaceDetection": 0,
                     "FireDetection": 0,
                     "LoiteringDetection": 0,
                     "MovementDetection": 0,
                     "PersonExit": 0,
                     "SmokeDetection": 0,
                     "Trespass": 0,
                     "Tripwire": 0,
                     "WrongWayDetection": 0,
                     "PersonLineCrossCount": 0,
                     "WrongDirection": 0,
                     "MaskDetection": 0,
                     "SocialDistance": 0
                     }

    object_events = {"LicensePlateDetection": 0,
                     "LeftObjectDetection": 0,
                     "NoParking": 0,
                     "ObjectDetection": 0,
                     "MissingObjectDetection": 0,
                     "VehicleLineCrossCount": 0,
                     "VehicleTresspass": 0,
                     "VehicleTripwire": 0,
                     "VehicleWrongDirection": 0,
                     "MissingObjectDetectionArea": 0,
                     "SignalViolation": 0,
                     "Helmet Detection": 0
                     }

    res_obj = {}
    from_date = None
    to_date = None
    split_type = "days"
    activity_data = None
    try:
        trace.info('Starting get predicted data date')
        user_id = session.get('id')
        event_type = request.json['event_type']
        date_str = request.json['date_string']
        # use_case=request.json['use_case']

        use_case_list = None
        if event_type == "Person":
            use_case_list = person_events
        elif event_type == "Object":
            use_case_list = object_events

        if date_str == 'custom':
            split_type = None
            use_case = None
            from_date = request.json['start_date']
            to_date = request.json['end_date']
            from_date = datetime.datetime.strptime(from_date, '%m/%d/%Y')
            to_date = datetime.datetime.strptime(to_date, '%m/%d/%Y')
            activity_data = DataAccess.get_custom_date_report(user_id, use_case, event_type, from_date, to_date)
        if date_str == 'today':
            from_date = to_date = datetime.date.today()
            split_type = "hours"
        elif date_str == 'week':
            dates = get_week_days()
            if len(dates) > 1:
                from_date = dates[0]
                to_date = dates[-1]
        elif date_str == 'month':
            from_date, to_date, dates = get_days_in_month()
        if from_date and to_date and date_str != "custom":
            try:
                activity_data = DataAccess.get_report_by_type(user_id, event_type, from_date, to_date, split_type)
            except Exception as e:
                activity_data = ()
                print("Error while fetching data from DB...", e)
        list_range = None
        if split_type == "hours":
            list_range = range(0, 24)
        elif split_type == "days":
            list_range = range(int(from_date.day), int(31) + 1)
        result = defaultdict(list)
        for tup in activity_data:
            result[tup[0]].append(tup[1:3])
        dic_res = dict(result)
        iter_count = 0
        for lst_ind in list_range:
            if split_type == "hours":
                # res_key_obj=str(lst_ind) + "-" + str(lst_ind + 1)
                res_key_obj = str(lst_ind)
                search_item = lst_ind
            elif split_type == "days":
                res_key_obj = str(dates[iter_count])
                search_item = dates[iter_count]
            if res_key_obj not in res_obj:
                res_obj[res_key_obj] = use_case_list.copy()
            if search_item in dic_res:
                uc_lst = dic_res[search_item]
                for uc in uc_lst:
                    if uc[0] in use_case_list:
                        res_obj[res_key_obj][uc[0]] = int(uc[1])
            iter_count += 1
        return Response(json.dumps(res_obj))
    except Exception as e:
        exc.exception('Failed while getting predicted data date {}'.format(e))
        print("Error in getting the custom date values...", e)
        return Response(json.dumps(res_obj))


def get_week_days():
    try:
        trace.info('Starting to get week days')
        cur_date = datetime.date.today()
        weekday = cur_date.isoweekday()
        start_day = cur_date - datetime.timedelta(days=weekday)
        get_week_date = [start_day + datetime.timedelta(days=d) for d in range(0, 7)]
        return get_week_date
    except Exception as e:
        exc.exception('Failed while getting the week days {}'.format(e))
        print("Error in getting the week days... ", e)
        return []


def get_days_in_month():
    first_day = None
    last_day = None
    dates = []
    try:
        trace.info('Starting to get the days in month')
        curr_date = datetime.datetime.today().date()
        first_day = curr_date.replace(day=1)
        if curr_date.month == 12:
            last_day = curr_date.replace(day=31)
        else:
            last_day = curr_date.replace(month=curr_date.month + 1, day=1) - datetime.timedelta(days=1)
        dates = [first_day + datetime.timedelta(days=d) for d in range(0, int(last_day.day))]
        return first_day, last_day, dates
    except Exception as e:
        exc.exception('Failed while finding the start and end date of the given month {}'.format(ex))
        print("Error while finding the start and end of the given month ", e)
        return first_day, last_day, dates


def update_status():
    try:
        global camera_status
        path = None
        trace.info('Updating camera active or not status..')
        threading.Timer(60.0, update_status).start()
        cam_list = []
        dict_path = []
        true_counts = 0
        false_counts = 0
        camera_details = root + "/cam_details.json"
        with open(camera_details, "r") as read_file:
            json_obj = json.load(read_file)
        for cam_dict in json_obj:
            cam_keys = list(cam_dict.keys())
            cam_path = root + "/" + cam_keys[1]
            dict_path.append(cam_path)
            if not os.path.exists(cam_path + "/playlist.m3u8"):
                GstreamerProcess().gstreamer_new_pipeline(cam_dict[cam_keys[1]], cam_keys[1])
            cap = cv2.VideoCapture(cam_dict[cam_keys[1]],cv2.CAP_FFMPEG)
            ret, frame = cap.read()
            if ret:
                true_counts = true_counts + 1
            else:
                false_counts = false_counts + 1
            cam_dict[cam_keys[2]] = ret
            cam_list.append(cam_dict)  # adding true or false and getting count of active and inactive camera

        check_list = []  # For removing duplicate entries from json file
        for camera_dict in cam_list:
            for num, i in enumerate(camera_dict):
                if i == list(camera_dict.keys())[1]:
                    cur = i
                    if cur in check_list:
                        for x, y in enumerate(check_list):
                            if cur == y:
                                cam_list[x] = {}
                    check_list.append(i)
        cam_list = list(filter(None, cam_list))
        with open(camera_details, "w") as write_file:
            json.dump(cam_list, write_file, ensure_ascii=False, indent=4)
        if cam_list:
            for key in json_obj:
                dict_key = list(key.keys())[1]
                path = root + "/" + dict_key
            path_head = os.path.split(path)[0]
            for file in os.listdir(path_head):
                save_path = path_head + "/" + file
                if save_path not in dict_path:
                    try:
                        shutil.rmtree(save_path)
                    except AttributeError:
                        pass
        camera_status = {"Active_cams": true_counts, "Inactive_cams": false_counts, "Total_cams": json_obj.__len__()}
    except Exception as e:
        exc.exception('Error while updating camera status {}'.format(e))


@app.route('/report_camera_status', methods=['GET', 'POST'])
@login_required
def cam_status_update():
    try:
        trace.info('Starting Camera status update..')
        camera_details_status = camera_status
        user_id = session.get('id')
        no_of_objects = DataAccess.get_object_count(user_id)
        person_violation_data = DataAccess.get_person_violation_count(user_id, use_case="Person")
        object_violation_data = DataAccess.get_object_violation_count(user_id, use_case="Object")
        camera_details_status["Object_count"] = no_of_objects
        camera_details_status["Person_violation"] = person_violation_data
        camera_details_status['Object_violation'] = object_violation_data
        return Response(json.dumps(camera_details_status))
    except Exception as e:
        exc.exception("Error while getting camera status {}".format(e))
        print(e)


@app.route('/report_gender_details', methods=['GET', 'POST'])
@login_required
def get_gender_details():
    global from_date, to_date
    try:
        trace.info('starting to get gender details..')
        user_id = session.get('id')
        date_str = request.json['date_string']
        print("data_str", date_str)
        if date_str == 'today':
            from_date = to_date = datetime.date.today()
        elif date_str == 'week':
            dates = get_week_days()
            if len(dates) > 1:
                from_date = dates[0]
                to_date = dates[-1]
        elif date_str == 'month':
            from_date, to_date, dates = get_days_in_month()
        gen_res = DataAccess.get_gender_details(user_id, from_date, to_date)
        count_data = {"Male": gen_res[0][0], "Female": gen_res[0][1], "Others": gen_res[0][2]}
        return Response(json.dumps(count_data))
    except Exception as e:
        exc.exception("Failed while getting getting gender details {}".format(e))
        print(e)


@app.route('/report_details', methods=['GET', 'POST'])
@login_required
def report_details():
    img_list = []
    from_dt = None
    to_dt = None
    try:
        if request.method == 'POST':
            trace.info('Starting to search report details')
            user_id = session.get('id')
            cam_name = request.json['camera_name']
            from_date = request.json['start_date']
            to_date = request.json['end_date']
            event = request.json['event']
            use_case = event.replace(" ", "")
            if user_id is None:
                user_id = 3
                cam_name = 'cam-22'
            if from_date:
                from_dt = from_date + " " + '00:00:00'
            if to_date:
                to_dt = to_date + " " + '23:59:59'
            report_data = DataAccess.get_report_data(user_id, cam_name, use_case, from_dt, to_dt)
            for data_item in report_data:
                if data_item[1] == 'FaceDetection':
                    b64 = str(data_item[2]).replace(root, '')

                    data_obj = {"Camera_Name": data_item[0], "Category": data_item[1], 'Person_Name': data_item[3],
                                'Gender': data_item[4], 'Age': data_item[5], 'Created_Date': str(data_item[6]),
                                "Event_Image": request.host_url.rstrip('/') + b64}
                    img_list.append(data_obj)
                elif data_item[1] == 'ObjectDetection':
                    b64 = str(data_item[2]).replace(root, '')
                    data_obj = {"Camera_Name": data_item[0], "Category": data_item[1],
                                'Detected_Object': data_item[3], 'Detected_Color': data_item[4],
                                'Created_Date': str(data_item[5]), "Event_Image": request.host_url.rstrip('/') + b64}
                    img_list.append(data_obj)
                else:
                    b64 = str(data_item[2]).replace(root, '')
                    data_obj = {"Camera_Name": data_item[0], "Category": data_item[1],
                                'Created_Date': str(data_item[3]),
                                "Event_Image": request.host_url.rstrip('/') + b64}
                    img_list.append(data_obj)

            return Response(json.dumps(img_list))
        else:
            user_id = session.get('id')
            if user_id is None:
                user_id = 3
            report_data = DataAccess.get_report(user_id)

            for data_item in report_data:
                b64 = str(data_item[2]).replace(root, '')
                data_obj = {"Camera_Name": data_item[0], "Category": data_item[1],
                            'Created_Date': str(data_item[3]),
                            "Event_Image": request.host_url.rstrip('/') + b64}
                img_list.append(data_obj)

            return Response(json.dumps(img_list))
    except Exception as e:
        exc.exception('Failed while searching the report details {}'.format(e))
        return Response(json.dumps(img_list))


@app.route('/report_age_details', methods=['GET', 'POST'])
@login_required
def get_age_details():
    try:
        from_date = None
        to_date = None
        trace.info('starting to get age details..')
        user_id = session.get('id')
        date_str = request.json['date_string']
        if date_str == 'today':
            from_date = to_date = datetime.date.today()
        elif date_str == 'week':
            dates = get_week_days()
            if len(dates) > 1:
                from_date = dates[0]
                to_date = dates[-1]
        elif date_str == 'month':
            from_date, to_date, dates = get_days_in_month()
        age_res = DataAccess.get_age_details(user_id, from_date, to_date)
        count_data = {"Kids": age_res[0][0], "Teen": age_res[0][1], "Adult": age_res[0][2], "Senior": age_res[0][3]}
        return Response(json.dumps(count_data))
    except Exception as e:
        exc.exception('Failed while getting age details {}'.format(e))
        print("Error while getting the age details...", e)
        return {}


@app.route('/report_image_list', methods=['GET', 'POST'])
@login_required
def get_predicted_image_list():
    try:
        trace.info('Starting to get predicted image list...')
        img_list = []
        user_id = session.get('id')
        activity_data = DataAccess.get_predicted_video(user_id)
        for data_item in activity_data:
            if os.path.isfile(data_item[5]):
                b64 = str(data_item[5]).replace(root, '')
                # b64=ad.convertToBinaryData_image(data_item[5])
                data_obj = {"timestamp": str(data_item[6]), "event_name": data_item[4],
                            "event_value": request.host_url.rstrip('/') + b64}
                img_list.append(data_obj)
        return Response(json.dumps(img_list))
    except Exception as e:
        exc.exception('Failed while getting predicted images lists {}'.format(e))
        print("Error while getting the image list for dashboard...", e)
        return


@app.route('/data/<path:path>', methods=['GET', 'POST'])
@cross_origin(origin='*')
def process_image_stream(path):
    try:
        video_dir = "data/"
        return send_from_directory(directory=video_dir, path=path)
    except FileNotFoundError:
        abort(404)


@app.route('/test/<path:path>', methods=['GET', 'POST'])
@cross_origin(origin='*')
def video_stream(path):
    try:
        video_dir = "test/"
        return send_from_directory(directory=video_dir, path=path)
    except FileNotFoundError:
        abort(404)


@app.route('/action_video/<path:path>', methods=['GET', 'POST'])
@cross_origin(origin='*')
def action_video(path):
    try:
        video_dir = "action_video/"
        # path = "output.mp4"
        return send_from_directory(directory=video_dir, path=path)
    except FileNotFoundError:
        abort(404)


@app.route('/logout')
def logout():
    try:
        session.clear()
        return render_template('Login.html')
    except Exception as e:
        print("Error while logout..", e)


@app.route('/analytics_image', methods=['GET', 'POST'])
@login_required
def analytics_image():
    try:
        trace.info('Loading image in analytics dashboard')
        user_id = session.get('id')
        if user_id is None:
            user_id = 1
        url = request.host_url.rstrip('/')
        img_data = SearchEngine().search_analytics_image(user_id, root, url)
        return Response(json.dumps(img_data))
    except Exception as Ex:
        exc.exception("Failed to load image in analytics dashboard {}".format(Ex))


@app.route('/live_monitoring')
@login_required
def live_monitoring():
    try:
        global image_flag, elasticsearch_flag, Db_image_flag, email_flag, audio_enable_flag, time_date_flag, whatsapp_flag
        trace.info("Starting To Render Live Detection Page")
        camera_dict = []
        usr_id = session.get('id')
        if usr_id is None:
            usr_id = 1
        if db_res:
            for cam_details in db_res:
                if int(cam_details[3]) == int(usr_id):
                    user_dict = dict()
                    user_dict["usr_id"] = usr_id
                    user_dict["cam_name"] = cam_details[0]
                    if str(cam_details[1]).__contains__('.mp4'):
                        user_dict["stream_url"] = request.host_url.rstrip('/') + '/' + cam_details[1]
                        if Av3ar_gviewer:
                            user_dict['Av3ar_gviewer'] = "ON"
                        else:
                            user_dict['Av3ar_gviewer'] = "OFF"
                        user_dict['stream_type'] = cam_details[8]
                    else:
                        if Av3ar_gviewer:
                            user_dict['Av3ar_gviewer'] = "ON"
                            user_dict["stream_url"] = cam_details[7]
                            user_dict['stream_type'] = cam_details[8]
                        else:
                            user_dict['Av3ar_gviewer'] = "OFF"
                            user_dict["stream_url"] = cam_details[1]
                            user_dict['stream_type'] = cam_details[8]
                    user_dict['group_name'] = [cam_details[4]]
                    user_dict['camera_condition'] = cam_details[5]
                    user_dict['camera_description'] = cam_details[6]
                    rule_data = cam_details[2]
                    if cam_details[5] == 'ON':
                        if rule_data:
                            rule_cam = []
                            rule_dict = {"rule_type": []}
                            rules = json.loads(rule_data.decode('utf-8'))
                            for rule in rules:
                                for surv_mode in rule['ruleType']:
                                    rule_dict['rule_type'].append(surv_mode)
                                    rule_dict[surv_mode + '_start_time'] = rule['start_time']
                                    rule_dict[surv_mode + '_end_time'] = rule['end_time']
                                    rule_dict[surv_mode + '_start_date'] = rule['start_date']
                                    rule_dict[surv_mode + '_end_date'] = rule['end_date']
                                    rule_dict[surv_mode + '_img_h'] = rule['img_h']
                                    rule_dict[surv_mode + '_img_w'] = rule['img_w']
                                    rule_dict[surv_mode + "_zones"] = rule['zones'][0]
                                    rule_dict[surv_mode + "_direction"] = rule['direction']
                                    rule_dict[surv_mode + "effection_target"] = rule['effection_target'][0]
                                    if rule["start_time"] is None:
                                        rule_dict[surv_mode + "_intrusion_time_limit"] = 0
                                    else:
                                        rule_dict[surv_mode + "_intrusion_time_limit"] = rule["start_time"].split(":")[
                                            1]
                                    if 'email' in rule['alarm']:
                                        rule_dict[surv_mode + "_email_alarm_mode"] = rule['alarm']['email']
                                    else:
                                        rule_dict[surv_mode + "_email_alarm_mode"] = None
                                    if 'whatsapp' in rule['alarm']:
                                        rule_dict[surv_mode + "_whatsapp_alarm_mode"] = rule['alarm']['whatsapp']
                                    else:
                                        rule_dict[surv_mode + "_whatsapp_alarm_mode"] = None
                                    if len(rule['object']):
                                        rule_dict[surv_mode + '_object'] = rule['object']
                                    else:
                                        rule_dict[surv_mode + '_object'] = ""

                            rule_cam.append(rule_dict)
                            user_dict["rules"] = json.dumps(rule_cam)
                            camera_dict.append(user_dict)
        session["camera_details"] = json.dumps(camera_dict)
        if not image_flag:
            image_flag = session['image_flag']
            elasticsearch_flag = session['elasticsearch_flag']
            Db_image_flag = session['Db_image_flag']
            audio_enable_flag = session['audio_enable_flag']
            time_date_flag = session['time_date_flag']
            email_flag = session['email_flag']
            whatsapp_flag = session['whatsapp_flag']
        return Response(json.dumps(camera_dict))
    except Exception as e:
        exc.exception("Error rendering Live detection page {}".format(e))


@app.route('/third_party', methods=['GET', 'POST'])
@login_required
def third_party_api():
    try:
        trace.info('Starting to integrate third party api')
        credential_json = root + "/third_party_credential.json"
        if request.method == "POST":
            with open(credential_json, "r") as file:
                data = json.load(file)
            url = request.json['url']
            user_name = request.json['user_name']
            password = request.json['password']
            resp = {"Url": url, "UserName": user_name, "Password": password}
            data = third_party_api.api_access(url, user_name, password)
            if data is None:
                data = []
            resp["camera_list"] = data
            with open(credential_json, "w") as cred_json_file:
                json.dump(resp, cred_json_file)
            return Response(json.dumps(data))
        else:
            with open(credential_json, "r") as file:
                json_file = json.load(file)
            return Response(json.dumps(json_file))
    except Exception as e:
        exc.exception('Error rendering Investigate page {}'.format(e))


@app.route('/group_details', methods=['GET', 'POST'])
@login_required
def group_creation():
    try:
        if request.method == "POST":
            user_id = session.get('id')
            if user_id is None:
                user_id = 1
            group = request.json['group']
            group_details = DataAccess.get_group_details(user_id)
            if group_details is None:
                group_details = []
            else:
                group_details = json.loads(group_details)
            group_details.append(group)
            DataAccess.insert_group(user_id, json.dumps(group_details))
            return Response("success")
        else:
            user_id = session.get('id')
            if user_id is None:
                user_id = 1
            group_details = DataAccess.get_group_details(user_id)
            if group_details is None:
                group_details = []
            else:
                group_details = json.loads(group_details)
            return Response(json.dumps(group_details))
    except Exception as e:
        exc.exception('Error rendering Investigate page {}'.format(e))


@app.route('/update_camera_flag', methods=['POST'])
@login_required
def update_camera_flag():
    try:
        if request.method == "POST":
            user_id = session.get('id')
            if user_id is None:
                user_id = 1
            camera_name = request.json['camera_name']
            camera_flag = request.json['camera_condition']
            DataAccess.update_camera_flag(user_id, camera_name, camera_flag)
            reset_stream_details()
            return Response("success")
    except Exception as e:
        exc.exception('Error camera on/off process {}'.format(e))
        return Response("Fail")


@app.route('/live_thread_detect', methods=['GET', 'POST'])
@login_required
def get_live_thread_detect():
    try:
        trace.info('Starting to get live detect image list...')
        user_id = session.get('id')
        camera_name = None
        if user_id is None:
            user_id = 1
        try:
            if 'camera_name' in request.json:
                camera_name = request.json['camera_name']
        except AttributeError:
            camera_name = None
        activity_data = database_pool.apply_async(DataAccess.get_live_thread_image, (user_id, camera_name)).get()
        if activity_data:
            img_list = []
            for data_item in activity_data:
                b64 = str(data_item[5]).replace(root, '')
                # b64=ad.convertToBinaryData_image(data_item[5])
                data_obj = {"Camera_Name": str(data_item[2]), "Event": data_item[3], "Use_Case": data_item[4],
                            "TimeStamp": str(data_item[6]), "Event_Value": request.host_url.rstrip('/') + b64}
                img_list.append(data_obj)
                # queue_list.put(data_obj)
            result = database_pool.apply_async(json.dumps, (img_list,)).get()
            return Response(result)
        else:
            return Response(json.dumps([]))
    except Exception as e:
        exc.exception('Failed while getting predicted images lists {}'.format(e))
        print("Error while getting the image list for dashboard...", e)


def object_detection_image_save(image_name, image):
    d = threading.Thread(target=object_detection_image_save1, args=(image_name, image))
    d.start()
    d.join()


def object_detection_image_save1(image_name, image):
    cv2.imwrite(image_name, image)


def webm_to_mp4_video_save(input_path, output_path):
    d = threading.Thread(target=convert_webm_mp4_converter, args=(input_path, output_path))
    d.start()
    d.join()


def convert_webm_mp4_converter(input_path, output_path):
    try:
        stream_input = ffmpeg.input(input_path)
        stream_output = ffmpeg.output(stream_input, output_path)
        ffmpeg.run(stream_output)
    except Exception as e:
        print('Error'.format(e))


RESIZE_HEIGHT = 480
face_down_sample_ratio = 1.5
face_swap_detector = dlib.get_frontal_face_detector()
face_swap_predictor = dlib.shape_predictor(root + '/faceswap/models/shape_predictor_68_face_landmarks.dat')


def face_mash(tar_source, in_source):
    output_video_path = root + '/faceswap/video/'
    image_path = root + '/faceswap/images/' + tar_source
    img1 = cv2.imread(image_path)
    height, width = img1.shape[:2]
    image_resize = np.float32(height) / RESIZE_HEIGHT
    img1 = cv2.resize(
        img1,
        None,
        fx=1.0 / image_resize,
        fy=1.0 / image_resize,
        interpolation=cv2.INTER_LINEAR,
    )
    points1 = face_swap.detect_facial_landmarks(face_swap_detector, face_swap_predictor, img1, face_down_sample_ratio)
    original_hull_index = cv2.convexHull(np.array(points1), returnPoints=False)
    mouth_points = [
        [48],  # <outer mouth>
        [49],
        [50],
        [51],
        [52],
        [53],
        [54],
        [55],
        [56],
        [57],
        [58],  # </outer mouth>
        [60],  # <inner mouth>
        [61],
        [62],
        [63],
        [64],
        [65],
        [66],
        [67],  # </inner mouth>
    ]
    hull_index = np.concatenate((original_hull_index, mouth_points))
    landmark_idx_to_list_idx = {elem[0]: i for i, elem in enumerate(hull_index)}
    hull1 = [points1[hull_index_element[0]] for hull_index_element in hull_index]
    rect = (0, 0, img1.shape[1], img1.shape[0])
    delaunay_triangles = face_swap.get_delaunay_triangles(rect, hull1, [hi[0] for hi in hull_index])
    cap = cv2.VideoCapture(root + '/faceswap/video/' + in_source)
    frame_width = int(cap.get(3))
    frame_height = int(cap.get(4))
    ret, img = cap.read()
    first_frame = False
    if ret:
        size = (frame_width, frame_height)
        fourcc = cv2.VideoWriter_fourcc(*'MP4V')
        out = cv2.VideoWriter(output_video_path + 'video.mp4', fourcc, 30, size)
        while True:
            ret, img2 = cap.read()
            if ret:
                height, width = img2.shape[:2]
                image_resize = np.float32(height) / RESIZE_HEIGHT
                img2 = cv2.resize(
                    img2,
                    None,
                    fx=1.0 / image_resize,
                    fy=1.0 / image_resize,
                    interpolation=cv2.INTER_LINEAR,
                )
                try:
                    points2 = face_swap.detect_facial_landmarks(face_swap_detector, face_swap_predictor, img2,
                                                                face_down_sample_ratio)
                except Exception:
                    pass
                else:
                    hull2 = [points2[hull_index_element[0]] for hull_index_element in hull_index]
                    original_hull2 = [
                        points2[hull_index_element[0]] for hull_index_element in original_hull_index
                    ]
                    img2_gray = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
                    if first_frame is False:
                        hull2_prev = np.array(hull2, np.float32)
                        img2_gray_prev = np.copy(img2_gray)
                        first_frame = True
                    hull2_next, *_ = cv2.calcOpticalFlowPyrLK(
                        img2_gray_prev,
                        img2_gray,
                        hull2_prev,
                        np.array(hull2, np.float32),
                        winSize=(101, 101),
                        maxLevel=5,
                        criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 20, 0.001),
                    )
                    for i, _ in enumerate(hull2):
                        hull2[i] = 0.3 * np.array(hull2[i]) + 0.7 * hull2_next[i]
                    hull2_prev = np.array(hull2, np.float32)
                    img2_gray_prev = img2_gray
                    img1_warped = np.copy(img2)
                    img1_warped = np.float32(img1_warped)
                    for triangle in delaunay_triangles:
                        mouth_points_set = set(mp[0] for mp in mouth_points)
                        if (
                                triangle[0] in mouth_points_set
                                and triangle[1] in mouth_points_set
                                and triangle[2] in mouth_points_set
                        ):
                            continue
                        t1 = [points1[triangle[0]], points1[triangle[1]], points1[triangle[2]]]
                        t2 = [
                            hull2[landmark_idx_to_list_idx[triangle[0]]],
                            hull2[landmark_idx_to_list_idx[triangle[1]]],
                            hull2[landmark_idx_to_list_idx[triangle[2]]],
                        ]
                        face_swap.warp_triangle(img1, img1_warped, t1, t2)
                    mask = np.zeros(img2.shape, dtype=img2.dtype)
                    cv2.fillConvexPoly(mask, np.int32(original_hull2), (255, 255, 255))
                    r = cv2.boundingRect(np.float32([original_hull2]))
                    center = (r[0] + int(r[2] / 2), r[1] + int(r[3] / 2))
                    try:
                        img2 = cv2.seamlessClone(np.uint8(img1_warped), img2, mask, center, cv2.NORMAL_CLONE)
                    except Exception:
                        pass
                out.write(cv2.resize(img2, size))
                if cv2.waitKey(1) & 0xFF == 27:
                    break
            else:
                cap.release()
                out.release()
                cv2.destroyAllWindows()
                break
        save_path = output_video_path
        output_video_path = output_video_path.replace(root, "")
        out = request.host_url.rstrip('/') + output_video_path + 'video.mp4'
        clip = moviepy.VideoFileClip(out)
        clip.write_videofile(save_path + 'FaceSwaping.mp4')
        return request.host_url.rstrip('/') + output_video_path + 'FaceSwaping.mp4'
    else:
        img2 = cv2.imread(root + '/faceswap/images/' + in_source)
        height, width = img2.shape[:2]
        image_resize = np.float32(height) / RESIZE_HEIGHT
        img2 = cv2.resize(
            img2,
            None,
            fx=1.0 / image_resize,
            fy=1.0 / image_resize,
            interpolation=cv2.INTER_LINEAR,
        )
        try:
            points2 = face_swap.detect_facial_landmarks(face_swap_detector, face_swap_predictor, img2,
                                                        face_down_sample_ratio)

        except Exception:
            pass
        else:
            hull2 = [points2[hull_index_element[0]] for hull_index_element in hull_index]
            original_hull2 = [
                points2[hull_index_element[0]] for hull_index_element in original_hull_index
            ]
            img2_gray = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
            if first_frame is False:
                hull2_prev = np.array(hull2, np.float32)
                img2_gray_prev = np.copy(img2_gray)
            hull2_next, *_ = cv2.calcOpticalFlowPyrLK(
                img2_gray_prev,
                img2_gray,
                hull2_prev,
                np.array(hull2, np.float32),
                winSize=(101, 101),
                maxLevel=5,
                criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 20, 0.001),
            )
            for i, _ in enumerate(hull2):
                hull2[i] = 0.3 * np.array(hull2[i]) + 0.7 * hull2_next[i]
            img1_warped = np.copy(img2)
            img1_warped = np.float32(img1_warped)
            for triangle in delaunay_triangles:
                mouth_points_set = set(mp[0] for mp in mouth_points)
                if (
                        triangle[0] in mouth_points_set
                        and triangle[1] in mouth_points_set
                        and triangle[2] in mouth_points_set
                ):
                    continue
                t1 = [points1[triangle[0]], points1[triangle[1]], points1[triangle[2]]]
                t2 = [
                    hull2[landmark_idx_to_list_idx[triangle[0]]],
                    hull2[landmark_idx_to_list_idx[triangle[1]]],
                    hull2[landmark_idx_to_list_idx[triangle[2]]],
                ]
                face_swap.warp_triangle(img1, img1_warped, t1, t2)
            mask = np.zeros(img2.shape, dtype=img2.dtype)
            cv2.fillConvexPoly(mask, np.int32(original_hull2), (255, 255, 255))
            r = cv2.boundingRect(np.float32([original_hull2]))
            center = (r[0] + int(r[2] / 2), r[1] + int(r[3] / 2))
            try:
                img2 = cv2.seamlessClone(np.uint8(img1_warped), img2, mask, center, cv2.NORMAL_CLONE)
            except Exception:
                pass
        cv2.imwrite(output_video_path + 'swap_image.jpg', img2)
    output_video_path = output_video_path.replace(root, "")
    return request.host_url.rstrip('/') + output_video_path


@app.route('/facemash_image', methods=['GET', 'POST'])
@login_required
def facemash_image():
    try:
        trace.info('Loading image in facemash image')
        try:
            image_name = request.json['image_name']
            video_name = request.json['video_name']
        except AttributeError:
            image_name = 'Jayanth.png'
            video_name = 'faceswap.mp4'
        path = face_mash(image_name, video_name)
        return Response(path)
    except Exception as Ex:
        exc.exception("Failed to facemash_image {}".format(Ex))


@app.route('/face_recognition_image', methods=['GET', 'POST'])
def face_recognition_image():
    try:
        global face_frame_processor
        trace.info('Insert the images in face recognition dataset')
        image_name = request.json['image_name']
        dest_folder = root + '/models/FaceRecognition_model/Images'
        if image_name:
            for image_data in image_name:
                img = Image.open(io.BytesIO(base64.decodebytes(bytes(str(image_data['base64']).split(',')[1], "utf-8"))))
                image_path = dest_folder + "/" + str(image_data['name'])
                img.save(image_path)
            # for files in image_name:
            #     shutil.copy(files, dest_folder)
            face_frame_processor = FrameProcessor(face_args)
        return Response('true')
    except Exception as Ex:
        exc.exception("Failed to face_recognition_image {}".format(Ex))
        return Response('false')


@app.route('/face_image_capture', methods=['GET'])
def face_image_capture():
    try:
        global trigger, face_image_name, face_frame_processor, startTime, trigger1
        trigger = True
        trigger1 = True
        startTime = time.time()
        face_image_name = request.json['image_name']
        return "image writed"
    except Exception as Ex:
        exc.exception("Error in face_image_capture {}".format(Ex))


def face_generate_frames(url):
    try:
        global trigger, face_image_name, face_frame_processor, startTime, trigger1
        cam = cv2.VideoCapture(url)
        while True:
            ret, frame = cam.read()
            if not ret:
                print("failed to grab frame")
                break
            else:
                ret, buffer = cv2.imencode('.jpg', frame)
                if trigger:
                    end = time.time()
                    dest_folder = root + '/models/FaceRecognition_model/Images/'
                    file = dest_folder + "/image_" + str(datetime.now().strftime("%d-%m-%Y_%I-%M-%S_%p")) + ".jpg"
                    not_exits = os.path.exists(os.path.join(os.getcwd(), 'name', 'file'))
                    print(not_exits)
                    total = end - startTime
                    if trigger1:
                        cv2.imwrite(file, frame)
                        trigger1 = False
                    if (total > 20):
                        for duplicate in range(0, 2):
                            dest_folder = root + '/models/FaceRecognition_model/Images/'
                            file = dest_folder + face_image_name + "_" + str(duplicate) + str(
                                datetime.datetime.now().strftime("%d-%m-%Y_%I-%M-%S_%p")) + ".jpg"

                            cv2.imwrite(file, frame)
                            face_frame_processor = FrameProcessor(face_args)
                        trigger = False

                frame = buffer.tobytes()
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')

    except Exception as Ex:
        exc.exception("Error in face_generate_frames {}".format(Ex))


@app.route('/face_video')
def face_video():
    try:
        url = request.json['url']
        return Response(face_generate_frames(url), mimetype='multipart/x-mixed-replace; boundary=frame')
    except Exception as Ex:
        exc.exception("Error in face_video {}".format(Ex))


@app.route('/faceswap/<path:path>', methods=['GET', 'POST'])
@cross_origin(origin='*')
def faceswap_stream(path):
    try:
        video_dir = "faceswap/"
        return send_from_directory(directory=video_dir, path=path)
    except FileNotFoundError:
        abort(404)


@app.route('/face_swap')
@login_required
def facemash():
    try:
        trace.info('Rendering PredictedView Page')
        return render_template('faceswap.html')
    except Exception as e:
        exc.exception('Error rendering PredictedView Page {}'.format(e))


@app.route('/video_summary')
@login_required
def video_summary():
    try:
        input_file = root + "/video_summary/input/moving_avg.mp4"
        # path for video chunk creation
        chunk_path = root + '/video_summary/chunk_video/'
        # path for video process using multiprocess
        process_path = root + '/video_summary/process_video/'
        # path for concatenating video files
        output_path = root + '/video_summary/output/'
        video_summary_out = main.video_summary_main(chunk_path, process_path, output_path, input_file,
                                                    split_timing='30')
        video_summary_data = video_summary_out.replace(root, "")
        return request.host_url.rstrip('/') + video_summary_data
    except Exception as e:
        exc.exception('Failed to create an video summary {}'.format(e))


@app.route('/video_summary/<path:path>', methods=['GET', 'POST'])
@cross_origin(origin='*')
def video_summary_stream(path):
    try:
        video_dir = "video_summary/"
        return send_from_directory(directory=video_dir, path=path)
    except FileNotFoundError:
        abort(404)


@app.route('/summarized_video', methods=['GET', 'POST'])
@login_required
def summarized_video():
    try:
        video_dir = root + "/output/"
        path = "finaloutput.mp4"
        return send_from_directory(directory=video_dir, path=path)
    except FileNotFoundError:
        abort(404)


@app.route('/settings')
@login_required
def settings():
    try:
        trace.info('Rendering PredictedView Page')
        return render_template('Settings.html')
    except Exception as e:
        exc.exception('Error rendering Settings Page {}'.format(e))


@app.route('/flag_update', methods=['GET', 'POST'])
@login_required
def flag_update():
    try:
        if request.method == 'POST':
            global image_flag, elasticsearch_flag, Db_image_flag, email_flag, audio_enable_flag, time_date_flag, whatsapp_flag
            user_id = session.get('id')
            if user_id is None:
                user_id = 1
            flg_details = request.json['data']
            flags = DataAccess.check_flag_details(user_id, flg_details['image_flag'], flg_details['elasticsearch_flag'], flg_details['db_flag'], flg_details['audio_enable_flag'], flg_details['time_flag'], flg_details['email_flag'], flg_details['whatsapp_flag'])
            session['image_flag'] = image_flag = bool(flags['image_flag'])
            session['elasticsearch_flag'] = elasticsearch_flag = bool(flags['elasticsearch_flag'])
            session['Db_image_flag'] = Db_image_flag = bool(flags['db_flag'])
            session['audio_enable_flag'] = audio_enable_flag = bool(flags['audio_enable_flag'])
            session['time_date_flag'] = time_date_flag = bool(flags['time_flag'])
            session['email_flag'] = email_flag = bool(flags['email_flag'])
            session['whatsapp_flag'] = whatsapp_flag = bool(flags['whatsapp_flag'])
            flags.pop('id')
            flags.pop('user_id')
            return jsonify(flags)
        else:
            user_id = session.get('id')
            if user_id is None:
                user_id = 1
            flags = DataAccess.flag_details(user_id)
            flags.pop('id')
            flags.pop('user_id')
            return jsonify(flags)
    except Exception as e:
        exc.exception('Error rendering flag_update function {}'.format(e))


def apparel_detection():
    try:
        # threading.Timer(120.0, apparel_detection).start()
        user_id_lst = []
        with open(root + "/cam_details.json") as apparel_cam_json_file:
            json_ob = json.load(apparel_cam_json_file)
        for i, stream_details in enumerate(json_ob):
            for key, value in stream_details.items():
                if key != 'status' and key != 'process_id':
                    txt = key.split('/')
                    if txt[2] not in user_id_lst:
                        user_id_lst.append(txt[2])
        if user_id_lst:
            user_id = int(user_id_lst[0])
            apparel_flg = "False"
            search = SearchEngine()
            apparel_thread = threading.Thread(target=search.apparel_elastic, args=(user_id,
                                                                                   apparel_flg, fashion_model, root))
            video_gen_thread = threading.Thread(target=search.vid_gen, args=(user_id, "False", root))
            face_dence_thread = threading.Thread(target=search.face_elastic, args=(user_id, "FaceDetection",
                                                                                   apparel_flg))
            person_based_apparel = threading.Thread(target=search.person_based_apparel_detect, args=(user_id,
                                                                                                     apparel_flg,
                                                                                                     fashion_model,
                                                                                                     root))
            face_dence_thread.daemon = True
            video_gen_thread.daemon = True
            apparel_thread.daemon = True
            person_based_apparel.daemon = True
            face_dence_thread.start()
            face_dence_thread.join()
            video_gen_thread.start()
            video_gen_thread.join()
            apparel_thread.start()
            apparel_thread.join()
            person_based_apparel.start()
            person_based_apparel.join()

    except Exception as e:
        exc.exception('Error in apparel_detection function {}'.format(e))


@app.after_request
def after_request_func(response):
    if request.endpoint == 'dashboard':
        for video_cap_id in video_processes:
            if video_cap_id.t.is_alive():
                video_cap_id.killing()
        for video_cap in videocapture1_list:
            if video_cap.t.is_alive():
                video_cap.killing()
        print("after_request executing!")
    return response


# scheduler = BackgroundScheduler()
# scheduler.add_job(apparel_detection, "interval", seconds=240)
# scheduler.start()

if __name__ == '__main__':
    app.add_url_rule('/', view_func=LoginView.as_view('initial'), methods=['GET'])
    app.add_url_rule('/login', view_func=LoginView.as_view('login'), methods=['GET', "POST"])
    app.add_url_rule('/dashboard', view_func=DashboardView.as_view('dashboard'), methods=['GET'])
    app.add_url_rule('/search_dashboard', view_func=SearchDashboardView.as_view('search_dashboard'), methods=['GET'])
    app.add_url_rule('/third_party_settings', view_func=ThirdPartySettings.as_view('third_party_settings'), methods=['GET'])
    app.add_url_rule('/report', view_func=ReportPageView.as_view('report'), methods=['GET'])
    app.add_url_rule('/add_camera', view_func=CameraView.as_view('add_camera'), methods=['GET'])
    app.add_url_rule('/camera_details', view_func=CameraDetailsView.as_view('camera_details'), methods=['POST'])
    app.add_url_rule('/add_cam', view_func=AddCameraView.as_view('add_cam'), methods=['DELETE', 'GET', 'POST', ])
    app.debug = True
    elasticbackup = False
    text_flag = False
    face_match = True
    GstreamerProcess().reload_gstreamer()
    update_status()
    reset_stream_details()
    app.run(host=app_host, port=app_port, threaded=True, use_reloader=False)
