import numpy as np

from alerts.alarm_sounds.audioplaybackbg import *

kernel = np.ones((5, 5), np.uint8)
root = os.getcwd()

import cv2
import numpy as np
import os
from dataclasses import dataclass
from typing import Optional, Dict
from datetime import datetime

BGRImageArray = np.ndarray

@dataclass
class MovementDetector:
    seed: int = 1234
    n_samples: int = 1000
    decay_rate: float = 0.2
    spatial_highpass_filter_width: int = 10
    max_black_frames: int = 10

    _ixs: Optional[np.ndarray] = None
    _lowpass_img: Optional[np.ndarray] = None
    _black_frame_count: int = 0

    def get_moving_score(self, image: BGRImageArray) -> float:
        im_grey = image.mean(axis=2)
        im_grey_highpass = im_grey - cv2.boxFilter(im_grey, ddepth=-1, ksize=(
            self.spatial_highpass_filter_width, self.spatial_highpass_filter_width))
        im_flat = im_grey_highpass.reshape(-1)

        if self._ixs is None:
            self._ixs = np.random.RandomState(self.seed).choice(len(im_flat), size=min(len(im_flat), self.n_samples),
                                                                replace=False)
            if self._lowpass_img is None:
                self._lowpass_img = np.zeros_like(im_flat, dtype=np.float32)

        historical_values = self._lowpass_img[self._ixs]
        current_values = im_flat[self._ixs]
        pixel_correlation = np.corrcoef(historical_values, current_values)
        self._lowpass_img = self.decay_rate * im_flat + (1 - self.decay_rate) * self._lowpass_img

        return 1 - pixel_correlation[0, 1]

    def is_camera_covered(self, image: BGRImageArray) -> bool:
        """ Check if the image is completely black. """
        return np.mean(image) < 10

    def process_frame(self, image: BGRImageArray) -> dict:
        frame = image
        cam_tampering_flag = False
        im = frame.copy()
        """ Process the frame to detect movement and camera coverage. """
        resized_frame = cv2.resize(image, (640, 360))

        result = {"status": None, "filename": None}

        if self.is_camera_covered(resized_frame):
            self._black_frame_count += 1
            if self._black_frame_count >= self.max_black_frames:
                print(f"\033[91mCamera Covered Detected!\033[0m")
                cv2.putText(frame, "TAMPERING DETECTED", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2, cv2.LINE_AA)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                filename = os.path.join("movement_detected", f"camera_covered_{timestamp}.jpg")
                cv2.imwrite(filename, frame)
                cam_tampering_flag = True
                print(f"Saved covered frame: {filename}")
                result["status"] = "Camera Covered"
                result["filename"] = filename

        else:
            self._black_frame_count = 0

        score = self.get_moving_score(resized_frame)
        print(f"Score : {score}")

        if score >= 0.8:
            print(f"\033[92mMovement Detected: {score:.2f}\033[0m")
            cv2.putText(frame, "TAMPERING DETECTED", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2, cv2.LINE_AA)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            filename = os.path.join("movement_detected", f"{timestamp}.jpg")
            cv2.imwrite(filename, frame)
            cam_tampering_flag = True
            print(f"Saved movement frame: {filename}")
            result["status"] = "Movement Detected"
            result["filename"] = filename

        return frame, cam_tampering_flag,im
