import numpy as np
import cv2
import os
import time
import threading

class RTSPStreamProcessor(threading.Thread):
    def __init__(self, rtsp_url, output_folder, log_file_path, frame_count_start):
        super().__init__()
        self.rtsp_url = rtsp_url
        self.output_folder = output_folder
        self.log_file_path = log_file_path
        self.frame_count = frame_count_start
        self.cap = cv2.VideoCapture(rtsp_url)
        self.fgbg = cv2.createBackgroundSubtractorMOG2()
        self.kernel = np.ones((5, 5), np.uint8)
        self.fps = 5  # Target frames per second
        self.frame_interval = 1.0 / self.fps  # Time between frames in seconds
        self.last_time = time.time()

    def run(self):
        while True:
            ret, frame = self.cap.read()
            if not ret or frame is None:
                print(f"End of stream or unable to read frame from {self.rtsp_url}")
                break

            current_time = time.time()
            if (current_time - self.last_time) >= self.frame_interval:
                self.last_time = current_time

                try:
                    fgmask = self.fgbg.apply(frame)
                    fgmask = cv2.erode(fgmask, self.kernel, iterations=5)
                    fgmask = cv2.dilate(fgmask, self.kernel, iterations=5)

                    contours, _ = cv2.findContours(fgmask, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
                    bounding_rect = [cv2.boundingRect(c) for c in contours]

                    a = sum(rect[2] * rect[3] for rect in bounding_rect if rect[2] >= 40 or rect[3] >= 40)
                    print(f"Area: {a}")

                    # text_1 = f"{a}"
                    # cv2.putText(fgmask, text_1, (5, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

                    if (a >80000) and (a < 700000):
                        print(f"TAMPERING DETECTED in Stream {self.rtsp_url} : {a}")
                        text = f"TAMPERING DETECTED : {a}"
                        cv2.putText(frame, text, (5, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)

                        # Save the tampered frame with text
                        frame_filename = os.path.join(self.output_folder, f"frame_{self.frame_count:04d}_a_{a}.jpg")
                        frame_filename_1 = os.path.join(self.output_folder, f"frame_{self.frame_count:04d}_a_{a}_1.jpg")
                        cv2.imwrite(frame_filename, frame)
                        cv2.imwrite(frame_filename_1, fgmask)

                        # Write the value of 'a' to the log file
                        with open(self.log_file_path, "a") as log_file:
                            log_file.write(f"Stream {self.rtsp_url} - Frame {self.frame_count}: a = {a}\n")

                        self.frame_count += 1

                    framee = cv2.resize(frame, (500, 500))
                    fgmaskk = cv2.resize(fgmask, (500, 500))
                    cv2.imshow(f'Stream_{self.rtsp_url}_frame', framee)
                    cv2.imshow(f'Stream_{self.rtsp_url}_fgmask', fgmaskk)

                except cv2.error as e:
                    print(f"OpenCV error: {e}")
                    continue

            k = cv2.waitKey(1) & 0xff
            if k == 27:  # ESC key
                break

        self.cap.release()
        cv2.destroyAllWindows()

def main():
    # Define RTSP URLs
    rtsp_urls = [
        # "rtsp://admin:Admin123$@10.11.25.60:554/stream1",
        # "rtsp://admin:Admin123$@10.11.25.61:554/stream2",
        "rtsp://admin:Admin123$@10.11.25.65:554/stream4"
    ]

    # Define the folder to save tampered frames and log file
    output_folder = "tampered_frames"
    os.makedirs(output_folder, exist_ok=True)
    log_file_path = "tampered_frames_log.txt"

    # Start processing each RTSP stream in a separate thread
    threads = []
    for i, url in enumerate(rtsp_urls):
        processor = RTSPStreamProcessor(url, output_folder, log_file_path, frame_count_start=i * 1000)
        processor.start()
        threads.append(processor)

    # Wait for all threads to complete
    for thread in threads:
        thread.join()

if __name__ == "__main__":
    main()
