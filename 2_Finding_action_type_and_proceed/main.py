import pandas as pd

# Load the Excel file
file_path = "input.xlsx"
df = pd.read_excel(file_path)

# Prepare the result
command_data = {}

for idx, command in df['Command'].dropna().items():
    lines = command.strip().splitlines()
    filtered = []
    skip_prefixes = ('sample', 'start', 'stop')

    # Skip lines with those prefixes, collect the rest
    for line in lines:
        line_lower = line.strip().lower()
        if line_lower.startswith(skip_prefixes):
            continue
        if line.strip():
            filtered.append(line.strip())

    if filtered:
        command_data[idx] = filtered

# Print or return the extracted command lines
for idx, commands in command_data.items():
    print(f"Row {idx}: {commands}")
