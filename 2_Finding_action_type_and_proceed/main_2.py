import pandas as pd
import requests
import os
import time
import gradio as gr

class ActionTypeClassifier:
    def __init__(self, file_path, api_url):
        self.file_path = file_path
        self.api_url = api_url
        self.df = self._load_file()

    def _load_file(self):
        _, ext = os.path.splitext(self.file_path)
        if ext.lower() == ".csv":
            return pd.read_csv(self.file_path)
        elif ext.lower() in [".xls", ".xlsx"]:
            return pd.read_excel(self.file_path)
        else:
            raise ValueError("Unsupported file format. Use .csv or .xlsx")

    def _get_first_valid_command(self, command):
        lines = command.strip().splitlines()
        skip_prefixes = ("sample", "start", "stop")
        return [line.strip() for line in lines if not line.strip().lower().startswith(skip_prefixes)]

    def _call_api(self, command):
        try:
            response = requests.post(self.api_url, json={"command": command}, timeout=50)
            response.raise_for_status()
            return response.json().get("category", "")
        except Exception as e:
            return f"Error: {e}"

    def _adjust_action_type(self, action_type, success_criteria):
        if pd.notna(success_criteria):
            if "ExecuteOSCommand" in action_type:
                return action_type.replace("ExecuteOSCommand", "ExecuteCheckOSCommand")
            elif "ExecuteSqlCommand" in action_type:
                return action_type.replace("ExecuteSqlCommand", "ExecuteCheckSqlCommand")
            elif "ExecutePowerShellCommand" in action_type:
                return action_type.replace("ExecutePowerShellCommand", "ExecuteCheckPowerShellCommand")
        return action_type

    def process(self):
        for idx, row in self.df.iterrows():
            if pd.notna(row.get("Preferred Action Type", "")):
                continue

            command = row.get("Command", "")
            description = row.get("Description", "")
            if pd.isna(command) or pd.isna(description):
                continue

            actual_commands = self._get_first_valid_command(command)
            if not actual_commands:
                continue

            first_command = actual_commands[0]
            print(f"{idx + 1} - Processing: {first_command}")
            action_type = self._call_api(first_command)
            action_type = self._adjust_action_type(action_type, row.get("Success Criteria in the output", ""))
            print(f"{action_type}")
            self.df.at[idx, "Preferred Action Type"] = action_type

    def save(self, output_path="output_with_action_types.xlsx"):
        with pd.ExcelWriter(output_path, engine='openpyxl', mode='w') as writer:
            self.df.to_excel(writer, index=False)
        return output_path


def process_file(file, api_url):
    file_path = file.name
    classifier = ActionTypeClassifier(file_path, api_url)
    classifier.process()
    output_file = classifier.save()
    return output_file


demo = gr.Interface(
    fn=process_file,
    inputs=[
        gr.File(label="Upload Excel or CSV file"),
        gr.Textbox(label="API URL", value="http://***********:8083/classify")
    ],
    outputs=gr.File(label="Download Processed File"),
    title="Action Type Classifier",
    description="Upload a document with command descriptions and get back an updated file with action types.",
)

demo.launch(share=True)  # This enables a public Gradio link
