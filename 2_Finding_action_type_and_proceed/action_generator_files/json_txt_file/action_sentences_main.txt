Execute and check Power shell (powershell) command or Check powershell,
Verify if SSH is installed,
Automate UI interactions using a password,
Automate UI interactions,
Disable or stop or kill User Account Control (UAC),
Test SRM (Site Recovery Manager) connectivity,
Stop the Microsoft Cluster or kill ms cluster,
Start a scheduled task or Start job,
Start the Microsoft Cluster service,
Kill or stop a service or process in unix or linux,
Check if the cluster is online | check the cluster status,
Check if the cluster is offline | check the status of the cluster,
Install OpenSSH (new method),
Install OpenSSH,
Compare two folders for differences,
Compare two files for differences,
Execute or run a script with user password or credentials,
Execute or run a script with a password,
Execute or run a script using environment variables for password,
Execute or run a script along with the password,
Execute or run a script,
Run an SSH command in a single session,
Execute or run OS ZFS command,
Execute or run OS command using a password,
Execute or Run a local process on the operating system,
Check OS command output using a password,
Compare files or folders,
Remove a dependency from a cluster resource,
Add a dependency to a cluster resource,
Change the IP address of a node,
Monitor the status of an application service,
Add a user to the administrator group,
Execute powershell command | Execute power shell command | Execute shell command,
Enable folder target referral namespace | Enable folder | Set folder | Enable folder target referral namespace | Enable folder | Set folder in Windows,
Disable folder target referral namespace | Disable folder | Remove folder | Delete folder | Disable folder target referral namespace | Disable folder | Remove folder | Delete folder in Windows,
Check the status of folder target referral namespace | Check folder | Check the status of folder target referral namespace | Check folder in Windows,
Pause execution for a specified time | wait command | wait | Pause execution for a specified time | wait command | wait in Windows,
Kill a Windows process or service by its name | Kill a Windows process or service by its name in Windows,
Check if a file exists in Windows | Check if a file exists in Windows in Windows,
Rename a file in Windows | Rename a file in Windows in Windows,
Change text in a file in Windows | Change text in a file in Windows in Windows,
Wait for a ping response from a server | Wait for a ping response from a server in Windows,
Stop a scheduled task or Stop job | Stop a scheduled task or Stop job in Windows,
Start a scheduled task or Start job | Start a scheduled task or Start job in Windows,
Shut down a remote server | Shut down a remote server in Windows,
Shut down the local server | Shut down the local server in Windows,
Transfer files using SCP | Transfer files using SCP in Windows,
Replicate the standby control file | replicate control file | replicate file | Replicate the standby control file | replicate control file | replicate file in Windows,
Replicate and sync data folders on Windows | replicate folder | replicate file | Replicate and sync data folders on Windows | replicate folder | replicate file in Windows,
Replicate and sync a single file on Windows | replicate file | Replicate and sync a single file on Windows | replicate file in Windows,
Replace text within a file | Change text within a file | Replace text | Replace text within a file | Change text within a file | Replace text in Windows,
Replace a file with another | Replace a file with another in Windows,
Remove NT AUTHORITY SYSTEM account from DNS record with allow or deny permission | Remove NT AUTHORITY SYSTEM account from DNS record with allow or deny permission in Windows,
Remove a computer account from DNS record with allow or deny permission | Remove a computer account from DNS record with allow or deny permission in Windows,
Reboot a remote server | Reboot a remote server in Windows,
Replicate data for disaster recovery | Replicate data for disaster recovery in Windows,
Modify DNS record value of A-type without TTL | Modify DNS record value of A-type without TTL in Windows,
Modify DNS record value of A-type with TTL | Modify DNS record value of A-type with TTL in Windows,
Verify if Microsoft SQL Server and SSMS are installed | Verify if Microsoft SQL Server and SSMS are installed in Windows,
Verify if Microsoft SQL Server is installed | Verify if Microsoft SQL Server is installed in Windows,
Execute a Windows process | Execute a Windows process in Windows,
Run a Symcli command | Run a Symcli command in Windows,
Run an SSH command | Run an SSH command in Windows,
Run an OS command | Run an OS command in Windows,
Verify Symcli command output | Verify Symcli command output in Windows,
Execute a batch file | Execute a batch file in Windows,
Enable a scheduled task | Enable a scheduled task in Windows,
Disable a scheduled task | Disable a scheduled task in Windows,
Delete a DNS record | Delete a DNS record in Windows,
Replicate a file for data synchronization | Replicate a file for data synchronization in Windows,
Sync redo archive folders for data | Sync redo archive folders for data in Windows,
Sync folders for data | Sync folders for data in Windows,
Sync archive folders for data | Sync archive folders for data in Windows,
Set TTL value for DNS SOA record on a DNS server | Set TTL value for DNS SOA record on a DNS server in Windows,
Check if a string exists in a file | Check if a string exists in a file in Windows,
Check the status of a service | Check the status of a service in Windows,
Check the status of a scheduled task | Check the status of a scheduled task in Windows,
Verify if a file exists | Verify if a file exists in Windows,
Check if a DNS record without TTL exists for A-type | Check if a DNS record without TTL exists for A-type in Windows,
Check if a DNS record with TTL exists for A-type | Check if a DNS record with TTL exists for A-type in Windows,
Change the start mode of a service | Change the start mode of a service in Windows,
Update the DNS configuration | Update the DNS configuration in Windows,
Execute a batch file | Execute a batch file in Windows,
Stop an application | Stop an application in Windows,
Start an application | Start an application in Windows,
Add a new record to a text file | Add a new record to a text file in Windows,
Add NT AUTHORITY SYSTEM account to DNS record with deny permission | Add NT AUTHORITY SYSTEM account to DNS record with deny permission in Windows,
Add NT AUTHORITY SYSTEM account to DNS record with allow permission | Add NT AUTHORITY SYSTEM account to DNS record with allow permission in Windows,
Add a DNS record | Add a DNS record in Windows,
Add a computer account to a DNS record with deny permission | Add a computer account to a DNS record with deny permission in Windows,
Add a computer account to a DNS record with allow permission | Add a computer account to a DNS record with allow permission in Windows,
Verify if a Windows service or a process is running | Verify if a Windows service or a process is running in Windows,
Verify if a Windows service or a process is down | Verify if a Windows service or a process is down in Windows,
Stop a Windows service or process | Stop a Windows service or process in Windows,
Stop a service or a process | Stop a service or a process in Windows,
Start a service or a process | Start a service or a process in Windows,
Start a Windows service or a process | Start a Windows service or a process in Windows,
Enable a NetScaler load balancing server | Enable a NetScaler load balancing server in Windows,
Disable a NetScaler load balancing server | Disable a NetScaler load balancing server in Windows,
Check the status of the NetScaler load balancer | Check the status of the NetScaler load balancer in Windows,
Verify if a DNS CNAME record exists | Verify if a DNS CNAME record exists in Windows,
Transfer or seize the FSMO (Flexible Single Master Operation) roles | Transfer or seize the FSMO (Flexible Single Master Operation) roles in Windows,
Remove SPN (Service Principal Name) from an Active Directory computer object | Remove SPN (Service Principal Name) from an Active Directory computer object in Windows,
Modify a DNS CNAME record with TTL settings | Modify a DNS CNAME record with TTL settings in Windows,
Modify a DNS A record with TTL settings | Modify a DNS A record with TTL settings in Windows,
Verify DC (Domain Controller) server status | Verify DC (Domain Controller) server status in Windows,
Verify DNS configuration and settings | Verify DNS configuration and settings in Windows,
Modify DNS records | Modify DNS records in Windows,
Check the replication sync status across all Active Directory (AD) objects | Check the replication sync status across all Active Directory (AD) objects in Windows,
Check the replication status of Active Directory | Check the replication status of Active Directory in Windows,
Verify the FSMO roles for an Active Directory forest | Verify the FSMO roles for an Active Directory forest in Windows,
Verify the FSMO roles for an Active Directory domain | Verify the FSMO roles for an Active Directory domain in Windows,
Change the DNS glue record | Change the DNS glue record in Windows,
Add SPN (Service Principal Name) to an Active Directory computer object | Add SPN (Service Principal Name) to an Active Directory computer object in Windows,
Add a new user to Active Directory | Add a new user to Active Directory in Windows,
Check Active Directory replication sync status across all objects | Check Active Directory replication sync status across all objects in Windows,
Check the replication status of Active Directory | Check the replication status of Active Directory in Windows,
Verify the state of cluster resources in Windows Server 2008 | Verify the state of cluster resources in Windows Server 2008 in Windows,
Stop a cluster resource in Windows Server 2008 | Stop a cluster resource in Windows Server 2008 in Windows,
Start a cluster resource in Windows Server 2008 | Start a cluster resource in Windows Server 2008 in Windows,
Bring a resource online | Bring a resource online in Windows,
Take a resource offline | Take a resource offline in Windows,
Import a configuration or resource | Import a configuration or resource in Windows,
Rescan disks for new or updated configurations | Rescan disks for new or updated configurations in Windows,
Bring a disk online with an assigned drive letter | Bring a disk online with an assigned drive letter in Windows,
Check the status of a disk | Check the status of a disk in Windows,
Set a disk to read-write mode | Set a disk to read-write mode in Windows,
Bring a disk online | Bring a disk online in Windows,
Take a disk offline | Take a disk offline in Windows,
Verify drive letter assignment after bringing a disk online | Verify drive letter assignment after bringing a disk online in Windows,
Check disk details using LUN or WWN number | Check disk details using LUN or WWN number in Windows,
Change the drive letter of a disk | Change the drive letter of a disk in Windows,
Execute a 5250 transaction and check the popup count | Execute a 5250 transaction and check the popup count in MainFrame,
Execute a 5250 process | Execute a 5250 process in MainFrame,
Monitor AS/400 operations | Monitor AS/400 operations in MainFrame,
Check the status of a client LPAR (Logical Partition) | Check the status of a client LPAR (Logical Partition) in IBM_AIX,
Verify the boot disk mapping of a client LPAR | Verify the boot disk mapping of a client LPAR in IBM_AIX,
Activate a client LPAR | Activate a client LPAR in IBM_AIX,
Wait for the LPAR state to update | Wait for the LPAR state to update in IBM_AIX,
Shut down a client LPAR | Shut down a client LPAR in IBM_AIX,
Check the status of Ethernet connections | Check the status of Ethernet connections in IBM_AIX,
Bring volume groups online (vary on) | Bring volume groups online (vary on) in IBM_AIX,
Take volume groups offline (vary off) | Take volume groups offline (vary off) in IBM_AIX,
Unmount volume groups | Unmount volume groups in IBM_AIX,
Unmount volume groups (shorthand - VGS) | Unmount volume groups (shorthand - VGS) in IBM_AIX,
Unmount a specific volume group (VG) | Unmount a specific volume group (VG) in IBM_AIX,
Unmount an NFS volume | Unmount an NFS volume in IBM_AIX,
Replicate a standby trace file | Replicate a standby trace file in IBM_AIX,
Sync data folders in a POSIX environment | Sync data folders in a POSIX environment in IBM_AIX,
Sync data folders for disaster recovery | Sync data folders for disaster recovery in IBM_AIX,
Sync a single data file in a POSIX environment | Sync a single data file in a POSIX environment in IBM_AIX,
Remove an HDisk device | Remove an HDisk device in IBM_AIX,
Remove volume groups in a specific configuration | Remove volume groups in a specific configuration in IBM_AIX,
Mount volume groups | Mount volume groups in IBM_AIX,
Mount volume groups (shorthand - VGS) | Mount volume groups (shorthand - VGS) in IBM_AIX,
Mount a specific volume group (VG) | Mount a specific volume group (VG) in IBM_AIX,
Mount an NFS volume | Mount an NFS volume in IBM_AIX,
Kill a process by its ID or name | Kill a process by its ID or name in IBM_AIX,
Unmount volume groups in HP systems | Unmount volume groups in HP systems in IBM_AIX,
Unmount volume groups in parallel on HP systems | Unmount volume groups in parallel on HP systems in IBM_AIX,
Deactivate a volume group in HP-UX | Deactivate a volume group in HP-UX in IBM_AIX,
Activate a volume group in HP-UX | Activate a volume group in HP-UX in IBM_AIX,
Unmount a file system in HP-UX | Unmount a file system in HP-UX in IBM_AIX,
Mount a file system in HP-UX | Mount a file system in HP-UX in IBM_AIX,
Verify if a volume group is active in HP-UX | Verify if a volume group is active in HP-UX in IBM_AIX,
Import a volume group using a map file in HP-UX | Import a volume group using a map file in HP-UX in IBM_AIX,
Export a volume group to a map file in HP-UX | Export a volume group to a map file in HP-UX in IBM_AIX,
Mount volume groups in HP systems | Mount volume groups in HP systems in IBM_AIX,
Mount volume groups in parallel on HP systems | Mount volume groups in parallel on HP systems in IBM_AIX,
Export volume groups configuration | Export volume groups configuration in IBM_AIX,
Execute a command using nohup (no hang-up) | Execute a command using nohup (no hang-up) in IBM_AIX,
Execute a CPSL process | Execute a CPSL process in IBM_AIX,
Enable SLB (Server Load Balancer) real services | Enable SLB (Server Load Balancer) real services in IBM_AIX,
Disable SLB (Server Load Balancer) real services | Disable SLB (Server Load Balancer) real services in IBM_AIX,
Check the status of a specific SLB real service | Check the status of a specific SLB real service in IBM_AIX,
Mount a file system in AIX | Mount a file system in AIX in IBM_AIX,
Unmount a file system in AIX | Unmount a file system in AIX in IBM_AIX,
Change text in a file on AIX | Change text in a file on AIX in IBM_AIX,
Execute a CPL process | Execute a CPL process in IBM_AIX,
Run an OS command | Run an OS command in IBM_AIX,
Verify output of an OS command | Verify output of an OS command in IBM_AIX,
Replicate folders using rsync in POSIX environments | Replicate folders using rsync in POSIX environments in OracleRsync,
Replicate a file using rsync in POSIX environments | Replicate a file using rsync in POSIX environments in OracleRsync,
Replicate a standby control file using Oracle's rsync | Replicate a standby control file using Oracle's rsync in OracleRsync,
Replicate a standby trace file using Oracle's rsync | Replicate a standby trace file using Oracle's rsync in OracleRsync,
Verify the output of an XCLI command | Verify the output of an XCLI command in AIX  LINUX UNIX,
Verify the status of a virtual machine (VM) | Verify the status of a virtual machine (VM) in Hypervisor,
Check if a failover has completed successfully | Check if a failover has completed successfully in Hypervisor,
Verify the current state of a VM or resource | Verify the current state of a VM or resource in Hypervisor,
Check reverse replication connection configuration | Check reverse replication connection configuration in Hypervisor,
Verify the replication mode | Verify the replication mode in Hypervisor,
Verify the state of a replication process | Verify the state of a replication process in Hypervisor,
Check if the system is prepared for failover | Check if the system is prepared for failover in Hypervisor,
Verify if a Hyper-V VM is connected to the specified VLAN ID | Verify if a Hyper-V VM is connected to the specified VLAN ID in Hypervisor,
Verify if a Hyper-V VM is disconnected | Verify if a Hyper-V VM is disconnected in Hypervisor,
Verify if a Hyper-V VM is connected | Verify if a Hyper-V VM is connected in Hypervisor,
Check forward replication connection configuration | Check forward replication connection configuration in Hypervisor,
Start a virtual machine (VM) | Start a virtual machine (VM) in Hypervisor,
Initiate a failover process | Initiate a failover process in Hypervisor,
Retrieve the IP address of a single-network Hyper-V VM using its MAC address | Retrieve the IP address of a single-network Hyper-V VM using its MAC address in Hypervisor,
Shut down a virtual machine (VM) | Shut down a virtual machine (VM) in Hypervisor,
Set a VLAN ID to a virtual switch on a Hyper-V VM | Set a VLAN ID to a virtual switch on a Hyper-V VM in Hypervisor,
Resume replication for Hyper-V | Resume replication for Hyper-V in Hypervisor,
Fail over a replica VM | Fail over a replica VM in Hypervisor,
Disconnect from a Hyper-V VM | Disconnect from a Hyper-V VM in Hypervisor,
Connect to a virtual switch on a Hyper-V VM | Connect to a virtual switch on a Hyper-V VM in Hypervisor,
Verify the primary replication mode in a cluster | Verify the primary replication mode in a cluster in Hypervisor,
Change the replication mode | Change the replication mode in Hypervisor,
Change the DNS IP address | Change the DNS IP address in Hypervisor,
Change the IP address of a single-network Hyper-V VM without using a MAC address | Change the IP address of a single-network Hyper-V VM without using a MAC address in Hypervisor,
Change the IP address of a cluster Hyper-V VM using its MAC address | Change the IP address of a cluster Hyper-V VM using its MAC address in Hypervisor,
Cancel a failover for a cluster Hyper-V VM | Cancel a failover for a cluster Hyper-V VM in Hypervisor,
Retrieve the IP address of a single-network Hyper-V VM with incorrect MAC address handling | Retrieve the IP address of a single-network Hyper-V VM with incorrect MAC address handling in Hypervisor,
Monitor actions in Hyper-V | Monitor actions in Hyper-V in Hypervisor,
Remove a cluster resource dependency from a dependent resource | Remove a cluster resource dependency from a dependent resource in Hypervisor,
Add a cluster resource dependency to a dependent resource | Add a cluster resource dependency to a dependent resource in Hypervisor,
Verify a cluster resource dependency in a dependent resource | Verify a cluster resource dependency in a dependent resource in Hypervisor,
Remove a cluster resource from the cluster owner group | Remove a cluster resource from the cluster owner group in Hypervisor,
Add a cluster resource to the cluster owner group | Add a cluster resource to the cluster owner group in Hypervisor,
Verify the disk status on a cluster shared volume | Verify the disk status on a cluster shared volume in Microsoft,
Check the status of a cluster shared volume disk in a failover cluster | Check the status of a cluster shared volume disk in a failover cluster in Microsoft,
Remove a virtual sharing hard disk from a duplicate VM | Remove a virtual sharing hard disk from a duplicate VM in Microsoft,
Remove a virtual sharing hard disk | Remove a virtual sharing hard disk in Microsoft,
Enable the virtual hard disk sharing option for a duplicate VM | Enable the virtual hard disk sharing option for a duplicate VM in Microsoft,
Enable the virtual hard disk sharing option | Enable the virtual hard disk sharing option in Microsoft,
Disable the virtual hard disk sharing option for a duplicate VM | Disable the virtual hard disk sharing option for a duplicate VM in Microsoft,
Disable the virtual hard disk sharing option | Disable the virtual hard disk sharing option in Microsoft,
Check the status of the virtual hard disk sharing option for a duplicate VM | Check the status of the virtual hard disk sharing option for a duplicate VM in Microsoft,
Verify the status of the virtual hard disk sharing option | Verify the status of the virtual hard disk sharing option in Microsoft,
Add a new virtual hard disk to a duplicate VM | Add a new virtual hard disk to a duplicate VM in Microsoft,
Add a new virtual hard disk | Add a new virtual hard disk in Microsoft,
Monitor Hyper-V operations | Monitor Hyper-V operations in Microsoft,
Verify the power-off status of a replica protected resource (PR) | Verify the power-off status of a replica protected resource (PR) in Microsoft,
Check the running status of the primary disaster recovery (DR) resource | Check the running status of the primary disaster recovery (DR) resource in Microsoft,
Start a primary Hyper-V VM for disaster recovery | Start a primary Hyper-V VM for disaster recovery in Microsoft,
Verify the replication mode of a replica protected resource in Hyper-V | Verify the replication mode of a replica protected resource in Hyper-V in Microsoft,
Check the Hyper-V replication state for a protected resource | Check the Hyper-V replication state for a protected resource in Microsoft,
Verify the primary replication mode in Hyper-V | Verify the primary replication mode in Hyper-V in Microsoft,
Confirm the Hyper-V replication state | Confirm the Hyper-V replication state in Microsoft,
Switch to primary replication mode and start replication | Switch to primary replication mode and start replication in Microsoft,
Verify the failover waiting completion status | Verify the failover waiting completion status in Microsoft,
Failover a replica to a target host for disaster recovery | Failover a replica to a target host for disaster recovery in Microsoft,
Verify the prepared failover status of a protected resource | Verify the prepared failover status of a protected resource in Microsoft,
Start failover for a primary replica with pending tasks | Start failover for a primary replica with pending tasks in Microsoft,
Shut down a primary Hyper-V server in an off state | Shut down a primary Hyper-V server in an off state in Microsoft,
Shut down a primary Hyper-V server | Shut down a primary Hyper-V server in Microsoft,
Verify the reverse replication connection configuration for disaster recovery | Verify the reverse replication connection configuration for disaster recovery in Microsoft,
Check the forward replication connection configuration for a protected resource | Check the forward replication connection configuration for a protected resource in Microsoft,
Verify the primary replication mode for a protected resource in Hyper-V | Verify the primary replication mode for a protected resource in Hyper-V in Microsoft,
Confirm the replica replication mode for disaster recovery in Hyper-V | Confirm the replica replication mode for disaster recovery in Hyper-V in Microsoft,
Check the Hyper-V replication state for disaster recovery | Check the Hyper-V replication state for disaster recovery in Microsoft,
Verify the existence status of a virtual switch | Verify the existence status of a virtual switch in Microsoft,
Confirm the reverse replication connection configuration | Confirm the reverse replication connection configuration in Microsoft,
Check the forward replication connection configuration | Check the forward replication connection configuration in Microsoft,
Verify the completion status of a failed-over resource | Verify the completion status of a failed-over resource in Microsoft,
Verify the status of a VM's network adapter | Verify the status of a VM's network adapter in Microsoft,
Check the VLAN ID status of a VM's network adapter | Check the VLAN ID status of a VM's network adapter in Microsoft,
Stop a virtual machine (VM) | Stop a virtual machine (VM) in Microsoft,
Start a VM failover | Start a VM failover in Microsoft,
Retrieve the IP address of a single-network Hyper-V VM without using a MAC address | Retrieve the IP address of a single-network Hyper-V VM without using a MAC address in Microsoft,
Retrieve the IP address of a single-network Hyper-V VM using its MAC address | Retrieve the IP address of a single-network Hyper-V VM using its MAC address in Microsoft,
Set reverse replication for a VM | Set reverse replication for a VM in Microsoft,
Set a VLAN ID to a virtual switch | Set a VLAN ID to a virtual switch in Microsoft,
Connect a network adapter to a VM | Connect a network adapter to a VM in Microsoft,
Prepare a VM for failover | Prepare a VM for failover in Microsoft,
Execute a command to check Hyper-V status | Execute a command to check Hyper-V status in Microsoft,
Disconnect a network adapter from a VM | Disconnect a network adapter from a VM in Microsoft,
Check the state of a virtual machine (VM) | Check the state of a virtual machine (VM) in Microsoft,
Verify the replication state of a virtual machine | Verify the replication state of a virtual machine in Microsoft,
Check the replication mode of a VM | Check the replication mode of a VM in Microsoft,
Confirm the Hyper-V replication status | Confirm the Hyper-V replication status in Microsoft,
Change the IP address of a Hyper-V VM without using a MAC address | Change the IP address of a Hyper-V VM without using a MAC address in Microsoft,
Change the IP address of a Hyper-V VM using its MAC address | Change the IP address of a Hyper-V VM using its MAC address in Microsoft,
Cancel a failover for a standalone Hyper-V VM | Cancel a failover for a standalone Hyper-V VM in Microsoft,
Verify the availability of the target local zone for Leap recovery | Verify the availability of the target local zone for Leap recovery in Nutanix,
Verify the availability of the source failed zone for Leap recovery | Verify the availability of the source failed zone for Leap recovery in Nutanix,
Check the name of a Nutanix Leap recovery plan | Check the name of a Nutanix Leap recovery plan in Nutanix,
Assign an IP address to a network interface card in Nutanix | Assign an IP address to a network interface card in Nutanix in Nutanix,
Validate the recovery plan's target site for Nutanix Leap recovery | Validate the recovery plan's target site for Nutanix Leap recovery in Nutanix,
Execute an unplanned failover to a target site for Nutanix Leap recovery | Execute an unplanned failover to a target site for Nutanix Leap recovery in Nutanix,
Perform a planned failover to a target site for Nutanix Leap recovery | Perform a planned failover to a target site for Nutanix Leap recovery in Nutanix,
Verify that the recovery point entity VM does not exist at the target local availability zone | Verify that the recovery point entity VM does not exist at the target local availability zone in Nutanix,
Check if the recovery point entity VM exists at the source failed availability zone | Check if the recovery point entity VM exists at the source failed availability zone in Nutanix,
Verify the existence of a network adapter using its NIC IP address | Verify the existence of a network adapter using its NIC IP address in Nutanix,
Check the existence of a network adapter using its MAC address | Check the existence of a network adapter using its MAC address in Nutanix,
Assign a DNS IP address randomly to a Windows server | Assign a DNS IP address randomly to a Windows server in Nutanix,
Assign a DNS IP address to a Windows server using its MAC address | Assign a DNS IP address to a Windows server using its MAC address in Nutanix,
Assign a DNS IP address to a Windows server using its IP address | Assign a DNS IP address to a Windows server using its IP address in Nutanix,
Add a network interface card to a virtual machine | Add a network interface card to a virtual machine in Nutanix,
Verify no replication is pending for the protection domain in the inactive site | Verify no replication is pending for the protection domain in the inactive site in Nutanix,
Ensure no replication is pending for the protection domain in the active site | Ensure no replication is pending for the protection domain in the active site in Nutanix,
Migrate a protection domain | Migrate a protection domain in Nutanix,
Execute a power-on operation for a virtual machine (VM) | power on vm in nutanix | Execute a power-on operation for a virtual machine (VM) | power on vm in nutanix in Nutanix,
Execute a power-off operation for a virtual machine (VM) | Execute a power-off operation for a virtual machine (VM) in Nutanix,
Check the state of a virtual machine | Check the state of a virtual machine in Nutanix,
Verify if a VM exists in a consistency group under the protection domain at an inactive site | Verify if a VM exists in a consistency group under the protection domain at an inactive site in Nutanix,
Check if a VM exists in a consistency group under the protection domain at an active site | Check if a VM exists in a consistency group under the protection domain at an active site in Nutanix,
Verify the status of the protection domain | Verify the status of the protection domain in Nutanix,
Confirm if a consistency group exists under the protection domain in the inactive site | Confirm if a consistency group exists under the protection domain in the inactive site in Nutanix,
Verify if a consistency group exists under the protection domain in the active site | Verify if a consistency group exists under the protection domain in the active site in Nutanix,
Unmount a datastore | Unmount a datastore in PowerCLI,
Unmount a datastore from all ESXi hosts | Unmount a datastore from all ESXi hosts in PowerCLI,
Unregister a virtual machine (VM) | Unregister a virtual machine (VM) in PowerCLI,
Unregister a datastore | Unregister a datastore in PowerCLI,
Stop the vSphere HA agent service | Stop the vSphere HA agent service in PowerCLI,
Start the vSphere HA agent service | Start the vSphere HA agent service in PowerCLI,
Rescan HBA to find a new storage LUN | Rescan HBA to find a new storage LUN in PowerCLI,
Register a virtual machine without associating it with an ESXi host | Register a virtual machine without associating it with an ESXi host in PowerCLI,
Register a virtual machine (VM) | Register a virtual machine (VM) in PowerCLI,
Rescan VMFS to locate storage LUNs | Rescan VMFS to locate storage LUNs in PowerCLI,
Power on a virtual machine (VM) | Power on a virtual machine (VM) in PowerCLI,
Power on a datastore | Power on a datastore in PowerCLI,
Power off a virtual machine (VM) | Power off a virtual machine (VM) in PowerCLI,
Power off a datastore | Power off a datastore in PowerCLI,
Mount a datastore on all ESXi hosts | Mount a datastore on all ESXi hosts in PowerCLI,
Mount a datastore | Mount a datastore in PowerCLI,
Detach a LUN | Detach a LUN in PowerCLI,
Check if a datastore can be unmounted | Check if a datastore can be unmounted in PowerCLI,
Attach a LUN to multiple ESXi hosts | Attach a LUN to multiple ESXi hosts in PowerCLI,
Attach a LUN | Attach a LUN in PowerCLI,
Update failover network settings for vApp | Update failover network settings for vApp in VMWare,
Set reverse replication for vApp | Set reverse replication for vApp in VMWare,
Power on a vApp | Power on a vApp in VMWare,
Power off a vApp | Power off a vApp in VMWare,
Execute failover for a vApp | Execute failover for a vApp in VMWare,
Delete a vApp | Delete a vApp in VMWare,
Check the replication state of a vApp | Check the replication state of a vApp in VMWare,
Check the recovery state of a vApp | Check the recovery state of a vApp in VMWare,
Check the overall health of a vApp | Check the overall health of a vApp in VMWare,
Verify failover network settings between source and target for vApp | Verify failover network settings between source and target for vApp in VMWare,
Check failover network settings for vApp | Check failover network settings for vApp in VMWare,
Execute vApp synchronization in VMware Cloud Availability (VCAV) | Execute vApp synchronization in VMware Cloud Availability (VCAV) in VMWare,
Execute recovery to production for RecoverPoint for VMs | Execute recovery to production for RecoverPoint for VMs in VMWare,
Perform recovery activities for RecoverPoint for VMs during recovery to production | Perform recovery activities for RecoverPoint for VMs during recovery to production in VMWare,
Stop test copy activity for RecoverPoint for VMs during recovery to production | Stop test copy activity for RecoverPoint for VMs during recovery to production in VMWare,
Start test copy for RecoverPoint for VMs during recovery to production | Start test copy for RecoverPoint for VMs during recovery to production in VMWare,
Execute failover role change for RecoverPoint for VMs | Execute failover role change for RecoverPoint for VMs in VMWare,
Perform failover role change activities for RecoverPoint for VMs | Perform failover role change activities for RecoverPoint for VMs in VMWare,
Stop test copy activity for RecoverPoint for VMs during failover | Stop test copy activity for RecoverPoint for VMs during failover in VMWare,
Start test copy for RecoverPoint for VMs during failover | Start test copy for RecoverPoint for VMs during failover in VMWare,
Stop a test copy for RecoverPoint for VMs | Stop a test copy for RecoverPoint for VMs in VMWare,
Start a test copy for RecoverPoint for VMs | Start a test copy for RecoverPoint for VMs in VMWare,
Check the transfer status of an active consistency group | Check the transfer status of an active consistency group in VMWare,
Check the recovery activities status of a consistency group | Check the recovery activities status of a consistency group in VMWare,
Verify the details of the production replica in an RPA cluster for a consistency group | Verify the details of the production replica in an RPA cluster for a consistency group in VMWare,
Monitor replication for virtualization using RecoverPoint for VMs | Monitor replication for virtualization using RecoverPoint for VMs in VMWare,
Rollback a VPG before committing a move | Rollback a VPG before committing a move in VMWare,
Commit a VPG move with no policy | Commit a VPG move with no policy in VMWare,
Commit a VPG move without reverse protection | Commit a VPG move without reverse protection in VMWare,
Commit a VPG move with reverse protection | Commit a VPG move with reverse protection in VMWare,
Execute VPG failover without committing and shut down the source VM | Execute VPG failover without committing and shut down the source VM in VMWare,
Check the initial synchronization state of a volume in VPG | Check the initial synchronization state of a volume in VPG in VMWare,
Verify the moving state before committing in VPG | Verify the moving state before committing in VPG in VMWare,
Commit a VPG failover with reverse protection | Commit a VPG failover with reverse protection in VMWare,
Stop a VPG failover test | Stop a VPG failover test in VMWare,
Rollback a VPG before committing a failover | Rollback a VPG before committing a failover in VMWare,
Check the protection status of a VPG meeting SLA and state is none | Check the protection status of a VPG meeting SLA and state is none in VMWare,
Execute VPG failover without committing | Execute VPG failover without committing in VMWare,
Perform a failover test for VPG | Perform a failover test for VPG in VMWare,
Check the delta synchronization state in VPG | Check the delta synchronization state in VPG in VMWare,
Verify the recovery site for disaster recovery in VPG | Verify the recovery site for disaster recovery in VPG in VMWare,
Check the protection state of VPG during failing over or rolling back | Check the protection state of VPG during failing over or rolling back in VMWare,
Check the protection state of VPG during failover before committing | Check the protection state of VPG during failover before committing in VMWare,
Verify the failing-over protection status of VPG | Verify the failing-over protection status of VPG in VMWare,
Failover using predefined network for RecoverPoint for VMs | Failover using predefined network for RecoverPoint for VMs in vCenter,
Enable the latest image in RecoverPoint for VMs | Enable the latest image in RecoverPoint for VMs in vCenter,
Start a replica transfer in RecoverPoint for VMs | Start a replica transfer in RecoverPoint for VMs in vCenter,
Add an additional disk to a VM | Add an additional disk to a VM in vCenter,
Verify the volume mount status | Verify the volume mount status in vCenter,
Check the unmount status of a volume | Check the unmount status of a volume in vCenter,
Verify a storage LUN | Verify a storage LUN in vCenter,
Check the mount status of a volume | Check the mount status of a volume in vCenter,
Verify a disk | Verify a disk in vCenter,
Check the device associated with a disk | Check the device associated with a disk in vCenter,
Verify the attach status of a disk | Verify the attach status of a disk in vCenter,
Power on a virtual machine in vCenter | Power on a virtual machine in vCenter in vCenter,
Power off a virtual machine in vCenter | Power off a virtual machine in vCenter in vCenter,
Check the running state of a VM in vCenter | Check the running state of a VM in vCenter in vCenter,
Remove a virtual machine in vCenter | Remove a virtual machine in vCenter in vCenter,
Remove a snapshot in vCenter | Remove a snapshot in vCenter in vCenter,
Provision a new VM in vCenter | Provision a new VM in vCenter in vCenter,
Power on a VM in vCenter | Power on a VM in vCenter in vCenter,
Power off a VM in vCenter | Power off a VM in vCenter in vCenter,
Execute a command on a VM in vCenter | Execute a command on a VM in vCenter in vCenter,
Create a linked clone in vCenter | Create a linked clone in vCenter in vCenter,
Check the VM tools status in vCenter | Check the VM tools status in vCenter in vCenter,
Verify the power state of a VM in vCenter | Verify the power state of a VM in vCenter in vCenter,
Check if a VM exists in vCenter | Check if a VM exists in vCenter in vCenter,
Unmount a VM | Unmount a VM in vCenter,
Unregister a VM | Unregister a VM in vCenter,
Remove a snapshot from a VM | Remove a snapshot from a VM in vCenter,
Register a virtual machine | Register a virtual machine in vCenter,
Mount a VM | Mount a VM in vCenter,
Check if a VM is available | Check if a VM is available in vCenter,
Create a snapshot for a VM | Create a snapshot for a VM in vCenter,
Verify if a VM is running | Verify if a VM is running in vCenter,
Update the hostname of a Windows server | Update the hostname of a Windows server in vCenter,
Update network information for a VM | Update network information for a VM in vCenter,
Rescan all adaptors | Rescan all adaptors in vCenter,
Replicate a virtual machine. | Replicate a virtual machine. in vCenter,
Remove a virtual machine (VM) | Remove a virtual machine (VM) in vCenter,
Remove a LUN from an ESXi host | Remove a LUN from an ESXi host in vCenter,
Remove a guest VM system from a domain | Remove a guest VM system from a domain in vCenter,
Remove an additional disk from a virtual machine | Remove an additional disk from a virtual machine in vCenter,
Power on a machine | Power on a machine in vCenter,
Power off a machine | Power off a machine in vCenter,
Unmount a NepApp VM | Unmount a NepApp VM in vCenter,
Mount using a VMFS UUID | Mount using a VMFS UUID in vCenter,
Join a guest VM system to a domain | Join a guest VM system to a domain in vCenter,
Check if a machine is running | Check if a machine is running in vCenter,
Execute a VMware PowerCLI command | Execute a VMware PowerCLI command in vCenter,
Run a PowerCLI script | Run a PowerCLI script in vCenter,
Execute a specialized VMware PowerCLI command for NCheOp | Execute a specialized VMware PowerCLI command for NCheOp in vCenter,
Execute a command to check VM status | Execute a command to check VM status in vCenter,
Detach a LUN from an ESXi host | Detach a LUN from an ESXi host in vCenter,
Create a new virtual machine (VM) | Create a new virtual machine (VM) in vCenter,
Create a linked clone of a virtual machine | Create a linked clone of a virtual machine in vCenter,
Check the VM tools status | Check the VM tools status in vCenter,
Verify if a VM exists | Verify if a VM exists in vCenter,
Change the hostname of a guest VM | Change the hostname of a guest VM in vCenter,
Attach LUNs to an ESXi host | Attach LUNs to an ESXi host in vCenter,
Re-protect a recovery plan using VMware SRM | Re-protect a recovery plan using VMware SRM in SRM,
Perform a planned migration failover using VMware SRM | Perform a planned migration failover using VMware SRM in SRM,
Execute disaster recovery failover using VMware SRM | Execute disaster recovery failover using VMware SRM in SRM,
Initiate a recovery plan using VMware SRM | Initiate a recovery plan using VMware SRM in SRM,
Execute a test recovery plan using VMware SRM | Execute a test recovery plan using VMware SRM in SRM,
Clean up a recovery plan after execution in VMware SRM | Clean up a recovery plan after execution in VMware SRM in SRM,
Check the state of a recovery plan in VMware SRM | Check the state of a recovery plan in VMware SRM in SRM,
Verify the state of a protection group in VMware SRM | Verify the state of a protection group in VMware SRM in SRM,
Reproduce a recovery plan | Reproduce a recovery plan in SRM,
Check the state of a recovery plan | Check the state of a recovery plan in SRM,
Monitor recovery actions in VMware SRM | Monitor recovery actions in VMware SRM in SRM,
Verify the target local availability zone for Leap recovery plan | Verify the target local availability zone for Leap recovery plan in Nutanix,
Verify the source failed availability zone for Leap recovery plan | Verify the source failed availability zone for Leap recovery plan in Nutanix,
Validate the recovery plan name for Nutanix Leap | Validate the recovery plan name for Nutanix Leap in Nutanix,
Assign an IP address to a network interface card in Nutanix | Assign an IP address to a network interface card in Nutanix in Nutanix,
Validate the target site state for Nutanix Leap recovery plan | Validate the target site state for Nutanix Leap recovery plan in Nutanix,
Perform an unplanned failover to the target site using Nutanix Leap | Perform an unplanned failover to the target site using Nutanix Leap in Nutanix,
Execute a planned failover to the target site using Nutanix Leap | Execute a planned failover to the target site using Nutanix Leap in Nutanix,
Check if the recovery point entity VM does not exist at the target local AZ | Check if the recovery point entity VM does not exist at the target local AZ in Nutanix,
Check if the recovery point entity VM exists at the source failed AZ | Check if the recovery point entity VM exists at the source failed AZ in Nutanix,
Verify the existence of a network adapter using its NIC IP address | Verify the existence of a network adapter using its NIC IP address in Nutanix,
Verify the existence of a network adapter using its MAC address | Verify the existence of a network adapter using its MAC address in Nutanix,
Randomly assign a DNS IP address to a Windows server | Randomly assign a DNS IP address to a Windows server in Nutanix,
Assign a DNS IP address to a Windows server using a MAC address | Assign a DNS IP address to a Windows server using a MAC address in Nutanix,
Assign a DNS IP address to a Windows server using its IP address | Assign a DNS IP address to a Windows server using its IP address in Nutanix,
Add a network interface card to a VM | Add a network interface card to a VM in Nutanix,
Verify no pending replication for an inactive site under a protection domain | Verify no pending replication for an inactive site under a protection domain in Nutanix,
Verify no pending replication for an active site under a protection domain | Verify no pending replication for an active site under a protection domain in Nutanix,
Migrate a protection domain | Migrate a protection domain in Nutanix,
Execute power-on operation for a virtual machine | Execute power-on operation for a virtual machine in Nutanix,
Execute power-off operation for a virtual machine | Execute power-off operation for a virtual machine in Nutanix,
Check the state of a virtual machine | Check the state of a virtual machine in Nutanix,
Verify if a VM exists in a consistency group under a protection domain for an inactive site | Verify if a VM exists in a consistency group under a protection domain for an inactive site in Nutanix,
Verify if a VM exists in a consistency group under a protection domain for an active site | Verify if a VM exists in a consistency group under a protection domain for an active site in Nutanix,
Check the status of a protection domain | Check the status of a protection domain in Nutanix,
Verify if a consistency group exists in a protection domain for an inactive site | Verify if a consistency group exists in a protection domain for an inactive site in Nutanix,
Verify if a consistency group exists in a protection domain for an active site | Verify if a consistency group exists in a protection domain for an active site in Nutanix,
Verify no replication pending for a protection domain in an inactive site | Verify no replication pending for a protection domain in an inactive site in NutanixPDFailOver,
Verify no replication is pending for the protection domain in an active site | Verify no replication is pending for the protection domain in an active site in NutanixPDFailOver,
Activate a protection domain failover from the disaster recovery site | Activate a protection domain failover from the disaster recovery site in NutanixPDFailOver,
Check if a virtual machine exists in the consistency group under the protection domain in an active site | Check if a virtual machine exists in the consistency group under the protection domain in an active site in NutanixPDFailOver,
Verify if a VM exists in the consistency group under the protection domain in the active state | Verify if a VM exists in the consistency group under the protection domain in the active state in NutanixPDFailOver,
Check the protection domain status for a specific site | Check the protection domain status for a specific site in NutanixPDFailOver,
Verify the member status of the consistency group under the protection domain in the active state | Verify the member status of the consistency group under the protection domain in the active state in NutanixPDFailOver,
Check the consistency group member status under the protection domain in the active state | Check the consistency group member status under the protection domain in the active state in NutanixPDFailOver,
Acknowledge and resolve recent informative alerts for a protection domain | Acknowledge and resolve recent informative alerts for a protection domain in NutanixPDFailOver,
Verify the disk status on a cluster shared volume | Verify the disk status on a cluster shared volume in Microsoft,
Check the cluster shared volume disk status in a failover cluster | Check the cluster shared volume disk status in a failover cluster in Microsoft,
Remove a duplicate virtual sharing hard disk from a virtual machine | Remove a duplicate virtual sharing hard disk from a virtual machine in Microsoft,
Remove a virtual sharing hard disk | Remove a virtual sharing hard disk in Microsoft,
Enable the virtual hard disk sharing option for a duplicate virtual machine | Enable the virtual hard disk sharing option for a duplicate virtual machine in Microsoft,
Enable the virtual hard disk sharing option | Enable the virtual hard disk sharing option in Microsoft,
Disable the virtual hard disk sharing option for a duplicate virtual machine | Disable the virtual hard disk sharing option for a duplicate virtual machine in Microsoft,
Disable the virtual hard disk sharing option | Disable the virtual hard disk sharing option in Microsoft,
Check the status of the virtual hard disk sharing option for a duplicate virtual machine | Check the status of the virtual hard disk sharing option for a duplicate virtual machine in Microsoft,
Check the status of the virtual hard disk sharing option | Check the status of the virtual hard disk sharing option in Microsoft,
Add a new virtual hard disk to a duplicate virtual machine | Add a new virtual hard disk to a duplicate virtual machine in Microsoft,
Add a new virtual hard disk | Add a new virtual hard disk in Microsoft,
Monitor operations related to Hyper-V | Monitor operations related to Hyper-V in Microsoft,
Verify the power-off status of a replica PR | Verify the power-off status of a replica PR in Microsoft,
Verify the running status of the primary disaster recovery site | Verify the running status of the primary disaster recovery site in Microsoft,
Start a primary Hyper-V virtual machine in the disaster recovery site | Start a primary Hyper-V virtual machine in the disaster recovery site in Microsoft,
Verify the replication mode of the replica in the primary region | Verify the replication mode of the replica in the primary region in Microsoft,
Check the Hyper-V replication state for the primary region | Check the Hyper-V replication state for the primary region in Microsoft,
Verify the replication mode of the primary site | Verify the replication mode of the primary site in Microsoft,
Check the replication state of Hyper-V | Check the replication state of Hyper-V in Microsoft,
Switch the replication mode to primary replication and start replication | Switch the replication mode to primary replication and start replication in Microsoft,
Verify the failover wait commit status of the replication | Verify the failover wait commit status of the replication in Microsoft,
Fail over the replica on the target host in the disaster recovery site | Fail over the replica on the target host in the disaster recovery site in Microsoft,
Check the prepared failover status for the primary region | Check the prepared failover status for the primary region in Microsoft,
Start failover for a primary replication in a pending state | Start failover for a primary replication in a pending state in Microsoft,
Shut down the primary Hyper-V virtual machine | Shut down the primary Hyper-V virtual machine in Microsoft,
Shut down the primary Hyper-V in an off state | Shut down the primary Hyper-V in an off state in Microsoft,
Verify the reverse replication connection configuration for the disaster recovery site | Verify the reverse replication connection configuration for the disaster recovery site in Microsoft,
Verify the forward replication connection configuration for the primary region | Verify the forward replication connection configuration for the primary region in Microsoft,
Check the primary replication mode for the primary region | Check the primary replication mode for the primary region in Microsoft,
Verify the replication mode of the replica in the disaster recovery site | Verify the replication mode of the replica in the disaster recovery site in Microsoft,
Check the replication state of Hyper-V in the disaster recovery site | Check the replication state of Hyper-V in the disaster recovery site in Microsoft,
Verify the existence status of a virtual switch. | Verify the existence status of a virtual switch. in Microsoft,
Verify the reverse replication connection status | Verify the reverse replication connection status in Microsoft,
Verify the forward replication connection status | Verify the forward replication connection status in Microsoft,
Verify if the failover is waiting for completion status | Verify if the failover is waiting for completion status in Microsoft,
Verify the status of the virtual machine network adapter | Verify the status of the virtual machine network adapter in Microsoft,
Verify the VLAN ID status of the virtual machine network adapter | Verify the VLAN ID status of the virtual machine network adapter in Microsoft,
Stop the virtual machine | Stop the virtual machine in Microsoft,
Start the virtual machine failover | Start the virtual machine failover in Microsoft,
Configure the Hyper-V VM's IP address without the MAC address in a single network | Configure the Hyper-V VM's IP address without the MAC address in a single network in Microsoft,
Configure the Hyper-V VM's IP address with the MAC address in a single network | Configure the Hyper-V VM's IP address with the MAC address in a single network in Microsoft,
Set the virtual machine reverse replication configuration | Set the virtual machine reverse replication configuration in Microsoft,
Set the VLAN ID for the virtual switch | Set the VLAN ID for the virtual switch in Microsoft,
Set the network adapter connection for the virtual machine | Set the network adapter connection for the virtual machine in Microsoft,
Prepare the virtual machine for failover | Prepare the virtual machine for failover in Microsoft,
Execute the check Hyper-V command | Execute the check Hyper-V command in Microsoft,
Disconnect the network adapter connection | Disconnect the network adapter connection in Microsoft,
Check the virtual machine state | Check the virtual machine state in Microsoft,
Check the virtual machine replication state | Check the virtual machine replication state in Microsoft,
Check the virtual machine replication mode | Check the virtual machine replication mode in Microsoft,
Check the Hyper-V replication status | Check the Hyper-V replication status in Microsoft,
Change the Hyper-V virtual machine's IP address without the MAC address | Change the Hyper-V virtual machine's IP address without the MAC address in Microsoft,
Change the Hyper-V virtual machine's IP address with the MAC address | Change the Hyper-V virtual machine's IP address with the MAC address in Microsoft,
Cancel failover for a standalone Hyper-V virtual machine | Cancel failover for a standalone Hyper-V virtual machine in Microsoft,
Shutdown the zone | Shutdown the zone in Oracle_Solaris,
Detach the zone | Detach the zone in Oracle_Solaris,
Check if the zone is up | Check if the zone is up in Oracle_Solaris,
Check the status of the zone | Check the status of the zone in Oracle_Solaris,
Check if the zone is down | Check if the zone is down in Oracle_Solaris,
Boot the zone | Boot the zone in Oracle_Solaris,
Attach the zone | Attach the zone in Oracle_Solaris,
Verify the power-on status of an LDOM (Logical Domain) | Verify the power-on status of an LDOM (Logical Domain) in Oracle_Solaris,
Verify the power-off status of an LDOM | Verify the power-off status of an LDOM in Oracle_Solaris,
Unbind the domain from LDOM | Unbind the domain from LDOM in Oracle_Solaris,
Stop the LDOM domain | Stop the LDOM domain in Oracle_Solaris,
Start the LDOM domain | Start the LDOM domain in Oracle_Solaris,
Remove the LDOM domain | Remove the LDOM domain in Oracle_Solaris,
Bind the domain to LDOM | Bind the domain to LDOM in Oracle_Solaris,
Add memory to the LDOM | Add memory to the LDOM in Oracle_Solaris,
Add a domain to the LDOM | Add a domain to the LDOM in Oracle_Solaris,
Add CPU to the LDOM | Add CPU to the LDOM in Oracle_Solaris,
Power on the AIX LPAR (Logical Partition) | Power on the AIX LPAR (Logical Partition) in AIX,
Power off the AIX LPAR | Power off the AIX LPAR in AIX,
Check if the AIX LPAR is powered on | Check if the AIX LPAR is powered on in AIX,
Check if the AIX LPAR is powered off | Check if the AIX LPAR is powered off in AIX,
Check if the Redis array is empty in DR (Disaster Recovery) environment | Check if the Redis array is empty in DR (Disaster Recovery) environment in Redis,
Check if the Redis array is empty in PR (Production) environment | Check if the Redis array is empty in PR (Production) environment in Redis,
Verify the row count of a single table in Redis DR environment | Verify the row count of a single table in Redis DR environment in Redis,
Verify the row count of a single table in Redis PR environment | Verify the row count of a single table in Redis PR environment in Redis,
Start the Postgres service | Start the Postgres service in Postgres,
Check the content for availability | Check the content for availability in Postgres,
Add content recovery to the system | Add content recovery to the system in Postgres,
Check the recovery file in the primary server | Check the recovery file in the primary server in Postgres,
Verify if the recovery process is complete | Verify if the recovery process is complete in Postgres,
Restart the service to ensure proper functioning | Restart the service to ensure proper functioning in Postgres,
Create a trigger file to initiate processes | Create a trigger file to initiate processes in Postgres,
Check the recovery file for consistency | Check the recovery file for consistency in Postgres,
Stop the Postgres service for maintenance | Stop the Postgres service for maintenance in Postgres,
Verify the status of the recovery process | Verify the status of the recovery process in Postgres,
Verify if the server is running correctly | Verify if the server is running correctly in Postgres,
Verify the PostgreSQL trigger file name and path for accuracy | Verify the PostgreSQL trigger file name and path for accuracy in Postgres,
Verify the DR replication status of the PostgreSQL server | Verify the DR replication status of the PostgreSQL server in Postgres,
Verify the PR replication status of the PostgreSQL server | Verify the PR replication status of the PostgreSQL server in Postgres,
Verify the state of the PostgreSQL database cluster | Verify the state of the PostgreSQL database cluster in Postgres,
Verify that the PostgreSQL XLog LSN (Log Sequence Number) is matching | Verify that the PostgreSQL XLog LSN (Log Sequence Number) is matching in Postgres,
Start the PostgreSQL server to bring it online | Start the PostgreSQL server to bring it online in Postgres,
Restart the PostgreSQL server to resolve issues or apply updates | Restart the PostgreSQL server to resolve issues or apply updates in Postgres,
Execute a DR (Disaster Recovery) failover to a standby server | Execute a DR (Disaster Recovery) failover to a standby server in Postgres,
Verify the DR trigger file path for accuracy | Verify the DR trigger file path for accuracy in Postgres,
Verify the shutdown status of the PR database cluster | Verify the shutdown status of the PR database cluster in Postgres,
Stop the PostgreSQL server for maintenance or troubleshooting | Stop the PostgreSQL server for maintenance or troubleshooting in Postgres,
Create a recovery configuration file at PR (Production Recovery) | Create a recovery configuration file at PR (Production Recovery) in Postgres,
Verify that the WAL (Write-Ahead Logging) LSN matches between PR and DR | Verify that the WAL (Write-Ahead Logging) LSN matches between PR and DR in Postgres,
Verify the status of the database cluster | Verify the status of the database cluster in Postgres,
Execute a Postgres SQL command for troubleshooting or updates | Execute a Postgres SQL command for troubleshooting or updates in Postgres,
Perform a cluster changeover for maintenance or failover purposes | Perform a cluster changeover for maintenance or failover purposes in Postgres,
Promote the cluster state to ensure high availability | Promote the cluster state to ensure high availability in Postgres,
Verify the DR replication status for proper failover operation | Verify the DR replication status for proper failover operation in Postgres,
Verify the status of the cluster for health and availability | Verify the status of the cluster for health and availability in Postgres,
Verify the PostgreSQL service status to ensure it's running | Verify the PostgreSQL service status to ensure it's running in Postgres,
Verify the current WAL location in PostgreSQL | Verify the current WAL location in PostgreSQL in Postgres,
Verify the PostgreSQL recovery status to ensure data integrity | Verify the PostgreSQL recovery status to ensure data integrity in Postgres,
Verify the PR replication status for proper database syncing | Verify the PR replication status for proper database syncing in Postgres,
Verify the cluster state to ensure smooth operation | Verify the cluster state to ensure smooth operation in Postgres,
Monitor PostgreSQL performance and health | Monitor PostgreSQL performance and health in Postgres,
Execute PostgreSQL recovery process for critical data | Execute PostgreSQL recovery process for critical data in Postgres,
Start the PostgreSQL service to bring it online | Start the PostgreSQL service to bring it online in Postgres,
Execute PostgreSQL recovery from a catalog file | Execute PostgreSQL recovery from a catalog file in Postgres,
Stop the PostgreSQL service for scheduled maintenance | Stop the PostgreSQL service for scheduled maintenance in Postgres,
Synchronize two clusters using PostgreSQL’s PG Rewind feature | Synchronize two clusters using PostgreSQL’s PG Rewind feature in Postgres,
Create a recovery configuration for a MV (Multi-Version) PostgreSQL setup | Create a recovery configuration for a MV (Multi-Version) PostgreSQL setup in Postgres,
Test the PG Rewind process to ensure data synchronization | Test the PG Rewind process to ensure data synchronization in Postgres,
Create a standby file in the secondary server for failover purposes | Create a standby file in the secondary server for failover purposes in Postgres,
Test a custom action for process validation | Test a custom action for process validation in Postgres,
Test a Power shell (powershell) script for system automation or Test shell command | Test a Power shell (powershell) script for system automation or Test shell command in Postgres,
Test an action to ensure it works as expected | Test an action to ensure it works as expected in Postgres,
Start the Sybase server for database operations | Start the Sybase server for database operations in Sybase,
Verify if the Sybase server is up and running | Verify if the Sybase server is up and running in Sybase,
Verify if the Sybase server is down and troubleshoot if necessary | Verify if the Sybase server is down and troubleshoot if necessary in Sybase,
Shut down the Sybase database for maintenance | Shut down the Sybase database for maintenance in Sybase,
Perform a checkpoint operation on the Sybase database to ensure consistency | Perform a checkpoint operation on the Sybase database to ensure consistency in Sybase,
Replicate a Sybase BCP (Bulk Copy Program) table for backup or recovery | Replicate a Sybase BCP (Bulk Copy Program) table for backup or recovery in Sybase,
Dump the transaction log file for standby access purposes | Dump the transaction log file for standby access purposes in Sybase,
Enable Sybase database for standby access during replication | Enable Sybase database for standby access during replication in Sybase,
Bring the Sybase database online after maintenance or issues | Bring the Sybase database online after maintenance or issues in Sybase,
Load a transaction log file into Sybase for recovery or auditing | Load a transaction log file into Sybase for recovery or auditing in Sybase,
Execute a Sybase ISQL command for administrative tasks | Execute a Sybase ISQL command for administrative tasks in Sybase,
Generate the last transaction log for Sybase for disaster recovery | Generate the last transaction log for Sybase for disaster recovery in Sybase,
Execute the Sybase BCP in command to import data into Sybase | Execute the Sybase BCP in command to import data into Sybase in Sybase,
Execute the Sybase BCP out command to export data from Sybase | Execute the Sybase BCP out command to export data from Sybase in Sybase,
Verify the status of the database to ensure proper operation | Verify the status of the database to ensure proper operation in Sybase,
Verify the status of the backup server to confirm functionality | Verify the status of the backup server to confirm functionality in Sybase,
Verify the status of the Sybase data server for availability | Verify the status of the Sybase data server for availability in Sybase,
Copy the last transaction log for Sybase for disaster recovery | Copy the last transaction log for Sybase for disaster recovery in Sybase,
Execute Sybase ISQL command checks for validation | Execute Sybase ISQL command checks for validation in Sybase,
Resume the Sybase replication connection to continue data syncing | Resume the Sybase replication connection to continue data syncing in Sybase,
Start the Sybase replication agent on the primary server for data syncing | Start the Sybase replication agent on the primary server for data syncing in Sybase,
Check the Sybase replication role switch status to ensure smooth failover | Check the Sybase replication role switch status to ensure smooth failover in Sybase,
Check the Sybase replication switchover status to confirm the primary role change | Check the Sybase replication switchover status to confirm the primary role change in Sybase,
Failover Sybase from the primary to the standby server for high availability | Failover Sybase from the primary to the standby server for high availability in Sybase,
Verify if the Sybase replication agent is down during troubleshooting | Verify if the Sybase replication agent is down during troubleshooting in Sybase,
Stop the Sybase replication agent on the primary server for maintenance | Stop the Sybase replication agent on the primary server for maintenance in Sybase,
Verify the status of the Sybase standby database | Verify the status of the Sybase standby database in Sybase,
Verify the status of the Sybase primary database for health checks | Verify the status of the Sybase primary database for health checks in Sybase,
Test a command for database copy verification (e.g., Copy 9011) | Test a command for database copy verification (e.g., Copy 9011) in MSSQL,
Test the WW (Worldwide) replication process for database consistency (e.g., Copy 8328) | Test the WW (Worldwide) replication process for database consistency (e.g., Copy 8328) in MSSQL,
Verify the status of a database copy (e.g., Copy 3563) | Verify the status of a database copy (e.g., Copy 3563) in MSSQL,
Restore the last backup log with no recovery in LSSQL with pre-check (e.g., Copy 1761) | Restore the last backup log with no recovery in LSSQL with pre-check (e.g., Copy 1761) in MSSQL,
Enable log shipping with target database restoring in LSSQL (e.g., Copy 2143) | Enable log shipping with target database restoring in LSSQL (e.g., Copy 2143) in MSSQL,
Restore the last log with standby DR in LSSQL (e.g., Copy 1751) | Restore the last log with standby DR in LSSQL (e.g., Copy 1751) in MSSQL,
Restore the last log with recovery DR in LSSQL (e.g., Copy 6317) | Restore the last log with recovery DR in LSSQL (e.g., Copy 6317) in MSSQL,
Validate the backup with primary and secondary databases in LSSQL (e.g., Copy 8706) | Validate the backup with primary and secondary databases in LSSQL (e.g., Copy 8706) in MSSQL,
Copy the tail log backup for DR in LSSQL (e.g., Copy 4404) | Copy the tail log backup for DR in LSSQL (e.g., Copy 4404) in MSSQL,
Verify that the primary backup transaction log exists in LSSQL (e.g., Copy 4304) | Verify that the primary backup transaction log exists in LSSQL (e.g., Copy 4304) in MSSQL,
Execute secondary log shipping in LSSQL for data replication (e.g., Copy 6983) | Execute secondary log shipping in LSSQL for data replication (e.g., Copy 6983) in MSSQL,
Execute primary log shipping reverse in LSSQL for replication consistency (e.g., Copy 6024) | Execute primary log shipping reverse in LSSQL for replication consistency (e.g., Copy 6024) in MSSQL,
Verify the job name associated with log shipping in LSSQL (e.g., Copy 9987) | Verify the job name associated with log shipping in LSSQL (e.g., Copy 9987) in MSSQL,
Verify the source and destination backup directory in LSSQL (e.g., Copy 626) | Verify the source and destination backup directory in LSSQL (e.g., Copy 626) in MSSQL,
Verify the backup directory and shared path in LSSQL (e.g., Copy 2758) | Verify the backup directory and shared path in LSSQL (e.g., Copy 2758) in MSSQL,
Execute 2 DR site primary log shipping in LSSQL (e.g., Copy 3220) | Execute 2 DR site primary log shipping in LSSQL (e.g., Copy 3220) in MSSQL,
Restore the last backup log with recovery in LSSQL (e.g., Copy 8040) | Restore the last backup log with recovery in LSSQL (e.g., Copy 8040) in MSSQL,
Execute 3-site secondary log shipping in LSSQL (e.g., Copy 7757) | Execute 3-site secondary log shipping in LSSQL (e.g., Copy 7757) in MSSQL,
Execute 3 DR site primary log shipping in LSSQL (e.g., Copy 2643) | Execute 3 DR site primary log shipping in LSSQL (e.g., Copy 2643) in MSSQL,
Restore the log with standby in LSSQL (e.g., Copy 1265) | Restore the log with standby in LSSQL (e.g., Copy 1265) in MSSQL,
Verify the 3 DR site log file sequence in LSSQL (e.g., Copy 9302) | Verify the 3 DR site log file sequence in LSSQL (e.g., Copy 9302) in MSSQL,
Make the database writable after the last restored file failover in LSSQL (e.g., Copy 1308) | Make the database writable after the last restored file failover in LSSQL (e.g., Copy 1308) in MSSQL,
Execute the secondary log shipping job schedule in LSSQL (e.g., Copy 8777) | Execute the secondary log shipping job schedule in LSSQL (e.g., Copy 8777) in MSSQL,
Execute the primary log shipping job schedule in LSSQL (e.g., Copy 504) | Execute the primary log shipping job schedule in LSSQL (e.g., Copy 504) in MSSQL,
Check the database mode in SQL NLS for configuration (e.g., Copy 5504) | Check the database mode in SQL NLS for configuration (e.g., Copy 5504) in MSSQL,
Check the database state in SQL NLS for consistency (e.g., Copy 5936) | Check the database state in SQL NLS for consistency (e.g., Copy 5936) in MSSQL,
Verify the database mirroring status to ensure synchronization (e.g., Copy 8342) | Verify the database mirroring status to ensure synchronization (e.g., Copy 8342) in MSSQL,
Perform a failover of database mirroring (e.g., Copy 1092) | Perform a failover of database mirroring (e.g., Copy 1092) in MSSQL,
Kill the MSSQL process by its name for troubleshooting or termination (e.g., Copy 4082) | Kill the MSSQL process by its name for troubleshooting or termination (e.g., Copy 4082) in MSSQL,
Remove the restore job in ILSSQL to clean up unnecessary jobs (e.g., Copy 4676) | Remove the restore job in ILSSQL to clean up unnecessary jobs (e.g., Copy 4676) in MSSQL,
Remove the copy job in ILSSQL to clean up unnecessary jobs (e.g., Copy 8019) | Remove the copy job in ILSSQL to clean up unnecessary jobs (e.g., Copy 8019) in MSSQL,
Attach a database in SQL Server for use (e.g., Copy 9781) | Attach a database in SQL Server for use (e.g., Copy 9781) in MSSQL,
Set the database option to single-user mode (e.g., Copy 2035) | Set the database option to single-user mode (e.g., Copy 2035) in MSSQL,
Take the database offline in SQL Server for maintenance (e.g., Copy 5634) | Take the database offline in SQL Server for maintenance (e.g., Copy 5634) in MSSQL,
Migrate database roles in LSSQL (e.g., Copy 5841) | Migrate database roles in LSSQL (e.g., Copy 5841) in MSSQL,
Migrate logins in ILSSQL (e.g., Copy 6748) | Migrate logins in ILSSQL (e.g., Copy 6748) in MSSQL,
Execute the secondary log shipping in ILSSQL (e.g., Copy 8481) | Execute the secondary log shipping in ILSSQL (e.g., Copy 8481) in MSSQL,
Execute the primary log shipping in ILSSQL (e.g., Copy 1013) | Execute the primary log shipping in ILSSQL (e.g., Copy 1013) in MSSQL,
Remove the backup job in ILSSQL (e.g., Copy 8703) | Remove the backup job in ILSSQL (e.g., Copy 8703) in MSSQL,
Run the backup job in ILSSQL (e.g., Copy 1312) | Run the backup job in ILSSQL (e.g., Copy 1312) in MSSQL,
Unmount a process or service in linux or unix | Unmount a process or service in linux or unix in MSSQL,
Mount a service or process in linux or unix | Mount a service or process in linux or unix in MSSQL,
Migrate database roles in ILSSQL (e.g., Copy 6694) | Migrate database roles in ILSSQL (e.g., Copy 6694) in MSSQL,
Migrate logins in LSSQL (e.g., Copy 2225) | Migrate logins in LSSQL (e.g., Copy 2225) in MSSQL,
Detach the SQL database for maintenance or troubleshooting (e.g., Copy 6169) | Detach the SQL database for maintenance or troubleshooting (e.g., Copy 6169) in MSSQL,
Attach the SQL database for restoration or access (e.g., Copy 4977) | Attach the SQL database for restoration or access (e.g., Copy 4977) in MSSQL,
Test the NLS (National Language Support) configuration (e.g., Copy 697) | Test the NLS (National Language Support) configuration (e.g., Copy 697) in MSSQL,
Remove the secondary job in LSSQL (e.g., Copy 5188) | Remove the secondary job in LSSQL (e.g., Copy 5188) in MSSQL,
Check if the database entry exists on the primary server in LSSQL (e.g., Copy 6825) | Check if the database entry exists on the primary server in LSSQL (e.g., Copy 6825) in MSSQL,
Check if the database entry exists on the secondary server in LSSQL (e.g., Copy 3771) | Check if the database entry exists on the secondary server in LSSQL (e.g., Copy 3771) in MSSQL,
Check if the primary log shipping exists in LSSQL (e.g., Copy 1013) | Check if the primary log shipping exists in LSSQL (e.g., Copy 1013) in MSSQL,
Check if primary and secondary log shipping exist in LSSQL (e.g., Copy 153) | Check if primary and secondary log shipping exist in LSSQL (e.g., Copy 153) in MSSQL,
Restore secondary log shipping in LSSQL (e.g., Copy 6109) | Restore secondary log shipping in LSSQL (e.g., Copy 6109) in MSSQL,
Restore primary log shipping in LSSQL (e.g., Copy 8024) | Restore primary log shipping in LSSQL (e.g., Copy 8024) in MSSQL,
Restore the last backup log with no recovery in LSSQL (e.g., Copy 5534) | Restore the last backup log with no recovery in LSSQL (e.g., Copy 5534) in MSSQL,
Set the database to multi-user access mode in LSSQL (e.g., Copy 3724) | Set the database to multi-user access mode in LSSQL (e.g., Copy 3724) in MSSQL,
Generate the last backup log in LSSQL (e.g., Copy 8879) | Generate the last backup log in LSSQL (e.g., Copy 8879) in MSSQL,
Kill all sessions in LSSQL for troubleshooting or maintenance (e.g., Copy 6006) | Kill all sessions in LSSQL for troubleshooting or maintenance (e.g., Copy 6006) in MSSQL,
Monitor the NLS settings in MSSQL (e.g., Copy 5990) | Monitor the NLS settings in MSSQL (e.g., Copy 5990) in MSSQL,
Fix orphaned users in the SQL database (e.g., Copy 144) | Fix orphaned users in the SQL database (e.g., Copy 144) in MSSQL,
Set the DR database in multi-user access mode in ILSSQL (e.g., Copy 7365) | Set the DR database in multi-user access mode in ILSSQL (e.g., Copy 7365) in MSSQL,
Update the restore job with the DR IP address in LSSQL (e.g., Copy 7250) | Update the restore job with the DR IP address in LSSQL (e.g., Copy 7250) in MSSQL,
Update the restore job with the DR IP address in ILSSQL (e.g., Copy 3921) | Update the restore job with the DR IP address in ILSSQL (e.g., Copy 3921) in MSSQL,
Update the copy job with the DR IP address in LSSQL (e.g., Copy 1697) | Update the copy job with the DR IP address in LSSQL (e.g., Copy 1697) in MSSQL,
Update the copy job with the DR IP address in ILSSQL (e.g., Copy 7043) | Update the copy job with the DR IP address in ILSSQL (e.g., Copy 7043) in MSSQL,
Update the backup job with the PR IP address in ILSSQL (e.g., Copy 6952) | Update the backup job with the PR IP address in ILSSQL (e.g., Copy 6952) in MSSQL,
Update the backup job with the PR IP address in LSSQL (e.g., Copy 5431) | Update the backup job with the PR IP address in LSSQL (e.g., Copy 5431) in MSSQL,
Remove primary and secondary log shipping in LSSQL (e.g., Copy 3349) | Remove primary and secondary log shipping in LSSQL (e.g., Copy 3349) in MSSQL,
Remove primary log shipping in ILSSQL (e.g., Copy 9087) | Remove primary log shipping in ILSSQL (e.g., Copy 9087) in MSSQL,
Remove secondary log shipping in LSSQL (e.g., Copy 6454) | Remove secondary log shipping in LSSQL (e.g., Copy 6454) in MSSQL,
Remove secondary log shipping in ILSSQL (e.g., Copy 9524) | Remove secondary log shipping in ILSSQL (e.g., Copy 9524) in MSSQL,
Remove primary log shipping in LSSQL (e.g., Copy 7851) | Remove primary log shipping in LSSQL (e.g., Copy 7851) in MSSQL,
Remove primary and secondary log shipping in ILSSQL (e.g., Copy 3414) | Remove primary and secondary log shipping in ILSSQL (e.g., Copy 3414) in MSSQL,
Restore the database with recovery in LSSQL (e.g., Copy 8869) | Restore the database with recovery in LSSQL (e.g., Copy 8869) in MSSQL,
Restore the database with recovery in ILSSQL (e.g., Copy 3308) | Restore the database with recovery in ILSSQL (e.g., Copy 3308) in MSSQL,
Set the database to single-user access mode in ILSSQL for DR (e.g., Copy 2666) | Set the database to single-user access mode in ILSSQL for DR (e.g., Copy 2666) in MSSQL,
Kill the session in ILSSQL for DR (e.g., Copy 1484) | Kill the session in ILSSQL for DR (e.g., Copy 1484) in MSSQL,
Set the database to multi-user access mode in ILSSQL for the primary server (e.g., Copy 6753) | Set the database to multi-user access mode in ILSSQL for the primary server (e.g., Copy 6753) in MSSQL,
Verify the database is in single-user access mode in LSSQL (e.g., Copy 3803) | Verify the database is in single-user access mode in LSSQL (e.g., Copy 3803) in MSSQL,
Verify the PR database is in single-user access mode in ILSSQL (e.g., Copy 4681) | Verify the PR database is in single-user access mode in ILSSQL (e.g., Copy 4681) in MSSQL,
Set the database to single-user access mode in LSSQL (e.g., Copy 9415) | Set the database to single-user access mode in LSSQL (e.g., Copy 9415) in MSSQL,
Set the database to single-user access mode on the primary server in ILSSQL (e.g., Copy 4551) | Set the database to single-user access mode on the primary server in ILSSQL (e.g., Copy 4551) in MSSQL,
Import login roles on the DR server in ILSSQL (e.g., Copy 5070) | Import login roles on the DR server in ILSSQL (e.g., Copy 5070) in MSSQL,
Import logins on the DR server in ILSSQL (e.g., Copy 2279) | Import logins on the DR server in ILSSQL (e.g., Copy 2279) in MSSQL,
Export login roles from the production server in LSSQL (e.g., Copy 951) | Export login roles from the production server in LSSQL (e.g., Copy 951) in MSSQL,
Export logins from the production server in LSSQL (e.g., Copy 8248) | Export logins from the production server in LSSQL (e.g., Copy 8248) in MSSQL,
Kill the session on the primary server in LSSQL (e.g., Copy 3833) | Kill the session on the primary server in LSSQL (e.g., Copy 3833) in MSSQL,
Fix orphaned users in LSSQL (e.g., Copy 4095) | Fix orphaned users in LSSQL (e.g., Copy 4095) in MSSQL,
Enable the job in LSSQL for scheduled operations (e.g., Copy 978) | Enable the job in LSSQL for scheduled operations (e.g., Copy 978) in MSSQL,
Generate the last backup log with no recovery in LSSQL (e.g., Copy 9108) | Generate the last backup log with no recovery in LSSQL (e.g., Copy 9108) in MSSQL,
Kill the database session with a timeout in LSSQL (e.g., Copy 8578) | Kill the database session with a timeout in LSSQL (e.g., Copy 8578) in MSSQL,
Run a job in LSSQL for execution (e.g., Copy 7046) | Run a job in LSSQL for execution (e.g., Copy 7046) in MSSQL,
Disable the job in LSSQL to pause operations (e.g., Copy 903) | Disable the job in LSSQL to pause operations (e.g., Copy 903) in MSSQL,
Generate the backup log sequence in LSSQL (e.g., Copy 2486) | Generate the backup log sequence in LSSQL (e.g., Copy 2486) in MSSQL,
Run the restore job in LSSQL for database recovery (e.g., Copy 5153) | Run the restore job in LSSQL for database recovery (e.g., Copy 5153) in MSSQL,
Run the copy job in LSSQL for database replication (e.g., Copy 7219) | Run the copy job in LSSQL for database replication (e.g., Copy 7219) in MSSQL,
Disable the restore job in LSSQL (e.g., Copy 9208) | Disable the restore job in LSSQL (e.g., Copy 9208) in MSSQL,
Disable the copy job in LSSQL (e.g., Copy 5245) | Disable the copy job in LSSQL (e.g., Copy 5245) in MSSQL,
Disable the backup job in LSSQL (e.g., Copy 5566) | Disable the backup job in LSSQL (e.g., Copy 5566) in MSSQL,
Check the backup job status in LSSQL (e.g., Copy 9896) | Check the backup job status in LSSQL (e.g., Copy 9896) in MSSQL,
Verify the transaction log shipping state in LSSQL (e.g., Copy 5868) | Verify the transaction log shipping state in LSSQL (e.g., Copy 5868) in MSSQL,
Check the database access mode in LSSQL (e.g., Copy 5213) | Check the database access mode in LSSQL (e.g., Copy 5213) in MSSQL,
Check the database recovery model in LSSQL (e.g., Copy 1968) | Check the database recovery model in LSSQL (e.g., Copy 1968) in MSSQL,
Verify the database recovery mode in LSSQL (e.g., Copy 4754) | Verify the database recovery mode in LSSQL (e.g., Copy 4754) in MSSQL,
Verify the updateability state in DR in LSSQL (e.g., Copy 9100) | Verify the updateability state in DR in LSSQL (e.g., Copy 9100) in MSSQL,
Verify the updateability state in PR in LSSQL (e.g., Copy 8009) | Verify the updateability state in PR in LSSQL (e.g., Copy 8009) in MSSQL,
Verify the database status in LSSQL (e.g., Copy 2448) | Verify the database status in LSSQL (e.g., Copy 2448) in MSSQL,
Execute an MSSQL command for database operations | Execute an MSSQL command for database operations in MSSQL,
Execute a check command for MSSQL configurations | Execute a check command for MSSQL configurations in MSSQL,
Compare the row count in a SQL Server table in DR (e.g., Copy 1308) | Compare the row count in a SQL Server table in DR (e.g., Copy 1308) in MSSQL,
Compare the row count in a SQL Server table in PR (e.g., Copy 504) | Compare the row count in a SQL Server table in PR (e.g., Copy 504) in MSSQL,
Unjoin a database from the availability group for maintenance | Unjoin a database from the availability group for maintenance in MSSQL,
Restore the database with recovery only in DR in LSSQL | Restore the database with recovery only in DR in LSSQL in MSSQL,
Remove the primary database from the availability group for changes | Remove the primary database from the availability group for changes in MSSQL,
Modify the AG mode for the availability group | Modify the AG mode for the availability group in MSSQL,
Join the secondary database to the availability group for syncing | Join the secondary database to the availability group for syncing in MSSQL,
Check if a database is joined or unjoined from the availability group | Check if a database is joined or unjoined from the availability group in MSSQL,
Add the primary database to the availability group for high availability | Add the primary database to the availability group for high availability in MSSQL,
Test the health of all availability replicas in production | Test the health of all availability replicas in production in MSSQL,
Test the health of all availability replicas in DR | Test the health of all availability replicas in DR in MSSQL,
Synchronize AlwaysOn failover with a specific secondary replica | Synchronize AlwaysOn failover with a specific secondary replica in MSSQL,
Synchronize AlwaysOn failover with a random secondary replica | Synchronize AlwaysOn failover with a random secondary replica in MSSQL,
Suspend replication for all databases in an availability group for maintenance | Suspend replication for all databases in an availability group for maintenance in MSSQL,
Resume replication in an availability group after suspension | Resume replication in an availability group after suspension in MSSQL,
Perform a manual planned failover for a controlled switch over to a secondary replica | Perform a manual planned failover for a controlled switch over to a secondary replica in MSSQL,
Forcefully failover to a secondary replica in an emergency situation | Forcefully failover to a secondary replica in an emergency situation in MSSQL,
Check the suspended state of replication for availability groups | Check the suspended state of replication for availability groups in MSSQL,
Check if replication has been resumed after suspension | Check if replication has been resumed after suspension in MSSQL,
Check the status of the availability group in the production environment | Check the status of the availability group in the production environment in MSSQL,
Enable or disable the availability group in the production environment | Enable or disable the availability group in the production environment in MSSQL,
Check the status of the availability group in the DR environment | Check the status of the availability group in the DR environment in MSSQL,
Verify if all connections are allowed in both production and DR environments | Verify if all connections are allowed in both production and DR environments in MSSQL,
Check the state of all databases in an availability group | Check the state of all databases in an availability group in MSSQL,
Synchronize AlwaysOn failover with a specific secondary replica | Synchronize AlwaysOn failover with a specific secondary replica in MSSQL,
Synchronize AlwaysOn failover with a random secondary replica for redundancy | Synchronize AlwaysOn failover with a random secondary replica for redundancy in MSSQL,
Check the running states of SQL Server instances | Check the running states of SQL Server instances in MSSQL,
Verify the database role and mirror state in SQL mirroring | Verify the database role and mirror state in SQL mirroring in MSSQL,
Execute a database cluster changeover operation | Execute a database cluster changeover operation in MSSQL,
Monitor SQL Server mirroring state and health | Monitor SQL Server mirroring state and health in MSSQL,
Change the cluster node weight for multiple nodes in a failover cluster | Change the cluster node weight for multiple nodes in a failover cluster in MSSQL,
Change the cluster node weight for a single node in a failover cluster | Change the cluster node weight for a single node in a failover cluster in MSSQL,
Check the cluster node weight for multiple nodes in a failover cluster | Check the cluster node weight for multiple nodes in a failover cluster in MSSQL,
Check the cluster node weight for a single node in a failover cluster | Check the cluster node weight for a single node in a failover cluster in MSSQL,
Associate a cluster name with a group in SQL Server failover clustering | Associate a cluster name with a group in SQL Server failover clustering in MSSQL,
Restore the last log in SQL DSM for a specific database | Restore the last log in SQL DSM for a specific database in MSSQL,
Migrate server roles to the DR server in SQL DSM | Migrate server roles to the DR server in SQL DSM in MSSQL,
Restore a log in SQL DSM for database recovery | Restore a log in SQL DSM for database recovery in MSSQL,
Scan a file from an application server for processing or replication | Scan a file from an application server for processing or replication in MSSQL,
Migrate server roles to the PR server in SQL DSM | Migrate server roles to the PR server in SQL DSM in MSSQL,
Migrate the logging roles to the PR server in SQL DSM | Migrate the logging roles to the PR server in SQL DSM in MSSQL,
Migrate the logging roles to the DR server in SQL DSM | Migrate the logging roles to the DR server in SQL DSM in MSSQL,
Restore the database with recovery in SQL DSM for both primary and secondary servers | Restore the database with recovery in SQL DSM for both primary and secondary servers in MSSQL,
Kill the process on the secondary SQL Server instance in SQL DSM | Kill the process on the secondary SQL Server instance in SQL DSM in MSSQL,
Kill the process on the primary SQL Server instance in SQL DSM | Kill the process on the primary SQL Server instance in SQL DSM in MSSQL,
Generate the last log for failover control in SQL DSM | Generate the last log for failover control in SQL DSM in MSSQL,
Set the database option in SQL DSM for optimized performance | Set the database option in SQL DSM for optimized performance in MSSQL,
Sync SQL Server 2000 data across platforms in SQL DSM | Sync SQL Server 2000 data across platforms in SQL DSM in MSSQL,
Move a CSV file to the database server for integration | Move a CSV file to the database server for integration in MSSQL,
Replicate files from the application server to the database server for backup or processing | Replicate files from the application server to the database server for backup or processing in MSSQL,
Download the response file from the production server for analysis | Download the response file from the production server for analysis in MSSQL,
Create a CSV file from response data for integration or reporting | Create a CSV file from response data for integration or reporting in MSSQL,
Sync application data with the database server for consistency | Sync application data with the database server for consistency in MSSQL,
Monitor the NLS (National Language Support) settings in MSSQL for compatibility | Monitor the NLS (National Language Support) settings in MSSQL for compatibility in MSSQL,
Fix orphaned users in the SQL database to restore integrity | Fix orphaned users in the SQL database to restore integrity in MSSQL,
Set the DR database to multi-user access mode in ILSSQL for general use | Set the DR database to multi-user access mode in ILSSQL for general use in MSSQL,
Update the restore job with the DR IP address in LSSQL for disaster recovery scenarios | Update the restore job with the DR IP address in LSSQL for disaster recovery scenarios in MSSQL,
Update the restore job with the DR IP address in ILSSQL for consistency | Update the restore job with the DR IP address in ILSSQL for consistency in MSSQL,
Update the copy job with the DR IP address in LSSQL for redundancy | Update the copy job with the DR IP address in LSSQL for redundancy in MSSQL,
Update the copy job with the DR IP address in ILSSQL for replication consistency | Update the copy job with the DR IP address in ILSSQL for replication consistency in MSSQL,
Update the backup job with the PR IP address in ILSSQL for primary operations | Update the backup job with the PR IP address in ILSSQL for primary operations in MSSQL,
Update the backup job with the PR IP address in LSSQL for primary server operations | Update the backup job with the PR IP address in LSSQL for primary server operations in MSSQL,
Remove primary and secondary log shipping in LSSQL to reset configuration | Remove primary and secondary log shipping in LSSQL to reset configuration in MSSQL,
Remove primary log shipping in ILSSQL for decommissioning | Remove primary log shipping in ILSSQL for decommissioning in MSSQL,
Remove secondary log shipping in LSSQL to stop replication | Remove secondary log shipping in LSSQL to stop replication in MSSQL,
Remove secondary log shipping in ILSSQL for consistency management | Remove secondary log shipping in ILSSQL for consistency management in MSSQL,
Remove primary log shipping in LSSQL for reset or changes | Remove primary log shipping in LSSQL for reset or changes in MSSQL,
Remove primary and secondary log shipping in ILSSQL for cleanup | Remove primary and secondary log shipping in ILSSQL for cleanup in MSSQL,
Restore the database with recovery in LSSQL for both primary and secondary servers | Restore the database with recovery in LSSQL for both primary and secondary servers in MSSQL,
Restore the database with recovery in ILSSQL for production or DR restoration | Restore the database with recovery in ILSSQL for production or DR restoration in MSSQL,
Set the database to single-user access mode for DR purposes in ILSSQL | Set the database to single-user access mode for DR purposes in ILSSQL in MSSQL,
Kill the session on the DR server in ILSSQL for troubleshooting | Kill the session on the DR server in ILSSQL for troubleshooting in MSSQL,
Set the database to multi-user access mode on the primary server in ILSSQL | Set the database to multi-user access mode on the primary server in ILSSQL in MSSQL,
Verify the database is in single-user access mode in LSSQL for recovery | Verify the database is in single-user access mode in LSSQL for recovery in MSSQL,
Verify the PR database is in single-user access mode in ILSSQL for maintenance | Verify the PR database is in single-user access mode in ILSSQL for maintenance in MSSQL,
Set the database to single-user access mode in LSSQL for maintenance or recovery | Set the database to single-user access mode in LSSQL for maintenance or recovery in MSSQL,
Set the database to single-user access mode on the primary server in ILSSQL | Set the database to single-user access mode on the primary server in ILSSQL in MSSQL,
Import login roles to the DR server in ILSSQL for replication | Import login roles to the DR server in ILSSQL for replication in MSSQL,
Import logins to the DR server in ILSSQL for synchronization | Import logins to the DR server in ILSSQL for synchronization in MSSQL,
Export login roles from the production server for migration or backup | Export login roles from the production server for migration or backup in MSSQL,
Export logins from the production server for replication or backup | Export logins from the production server for replication or backup in MSSQL,
Kill the session on the primary server in LSSQL for troubleshooting | Kill the session on the primary server in LSSQL for troubleshooting in MSSQL,
Fix orphaned users in LSSQL for restoring user mappings | Fix orphaned users in LSSQL for restoring user mappings in MSSQL,
Enable the job in LSSQL for scheduled operations to run | Enable the job in LSSQL for scheduled operations to run in MSSQL,
Generate the last backup log with no recovery in LSSQL for failover preparation | Generate the last backup log with no recovery in LSSQL for failover preparation in MSSQL,
Kill the database session with a timeout in LSSQL for resource management | Kill the database session with a timeout in LSSQL for resource management in MSSQL,
Run a job in LSSQL for scheduled database operations | Run a job in LSSQL for scheduled database operations in MSSQL,
Disable a job in LSSQL to stop scheduled operations temporarily | Disable a job in LSSQL to stop scheduled operations temporarily in MSSQL,
Verify the log file sequence in LSSQL for consistency checking | Verify the log file sequence in LSSQL for consistency checking in MSSQL,
Run the restore job | Run the restore job in MSSQL,
Run the copy job | Run the copy job in MSSQL,
Disable the restore job | Disable the restore job in MSSQL,
Disable the copy job | Disable the copy job in MSSQL,
Disable the backup | Disable the backup in MSSQL,
Check the backup job | Check the backup job in MSSQL,
Verify the transaction log shipping state in LSSQL for replication monitoring | Verify the transaction log shipping state in LSSQL for replication monitoring in MSSQL,
Check the database access mode in LSSQL for operation validation | Check the database access mode in LSSQL for operation validation in MSSQL,
Check the database recovery model in LSSQL for consistency | Check the database recovery model in LSSQL for consistency in MSSQL,
Restore secondary log shipping in LSSQL for failover management | Restore secondary log shipping in LSSQL for failover management in MSSQL,
Restore primary log shipping in LSSQL for failover management | Restore primary log shipping in LSSQL for failover management in MSSQL,
Generate the last backup log in LSSQL for database recovery preparation | Generate the last backup log in LSSQL for database recovery preparation in MSSQL,
Kill all sessions in LSSQL for system cleanup or troubleshooting | Kill all sessions in LSSQL for system cleanup or troubleshooting in MSSQL,
Restore the last backup log with no recovery in LSSQL for failover | Restore the last backup log with no recovery in LSSQL for failover in MSSQL,
Set the database to multi-user access mode in LSSQL after maintenance | Set the database to multi-user access mode in LSSQL after maintenance in MSSQL,
Remove the secondary job in LSSQL for replication cleanup | Remove the secondary job in LSSQL for replication cleanup in MSSQL,
Check if the database entry exists on the primary server in LSSQL | Check if the database entry exists on the primary server in LSSQL in MSSQL,
Check if the database entry exists on the secondary server in LSSQL | Check if the database entry exists on the secondary server in LSSQL in MSSQL,
Check if the primary log shipping exists in LSSQL for configuration verification | Check if the primary log shipping exists in LSSQL for configuration verification in MSSQL,
Check if primary and secondary log shipping exist in LSSQL for redundancy | Check if primary and secondary log shipping exist in LSSQL for redundancy in MSSQL,
Verify the database recovery mode in LSSQL for consistency checks | Verify the database recovery mode in LSSQL for consistency checks in MSSQL,
Verify the updateability state in DR in LSSQL for operational readiness | Verify the updateability state in DR in LSSQL for operational readiness in MSSQL,
Verify the updateability state in PR in LSSQL for operational readiness | Verify the updateability state in PR in LSSQL for operational readiness in MSSQL,
Verify the status of the database to ensure it is online and operational | Verify the status of the database to ensure it is online and operational in MSSQL,
Test the National Language Support (NLS) compatibility in SQL Server | Test the National Language Support (NLS) compatibility in SQL Server in MSSQL,
Restore the last backup log with no recovery in LSSQL with prechecks | Restore the last backup log with no recovery in LSSQL with prechecks in MSSQL,
Enable log shipping with the target database in restoring mode in LSSQL | Enable log shipping with the target database in restoring mode in LSSQL in MSSQL,
Restore the last transaction log with standby mode in DR using LSSQL | Restore the last transaction log with standby mode in DR using LSSQL in MSSQL,
Restore the last transaction log with recovery mode in DR using LSSQL | Restore the last transaction log with recovery mode in DR using LSSQL in MSSQL,
Validate backup consistency between primary and secondary servers in LSSQL | Validate backup consistency between primary and secondary servers in LSSQL in MSSQL,
Copy the tail-log backup to DR using LSSQL | Copy the tail-log backup to DR using LSSQL in MSSQL,
Verify if the primary backup transaction log exists in LSSQL | Verify if the primary backup transaction log exists in LSSQL in MSSQL,
Execute the secondary log shipping process in LSSQL | Execute the secondary log shipping process in LSSQL in MSSQL,
Execute reverse primary log shipping in LSSQL for failback | Execute reverse primary log shipping in LSSQL for failback in MSSQL,
Verify if the correct job name is associated with log shipping | Verify if the correct job name is associated with log shipping in MSSQL,
Verify the source and destination backup directory for log shipping | Verify the source and destination backup directory for log shipping in MSSQL,
Check the backup directory and shared path for consistency | Check the backup directory and shared path for consistency in MSSQL,
Execute the primary log shipping process for a secondary DR site | Execute the primary log shipping process for a secondary DR site in MSSQL,
Restore the last backup log with recovery in LSSQL for database recovery | Restore the last backup log with recovery in LSSQL for database recovery in MSSQL,
Execute secondary log shipping for a three-site configuration | Execute secondary log shipping for a three-site configuration in MSSQL,
Execute primary log shipping for a three-DR site configuration | Execute primary log shipping for a three-DR site configuration in MSSQL,
Restore the transaction log in standby mode | Restore the transaction log in standby mode in MSSQL,
Verify the log file sequence for a three-DR site setup | Verify the log file sequence for a three-DR site setup in MSSQL,
Make the database writable after failover of the last restored file in LSSQL | Make the database writable after failover of the last restored file in LSSQL in MSSQL,
Execute the secondary log shipping job schedule in LSSQL | Execute the secondary log shipping job schedule in LSSQL in MSSQL,
Execute the primary log shipping job schedule in LSSQL | Execute the primary log shipping job schedule in LSSQL in MSSQL,
Check the database mode for SQL NLS compatibility | Check the database mode for SQL NLS compatibility in MSSQL,
Check the database state for SQL NLS validation | Check the database state for SQL NLS validation in MSSQL,
Verify the database mirroring status for consistency | Verify the database mirroring status for consistency in MSSQL,
Perform a mirroring failover for database redundancy | Perform a mirroring failover for database redundancy in MSSQL,
Kill a specific SQL Server process by name for troubleshooting | Kill a specific SQL Server process by name for troubleshooting in MSSQL,
Remove the restore job in ILSSQL for cleanup | Remove the restore job in ILSSQL for cleanup in MSSQL,
Remove the copy job in ILSSQL for cleanup | Remove the copy job in ILSSQL for cleanup in MSSQL,
Attach a database to the SQL Server instance | Attach a database to the SQL Server instance in MSSQL,
Set the database option to single-user mode for maintenance | Set the database option to single-user mode for maintenance in MSSQL,
Take the database offline for maintenance or troubleshooting | Take the database offline for maintenance or troubleshooting in MSSQL,
Migrate server roles in LSSQL for role consistency | Migrate server roles in LSSQL for role consistency in MSSQL,
Migrate logins in ILSSQL for login synchronization | Migrate logins in ILSSQL for login synchronization in MSSQL,
Execute secondary log shipping in ILSSQL for redundancy | Execute secondary log shipping in ILSSQL for redundancy in MSSQL,
Execute primary log shipping in ILSSQL for DR operations | Execute primary log shipping in ILSSQL for DR operations in MSSQL,
Remove the backup job in ILSSQL for cleanup | Remove the backup job in ILSSQL for cleanup in MSSQL,
Run the backup job in ILSSQL for scheduled operations | Run the backup job in ILSSQL for scheduled operations in MSSQL,
Unmount a service or process in linux or unix | Unmount a service or process in linux or unix in MSSQL,
Mount a service or process in linux or unix | Mount a service or process in linux or unix in MSSQL,
Migrate roles in ILSSQL for role management | Migrate roles in ILSSQL for role management in MSSQL,
Migrate logins in LSSQL for login consistency | Migrate logins in LSSQL for login consistency in MSSQL,
Detach a SQL database for relocation or maintenance | Detach a SQL database for relocation or maintenance in MSSQL,
Attach a SQL database after detachment or migration | Attach a SQL database after detachment or migration in MSSQL,
Verify if the database entry exists on the primary server in LSSQL | Verify if the database entry exists on the primary server in LSSQL in MSSQL,
Verify if the database entry exists on the secondary server in LSSQL | Verify if the database entry exists on the secondary server in LSSQL in MSSQL,
Check if primary log shipping exists in LSSQL | Check if primary log shipping exists in LSSQL in MSSQL,
Check if primary and secondary log shipping exist in LSSQL | Check if primary and secondary log shipping exist in LSSQL in MSSQL,
Verify the database mode in SQL NLS for compatibility | Verify the database mode in SQL NLS for compatibility in MSSQL,
Change the availability mode for AlwaysOn Availability Groups | Change the availability mode for AlwaysOn Availability Groups in MSSQL,
Execute a normal failover in AlwaysOn for redundancy | Execute a normal failover in AlwaysOn for redundancy in MSSQL,
Perform a pre-flight check for database sync state in AlwaysOn | Perform a pre-flight check for database sync state in AlwaysOn in MSSQL,
Monitor the AlwaysOn Availability Group status | Monitor the AlwaysOn Availability Group status in MSSQL,
Resume data movement in AlwaysOn Availability Groups | Resume data movement in AlwaysOn Availability Groups in MSSQL,
Execute a forced failover in AlwaysOn for disaster recovery | Execute a forced failover in AlwaysOn for disaster recovery in MSSQL,
Check the availability mode of an AlwaysOn Availability Group | Check the availability mode of an AlwaysOn Availability Group in MSSQL,
Check the role of the server in an AlwaysOn setup | Check the role of the server in an AlwaysOn setup in MSSQL,
Verify the custom priority state of an AlwaysOn replica | Verify the custom priority state of an AlwaysOn replica in MongoDB,
Check the health status of an AlwaysOn setup | Check the health status of an AlwaysOn setup in MongoDB,
Monitor MongoDB status and operations | Monitor MongoDB status and operations in MongoDB,
Check the replication lag status in MongoDB | Check the replication lag status in MongoDB in MongoDB,
Set the priority of a replica set in MongoDB | Set the priority of a replica set in MongoDB in MongoDB,
Verify the primary state of a MongoDB replica set | Verify the primary state of a MongoDB replica set in MongoDB,
Verify the secondary state of a MongoDB replica set | Verify the secondary state of a MongoDB replica set in MongoDB,
Perform a DB2 HADR takeover for disaster recovery | Perform a DB2 HADR takeover for disaster recovery in IBM_DB2,
Start the DB2 database | Start the DB2 database in IBM_DB2,
Deactivate and start the DB2 database | Deactivate and start the DB2 database in IBM_DB2,
Activate the DB2 database for operations | Activate the DB2 database for operations in IBM_DB2,
Switch back to the primary DB2 database | Switch back to the primary DB2 database in IBM_DB2,
Perform a switchover operation in DB2 for planned maintenance | Perform a switchover operation in DB2 for planned maintenance in IBM_DB2,
Verify if the DB2 database is online | Verify if the DB2 database is online in IBM_DB2,
Verify if DB2 HADR is active | Verify if DB2 HADR is active in IBM_DB2,
Stop the DB2 HADR process | Stop the DB2 HADR process in IBM_DB2,
Start the DB2 HADR primary instance | Start the DB2 HADR primary instance in IBM_DB2,
Deactivate and terminate the DB2 database | Deactivate and terminate the DB2 database in IBM_DB2,
Start the DB2 HADR standby instance | Start the DB2 HADR standby instance in IBM_DB2,
Deactivate the DB2 database for maintenance | Deactivate the DB2 database for maintenance in IBM_DB2,
Terminate the DB2 instance | Terminate the DB2 instance in IBM_DB2,
Verify if the DB2 database is active | Verify if the DB2 database is active in IBM_DB2,
Unquiesce the DB2 database for operations | Unquiesce the DB2 database for operations in IBM_DB2,
Check if the DB2 database is quiesced | Check if the DB2 database is quiesced in IBM_DB2,
Quiesce the DB2 database for maintenance | Quiesce the DB2 database for maintenance in IBM_DB2,
Verify the log gap in DB2 HADR | Verify the log gap in DB2 HADR in IBM_DB2,
Verify the log position in DB2 HADR | Verify the log position in DB2 HADR in IBM_DB2,
Check if DB2 HADR state is in PEER mode | Check if DB2 HADR state is in PEER mode in IBM_DB2,
Check if DB2 HADR role is standby | Check if DB2 HADR role is standby in IBM_DB2,
Check if DB2 HADR role is primary | Check if DB2 HADR role is primary in IBM_DB2,
Verify if the DB2 database is in standby mode | Verify if the DB2 database is in standby mode in IBM_DB2,
Verify if the DB2 database is in primary mode | Verify if the DB2 database is in primary mode in IBM_DB2,
Monitor DB2 HADR status for health and consistency | Monitor DB2 HADR status for health and consistency in IBM_DB2,
Verify the status of a virtual machine (VM) | Verify the status of a virtual machine (VM) in Hypervisor,
Check if the VM failover is in a waiting completion status | Check if the VM failover is in a waiting completion status in Hypervisor,
Verify the state of a VM or server | Verify the state of a VM or server in Hypervisor,
Check the reverse replication connection configuration | Check the reverse replication connection configuration in Hypervisor,
Verify the replication mode for consistency | Verify the replication mode for consistency in Hypervisor,
Verify the replication state for health | Verify the replication state for health in Hypervisor,
Check if the system is prepared for failover status | Check if the system is prepared for failover status in Hypervisor,
Verify the VLAN ID connectivity status for Hyper-V VMs | Verify the VLAN ID connectivity status for Hyper-V VMs in Hypervisor,
Verify the disconnected status for Hyper-V VMs | Verify the disconnected status for Hyper-V VMs in Hypervisor,
Verify the connection status of a Hyper-V virtual machine (VM) | Verify the connection status of a Hyper-V virtual machine (VM) in Hypervisor,
Check the forward replication connection configuration for Hyper-V | Check the forward replication connection configuration for Hyper-V in Hypervisor,
Start a virtual machine on Hyper-V | Start a virtual machine on Hyper-V in Hypervisor,
Initiate a failover process for a VM | Initiate a failover process for a VM in Hypervisor,
Assign a single network cluster Hyper-V VM with an IP address and its MAC address | Assign a single network cluster Hyper-V VM with an IP address and its MAC address in Hypervisor,
Shut down a virtual machine on Hyper-V | Shut down a virtual machine on Hyper-V in Hypervisor,
Set a VLAN ID to the virtual switch associated with a Hyper-V VM | Set a VLAN ID to the virtual switch associated with a Hyper-V VM in Hypervisor,
Resume replication for a Hyper-V VM | Resume replication for a Hyper-V VM in Hypervisor,
Failover a replica virtual machine on Hyper-V | Failover a replica virtual machine on Hyper-V in Hypervisor,
Disconnect a Hyper-V VM from the network or switch | Disconnect a Hyper-V VM from the network or switch in Hypervisor,
Connect a Hyper-V VM to a virtual switch | Connect a Hyper-V VM to a virtual switch in Hypervisor,
Verify the primary replication mode of a Hyper-V cluster | Verify the primary replication mode of a Hyper-V cluster in Hypervisor,
Change the replication mode for a Hyper-V setup | Change the replication mode for a Hyper-V setup in Hypervisor,
Change the DNS IP address configuration for a system | Change the DNS IP address configuration for a system in Hypervisor,
Change the cluster Hyper-V VM IP address without modifying the MAC address | Change the cluster Hyper-V VM IP address without modifying the MAC address in Hypervisor,
Change the cluster Hyper-V VM IP address along with the MAC address | Change the cluster Hyper-V VM IP address along with the MAC address in Hypervisor,
Cancel an ongoing failover process for a Hyper-V VM in a cluster | Cancel an ongoing failover process for a Hyper-V VM in a cluster in Hypervisor,
Assign a single network cluster Hyper-V VM with an incorrect MAC address | Assign a single network cluster Hyper-V VM with an incorrect MAC address in Hypervisor,
Perform monitoring actions on a Hyper-V setup | Perform monitoring actions on a Hyper-V setup in Hypervisor,
Remove cluster resource dependencies from dependent resources | Remove cluster resource dependencies from dependent resources in Hypervisor,
Add a cluster resource dependency to dependent resources | Add a cluster resource dependency to dependent resources in Hypervisor,
Verify the cluster resource dependencies in dependent resources | Verify the cluster resource dependencies in dependent resources in Hypervisor,
Remove a cluster resource from the cluster owner group | Remove a cluster resource from the cluster owner group in Hypervisor,
Add a cluster resource to the cluster owner group | Add a cluster resource to the cluster owner group in Hypervisor,
Verify the master log file and its position on master and slave servers | Verify the master log file and its position on master and slave servers in MySQL,
Stop the slave process in a database replication setup | Stop the slave process in a database replication setup in MySQL,
Check the running status of the slave SQL process | Check the running status of the slave SQL process in MySQL,
Verify the state of the slave I/O process | Verify the state of the slave I/O process in MySQL,
Verify the running status of the slave I/O process | Verify the running status of the slave I/O process in MySQL,
Read the relay master log file in a database replication setup | Read the relay master log file in a database replication setup in MySQL,
Read the master log position for replication | Read the master log position for replication in MySQL,
Check the slave status in a MySQL replication setup | Check the slave status in a MySQL replication setup in MySQL,
Check the status of the MySQL service | Check the status of the MySQL service in MySQL,
Read the relay master log file on the master server | Read the relay master log file on the master server in MySQL,
Read the master log position on the master server | Read the master log position on the master server in MySQL,
Verify the master log file in a replication setup | Verify the master log file in a replication setup in MySQL,
Execute a MySQL database command | Execute a MySQL database command in MySQL,
Execute a MySQL database check command | Execute a MySQL database check command in MySQL,
Verify the master log position in a replication setup | Verify the master log position in a replication setup in MySQL,
Check the connection status of the database | Check the connection status of the database in MySQL,
Change the master host and log file in a replication setup | Change the master host and log file in a replication setup in MySQL,
Change the master host in a replication setup | Change the master host in a replication setup in MySQL,
Set the global read-only mode to OFF | Set the global read-only mode to OFF in MySQL,
Set the global read-only mode to ON | Set the global read-only mode to ON in MySQL,
Flush logs in a MySQL database | Flush logs in a MySQL database in MySQL,
Flush tables in a MySQL database | Flush tables in a MySQL database in MySQL,
Verify read/write operations on the master instance | Verify read/write operations on the master instance in MySQL,
Enable read/write mode on the master instance | Enable read/write mode on the master instance in MySQL,
Show the master status in a MySQL replication setup | Show the master status in a MySQL replication setup in MySQL,
Start the slave process on the master server | Start the slave process on the master server in MySQL,
Check if the slave process has stopped | Check if the slave process has stopped in MySQL,
Monitor the MySQL replication setup | Monitor the MySQL replication setup in MySQL,
Stop replication on a slave server | Stop replication on a slave server in MySQL,
Set the master instance to read-only mode | Set the master instance to read-only mode in MySQL,
Verify the read-only status of a slave instance | Verify the read-only status of a slave instance in MySQL,
Verify replication user connectivity from slave to master | Verify replication user connectivity from slave to master in MySQL,
Verify the read-only status on the master instance | Verify the read-only status on the master instance in MySQL,
Verify that SQL binary logging is enabled on the master | Verify that SQL binary logging is enabled on the master in MySQL,
Verify the read-only status of the master instance | Verify the read-only status of the master instance in MySQL,
Verify the SQL log position on both master and slave servers | Verify the SQL log position on both master and slave servers in MySQL,
Recover a standby database in a disaster recovery setup | Recover a standby database in a disaster recovery setup in ASM Oracle,
Prepare a database for shutdown | Prepare a database for shutdown in ASM Oracle,
Execute a pre-shutdown redo control script for PR | Execute a pre-shutdown redo control script for PR in ASM Oracle,
Mount a standby database for recovery | Mount a standby database for recovery in ASM Oracle,
Create a control script for database management | Create a control script for database management in ASM Oracle,
Create a control file from a trace file for recovery | Create a control file from a trace file for recovery in ASM Oracle,
Backup the redo control file for database recovery | Backup the redo control file for database recovery in ASM Oracle,
Add a temporary tablespace to the database | Add a temporary tablespace to the database in ASM Oracle,
Restore the standby control file using ASM | Restore the standby control file using ASM in ASM Oracle,
Recover a database using ASM | Recover a database using ASM in ASM Oracle,
Open the database using ASM | Open the database using ASM in ASM Oracle,
Create a standby control file using ASM | Create a standby control file using ASM in ASM Oracle,
Start up a database in no-mount mode | Start up a database in no-mount mode in ASM Oracle,
Start up a database in mount mode | Start up a database in mount mode in ASM Oracle,
Shut down a standby database | Shut down a standby database in ASM Oracle,
Shut down a primary database | Shut down a primary database in ASM Oracle,
Shut down a disaster recovery (DR) database | Shut down a disaster recovery (DR) database in ASM Oracle,
Replicate folders using rsync in a POSIX environment | Replicate folders using rsync in a POSIX environment in OracleRsync,
Replicate files using rsync in a POSIX environment | Replicate files using rsync in a POSIX environment in OracleRsync,
Replicate a standby control file using Oracle rsync | Replicate a standby control file using Oracle rsync in OracleRsync,
Replicate a standby trace file using Oracle rsync | Replicate a standby trace file using Oracle rsync in OracleRsync,
Update the disaster recovery operation status | Update the disaster recovery operation status in Generic Database Actions,
Verify the state of the database | Verify the state of the database in MaxDB,
Start a MaxDB database instance | Start a MaxDB database instance in MaxDB,
Stop a MaxDB database instance | Stop a MaxDB database instance in MaxDB,
Execute a database check command | Execute a database check command in MaxDB,
Validate the state of a Data Guard Broker (DGB) setup | Validate the state of a Data Guard Broker (DGB) setup in Oracle,
Switch over in a Data Guard Broker setup | Switch over in a Data Guard Broker setup in Oracle,
Perform a failover in a Data Guard Broker setup | Perform a failover in a Data Guard Broker setup in Oracle,
Convert a snapshot standby database in DGB | Convert a snapshot standby database in DGB in Oracle,
Convert a physical standby database in DGB | Convert a physical standby database in DGB in Oracle,
Verify the configuration of a Data Guard Broker setup | Verify the configuration of a Data Guard Broker setup in Oracle,
Start recovery for Oracle Data Guard 12c | Start recovery for Oracle Data Guard 12c in Oracle,
Start the database for Oracle Data Guard 12c | Start the database for Oracle Data Guard 12c in Oracle,
Shut down the database for Oracle Data Guard 12c | Shut down the database for Oracle Data Guard 12c in Oracle,
Mount the database for Oracle Data Guard 12c | Mount the database for Oracle Data Guard 12c in Oracle,
Verify a switchover in Oracle Data Guard 12c | Verify a switchover in Oracle Data Guard 12c in Oracle,
Perform a switchover in Oracle Data Guard 12c | Perform a switchover in Oracle Data Guard 12c in Oracle,
Stop the Node Manager service in Oracle WebLogic | Stop the Node Manager service in Oracle WebLogic in Oracle,
Stop a managed server in Oracle WebLogic | Stop a managed server in Oracle WebLogic in Oracle,
Stop the HTTP server | Stop the HTTP server in Oracle,
Stop the administration server | Stop the administration server in Oracle,
Start the Node Manager service | Start the Node Manager service in Oracle,
Start a managed server | Start a managed server in Oracle,
Start the HTTP server | Start the HTTP server in Oracle,
Start the administration server | Start the administration server in Oracle,
Verify the database mode after reverting a snapshot | Verify the database mode after reverting a snapshot in Oracle,
Verify the database role after reverting a snapshot | Verify the database role after reverting a snapshot in Oracle,
Verify the database role before reverting a snapshot | Verify the database role before reverting a snapshot in Oracle,
Verify the database role before taking a snapshot | Verify the database role before taking a snapshot in Oracle,
Check if the flashback feature is turned off | Check if the flashback feature is turned off in Oracle,
Convert a snapshot standby database to a physical standby database | Convert a snapshot standby database to a physical standby database in Oracle,
Verify the database role after converting a snapshot | Verify the database role after converting a snapshot in Oracle,
Verify the database mode after converting a snapshot | Verify the database mode after converting a snapshot in Oracle,
Verify the current system change number (SCN) | Verify the current system change number (SCN) in Oracle,
Convert a database to a snapshot standby | Convert a database to a snapshot standby in Oracle,
Verify the flashback retention target for the database | Verify the flashback retention target for the database in Oracle,
Verify the recovery file destination size for the database | Verify the recovery file destination size for the database in Oracle,
Verify the recovery file destination for the database | Verify the recovery file destination for the database in Oracle,
Verify the Data Guard status | Verify the Data Guard status in Oracle,
Verify the database mode before taking a snapshot | Verify the database mode before taking a snapshot in Oracle,
Verify the database mode before reverting a snapshot | Verify the database mode before reverting a snapshot in Oracle,
Verify the maximum sequence number in Data Guard for Windows | Verify the maximum sequence number in Data Guard for Windows in Oracle,
Open the database in Data Guard | Open the database in Data Guard in Oracle,
Shut down the primary database in Data Guard | Shut down the primary database in Data Guard in Oracle,
Establish a Data Guard connector | Establish a Data Guard connector in Oracle,
Execute an SQL command | Execute an SQL command in Oracle,
Perform a while-check operation | Perform a while-check operation in Oracle,
Mount a standby database in Data Guard | Mount a standby database in Data Guard in Oracle,
Check the job status in Data Guard | Check the job status in Data Guard in Oracle,
Test the chatbot functionality | Test the chatbot functionality in Oracle,
Test role and mode actions in Data Guard | Test role and mode actions in Data Guard in Oracle,
Start an Oracle instance in open mode using SRVCTL | Start an Oracle instance in open mode using SRVCTL in Oracle,
Monitor Oracle Data Guard actions | Monitor Oracle Data Guard actions in Oracle,
Perform a database switch over using the `ALTER DATABASE` command | Perform a database switch over using the `ALTER DATABASE` command in Oracle,
Verify the `ALTER DATABASE` switchover operation | Verify the `ALTER DATABASE` switchover operation in Oracle,
Stop a database using SRVCTL | Stop a database using SRVCTL in Oracle,
Start a database using SRVCTL | Start a database using SRVCTL in Oracle,
Stop an Oracle instance using SRVCTL | Stop an Oracle instance using SRVCTL in Oracle,
Start an Oracle instance in mount mode using SRVCTL | Start an Oracle instance in mount mode using SRVCTL in Oracle,
Check the open mode of a database | Check the open mode of a database in Oracle,
Mount a database using the `ALTER DATABASE` command | Mount a database using the `ALTER DATABASE` command in Oracle,
Start the database in no-mount mode | Start the database in no-mount mode in Oracle,
Check the switchover status in Data Guard | Check the switchover status in Data Guard in Oracle,
Execute a SQL command for verification | Execute a SQL command for verification in Oracle,
Switch a Data Guard standby database to primary | Switch a Data Guard standby database to primary in Oracle,
Switch a Data Guard primary database to standby | Switch a Data Guard primary database to standby in Oracle,
Switch the log file in Data Guard | Switch the log file in Data Guard in Oracle,
Recover a standby database in Data Guard | Recover a standby database in Data Guard in Oracle,
Verify the user status in Data Guard | Verify the user status in Data Guard in Oracle,
Verify the maximum sequence number in Data Guard | Verify the maximum sequence number in Data Guard in Oracle,
Verify the database mode and role in Data Guard | Verify the database mode and role in Data Guard in Oracle,
Switch to the current log file in Data Guard | Switch to the current log file in Data Guard in Oracle,
Check the job status in PR | Check the job status in PR in Oracle,
Start the recovery process in a new disaster recovery setup | Start the recovery process in a new disaster recovery setup in Oracle,
Shut down the disaster recovery environment | Shut down the disaster recovery environment in Oracle,
Mount a new disaster recovery standby database | Mount a new disaster recovery standby database in Oracle,
Shut down the primary database | Shut down the primary database in Oracle,
Verify the archive log sequence | Verify the archive log sequence in Oracle,
Verify the switchover status in Data Guard | Verify the switchover status in Data Guard in Oracle,
Monitor Oracle RAC actions | Monitor Oracle RAC actions in Oracle,
Switch a database in Oracle Data Guard using API | Switch a database in Oracle Data Guard using API in Oracle,
Check database synchronization status using Oracle Data Guard API | Check database synchronization status using Oracle Data Guard API in Oracle,
Verify the log sequence in Oracle Data Guard | Verify the log sequence in Oracle Data Guard in Oracle,
Shut down the primary database for switching | Shut down the primary database for switching in Oracle,
Shut down the PR database for Windows disaster recovery | Shut down the PR database for Windows disaster recovery in Oracle,
Shut down the PR database for Windows | Shut down the PR database for Windows in Oracle,
Stop the Oracle listener | Stop the Oracle listener in Oracle,
Stop the listener with a password | Stop the listener with a password in Oracle,
Start the database in mount mode with force | Start the database in mount mode with force in Oracle,
Start the Oracle listener | Start the Oracle listener in Oracle,
Start the listener with a password | Start the listener with a password in Oracle,
Start the database in no-mount mode | Start the database in no-mount mode in Oracle,
Start the database in mount mode | Start the database in mount mode in Oracle,
Start the database | Start the database in Oracle,
Start the database in read-only mode | Start the database in read-only mode in Oracle,
Start the database standby for Windows disaster recovery | Start the database standby for Windows disaster recovery in Oracle,
Start the database standby for Windows | Start the database standby for Windows in Oracle,
Start the database standby | Start the database standby in Oracle,
Start the database in read-write mode for Windows disaster recovery | Start the database in read-write mode for Windows disaster recovery in Oracle,
Start the database in read-write mode for Windows | Start the database in read-write mode for Windows in Oracle,
Start the database in read-write mode | Start the database in read-write mode in Oracle,
Shut down the standby database for Windows disaster recovery | Shut down the standby database for Windows disaster recovery in Oracle,
Shut down the database | Shut down the database in Oracle,
Shut down the database instance | Shut down the database instance in Oracle,
Restore a standby control file | Restore a standby control file in Oracle,
Replicate the standby control file | Replicate the standby control file in Oracle,
Recover the standby database for Windows disaster recovery | Recover the standby database for Windows disaster recovery in Oracle,
Recover the standby database file | Recover the standby database file in Oracle,
Recover the database | Recover the database in Oracle,
Execute a pre-shutdown redo control script | Execute a pre-shutdown redo control script in Oracle,
Open the database | Open the database in Oracle,
Terminate Oracle sessions | Terminate Oracle sessions in Oracle,
Check if the checkpoint count is one | Check if the checkpoint count is one in Oracle,
Generate a redo control backup script for Windows disaster recovery | Generate a redo control backup script for Windows disaster recovery in Oracle,
Generate a redo control backup script for Windows | Generate a redo control backup script for Windows in Oracle,
Flashback to a restore point | Flashback to a restore point in Oracle,
Execute an SQL script with a password | Execute an SQL script with a password in Oracle,
Execute an SQL script with an environment password | Execute an SQL script with an environment password in Oracle,
Execute a redo control backup for Windows disaster recovery | Execute a redo control backup for Windows disaster recovery in Oracle,
Execute a redo control backup for Windows | Execute a redo control backup for Windows in Oracle,
Execute a database command | Execute a database command in Oracle,
Drop a restore point | Drop a restore point in Oracle,
Switch the database log for Windows disaster recovery | Switch the database log for Windows disaster recovery in Oracle,
Create a temporary tablespace file | Create a temporary tablespace file in Oracle,
Create a temporary file | Create a temporary file in Oracle,
Create a standby control file | Create a standby control file in Oracle,
Create a restore point | Create a restore point in Oracle,
Generate a control file creation script | Generate a control file creation script in Oracle,
Create a control file using a script | Create a control file using a script in Oracle,
Copy the redo control file | Copy the redo control file in Oracle,
Compare table counts between PR and DR | Compare table counts between PR and DR in Oracle,
Compare table counts between primary and standby databases | Compare table counts between primary and standby databases in Oracle,
Check for no-logging operations in the database | Check for no-logging operations in the database in Oracle,
Check if the flashback feature is turned on | Check if the flashback feature is turned on in Oracle,
Backup the control file | Backup the control file in Oracle,
Open the database using the `ALTER DATABASE` command | Open the database using the `ALTER DATABASE` command in Oracle,
Apply incremental logs to the database | Apply incremental logs to the database in Oracle,
Enable log archive destination using the `ALTER SYSTEM` command | Enable log archive destination using the `ALTER SYSTEM` command in Oracle,
Disable log archive destination using the `ALTER SYSTEM` command | Disable log archive destination using the `ALTER SYSTEM` command in Oracle,
Log system operations using the `ALTER SYSTEM` command | Log system operations using the `ALTER SYSTEM` command in Oracle,
Set the standby database to maximum performance using `ALTER DATABASE` | Set the standby database to maximum performance using `ALTER DATABASE` in Oracle,
Mount the database using the `ALTER DATABASE` command | Mount the database using the `ALTER DATABASE` command in Oracle,
Enable flashback on the database using `ALTER DATABASE` | Enable flashback on the database using `ALTER DATABASE` in Oracle,
Disable flashback on the database using `ALTER DATABASE` | Disable flashback on the database using `ALTER DATABASE` in Oracle,
Convert the database to a physical standby using `ALTER DATABASE` | Convert the database to a physical standby using `ALTER DATABASE` in Oracle,
Activate the standby database using `ALTER DATABASE` | Activate the standby database using `ALTER DATABASE` in Oracle,
Cancel the managed recovery mode for a standby database using `ALTER DATABASE` | Cancel the managed recovery mode for a standby database using `ALTER DATABASE` in Oracle,
Activate the database in read-write mode | Activate the database in read-write mode in Oracle,
Force-enable the node state of a virtual server | Force-enable the node state of a virtual server in F5 Load Balancer,
Force-disable the node state of a virtual server | Force-disable the node state of a virtual server in F5 Load Balancer,
Enable a Local Traffic Manager (LTM) virtual server | Enable a Local Traffic Manager (LTM) virtual server in F5 Load Balancer,
Disable a Local Traffic Manager (LTM) virtual server | Disable a Local Traffic Manager (LTM) virtual server in F5 Load Balancer,
Check the state of the Local Traffic Manager (LTM) virtual server | Check the state of the Local Traffic Manager (LTM) virtual server in F5 Load Balancer,
Check if the default route pools name exists in the F5 Load Balancer | Check if the default route pools name exists in the F5 Load Balancer in F5 Load Balancer,
Check if the F5 Load Balancer name exists and is enabled | Check if the F5 Load Balancer name exists and is enabled in F5 Load Balancer,
Check the API token expiry date | Check the API token expiry date in F5 Load Balancer,
Migrate the F5 Load Balancer default route pools name change | Migrate the F5 Load Balancer default route pools name change in F5 Load Balancer,
Modify NS1 DNS records with multiple A-type IP addresses | Modify NS1 DNS records with multiple A-type IP addresses in NS1_DNS,
Modify NS1 DNS record of CNAME type | Modify NS1 DNS record of CNAME type in NS1_DNS,
Modify NS1 DNS record of A-type with a single IP address | Modify NS1 DNS record of A-type with a single IP address in NS1_DNS,
Check if NS1 DNS record is of CNAME type | Check if NS1 DNS record is of CNAME type in NS1_DNS,
Check if NS1 DNS record is of A-type with a single IP address | Check if NS1 DNS record is of A-type with a single IP address in NS1_DNS,
Check if NS1 DNS record is of A-type with multiple IP addresses | Check if NS1 DNS record is of A-type with multiple IP addresses in NS1_DNS,
Query DNS records using INFOBLOX | Query DNS records using INFOBLOX in InfoBlox,
Modify DNS records using INFOBLOX | Modify DNS records using INFOBLOX in InfoBlox,
Delete DNS records using INFOBLOX | Delete DNS records using INFOBLOX in InfoBlox,
View DNS query statistics with INFOBLOX | View DNS query statistics with INFOBLOX in InfoBlox,
Add DNS records using INFOBLOX | Add DNS records using INFOBLOX in InfoBlox,
Delete a CNAME record | Delete a CNAME record in InfoBlox,
Enable or disable DNS record mapping | Enable or disable DNS record mapping in InfoBlox,
Check the DNS record status | Check the DNS record status in InfoBlox,
Check if a DNS record exists in a specific view and zone | Check if a DNS record exists in a specific view and zone in InfoBlox,
Check if a DNS zone exists in a DNS view | Check if a DNS zone exists in a DNS view in InfoBlox,
Check if a DNS view exists | Check if a DNS view exists in InfoBlox,
Modify a CNAME record | Modify a CNAME record in InfoBlox,
Add a CNAME record | Add a CNAME record in InfoBlox,
Check VLAN configuration | Check VLAN configuration in Routers,
Check static route configuration | Check static route configuration in Routers,
Update firewall policies | Update firewall policies in FireWall,
Monitor OpenShift clusters | Monitor OpenShift clusters in Kubernet,
Scale up or down OpenShift pods and deployment counts | Scale up or down OpenShift pods and deployment counts in Kubernet,
Check the pod deployment count with all pods in ready status | Check the pod deployment count with all pods in ready status in Kubernet,
Scale up or down OpenShift pods in replica sets | Scale up or down OpenShift pods in replica sets in Kubernet,
Check the pod replica set count with all pods in ready status | Check the pod replica set count with all pods in ready status in Kubernet,
Scale up or down OpenShift pods in stateful sets | Scale up or down OpenShift pods in stateful sets in Kubernet,
Check the pod stateful set count with all pods in ready status | Check the pod stateful set count with all pods in ready status in Kubernet,
Scale up or down OpenShift machine sets machine count | Scale up or down OpenShift machine sets machine count in Kubernet,
Check the OpenShift machine set machine count | Check the OpenShift machine set machine count in Kubernet,
Stop a virtual machine | Stop a virtual machine in Kubernet,
Start a virtual machine | Start a virtual machine in Kubernet,
Check the status of a specific virtual machine | Check the status of a specific virtual machine in Kubernet,
Test pod deployment in OpenShift | Test pod deployment in OpenShift in Kubernet,
Update the AirGap action in IM (Incident Management) | Update the AirGap action in IM (Incident Management) in Switches,
Enable a port in IM (Incident Management) | Enable a port in IM (Incident Management) in Switches,
Verify if a port is enabled in IM (Incident Management) | Verify if a port is enabled in IM (Incident Management) in Switches,
Verify if a port is disabled in IM (Incident Management) | Verify if a port is disabled in IM (Incident Management) in Switches,
Disable a port in IM (Incident Management) | Disable a port in IM (Incident Management) in Switches,
Add a route in Nexus | Add a route in Nexus in Switches,
Unmount the volume,
Mount the volume,
Verify the status of LUNs (Logical Unit Numbers) | Verify the status of LUNs (Logical Unit Numbers) in NetApp,
Restrict the SnapMirror volume | Restrict the SnapMirror volume in NetApp,
Bring the SnapMirror volume online | Bring the SnapMirror volume online in NetApp,
Update the SnapMirror configuration | Update the SnapMirror configuration in NetApp,
Resync the SnapMirror volume | Resync the SnapMirror volume in NetApp,
Check the volume status of SnapMirror | Check the volume status of SnapMirror in NetApp,
Break the SnapMirror replication | Break the SnapMirror replication in NetApp,
Initialize the SnapMirror | Initialize the SnapMirror in NetApp,
Verify the status of SnapMirror replication | Verify the status of SnapMirror replication in NetApp,
Monitor SnapMirror replication for NetApp | Monitor SnapMirror replication for NetApp in NetApp,
Synchronize replication and verify | Synchronize replication and verify in EMC,
Synchronize replication | Synchronize replication in EMC,
Delete the Mtree (multi-volume tree) | Delete the Mtree (multi-volume tree) in EMC,
Create an Mtree replication pair | Create an Mtree replication pair in EMC,
Create an Mtree | Create an Mtree in EMC,
Check Mtree replication pair status | Check Mtree replication pair status in EMC,
Verify the protect status of the site | Verify the protect status of the site in EMC,
Unprotect the site | Unprotect the site in EMC,
Switch the site | Switch the site in EMC,
Check the split status | Check the split status in EMC,
Protect the site | Protect the site in EMC,
Isolate the site | Isolate the site in EMC,
Halt the consistency group (CG) | Halt the consistency group (CG) in EMC,
Enable the Star configuration | Enable the Star configuration in EMC,
Disconnect the site | Disconnect the site in EMC,
Disable the Star configuration | Disable the Star configuration in EMC,
Connect the site | Connect the site in EMC,
Perform PPDM backup | Perform PPDM backup in EMC,
Perform cross vCenter VM migration | Perform cross vCenter VM migration in EMC,
Check if the vCenter exists on the PPDM server asset source | Check if the vCenter exists on the PPDM server asset source in EMC,
Restore VM to an alternate location (multiple new locations) | Restore VM to an alternate location (multiple new locations) in EMC,
Perform VM instant access restore | Perform VM instant access restore in EMC,
Restore VM to an alternate location (multiple new locations) | Restore VM to an alternate location (multiple new locations) in EMC,
Restore VM to an alternate location (multiple) | Restore VM to an alternate location (multiple) in EMC,
Restore VM to an alternate location (proxy) | Restore VM to an alternate location (proxy) in EMC,
Restore VM to an alternate location (new) | Restore VM to an alternate location (new) in EMC,
Restore VM to an alternate location | Restore VM to an alternate location in EMC,
Restore virtual machine to a new location | Restore virtual machine to a new location in EMC,
Restore virtual machine to the original location | Restore virtual machine to the original location in EMC,
Replicate all backups for a specific client | Replicate all backups for a specific client in EMC,
Replicate all backups for a specific domain | Replicate all backups for a specific domain in EMC,
Restore virtual machine from history to a new location | Restore virtual machine from history to a new location in EMC,
Restore virtual machine from history to the original location | Restore virtual machine from history to the original location in EMC,
Check if the domain exists after replication | Check if the domain exists after replication in EMC,
Replicate backup | Replicate backup in EMC,
Check if the client exists | Check if the client exists in EMC,
Check if the client exists after replication | Check if the client exists after replication in EMC,
Check if the latest backup image exists for the virtual machine | Check if the latest backup image exists for the virtual machine in EMC,
Check the virtual machine if the latest backup image exists after replica | Check the virtual machine if the latest backup image exists after replica in EMC,
Check if the VM exists and protect it (new) | Check if the VM exists and protect it (new) in EMC,
Check if the VM exists and protect it | Check if the VM exists and protect it in EMC,
Check the replication activity status | Check the replication activity status in EMC,
Check if the latest backup image exists of the target VM for restore operation | Check if the latest backup image exists of the target VM for restore operation in EMC,
Check if the target VM exists for restore | Check if the target VM exists for restore in EMC,
Check if the target vCenter name exists for restore | Check if the target vCenter name exists for restore in EMC,
Check if the target exists for restore operation | Check if the target exists for restore operation in EMC,
Check if the domain name exists | Check if the domain name exists in EMC,
Add vCenter to the PPDM asset source | Add vCenter to the PPDM asset source in EMC,
Add credentials to PPDM type vCenter | Add credentials to PPDM type vCenter in EMC,
Perform VM instant access restore (multiple) | Perform VM instant access restore (multiple) in EMC,
Execute sync copy | Execute sync copy in EMC,
Execute secure copy analyze | Execute secure copy analyze in EMC,
Execute secure copy | Execute secure copy in EMC,
Execute recovery check | Execute recovery check in EMC,
Execute policy application recovery | Execute policy application recovery in EMC,
Execute copy analyze | Execute copy analyze in EMC,
Check if the latest copy is available for a particular policy | Check if the latest copy is available for a particular policy in EMC,
Check the last analysis status | Check the last analysis status in EMC,
Check the recovery status of the latest selected check | Check the recovery status of the latest selected check in EMC,
Perform EMC switchover for the device group (DG) | Perform EMC switchover for the device group (DG) in EMC,
Perform EMC switchback for the device group (DG) | Perform EMC switchback for the device group (DG) in EMC,
Swap EMC personality | Swap EMC personality in EMC,
Split EMC device group | Split EMC device group in EMC,
Set EMC device group to async mode | Set EMC device group to async mode in EMC,
Set EMC device group to ACP device mode | Set EMC device group to ACP device mode in EMC,
Resume EMC device group | Resume EMC device group in EMC,
Check if EMC device group is split | Check if EMC device group is split in EMC,
Check if EMC device group is recent | Check if EMC device group is recent in EMC,
Check if EMC device group is consistent | Check if EMC device group is consistent in EMC,
Perform EMC failover for device group | Perform EMC failover for device group in EMC,
Establish EMC device group | Establish EMC device group in EMC,
Enable EMC device group | Enable EMC device group in EMC,
Disable EMC device group | Disable EMC device group in EMC,
Check EMC device group tracks zero | Check EMC device group tracks zero in EMC,
Disable group image access | Disable group image access in EMC,
Perform failover protection | Perform failover protection in EMC,
Enable group image access | Enable group image access in EMC,
Set failover protection group | Set failover protection group in EMC,
Verify logged access | Verify logged access in EMC,
Verify group statistics | Verify group statistics in EMC,
Verify group state | Verify group state in EMC,
Verify direct access | Verify direct access in EMC,
Verify data transfer | Verify data transfer in EMC,
Start group data transfer | Start group data transfer in EMC,
Set production copy | Set production copy in EMC,
Pause group data transfer | Pause group data transfer in EMC,
Check if the group is synchronized | Check if the group is synchronized in EMC,
Allow writes to the sync policy. | Allow writes to the sync policy. in EMC,
Run sync job | Run sync job in EMC,
Modify sync policy schedule | Modify sync policy schedule in EMC,
Failover sync policy | Failover sync policy in EMC,
Monitor disk global mirror | Monitor disk global mirror in IBM,
Update the target storage group | Update the target storage group in IBM,
Perform switch over | Perform switch over in IBM,
Perform data synchronization | Perform data synchronization in IBM,
Pause global mirror | Pause global mirror in IBM,
Check if CG (consistency group) is formed | Check if CG (consistency group) is formed in IBM,
Perform switch back | Perform switch back in IBM,
Resume global mirror | Resume global mirror in IBM,
Execute storage command | Execute storage command in IBM,
Execute DS wait for no flash copy | Execute DS wait for no flash copy in IBM,
Execute DS pause GMIR | Execute DS pause GMIR in IBM,
Execute DS make flash | Execute DS make flash in IBM,
Execute DS make session | Execute DS make session in IBM,
Execute DS make GMIR | Execute DS make GMIR in IBM,
Execute DS make flash | Execute DS make flash in IBM,
Execute DS failover PPRC | Execute DS failover PPRC in IBM,
Execute DS failback PPRC | Execute DS failback PPRC in IBM,
Execute DS commit flash | Execute DS commit flash in IBM,
Execute DS check no flash copy | Execute DS check no flash copy in IBM,
Execute DS check flash zero tracks | Execute DS check flash zero tracks in IBM,
Execute DS change session remove | Execute DS change session remove in IBM,
Execute DS change session add | Execute DS change session add in IBM,
Execute DS change volume group remove | Execute DS change volume group remove in IBM,
Execute DS change volume group add | Execute DS change volume group add in IBM,
Execute DS command | Execute DS command in IBM,
Execute check DS command | Execute check DS command in IBM,
Execute DS wait for flash tracks zero | Execute DS wait for flash tracks zero in IBM,
Execute DS set flash revertible | Execute DS set flash revertible in IBM,
Execute DS revert flash | Execute DS revert flash in IBM,
Execute DS reverse flash | Execute DS reverse flash in IBM,
Execute DS resync flash | Execute DS resync flash in IBM,
Execute DS remove GMIR | Execute DS remove GMIR in IBM,
Execute DS remove flash | Execute DS remove flash in IBM,
Execute DS pause PPRC | Execute DS pause PPRC in IBM,
Check storage mailbox path | Check storage mailbox path in IBM,
Check DS tracks for LSPPRC | Check DS tracks for LSPPRC in IBM,
Execute DS resume PPRC | Execute DS resume PPRC in IBM,
Execute DS resume GMIR | Execute DS resume GMIR in IBM,
Perform Hitachi pair swap | Perform Hitachi pair swap in HDS,
Display Hitachi pair for DR | Display Hitachi pair for DR in HDS,
Display Hitachi pair for PR | Display Hitachi pair for PR in HDS,
Swap application node replication | Swap application node replication in HDS,
Resume application node replication | Resume application node replication in HDS,
Pause application node replication | Pause application node replication in HDS,
Check the replication paused state | Check the replication paused state in HDS,
Check the replication state OK | Check the replication state OK in HDS,
Check the replication swapped state | Check the replication swapped state in HDS,
HUR monitor | HUR monitor in HDS,
Hitachi pair split | Hitachi pair split in HDS,
Hitachi pair resync | Hitachi pair resync in HDS,
Check if Hitachi volume is suspended | Check if Hitachi volume is suspended in HDS,
Perform Hitachi HORCC takeover | Perform Hitachi HORCC takeover in HDS,
Monitor HP3PAR storage | Monitor HP3PAR storage in HP3PAR,
Stop RCopy group | Stop RCopy group in HP3PAR,
Set RCopy group for restore | Set RCopy group for restore in HP3PAR,
Set RCopy group for failover | Set RCopy group for failover in HP3PAR,
Execute sync for RCopy | Execute sync for RCopy in HP3PAR,
Check RCopy group sync status | Check RCopy group sync status in HP3PAR,
Synchronize replication for Huawei | Synchronize replication for Huawei in HUAWEI,
Swap replica role | Swap replica role in HUAWEI,
Split replication | Split replication in HUAWEI,
Enable secondary resource access for read-write | Enable secondary resource access for read-write in HUAWEI,
Enable secondary resource access for read-only | Enable secondary resource access for read-only in HUAWEI,
Check secondary resource access for read-write | Check secondary resource access for read-write in HUAWEI,
Check secondary resource access for read-only | Check secondary resource access for read-only in HUAWEI,
Check replication running status | Check replication running status in HUAWEI,
Check LUN split state | Check LUN split state in HUAWEI,
Check consistency group time value | Check consistency group time value in HUAWEI,
Check if consistency group synchronization begins | Check if consistency group synchronization begins in HUAWEI,
Check consistency group replica role | Check consistency group replica role in HUAWEI,
Change consistency group time value | Change consistency group time value in HUAWEI,
Promote pod | Promote pod in Pure Storage,
Demote pod | Demote pod in Pure Storage,
Check replication data lag | Check replication data lag in Pure Storage,
Check pod replication direction | Check pod replication direction in Pure Storage,
Check pod promotion status | Check pod promotion status in Pure Storage,
Check pod replication status | Check pod replication status in Pure Storage,
Unmount file system | Unmount file system in VxVm,
Stop VxVMDG | Stop VxVMDG in VxVm,
Start VxVMDG | Start VxVMDG in VxVm,
Mount file system | Mount file system in VxVm,
Check if VxVMDG volume is enabled | Check if VxVMDG volume is enabled in VxVm,
Check if VxVMDG volume is disabled | Check if VxVMDG volume is disabled in VxVm,
Check if file system is unmounted | Check if file system is unmounted in VxVm,
Check if file system is mounted | Check if file system is mounted in VxVm,
Import VxVMDG | Import VxVMDG in VxVm,
Deport VxVMDG | Deport VxVMDG in VxVm,
Check if all volumes in the DG are enabled | Check if all volumes in the DG are enabled in VxVm,
Check if all volumes in the DG are disabled | Check if all volumes in the DG are disabled in VxVm,
Rename folder | Rename folder in FileHandling,
Rename file | Rename file in FileHandling,
Copy folder | Copy folder in FileHandling,
Copy file | Copy file in FileHandling,
Stop the service | Stop the service in eBDR,
Start command for the service | Start command for the service in eBDR,
Resume command for the service | Resume command for the service in eBDR,
Pause command for the service | Pause command for the service in eBDR,
Cancel command for the service | Cancel command for the service in eBDR,
Stop GoldenGate (golden gate) group | Stop GoldenGate (golden gate) group in GoldenGate,
Start GoldenGate (golden gate) group | Start GoldenGate (golden gate) group in GoldenGate,
Check if GoldenGate (golden gate) group is running | Check if GoldenGate (golden gate) group is running in GoldenGate,
Check GoldenGate (golden gate) group RBASync | Check GoldenGate (golden gate) group RBASync in GoldenGate,
GoldenGate (golden gate) replication | GoldenGate (golden gate) replication in GoldenGate,
Stop Rsync replication | Stop Rsync replication in RSync,
Execute Rsync job | Execute Rsync job in RSync,
Verify sync status | Verify sync status in RSync,
Verify connectivity | Verify connectivity in RSync,
Rsync replication | Rsync replication in RSync,
Monitor Rsync application | Monitor Rsync application in RSync,
Rsync application replication | Rsync application replication in RSync,
Monitor Robocopy | Monitor Robocopy in RoboCopy,
Robocopy replication | Robocopy replication in RoboCopy,
Execute failover | Execute failover in DoubleTake,
Verify failover | Verify failover in DoubleTake,
Check status of execute start | Check status of execute start in DoubleTake,
Verify job start | Verify job start in DoubleTake,
Execute failback | Execute failback in DoubleTake,
Verify failback | Verify failback in DoubleTake,
Execute restore job | Execute restore job in DoubleTake,
Verify job restore status | Verify job restore status in DoubleTake,
Verify job status | Verify job status in DoubleTake,
Verify job reverse | Verify job reverse in DoubleTake,
Execute reverse | Execute reverse in DoubleTake,
Verify DT replication status | Verify DT replication status in DoubleTake,
Verify DT replication queue | Verify DT replication queue in DoubleTake,
Verify DT job name | Verify DT job name in DoubleTake,
Stop DT job | Stop DT job in DoubleTake,
Start DT job | Start DT job in DoubleTake,
DT job restore | DT job restore in DoubleTake,
DT job perform failover | DT job perform failover in DoubleTake,
DT job perform failback | DT job perform failback in DoubleTake,
Check if DT job is not in error | Check if DT job is not in error in DoubleTake,
Check DT job mirror state | Check DT job mirror state in DoubleTake,
Check DT job mirror permillage | Check DT job mirror permillage in DoubleTake,
Check DT job mirror bytes remaining | Check DT job mirror bytes remaining in DoubleTake,
Check if DT job is in error | Check if DT job is in error in DoubleTake,
Check DT job high-level state | Check DT job high-level state in DoubleTake,
Check DT job health | Check DT job health in DoubleTake,
Check DT job disk queue bytes | Check DT job disk queue bytes in DoubleTake,
Check if DT job can stop | Check if DT job can stop in DoubleTake,
Check if DT job can start | Check if DT job can start in DoubleTake,
Check if DT job can restore | Check if DT job can restore in DoubleTake,
Check if DT job can failover | Check if DT job can failover in DoubleTake,
Check if DT job can failback | Check if DT job can failback in DoubleTake,
Check status of execute reverse | Check status of execute reverse in DoubleTake,
Execute VCS resource online | Execute VCS resource online in Cluster,
Execute VCS resource offline | Execute VCS resource offline in Cluster,
Check VCS resource state | Check VCS resource state in Cluster,
Check VVR service group state of cluster node | Check VVR service group state of cluster node in Cluster,
Check if VVR service group node is offline | Check if VVR service group node is offline in Cluster,
Check if VVR service group cluster node is online | Check if VVR service group cluster node is online in Cluster,
Check if VVR SG string value exists in file | Check if VVR SG string value exists in file in Cluster,
Execute VVR SG batch file | Execute VVR SG batch file in Cluster,
Execute VVR SG online on any one server group | Execute VVR SG online on any one server group in Cluster,
Execute VVR SG offline on any one server group | Execute VVR SG offline on any one server group in Cluster,
Check status of VVR SG service group cluster name | Check status of VVR SG service group cluster name in Cluster,
Check VVR link state up-to-date status | Check VVR link state up-to-date status in Cluster,
Check VVR link state in replication status | Check VVR link state in replication status in Cluster,
Switch VVR node state in cluster | Switch VVR node state in cluster in Cluster,
Get VVR service group state on specified node. | Get VVR service group state on specified node. in Cluster,
Verify the test replication health passed statistics except for database availability | Verify the test replication health passed statistics except for database availability in MsExchangeServer,
Verify that the server components are in an active state for all components | Verify that the server components are in an active state for all components in MsExchangeServer,
Verify the replay queue status for all mailbox databases | Verify the replay queue status for all mailbox databases in MsExchangeServer,
Verify the state of the primary active manager (PAM) at the primary site | Verify the state of the primary active manager (PAM) at the primary site in MsExchangeServer,
Verify that mailbox databases are mounted at the primary site level | Verify that mailbox databases are mounted at the primary site level in MsExchangeServer,
Verify that mailbox databases are mounted at the primary server level | Verify that mailbox databases are mounted at the primary server level in MsExchangeServer,
Verify the health status of mailbox databases at the disaster recovery site level | Verify the health status of mailbox databases at the disaster recovery site level in MsExchangeServer,
Verify the health status of mailbox databases at the disaster recovery server level | Verify the health status of mailbox databases at the disaster recovery server level in MsExchangeServer,
Verify that the database copy activation policy settings are correctly configured for mailbox servers | Verify that the database copy activation policy settings are correctly configured for mailbox servers in MsExchangeServer,
Verify that the content index state is healthy for all mailbox databases | Verify that the content index state is healthy for all mailbox databases in MsExchangeServer,
Stop the Database Availability Group (DAG) at the primary site without configuration changes | Stop the Database Availability Group (DAG) at the primary site without configuration changes in MsExchangeServer,
Stop the DAG at the primary site with configuration changes | Stop the DAG at the primary site with configuration changes in MsExchangeServer,
Start the DAG service | Start the DAG service in MsExchangeServer,
Set the database copy auto-activation policy | Set the database copy auto-activation policy in MsExchangeServer,
Enable the send and receive connector | Enable the send and receive connector in MsExchangeServer,
Restore the DAG to its original state | Restore the DAG to its original state in MsExchangeServer,
Redirect the mailbox server message queue to another server | Redirect the mailbox server message queue to another server in MsExchangeServer,
Move the primary active manager to a target server | Move the primary active manager to a target server in MsExchangeServer,
Move a database without skipping any steps | Move a database without skipping any steps in MsExchangeServer,
Move a database with specific skip options | Move a database with specific skip options in MsExchangeServer,
Move the active mailbox database with skip options | Move the active mailbox database with skip options in MsExchangeServer,
Mount the database | Mount the database in MsExchangeServer,
Verify that the DAG witness server and path are properly configured | Verify that the DAG witness server and path are properly configured in MsExchangeServer,
Verify the alternate witness server and directory for the DAG | Verify the alternate witness server and directory for the DAG in MsExchangeServer,
Set the witness server and directory | Set the witness server and directory in MsExchangeServer,
Set the alternate witness server and directory for the DAG | Set the alternate witness server and directory for the DAG in MsExchangeServer,
Enable the send connector | Enable the send connector in MsExchangeServer,
Enable mailbox database circular logging | Enable mailbox database circular logging in MsExchangeServer,
Unmount the database | Unmount the database in MsExchangeServer,
Disable the send connector | Disable the send connector in MsExchangeServer,
Disable mailbox database circular logging | Disable mailbox database circular logging in MsExchangeServer,
Check the transport server queue status | Check the transport server queue status in MsExchangeServer,
Check the site-level mailbox server status under the DAG | Check the site-level mailbox server status under the DAG in MsExchangeServer,
Check the full or incremental backup status of mailbox databases | Check the full or incremental backup status of mailbox databases in MsExchangeServer,
Check the mailbox server message queue count | Check the mailbox server message queue count in MsExchangeServer,
Check the mailbox database copy status | Check the mailbox database copy status in MsExchangeServer,
Check the database copy auto-activation policy | Check the database copy auto-activation policy in MsExchangeServer,
Check the mounted status of all mailbox databases | Check the mounted status of all mailbox databases in MsExchangeServer,
Verify the passive mailbox replay queue length | Verify the passive mailbox replay queue length in MsExchangeServer,
Verify the status of the passive mailbox database copy | Verify the status of the passive mailbox database copy in MsExchangeServer,
Verify the error message for the passive mailbox database copy | Verify the error message for the passive mailbox database copy in MsExchangeServer,
Verify the content of the passive mailbox database copy | Verify the content of the passive mailbox database copy in MsExchangeServer,
Verify the queue length for the passive mailbox database copy | Verify the queue length for the passive mailbox database copy in MsExchangeServer,
Verify that the DAG status is online | Verify that the DAG status is online in MsExchangeServer,
Verify the status of the active mailbox database copy | Verify the status of the active mailbox database copy in MsExchangeServer,
Verify the error message for the active mailbox database copy | Verify the error message for the active mailbox database copy in MsExchangeServer,
Verify the content of the active mailbox database copy | Verify the content of the active mailbox database copy in MsExchangeServer,
Verify the "suspend when ready to complete" mode status | Verify the "suspend when ready to complete" mode status in MsExchangeServer,
Suspend the process with "ready to complete" mode | Suspend the process with "ready to complete" mode in MsExchangeServer,
Stop mailbox servers | Stop mailbox servers in MsExchangeServer,
Start mailbox servers | Start mailbox servers in MsExchangeServer,
Resume a move mailbox request | Resume a move mailbox request in MsExchangeServer,
Remove a user mailbox move request if it exists | Remove a user mailbox move request if it exists in MsExchangeServer,
Move the active mailbox database | Move the active mailbox database in MsExchangeServer,
Get the move request status | Get the move request status in MsExchangeServer,
Get the move request statistics | Get the move request statistics in MsExchangeServer,
Check if a user move request can be created | Check if a user move request can be created in MsExchangeServer,
Check if the user mailbox exists | Check if the user mailbox exists in MsExchangeServer,
Set the SCR prerequisite | Set the SCR prerequisite in MsExchangeServer,
Check SCR status and copy queue length | Check SCR status and copy queue length in MsExchangeServer,
Resume SCR | Resume SCR in MsExchangeServer,
Move the mailbox configuration | Move the mailbox configuration in MsExchangeServer,
Mount the database | Mount the database in MsExchangeServer,
Get the mailbox list count before switching | Get the mailbox list count before switching in MsExchangeServer,
Get the mailbox list count | Get the mailbox list count in MsExchangeServer,
Enable SCR | Enable SCR in MsExchangeServer,
Unmount the mailbox database | Unmount the mailbox database in MsExchangeServer,
Disable SCR and restore logs | Disable SCR and restore logs in MsExchangeServer,
Create a storage group | Create a storage group in MsExchangeServer,
Compare the new storage group mailbox path | Compare the new storage group mailbox path in MsExchangeServer,
Check the target database status | Check the target database status in MsExchangeServer,
Check the target database file status | Check the target database file status in MsExchangeServer,
Allow file restore to the mailbox database | Allow file restore to the mailbox database in MsExchangeServer,
Verify the DAG parameter values | Verify the DAG parameter values in MsExchangeServer,
Stop the DAG on the mailbox server | Stop the DAG on the mailbox server in MsExchangeServer,
Stop the DAG active directory site configuration only | Stop the DAG active directory site configuration only in MsExchangeServer,
Start the DAG on the mailbox server | Start the DAG on the mailbox server in MsExchangeServer,
Set the preferred active directory server | Set the preferred active directory server in MsExchangeServer,
Set the database availability group | Set the database availability group in MsExchangeServer,
Restore the DAG active directory site | Restore the DAG active directory site in MsExchangeServer,
Move the cluster group | Move the cluster group in MsExchangeServer,
Move the active mailbox database | Move the active mailbox database in MsExchangeServer,
Check the mailbox database status parameter values | Check the mailbox database status parameter values in MsExchangeServer,
Verify the replication service on PAM | Verify the replication service on PAM in MsExchangeServer,
Verify mailbox server entries | Verify mailbox server entries in MsExchangeServer,
Stop the DAG on a mailbox server | Stop the DAG on a mailbox server in MsExchangeServer,
Stop the database availability group | Stop the database availability group in MsExchangeServer,
Set the public folder database | Set the public folder database in MsExchangeServer,
Set the database copy auto-activation policy to blocked | Set the database copy auto-activation policy to blocked in MsExchangeServer,
Set the database copy auto-activation policy | Set the database copy auto-activation policy in MsExchangeServer,
Restore the database availability group | Restore the database availability group in MsExchangeServer,
Move the mailbox database to the disaster recovery server | Move the mailbox database to the disaster recovery server in MsExchangeServer,
Move the default offline address book | Move the default offline address book in MsExchangeServer,
Execute Power shell (powershell) command in Ms exchange server or Execute power shell command or Run shell command in ms server | Execute Power shell (powershell) command in Ms exchange server or Execute power shell command or Run shell command in ms server in MsExchangeServer,
Verify the started mailbox server for Exchange DAG | Verify the started mailbox server for Exchange DAG in MsExchangeServer,
Verify the current site name associated with the mailbox server | Verify the current site name associated with the mailbox server in MsExchangeServer,
Verify and stop the cluster service for Exchange DAG | Verify and stop the cluster service for Exchange DAG in MsExchangeServer,
Stop the DAG at the primary mailbox server | Stop the DAG at the primary mailbox server in MsExchangeServer,
Start the DAG at the primary mailbox server | Start the DAG at the primary mailbox server in MsExchangeServer,
Set the DAG to seed all changes | Set the DAG to seed all changes in MsExchangeServer,
Resume the mailbox database copy | Resume the mailbox database copy in MsExchangeServer,
Mount the mailbox database on the active primary mailbox server | Mount the mailbox database on the active primary mailbox server in MsExchangeServer,
Verify the replication health on the Exchange mailbox server | Verify the replication health on the Exchange mailbox server in MsExchangeServer,
Check the primary and alternate file share witness in use | Check the primary and alternate file share witness in use in MsExchangeServer,
Check the replication health status | Check the replication health status in MsExchangeServer,
Check the status of mailbox server role services | Check the status of mailbox server role services in MsExchangeServer,
Check the details of healthy mailbox databases | Check the details of healthy mailbox databases in MsExchangeServer,
Check the mounted status of the database | Check the mounted status of the database in MsExchangeServer,
Check the Database Availability Group (DAG) membership status | Check the Database Availability Group (DAG) membership status in MsExchangeServer,
Stop the IIS website | Stop the IIS website in IIS,
Stop the application pool | Stop the application pool in IIS,
Start the IIS website | Start the IIS website in IIS,
Start the application pool | Start the application pool in IIS,
Access the HTTP URL | Access the HTTP URL in HTTP,
Access and check the HTTP URL | Access and check the HTTP URL in HTTP,
Execute and check the REST API command | Execute and check the REST API command in HTTP,
No replication action | No replication action in CP,
Mark the system as disaster recovery ready | Mark the system as disaster recovery ready in CP,
Determine the disaster recovery operation type | Determine the disaster recovery operation type in WorkFlow,
Wait for the workflow action to complete | Wait for the workflow action to complete in WorkFlow,
Wait for the parallel action to complete | Wait for the parallel action to complete in WorkFlow,
Display an alert | Display an alert in WorkFlow,
Stop the workflow for recovery time objective (RTO) | Stop the workflow for recovery time objective (RTO) in WorkFlow,
Start the workflow for RTO | Start the workflow for RTO in WorkFlow,
Execute and analyze a secure copy for cyber recovery | Execute and analyze a secure copy for cyber recovery in DELL,
Execute a secure copy for cyber recovery | Execute a secure copy for cyber recovery in DELL,
Execute a recovery application process using PPDM | Execute a recovery application process using PPDM in DELL,
Check if the latest copy is available for a particular policy | Check if the latest copy is available for a particular policy in DELL,
Check the status of the cyber recovery process | Check the status of the cyber recovery process in DELL,
Execute a secure copy analysis for cyber recovery | Execute a secure copy analysis for cyber recovery in DELL,
Execute a protected single VM process using PPDM | Execute a protected single VM process using PPDM in DELL,
Restore a VM to an alternate location using PPDM Version 1 | Restore a VM to an alternate location using PPDM Version 1 in DELL,
Check if an MSSQL database backup copy exists (Version 1.2) | Check if an MSSQL database backup copy exists (Version 1.2) in DELL,
Check if a VM is protected (Version 4) using PPDM | Check if a VM is protected (Version 4) using PPDM in DELL,
Execute a backup process for multiple unprotected VMs using PPDM | Execute a backup process for multiple unprotected VMs using PPDM in DELL,
Execute a backup process for a single unprotected VM using PPDM | Execute a backup process for a single unprotected VM using PPDM in DELL,
Restore a VM to an alternate location with the latest backup copy using PPDM | Restore a VM to an alternate location with the latest backup copy using PPDM in DELL,
Restore multiple MSSQL databases to an alternate location with the default version (Version 1.1) | Restore multiple MSSQL databases to an alternate location with the default version (Version 1.1) in DELL,
Restore multiple databases for Dell EMC PPDM MSSQL (Version 1.1) | Restore multiple databases for Dell EMC PPDM MSSQL (Version 1.1) in DELL,
Execute a protected multiple VM process using PPDM | Execute a protected multiple VM process using PPDM in DELL,
Remove the probe health from the load balancer | Remove the probe health from the load balancer in AzureLoadBalancer,
Remove a load balancer rule from the load balancer | Remove a load balancer rule from the load balancer in AzureLoadBalancer,
Remove an inbound NAT rule from the load balancer | Remove an inbound NAT rule from the load balancer in AzureLoadBalancer,
Remove the load balancer from a virtual machine | Remove the load balancer from a virtual machine in AzureLoadBalancer,
Add probe health to the load balancer | Add probe health to the load balancer in AzureLoadBalancer,
Add a load balancer to a virtual machine | Add a load balancer to a virtual machine in AzureLoadBalancer,
Add a rule to the load balancer | Add a rule to the load balancer in AzureLoadBalancer,
Add an inbound NAT rule to the load balancer | Add an inbound NAT rule to the load balancer in AzureLoadBalancer,
Execute reverse replication from Azure to on-premises | Execute reverse replication from Azure to on-premises in AzureToHyperV,
Execute reprotection from on-premises to Azure | Execute reprotection from on-premises to Azure in AzureToHyperV,
Execute reprotection from VMware on-premises to Azure | Execute reprotection from VMware on-premises to Azure in AzureToHyperV,
Execute reprotection from Azure to on-premises | Execute reprotection from Azure to on-premises in AzureToHyperV,
Execute a planned failover and commit from on-premises to Azure | Execute a planned failover and commit from on-premises to Azure in AzureToHyperV,
Execute a planned failover from Azure to on-premises | Execute a planned failover from Azure to on-premises in AzureToHyperV,
Execute a commit failover | Execute a commit failover in AzureToHyperV,
Check the replication state of protected VMs | Check the replication state of protected VMs in AzureToHyperV,
Check the allowed operations from Azure to on-premises | Check the allowed operations from Azure to on-premises in AzureToHyperV,
Power on a TCL cloud VM instance | Power on a TCL cloud VM instance in TCLCloud,
Power off a TCL cloud VM instance | Power off a TCL cloud VM instance in TCLCloud,
Check the status of a TCL cloud VM instance | Check the status of a TCL cloud VM instance in TCLCloud,
Start a wave in the Rackware RMM server | Start a wave in the Rackware RMM server in Rackware,
Execute a wave failover in Rackware | Execute a wave failover in Rackware in Rackware,
Execute a wave failback in Rackware | Execute a wave failback in Rackware in Rackware,
Check the wave status in Rackware | Check the wave status in Rackware in Rackware,
Check the disaster recovery policy status in Rackware | Check the disaster recovery policy status in Rackware in Rackware,
Perform a host sync when the target exists without waiting | Perform a host sync when the target exists without waiting in Rackware,
Perform a host sync when the target exists with waiting | Perform a host sync when the target exists with waiting in Rackware,
Create a wave with host auto-provisioning in OCI using parameters | Create a wave with host auto-provisioning in OCI using parameters in Rackware,
Create a host sync with an auto-target and static IP without waiting | Create a host sync with an auto-target and static IP without waiting in Rackware,
Create a host sync with an auto-target and static IP with waiting | Create a host sync with an auto-target and static IP with waiting in Rackware,
Create a host sync with an auto-target and dynamic IP without waiting | Create a host sync with an auto-target and dynamic IP without waiting in Rackware,
Perform an Oracle Cloud VM instance action | Perform an Oracle Cloud VM instance action in OracleCloud,
Detach all NSGs from an instance VNIC | Detach all NSGs from an instance VNIC in OracleCloud,
Create an NSG and add a security rule | Create an NSG and add a security rule in OracleCloud,
Check if an NSG is attached to an instance VNIC | Check if an NSG is attached to an instance VNIC in OracleCloud,
Check the status of an Oracle Cloud VM instance | Check the status of an Oracle Cloud VM instance in OracleCloud,
Add a new NSG to an instance VNIC, replacing existing NSGs | Add a new NSG to an instance VNIC, replacing existing NSGs in OracleCloud,
Add a new NSG to an instance VNIC, appending to existing NSGs | Add a new NSG to an instance VNIC, appending to existing NSGs in OracleCloud,
Upgrade the memory of a virtual machine in SoftLayer | Upgrade the memory of a virtual machine in SoftLayer in SoftLayer,
Upgrade the CPU of a virtual machine in SoftLayer | Upgrade the CPU of a virtual machine in SoftLayer in SoftLayer,
Upgrade a virtual machine by IDs in SoftLayer | Upgrade a virtual machine by IDs in SoftLayer in SoftLayer,
Upgrade a virtual guest in SoftLayer | Upgrade a virtual guest in SoftLayer in SoftLayer,
Provision a virtual machine by IDs in SoftLayer | Provision a virtual machine by IDs in SoftLayer in SoftLayer,
Provision a virtual guest in SoftLayer | Provision a virtual guest in SoftLayer in SoftLayer,
Power on a virtual guest in SoftLayer | Power on a virtual guest in SoftLayer in SoftLayer,
Power off a virtual guest in SoftLayer | Power off a virtual guest in SoftLayer in SoftLayer,
Check if a virtual guest is powered on in SoftLayer | Check if a virtual guest is powered on in SoftLayer in SoftLayer,
Check if a virtual guest is powered off in SoftLayer | Check if a virtual guest is powered off in SoftLayer in SoftLayer,
Remove a routing rule | Remove a routing rule in AzureApplicationGateway,
Remove a path-based rule from a backend pool rule | Remove a path-based rule from a backend pool rule in AzureApplicationGateway,
Check a listener with its associated rule | Check a listener with its associated rule in AzureApplicationGateway,
Check if an application gateway rule path exists | Check if an application gateway rule path exists in AzureApplicationGateway,
Check the operational state of the application gateway | Check the operational state of the application gateway in AzureApplicationGateway,
Add a routing rule | Add a routing rule in AzureApplicationGateway,
Add an HTTP-type listener | Add an HTTP-type listener in AzureApplicationGateway,
Add a backend pool rule path to an application gateway | Add a backend pool rule path to an application gateway in AzureApplicationGateway,
Delete an A-type DNS record in OCI | Delete an A-type DNS record in OCI in OracleCloud_DNS,
Check if an A-type DNS record exists in OCI | Check if an A-type DNS record exists in OCI in OracleCloud_DNS,
Check if an A-type DNS record does not exist in OCI | Check if an A-type DNS record does not exist in OCI in OracleCloud_DNS,
Check the existing TTL value of an A-type DNS record in OCI | Check the existing TTL value of an A-type DNS record in OCI in OracleCloud_DNS,
Add a DNS record of type A in OCI | Add a DNS record of type A in OCI in OracleCloud_DNS,
Execute stop and start service operations on Azure MySQL | Execute stop and start service operations on Azure MySQL in Azure,
Check the start and stop status of Azure MySQL service | Check the start and stop status of Azure MySQL service in Azure,
Promote a MySQL replica server to primary | Promote a MySQL replica server to primary in Azure,
Create a MySQL replica server | Create a MySQL replica server in Azure,
Delete a standalone MySQL server | Delete a standalone MySQL server in Azure,
Delete a MySQL replication replica server | Delete a MySQL replication replica server in Azure,
Monitor Azure MySQL services | Monitor Azure MySQL services in Azure,
Delete the MySQL replication source server | Delete the MySQL replication source server in Azure,
Check the availability status of MySQL server | Check the availability status of MySQL server in Azure,
Check the role of the Azure MySQL server | Check the role of the Azure MySQL server in Azure,
Check if the Azure MySQL server exists in the region | Check if the Azure MySQL server exists in the region in Azure,
Execute stop and start service operations on Azure PostgreSQL | Execute stop and start service operations on Azure PostgreSQL in Azure,
Check the stop and start status of Azure PostgreSQL service | Check the stop and start status of Azure PostgreSQL service in Azure,
Monitor Azure PostgreSQL services | Monitor Azure PostgreSQL services in Azure,
Stop all instances in the virtual machine scale set | Stop all instances in the virtual machine scale set in Azure,
Stop a specific virtual machine scale set instance | Stop a specific virtual machine scale set instance in Azure,
Stop MySQL replication | Stop MySQL replication in Azure,
Start all instances in the virtual machine scale set | Start all instances in the virtual machine scale set in Azure,
Start a specific virtual machine scale set instance | Start a specific virtual machine scale set instance in Azure,
Remove a security rule from the NSG | Remove a security rule from the NSG in Azure,
Execute an unplanned failover | Execute an unplanned failover in Azure,
Execute an unplanned failover from on-premise to Azure | Execute an unplanned failover from on-premise to Azure in Azure,
Execute a re-protect operation | Execute a re-protect operation in Azure,
Execute a planned failover | Execute a planned failover in Azure,
Execute a planned failover to Azure | Execute a planned failover to Azure in Azure,
Execute a force failover to Azure | Execute a force failover to Azure in Azure,
Execute a failover for Azure Cosmos DB | Execute a failover for Azure Cosmos DB in Azure,
Commit an unplanned failover | Commit an unplanned failover in Azure,
Enable or disable ATM endpoint | Enable or disable ATM endpoint in Azure,
Dissociate a Network Security Group (NSG) from a VM | Dissociate a Network Security Group (NSG) from a VM in Azure,
Dissociate a Network Security Group (NSG) from a network interface | Dissociate a Network Security Group (NSG) from a network interface in Azure,
Delete the replication source server | Delete the replication source server in Azure,
Delete the replication replica server | Delete the replication replica server in Azure,
Create a replication in Azure MySQL | Create a replication in Azure MySQL in Azure,
Check the status of a virtual machine scale set | Check the status of a virtual machine scale set in Azure,
Check the status of an unplanned failover completion | Check the status of an unplanned failover completion in Azure,
Check the status of an unplanned failover commit | Check the status of an unplanned failover commit in Azure,
Check the status of a specific virtual machine scale set | Check the status of a specific virtual machine scale set in Azure,
Check the protection state of the re-protect operation | Check the protection state of the re-protect operation in Azure,
Check the re-protect operation | Check the re-protect operation in Azure,
Check the status of a public IP address | Check the status of a public IP address in Azure,
Check the status of a planned failover | Check the status of a planned failover in Azure,
Check if the NSG name exists | Check if the NSG name exists in Azure,
Check if the NSG name is associated with a VM | Check if the NSG name is associated with a VM in Azure,
Check the enable or disable ATM endpoint status | Check the enable or disable ATM endpoint status in Azure,
Check the commit status of failover | Check the commit status of failover in Azure,
Check the write location of Azure Cosmos | Check the write location of Azure Cosmos in Azure,
Check the role of Azure SQL DB | Check the role of Azure SQL DB in Azure,
Check the replication state in Azure | Check the replication state in Azure in Azure,
Check the read location of Azure Cosmos | Check the read location of Azure Cosmos in Azure,
Check Azure DB activity provisioning statistics for Cosmos | Check Azure DB activity provisioning statistics for Cosmos in Azure,
Check if the operation is allowed | Check if the operation is allowed in Azure,
Check the availability status of Azure MySQL server | Check the availability status of Azure MySQL server in Azure,
Check the role of Azure MySQL server | Check the role of Azure MySQL server in Azure,
Execute test failover for Azure | Execute test failover for Azure in Azure,
Clean up failover in Azure | Clean up failover in Azure in Azure,
Associate a public IP address | Associate a public IP address in Azure,
Associate a specific NSG to a VM network interface | Associate a specific NSG to a VM network interface in Azure,
Associate the default NSG to a VM network interface | Associate the default NSG to a VM network interface in Azure,
Associate an NSG to a network interface | Associate an NSG to a network interface in Azure,
Assign an NSG to a VM and replace the existing NSG | Assign an NSG to a VM and replace the existing NSG in Azure,
Add a security rule to NSG | Add a security rule to NSG in Azure,
Verify the status of VCD | Verify the status of VCD in Azure,
Power on a VCD | Power on a VCD in Azure,
Power off a VCD | Power off a VCD in Azure,
Stop an Azure VM | Stop an Azure VM in Azure,
Start an Azure VM | Start an Azure VM in Azure,
Check the status of an Azure VM | Check the status of an Azure VM in Azure,
Check the power state of an Azure VM | Check the power state of an Azure VM in Azure,
Change the size of an Azure VM | Change the size of an Azure VM in Azure,
Monitor Azure MSSQL pass | Monitor Azure MSSQL pass in Azure,
Execute a forced failover for Azure SQL DB | Execute a forced failover for Azure SQL DB in Azure,
Execute a planned failover for Azure SQL DB | Execute a planned failover for Azure SQL DB in Azure,
Check the replication state in Azure SQL DB | Check the replication state in Azure SQL DB in Azure,
Check the PaaS role for Azure SQL DB | Check the PaaS role for Azure SQL DB in Azure,
Check the storage replication data lag with input | Check the storage replication data lag with input in Azure,
Set storage account replication | Set storage account replication in Azure,
Execute a storage account failover | Execute a storage account failover in Azure,
Check the storage account type | Check the storage account type in Azure,
Monitor Azure storage replication | Monitor Azure storage replication in Azure,
Execute a commit failover for Azure Site Recovery (ASR) | Execute a commit failover for Azure Site Recovery (ASR) in Azure,
Execute an unplanned failover for ASR | Execute an unplanned failover for ASR in Azure,
Execute a re-protect operation for ASR | Execute a re-protect operation for ASR in Azure,
Execute a test failover cleanup for ASR | Execute a test failover cleanup for ASR in Azure,
Check the status of ASR test failover | Check the status of ASR test failover in Azure,
Execute a test failover for ASR | Execute a test failover for ASR in Azure,
Check the status of ASR test failover cleanup | Check the status of ASR test failover cleanup in Azure,
Check the status of unplanned failover | Check the status of unplanned failover in Azure,
Check the status of commit failover | Check the status of commit failover in Azure,
Check the status of ASR re-protect | Check the status of ASR re-protect in Azure,
Check the protection status of ASR VM | Check the protection status of ASR VM in Azure,
Mount a virtual machine using Rubrik | Mount a virtual machine using Rubrik in Rubrik,
Migrate a datastore after mounting | Migrate a datastore after mounting in Rubrik,
Stop an EC2 instance | Stop an EC2 instance in Amazon,
Start an EC2 instance | Start an EC2 instance in Amazon,
Modify the size of an EC2 instance | Modify the size of an EC2 instance in Amazon,
Check if an EC2 instance is up | Check if an EC2 instance is up in Amazon,
Check if an EC2 instance is down | Check if an EC2 instance is down in Amazon,
Check the state of an EC2 instance | Check the state of an EC2 instance in Amazon,
Upload a file to an S3 bucket | Upload a file to an S3 bucket in Amazon,
Upload multiple files to an S3 bucket | Upload multiple files to an S3 bucket in Amazon,
Download a file from an S3 bucket | Download a file from an S3 bucket in Amazon,
Download multiple files from an S3 bucket | Download multiple files from an S3 bucket in Amazon,
Execute CyberRecovery copy analyze operation | Execute CyberRecovery copy analyze operation in DELL,
Execute CyberRecovery secure copy operation | Execute CyberRecovery secure copy operation in DELL,
Execute recovery application for PPDM | Execute recovery application for PPDM in DELL,
Check if the latest copy is available for a particular policy in CyberRecovery | Check if the latest copy is available for a particular policy in CyberRecovery in DELL,
Check the status of CyberRecovery | Check the status of CyberRecovery in DELL,
Execute secure copy analyze operation in CyberRecovery | Execute secure copy analyze operation in CyberRecovery in DELL,
Execute recovery of a protected single VM in PPDM | Execute recovery of a protected single VM in PPDM in DELL,
Copy a VM to an alternate location in PPDM | Copy a VM to an alternate location in PPDM in DELL,
Check if an MSSQL DB backup copy exists in PPDM | Check if an MSSQL DB backup copy exists in PPDM in DELL,
Check if a VM is protected in PPDM | Check if a VM is protected in PPDM in DELL,
Execute backup for multiple unprotected VMs in PPDM | Execute backup for multiple unprotected VMs in PPDM in DELL,
Execute backup for a single unprotected VM in PPDM | Execute backup for a single unprotected VM in PPDM in DELL,
Restore a VM to an alternate location with the latest backup copy in PPDM | Restore a VM to an alternate location with the latest backup copy in PPDM in DELL,
Restore an MSSQL DB to an alternate location from multiple DBs in PPDM | Restore an MSSQL DB to an alternate location from multiple DBs in PPDM in DELL,
Restore multiple DBs from a backup in PPDM | Restore multiple DBs from a backup in PPDM in DELL,
Execute protected backup for multiple VMs in PPDM | Execute protected backup for multiple VMs in PPDM in DELL,
Execute planned failover for VMware replica in VB | Execute planned failover for VMware replica in VB in Veeam,
Undo VMware replica failover in VBR | Undo VMware replica failover in VBR in Veeam,
Undo VMware replica failback in VBR | Undo VMware replica failback in VBR in Veeam,
Execute permanent VMware replica failover in VBR | Execute permanent VMware replica failover in VBR in Veeam,
Execute disaster recovery-only VMware replica failover in VBR | Execute disaster recovery-only VMware replica failover in VBR in Veeam,
Fail back VMware replica to the original VM in VBR | Fail back VMware replica to the original VM in VBR in Veeam,
Execute failover replication job | Execute failover replication job in Veeam,
Execute failback replication job | Execute failback replication job in Veeam,
Commit failback replication job | Commit failback replication job in Veeam,
Create replication job using the production VM state | Create replication job using the production VM state in Veeam,
Create replication job from backup files | Create replication job from backup files in Veeam,
Check the last state of a replication job | Check the last state of a replication job in Veeam,
Check the last result of a replication job | Check the last result of a replication job in Veeam,
Check the status of a replication job | Check the status of a replication job in Veeam,
Check the status of replica monitoring | Check the status of replica monitoring in Veeam,
Execute eBDR operations | Execute eBDR operations in eBDRPerpetuuiti,
Update an asset | Update an asset in Oracle_Ops_Center,
Unmanage an asset | Unmanage an asset in Oracle_Ops_Center,
Remove an asset from maintenance mode | Remove an asset from maintenance mode in Oracle_Ops_Center,
Refresh an asset | Refresh an asset in Oracle_Ops_Center,
Reboot an asset | Reboot an asset in Oracle_Ops_Center,
Put an asset in maintenance mode | Put an asset in maintenance mode in Oracle_Ops_Center,
Power on an asset | Power on an asset in Oracle_Ops_Center,
Power off an asset | Power off an asset in Oracle_Ops_Center,
Manage an asset | Manage an asset in Oracle_Ops_Center,
Execute a plan | Execute a plan in Oracle_Ops_Center,
Execute an OpsCenter command | Execute an OpsCenter command in Oracle_Ops_Center,
Execute check command in OpsCenter | Execute check command in OpsCenter in Oracle_Ops_Center,
Power on a blade server in HP | Power on a blade server in HP in HP1,
Power off a blade server in HP | Power off a blade server in HP in HP1,
Check the power status of HP is on | Check the power status of HP is on in HP1,
Check the power status of HP is off | Check the power status of HP is off in HP1,
Check the power status of an asset | Check the power status of an asset in Sun ILOM,
Power on an asset | Power on an asset in Sun ILOM,
Power off an asset | Power off an asset in Sun ILOM,
Check the link status for MSSQL | Check the link status for MSSQL in Base24,
Check transaction queue in MSSQL | Check transaction queue in MSSQL in Base24,
Check the status of a node in MSSQL | Check the status of a node in MSSQL in Base24,
Check SAF count in MSSQL | Check SAF count in MSSQL in Base24,
Check the status of the DRNet connection in MSSQL | Check the status of the DRNet connection in MSSQL in Base24,
Check the records in DRNet | Check the records in DRNet in Base24,
Check the status of the distributor file map in DRNet | Check the status of the distributor file map in DRNet in Base24,
Check the mode of the collector file map in DRNet | Check the mode of the collector file map in DRNet in Base24,
Check the audit mode status in DRNet | Check the audit mode status in DRNet in Base24,
Execute Base24 command | Execute Base24 command in Base24,
Execute Base24 check command | Execute Base24 check command in Base24,
