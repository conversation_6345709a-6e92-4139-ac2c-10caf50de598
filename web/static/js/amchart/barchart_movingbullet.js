// Create root element
// https://www.amcharts.com/docs/v5/getting-started/#Root_element
var root = am5.Root.new("bar_moving_bullets");

// Set themes
// https://www.amcharts.com/docs/v5/concepts/themes/
root.setThemes([
  am5themes_Animated.new(root)
]);

var data = [
  {
      name: "Wrong Direction",
      steps: 35781,
      pictureSettings: {
          src: "img/icons/ppl_based_events/wrong_direction.svg"
      }

  },
  {
      name: "Trespassing and Tripwire",
      steps: 25464,
      pictureSettings: {
          src: "img/icons/ppl_based_events/person_tripwire.svg"
      }
  },
  {
      name: "People Passed",
      steps: 18788,
      pictureSettings: {
          src: "img/icons/ppl_based_events/people_passed.svg"
      }
  },
  {
      name: "People Converged",
      steps: 15465,
      pictureSettings: {
          src: "img/icons/ppl_based_events/people_converged.svg"
      }
  },
  {
      name: "Stopped Running",
      steps: 11561,
      pictureSettings: {
          src: "img/icons/ppl_based_events/person_stopped.svg"
      },

  }
];

// Create chart
// https://www.amcharts.com/docs/v5/charts/xy-chart/
var chart = root.container.children.push(
  am5xy.XYChart.new(root, {
      panX: false,
      panY: false,
      wheelX: "none",
      wheelY: "none",
      paddingLeft: 0,
      paddingRight: 30
  })
);

// Create axes
// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/

var yRenderer = am5xy.AxisRendererY.new(root, {});
yRenderer.grid.template.set("visible", false);

yRenderer.labels.template.setAll({
    fill: am5.color("#ffffff")
});

var yAxis = chart.yAxes.push(
  am5xy.CategoryAxis.new(root, {
      categoryField: "name",
      renderer: yRenderer,
      paddingRight: 40
  })
);



var xRenderer = am5xy.AxisRendererX.new(root, {});
xRenderer.grid.template.set("strokeDasharray", [3]);

var xAxis = chart.xAxes.push(
  am5xy.ValueAxis.new(root, {
      min: 0,
      renderer: xRenderer
  })
);

//Axis Label Color
var xRenderer = xAxis.get("renderer");
xRenderer.labels.template.setAll({
    fill: am5.color("#ffffff")
});

// Add series
// https://www.amcharts.com/docs/v5/charts/xy-chart/series/
var series = chart.series.push(
  am5xy.ColumnSeries.new(root, {
      name: "Income",
      xAxis: xAxis,
      yAxis: yAxis,
      valueXField: "steps",
      categoryYField: "name",
      sequencedInterpolation: true,
      calculateAggregates: true,
      maskBullets: false,
      tooltip: am5.Tooltip.new(root, {
          dy: -30,
          pointerOrientation: "vertical",
          labelText: "{valueX}"
      })
  })
);

series.columns.template.setAll({
    strokeOpacity: 0,
    cornerRadiusBR: 0,
    cornerRadiusTR: 0,
    cornerRadiusBL: 0,
    cornerRadiusTL: 0,
    maxHeight: 16,
    fillOpacity: 0.8
});

var currentlyHovered;

series.columns.template.events.on("pointerover", function (e) {
    handleHover(e.target.dataItem);
});

series.columns.template.events.on("pointerout", function (e) {
    handleOut();
});

function handleHover(dataItem) {
    if (dataItem && currentlyHovered != dataItem) {
        handleOut();
        currentlyHovered = dataItem;
        var bullet = dataItem.bullets[0];
        bullet.animate({
            key: "locationX",
            to: 1,
            duration: 600,
            easing: am5.ease.out(am5.ease.cubic)
        });
    }
}

function handleOut() {
    if (currentlyHovered) {
        var bullet = currentlyHovered.bullets[0];
        bullet.animate({
            key: "locationX",
            to: 0,
            duration: 600,
            easing: am5.ease.out(am5.ease.cubic)
        });
    }
}


var circleTemplate = am5.Template.new({});

series.bullets.push(function (root, series, dataItem) {
    var bulletContainer = am5.Container.new(root, {});
    var circle = bulletContainer.children.push(
      am5.Circle.new(
        root,
        {
            radius: 20
        },
        circleTemplate
      )
    );

    var maskCircle = bulletContainer.children.push(
      am5.Circle.new(root, { radius: 20 })
    );

    // only containers can be masked, so we add image to another container
    var imageContainer = bulletContainer.children.push(
      am5.Container.new(root, {
          mask: maskCircle
      })
    );

    // not working
    var image = imageContainer.children.push(
      am5.Picture.new(root, {
          templateField: "pictureSettings",
          centerX: am5.p50,
          centerY: am5.p50,
          width: 40,
          height: 40
      })
    );

    return am5.Bullet.new(root, {
        locationX: 0,
        sprite: bulletContainer
    });
});

// heatrule
series.set("heatRules", [
  {
      dataField: "valueX",
      min: am5.color("#f03d3d"),
      max: am5.color("#ffd428"),
      target: series.columns.template,
      key: "fill"
  },
  {
      dataField: "valueX",
      min: am5.color("#ffffff8c"),
      max: am5.color("#ffffff8c"),
      target: circleTemplate,
      key: "fill"
  }
]);

series.data.setAll(data);
yAxis.data.setAll(data);

var cursor = chart.set("cursor", am5xy.XYCursor.new(root, {}));
cursor.lineX.set("visible", false);
cursor.lineY.set("visible", false);

cursor.events.on("cursormoved", function () {
    var dataItem = series.get("tooltip").dataItem;
    if (dataItem) {
        handleHover(dataItem)
    }
    else {
        handleOut();
    }
})

// Make stuff animate on load
// https://www.amcharts.com/docs/v5/concepts/animations/
series.appear();
chart.appear(1000, 100);