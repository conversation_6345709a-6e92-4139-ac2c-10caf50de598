var app = angular.module("myApp", []);
app.config([
  "$interpolateProvider",
  function ($interpolateProvider) {
    $interpolateProvider.startSymbol("{a");
    $interpolateProvider.endSymbol("a}");
  },
]);

app.controller("myCtrl", function ($scope, $http, $timeout) {
  //////////////////////initialize data/////////////////////////////////////////
  $scope.objectType = [
    {value: "All", label: "All"},
    {value: "Human", label: "Human"},
    {value: "Car", label: "Car"},
    {value: "Unknown", label: "Unknown"},
    {value: "Chair", label: "Chair"},
    {value: "Face", label: "Face"},
  ];
  $scope.personEvent = [
    {value: "Tampering", label: "Tampering"},
    {value: "FaceDetection", label: "FaceDetection"},
    {value: "LoiteringDetection", label: "LoiteringDetection"},
    {value: "MovementDetection", label: "MovementDetection"},
    {value: "PersonExit", label: "PersonExit"},
    {value: "FireDetection", label: "FireDetection"},
    {value: "Trespass", label: "Trespass"},
    {value: "Tripwire", label: "Tripwire"},
    {value: "PersonWrongWayDetection", label: "PersonWrongWayDetection"},
    {value: "PersonLineCrossCount", label: "PersonLineCrossCount"},
    {value: "WrongDirection", label: "WrongDirection"},
    {value: "HelmetDetection", label: "HelmetDetection"},
    {value: "FallDetection", label: "FallDetection"}
  ];
  $scope.objectEvent = [
    {value: "LicensePlateDetection", label: "LicensePlateDetection"},
    {value: "SmokeDetection", label: "SmokeDetection"},
    {value: "LeftObjectDetection", label: "LeftObjectDetection"},
    {value: "NoParking", label: "NoParking"},
    {value: "ObjectDetection", label: "ObjectDetection"},
    {value: "MissingObjectDetection", label: "MissingObjectDetection"},
    {value: "MissingObjectDetectionArea", label: "MissingObjectDetectionArea"},
    {value: "VehicleLineCrossCount", label: "VehicleLineCrossCount"},
    {value: "VehicleTresspass", label: "VehicleTresspass"},
    {value: "VehicleTripwire", label: "VehicleTripwire"},
    {value: "VehicleWrongDirection", label: "VehicleWrongDirection"},
    {value: "SignalViolation", label: "SignalViolation"},
  ];

  $scope.ClassAttributes = [
    {
      type: "People",
      values: [
        {value: "Male", label: "Male"},
        {value: "Female", label: "Female"},
//        {value: "Child", label: "Child"},
      ],
    },
    {
      type: "Vehicle",
      values: [
        {value: "Bicycle", label: "Bicycle"},
        {value: "Motorcycle", label: "Motorcycle"},
        {value: "Car", label: "Car"},
//        {value: "Pickup", label: "Pickup"},
//        {value: "Van", label: "Van"},
        {value: "Truck", label: "Truck"},
        {value: "Bus", label: "Bus"},
        {value: "Train", label: "Train"},
        {value: "AirPlane", label: "AirPlane"},
        {value: "Boat", label: "Boat"},
      ],
    },
    {
      type: "Animals",
      values: [
        {value: "Dog", label: "Dog"},
        {value: "Cat", label: "Cat"},
        {value: "Cow", label: "Cow"},
        {value: "Bird", label: "Bird"},
        {value: "Horse", label: "Horse"},
      ],
    },
  ];

  $scope.PersonAttributes = [
    {
      type: "Cloth",
      values: [
        {value: "Short sleeve", label: "Short sleeve"},
        {value: "Long sleeve", label: "Long sleeve"},
//        {value: "Short sleeve", label: "Short sleeve outwear"},
//        {value: "Long sleeve", label: "Long sleeve outwear"},
//        {value: "Vest", label: "Vest"},
        {value: "Sling", label: "Sling"},
//        {value: "Shorts", label: "Shorts"},
        {value: "Pant", label: "Pant"},
//        {value: "Skirt", label: "Skirt"},
//        {value: "Short sleeve", label: "Short sleeve dress"},
//        {value: "Long sleeve", label: "Long sleeve dress"},
//        {value: "Vest", label: "Vest dress"},
//        {value: "Sling", label: "Sling dress"},
      ],
    },
//    {
//      type: "Upper Wear",
//      values: [
//        {value: "Long Sleeves", label: "Short Sleeves"},
//        {value: "Female", label: "Female"},
//        {value: "Colors", label: "Colors"},
//      ],
//    },
//    {
//      type: "Lower Wear",
//      values: [
//        {value: "Long", label: "Long"},
//        {value: "Short", label: "Short"},
//        {value: "Colors", label: "Colors"},
//      ],
//    },
//    {
//      type: "Hat",
//      values: [
//        {value: "Hat", label: "Hat"},
//        {value: "No Hat", label: "No Hat"},
//      ],
//    },
    {
      type: "Bag",
      values: [
        {value: "Suitcase", label: "Suitcase"},
        {value: "Backpack", label: "Backpack"},
        {value: "Handbag", label: "Handbag"},
      ],
    },
  ];

  $scope.colorNames = [
    "red",
    "orange",
    "yellow",
    "green",
    "purple",
    "blue",
    "saddlebrown",
    "gray",
    "black",
    "white",
  ];
  $scope.rule = {
    object_name: [],
    event: [],
    color: "",
    event_type: "person",
    file: "",
    camera_name: "All",
  };
  $scope.url = "";
  $scope.cameraNames = [];
  $scope.datas = [];
  $scope.loader = false;
  $scope.cameraNameError = false;
  /////////////////////////////////////////////////////////////
  fetch("Config.json")
    .then((res) => res.json())
    .then((res) => {
      $scope.loader = true;
      $scope.url = res.url;
      $scope.getCameraNames();

      $http.get($scope.url + "search_data").then((res) => {
        $scope.datas = res.data;
        // console.log("datas", res.data);
        $timeout(function () {
          $scope.loader = false;
        }, 1000);
      });
    });

  $scope.onSubmit = function () {
    if (!$scope.rule.camera_name) {
      $scope.cameraNameError = true;
      return;
    }
    $scope.cameraNameError = false;
    $scope.loader = true;
    $http.post($scope.url + "search_data", $scope.rule).then((res) => {
      $scope.datas = res.data;
      // console.log("datas", res.data);
      $scope.loader = false;
    });
  };

  $scope.entrFunc = function (keyEvent) {
    if (keyEvent.which === 13) $scope.onSubmit();
  };

  $scope.onClear = function () {
    $scope.rule = {
      object_name: [],
      event: [],
      color: "",
      event_type: "person",
      file: "",
      camera_name: "All",
      start_date: "",
      end_date: "",
      People: [],
      Vehicle: [],
      Animals: [],
      "Upper Wear": [],
      "Lower Wear": [],
      Hat: [],
      Bag: [],
      start_time: 0,
      end_time: 0,
      width_from: 0,
      width_to: 0,
      height_form: 0,
      height_to: 0,
      speed_form: 0,
      speed_to: 0,
      rotation: 0,
    };

    setDate(moment(), moment());
    cb(moment(), moment(), false);
    $("#StartTime,#EndTime").val("--:--");
    $scope.$apply();
  };
  $scope.decideClassName = function (clr) {
    if (clr !== "white") {
      return "border_dashed_white";
    } else {
      return "border_dashed_black";
    }
  };
  $scope.setColor = function (clr) {
    $scope.rule.color = clr;
  };

  $scope.panel = {};
  $scope.panelData = function (data) {
    $scope.panel = data;

    var video = $("#actionvideo");

    var videoSrc = $scope.url + "action_video";
    // video.src

    if (data.video_path == "") {
      video[0].style.display = "none";
      $scope.nodatafound = true;
    } else {
      video[0].style.display = "block";
      video[0].src = data.video_path;
      video[0].play();
      $scope.nodatafound = false;
    }
  };

  $scope.getCameraNames = function () {
    $http.post($scope.url + "camera_details").then((res) => {
      $scope.cameraNames = res.data;
      // console.log("camera names", res);
    });
  };

  $scope.toggleSelection = function (data, propName, name) {
    if ($scope.rule.event_type == name) {
      var idx = $scope.rule[propName].indexOf(data);

      if (idx > -1) {
        $scope.rule[propName].splice(idx, 1);
      } else {
        $scope.rule[propName].push(data);
      }
      // console.log($scope.rule);
    }
  };

  $scope.notConsider = function (val) {
    $scope.rule.event = [];
    $scope.rule.event_type = val;

    // console.table($scope.rule);
  };
  $scope.cameraNameFunc = function () {
    if (!$scope.rule.camera_name) $scope.cameraNameError = true;
    else $scope.cameraNameError = false;
  };
  $scope.fromCommonJs = function (val, prop) {
    $scope.rule[prop] = val;
  };

  $scope.OpenGallery = function (src) {
    $(this).lightGallery({
      dynamic: true,
      dynamicEl: [{src: src}],
    });
  };

  $scope.OpenGallerySet = function (g) {
    var src = url + "video_feed/" + g.usr_id + "/" + g.cam_name;
    $(this).lightGallery({
      dynamic: true,
      dynamicEl: [{src: src}],
    });
  };

  $scope.colorPicker = function () {
    //////eye dropper
    // document.getElementById('start-button').addEventListener('click', () =>
    // {
    //   if (!window.EyeDropper) {
    //     alert("Color Picker Not Supported")
    //     return;
    //   }
    //   const eyeDropper = new EyeDropper();
    //   const abortController = new AbortController();
    //   eyeDropper.open({ signal: abortController.signal }).then(result => {
    //     $scope.setColor(result.sRGBHex);
    //     document.getElementById('start-button').value=result.sRGBHex;
    //   }).catch(e => {
    //     resultElement.textContent = e;
    //   });
    //   setTimeout(() => {
    //     abortController.abort();
    //   }, 5000);
    // });
  };

  $scope.CheckAttributes = function (checked, rule_type, value) {
    if (value == "all") {
      if (checked) {
        $scope.rule[rule_type] = ["all"];
      } else {
        $scope.rule[rule_type] = [];
      }
    } else {
      if ($scope.rule[rule_type] && $scope.rule[rule_type][0] == "all") {
        $scope.rule[rule_type] = [];
      }
      var index = $scope.rule[rule_type].indexOf(value);
      if (checked) {
        if (index == -1) $scope.rule[rule_type].push(value);
      } else {
        if (index != -1) $scope.rule[rule_type].splice(index, 1);
      }
    }
  };

  function cb(start, end, toggle = true) {
    $scope.rule.start_date = start.format("YYYY-MM-DD");
    $scope.rule.end_date = end.format("YYYY-MM-DD");
    toggle && $scope.$digest();
  }
  function setDate(start, end) {
    $("#reportrange span").daterangepicker(
      {
        startDate: start,
        endDate: end,
        ranges: {
          Today: [moment(), moment()],
          Yesterday: [
            moment().subtract(1, "days"),
            moment().subtract(1, "days"),
          ],
          "Last 7 Days": [moment().subtract(6, "days"), moment()],
          "Last 30 Days": [moment().subtract(29, "days"), moment()],
          "This Month": [moment().startOf("month"), moment().endOf("month")],
          "Last Month": [
            moment().subtract(1, "month").startOf("month"),
            moment().subtract(1, "month").endOf("month"),
          ],
        },
      },
      cb
    );
  }

  setDate(moment(), moment());
  cb(moment(), moment(), false);
});
function commonJs(e, options) {
  let angularScope = angular.element(document.querySelector("body")).scope();
  let val = "";
  let prop = "";
  switch (options) {
    case "start_time":
      val = e.value;
      prop = "start_time";
      break;
    case "end_time":
      val = e.value;
      prop = "end_time";
      break;
    default:
      let a = new FileReader();
      a.readAsDataURL(e.files[0]);
      a.onload = () => {
        angularScope.fromCommonJs(a.result, "file");
        return;
      };
  }
  angularScope.fromCommonJs(val, prop);
}
