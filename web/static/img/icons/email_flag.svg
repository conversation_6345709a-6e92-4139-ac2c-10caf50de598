<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="184" height="184" viewBox="0 0 184 184">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_5021" data-name="Rectangle 5021" width="184" height="184" fill="none" stroke="#707070" stroke-width="1"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="0.887" y1="2.453" x2="1.173" y2="1.901" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#a5e9ff"/>
      <stop offset="1" stop-color="#f0f3f5"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.189" y1="-0.106" x2="0.884" y2="1.102" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#84d5e0"/>
      <stop offset="1" stop-color="#57abec"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-81.143" y1="16.921" x2="-80.362" y2="17.847" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-4" x1="0.421" y1="0.22" x2="0.786" y2="1.284" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#84d5e0"/>
      <stop offset="1" stop-color="#28b5c8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="-645.121" y1="0.972" x2="-647.471" y2="0.207" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="-661.335" y1="-5.867" x2="-659.396" y2="-5.867" xlink:href="#linear-gradient-5"/>
    <linearGradient id="linear-gradient-7" x1="-333.495" y1="0.5" x2="-332.495" y2="0.5" xlink:href="#linear-gradient-5"/>
    <linearGradient id="linear-gradient-8" x1="-488.127" y1="1.296" x2="-488.404" y2="0.101" xlink:href="#linear-gradient-5"/>
    <linearGradient id="linear-gradient-9" x1="-443.391" y1="1.342" x2="-443.608" y2="-0.011" xlink:href="#linear-gradient-5"/>
    <linearGradient id="linear-gradient-10" x1="0.309" y1="4.139" x2="0.608" y2="-0.96" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#9cdbf0"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="-0.78" y1="1.604" x2="1.196" y2="-0.332" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#c73f86"/>
      <stop offset="1" stop-color="#e34899"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="0.203" y1="1.848" x2="0.725" y2="-0.568" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-13" x1="0.16" y1="3.741" x2="0.646" y2="-1.168" xlink:href="#linear-gradient-5"/>
    <linearGradient id="linear-gradient-14" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#5e74cf"/>
      <stop offset="1" stop-color="#728efc"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="0.396" y1="3.084" x2="0.553" y2="-0.804" xlink:href="#linear-gradient-14"/>
    <linearGradient id="linear-gradient-16" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-5"/>
  </defs>
  <g id="email_flag" clip-path="url(#clip-path)">
    <g id="Group_12144" data-name="Group 12144" transform="translate(28.002 -44.875)">
      <g id="Group_12143" data-name="Group 12143" transform="translate(-28.002 63.106)">
        <path id="Path_25063" data-name="Path 25063" d="M4828.77,2385.627c-21.683,28.284-32.725,50.236-12.435,55,15.932,3.74,51.181-9.9,72.2-6.312,9.5,1.62,34.986,8.063,50.9-9.38,14.161-15.518,26.248-61.627-6.911-51.195-13.32,4.191-7.467-39.856-33.5-38.817C4880.577,2335.658,4847,2361.843,4828.77,2385.627Z" transform="translate(-4790.513 -2293.743)" fill="#a9e1f2" opacity="0.15"/>
        <path id="Path_25064" data-name="Path 25064" d="M4940.434,2388.105c-3.208,7.084-7.456,13.668-11.713,18.012-17.586,17.944-39.5,13.256-49.543,11.173-22.21-4.6-52.253,11.9-69.121,7.586-10.256-2.628-10.474-13.028-3.407-29.727a222.349,222.349,0,0,1,13.017-25.067c16.15-27.495,46.037-54.462,59.593-56.166,27.5-3.454,30.638,43.836,44.959,39.885C4950.169,2346.641,4949.016,2369.161,4940.434,2388.105Z" transform="translate(-4788.171 -2280.853)" opacity="0.42" fill="url(#linear-gradient)"/>
        <g id="Group_12091" data-name="Group 12091" transform="translate(0 94.564)">
          <path id="Path_25065" data-name="Path 25065" d="M4802.605,2483.675c-1.477-3.662-3.454-8.939-6.569-9.954s-3.859,1.151-3.792,2.167-1.422-.744-5.282-2.777-6.976-1.8-8.194-1.286c-2.1.886-1.557,1.76-1.15,3.386s-8.668.556-10.227,2.344,1.423,4.428,5.079,4.36,4.673.61,3.59,1.761-.609,4.6,5.62,3.115,8.8-1.23,7.652.367-2.1,3.426,2.573,3.629,10.294-1.287,11.31,0S4804.062,2487.289,4802.605,2483.675Z" transform="translate(-4766.968 -2471.469)" fill="url(#linear-gradient-2)"/>
          <g id="Group_12087" data-name="Group 12087" transform="translate(1.521 6.665)">
            <path id="Path_25066" data-name="Path 25066" d="M4805.481,2501.208c-3.081-7.45-12.317-10.419-19.522-11.6a73.349,73.349,0,0,0-15.085-.778l-.017-.22a73.55,73.55,0,0,1,15.133.779c7.263,1.187,16.574,4.186,19.7,11.73Z" transform="translate(-4770.857 -2488.514)" fill="#fff"/>
          </g>
          <g id="Group_12088" data-name="Group 12088" transform="translate(12.463 8.425)">
            <path id="Path_25067" data-name="Path 25067" d="M4799,2497.1l-.164-.148c.038-.042,3.8-4.115,8.486-3.936l-.009.221C4802.725,2493.049,4799.04,2497.064,4799,2497.1Z" transform="translate(-4798.839 -2493.015)" fill="#fff"/>
          </g>
          <g id="Group_12089" data-name="Group 12089" transform="translate(14.351 2.346)">
            <path id="Path_25068" data-name="Path 25068" d="M4811.2,2484.025c-3.851-1.294-7.494-6.378-7.53-6.43l.18-.127c.036.05,3.639,5.077,7.42,6.348Z" transform="translate(-4803.667 -2477.468)" fill="#fff"/>
          </g>
          <g id="Group_12090" data-name="Group 12090" transform="translate(28.954 5.579)">
            <path id="Path_25069" data-name="Path 25069" d="M4843.422,2493.474c-3.12-4.053-2.322-7.7-2.314-7.737l.215.05c-.008.035-.779,3.587,2.274,7.553Z" transform="translate(-4841.011 -2485.737)" fill="#fff"/>
          </g>
        </g>
        <g id="Group_12096" data-name="Group 12096" transform="translate(125.366 86.536)">
          <path id="Path_25070" data-name="Path 25070" d="M5087.629,2469.139c.365-3.932.748-9.554,3.442-11.419s4.028,0,4.254.99,1.148-1.121,4.266-4.173,6.167-3.724,7.482-3.58c2.263.249,2,1.241,2.072,2.915s8.464-1.95,10.469-.683-.094,4.65-3.617,5.633-4.3,1.922-2.935,2.715,1.9,4.237-4.492,4.595-8.788,1.343-7.228,2.543,2.992,2.681-1.427,4.214-10.23,1.715-10.835,3.239S5087.268,2473.019,5087.629,2469.139Z" transform="translate(-5087.563 -2450.939)" fill="url(#linear-gradient-3)"/>
          <g id="Group_12092" data-name="Group 12092" transform="translate(1.409 3.215)">
            <path id="Path_25071" data-name="Path 25071" d="M5091.385,2481.145l-.22-.022c.829-8.123,8.892-13.663,15.51-16.881a73.547,73.547,0,0,1,14.275-5.08l.049.216a73.328,73.328,0,0,0-14.231,5.065C5100.2,2467.636,5092.2,2473.125,5091.385,2481.145Z" transform="translate(-5091.165 -2459.162)" fill="#fff"/>
          </g>
          <g id="Group_12093" data-name="Group 12093" transform="translate(13.058 9.926)">
            <path id="Path_25072" data-name="Path 25072" d="M5130.1,2478.3c-.048-.028-4.729-2.81-9.072-1.321l-.071-.209c4.439-1.524,9.21,1.312,9.258,1.341Z" transform="translate(-5120.956 -2476.323)" fill="#fff"/>
          </g>
          <g id="Group_12094" data-name="Group 12094" transform="translate(12.173 2.707)">
            <path id="Path_25073" data-name="Path 25073" d="M5118.82,2466.251l-.128-.18c3.26-2.3,5.271-8.149,5.291-8.208l.209.071C5124.172,2457.994,5122.137,2463.908,5118.82,2466.251Z" transform="translate(-5118.692 -2457.863)" fill="#fff"/>
          </g>
          <g id="Group_12095" data-name="Group 12095" transform="translate(4.271 9.964)">
            <path id="Path_25074" data-name="Path 25074" d="M5098.692,2484.5l-.207-.079c1.789-4.674.033-7.856.015-7.887l.192-.11C5098.71,2476.452,5100.519,2479.719,5098.692,2484.5Z" transform="translate(-5098.485 -2476.42)" fill="#fff"/>
          </g>
        </g>
        <g id="Group_12098" data-name="Group 12098" transform="translate(138.572 102.822)">
          <path id="Path_25075" data-name="Path 25075" d="M5158.547,2501.637c-.327,1.039-3.748-.961-3.748-.961S5159.074,2499.967,5158.547,2501.637Z" transform="translate(-5141.713 -2497.422)" fill="#ffb600"/>
          <path id="Path_25076" data-name="Path 25076" d="M5149.848,2502.247c.243-.544-1.229-1.69-3.17-1.954a17.72,17.72,0,0,1,1.267,1.608C5148.363,2502.551,5149.478,2503.076,5149.848,2502.247Z" transform="translate(-5136.768 -2497.28)" fill="#ffb600"/>
          <g id="Group_12097" data-name="Group 12097" transform="translate(0 2.939)">
            <path id="Path_25077" data-name="Path 25077" d="M5121.455,2502.8c7.775-3.377,13.346-2.271,13.4-2.256l.036-.137c-.057-.015-5.738-1.429-13.558,1.967Z" transform="translate(-5121.335 -2500.106)" fill="#ffb600"/>
          </g>
          <path id="Path_25078" data-name="Path 25078" d="M5148.945,2493.259c.2.694-3.167,2.453-3.167,2.453a7.864,7.864,0,0,1,1-2.271C5147.429,2492.563,5148.756,2492.584,5148.945,2493.259Z" transform="translate(-5136.22 -2492.697)" fill="#ffb600"/>
          <path id="Path_25079" data-name="Path 25079" d="M5138.574,2501s2.76.729,3.052,2.335S5139.56,2503.481,5138.574,2501Z" transform="translate(-5131.833 -2497.711)" fill="#ffb600"/>
          <path id="Path_25080" data-name="Path 25080" d="M5131.223,2502.905s3.576,1.066,3.01,2.781S5131.223,2502.905,5131.223,2502.905Z" transform="translate(-5127.356 -2498.871)" fill="#ffb600"/>
          <path id="Path_25081" data-name="Path 25081" d="M5127.4,2498.466s.515-3.19,2.456-3.4S5129.063,2498.141,5127.4,2498.466Z" transform="translate(-5125.027 -2494.094)" fill="#ffb600"/>
          <path id="Path_25082" data-name="Path 25082" d="M5137.714,2496.155s.313-3.216,2.238-3.544S5139.354,2495.726,5137.714,2496.155Z" transform="translate(-5131.31 -2492.589)" fill="#ffb600"/>
        </g>
        <g id="Group_12100" data-name="Group 12100" transform="translate(13.1 95.65)">
          <path id="Path_25083" data-name="Path 25083" d="M4800.47,2480.4c.063,1.11,3.942-.006,3.942-.006S4800.37,2478.617,4800.47,2480.4Z" transform="translate(-4800.468 -2477.51)" fill="#091d7b"/>
          <path id="Path_25084" data-name="Path 25084" d="M4809.46,2484.121c-.1-.6,1.639-1.36,3.621-1.132a18.056,18.056,0,0,0-1.654,1.27C4810.85,2484.794,4809.617,2485.031,4809.46,2484.121Z" transform="translate(-4805.941 -2479.545)" fill="#091d7b"/>
          <g id="Group_12099" data-name="Group 12099" transform="translate(3.481 2.755)">
            <path id="Path_25085" data-name="Path 25085" d="M4822.034,2487.034c-6.827-5.29-12.6-5.6-12.662-5.6v-.144c.06,0,6.023.032,12.888,5.352Z" transform="translate(-4809.37 -2481.292)" fill="#091d7b"/>
          </g>
          <path id="Path_25086" data-name="Path 25086" d="M4813.145,2474.564c-.367.636,2.509,3.218,2.509,3.218a8.008,8.008,0,0,0-.42-2.493C4814.816,2474.258,4813.5,2473.945,4813.145,2474.564Z" transform="translate(-4808.168 -2474.247)" fill="#091d7b"/>
          <path id="Path_25087" data-name="Path 25087" d="M4820.769,2485.79s-2.908.025-3.6,1.538S4819.171,2487.991,4820.769,2485.79Z" transform="translate(-4810.57 -2481.277)" fill="#091d7b"/>
          <path id="Path_25088" data-name="Path 25088" d="M4827.6,2489.519s-3.8.153-3.671,1.989S4827.6,2489.519,4827.6,2489.519Z" transform="translate(-4814.756 -2483.547)" fill="#091d7b"/>
          <path id="Path_25089" data-name="Path 25089" d="M4833.226,2485.025s.294-3.279-1.57-3.971S4831.667,2484.285,4833.226,2485.025Z" transform="translate(-4818.986 -2478.343)" fill="#091d7b"/>
          <path id="Path_25090" data-name="Path 25090" d="M4823.914,2480.215s.5-3.254-1.319-4.061S4822.4,2479.379,4823.914,2480.215Z" transform="translate(-4813.453 -2475.344)" fill="#091d7b"/>
        </g>
        <g id="Group_12105" data-name="Group 12105" transform="translate(18.446 78.526)">
          <path id="Path_25091" data-name="Path 25091" d="M4825.6,2459.929c-3.627-1.9-4.159-5.226-1.975-5.941,1.542-.5,1.462-1.973-3.628-5.256s-4.17-5.962-3.183-6.541,4.041-.944.981-3.786-4.384-6.924-3.258-7.768,4.445,1.385,6.773,4.383,5.311,6.671,5.588,4.269,2.576-2.624,4.184.451,2.723,10.491,3.834,9.243c1.037-1.165,2.774-1.494,3.356,2.914.487,3.688-.437,9.743-.437,9.743S4829.226,2461.825,4825.6,2459.929Z" transform="translate(-4814.139 -2430.456)" fill="url(#linear-gradient-4)"/>
          <g id="Group_12101" data-name="Group 12101" transform="translate(1.344 1.087)">
            <path id="Path_25092" data-name="Path 25092" d="M4839.83,2463.38c-6.922-14.124-22.1-29.833-22.253-29.99l.158-.154c.153.157,15.356,15.891,22.293,30.047Z" transform="translate(-4817.577 -2433.236)" fill="#fff"/>
          </g>
          <g id="Group_12102" data-name="Group 12102" transform="translate(4.984 14.099)">
            <path id="Path_25093" data-name="Path 25093" d="M4836.5,2469.351a17.043,17.043,0,0,0-9.6-2.6l-.021-.22a17.283,17.283,0,0,1,9.733,2.631Z" transform="translate(-4826.885 -2466.512)" fill="#fff"/>
          </g>
          <g id="Group_12103" data-name="Group 12103" transform="translate(11.039 25.928)">
            <path id="Path_25094" data-name="Path 25094" d="M4853.239,2498.829a13.009,13.009,0,0,0-10.783-1.2l-.085-.2a13.247,13.247,0,0,1,10.978,1.208Z" transform="translate(-4842.37 -2496.762)" fill="#fff"/>
          </g>
          <g id="Group_12104" data-name="Group 12104" transform="translate(14.461 10.438)">
            <path id="Path_25095" data-name="Path 25095" d="M4851.664,2464.209c-1.056-1.452-.256-6.83-.222-7.059l.219.033c-.008.055-.821,5.517.182,6.9Z" transform="translate(-4851.12 -2457.15)" fill="#fff"/>
          </g>
        </g>
        <g id="Group_12107" data-name="Group 12107" transform="translate(39.261 86.192)">
          <path id="Path_25096" data-name="Path 25096" d="M4867.484,2450.2c-.659.52,1.713,2.437,1.713,2.437S4868.543,2449.364,4867.484,2450.2Z" transform="translate(-4867.369 -2450.062)" fill="#091d7b"/>
          <path id="Path_25097" data-name="Path 25097" d="M4868.629,2457.729c.325-.323,1.551.424,2.271,1.749a13.691,13.691,0,0,0-1.5-.473C4868.815,2458.879,4868.134,2458.22,4868.629,2457.729Z" transform="translate(-4868.032 -2454.685)" fill="#091d7b"/>
          <g id="Group_12106" data-name="Group 12106" transform="translate(1.618 2.231)">
            <path id="Path_25098" data-name="Path 25098" d="M4873.535,2466.09c.313-6.517-2-10.225-2.027-10.261l.089-.063c.026.037,2.591,3.74,2.277,10.294Z" transform="translate(-4871.508 -2455.766)" fill="#091d7b"/>
          </g>
          <path id="Path_25099" data-name="Path 25099" d="M4875.845,2455.426c-.554.048-.9,2.947-.9,2.947a6.042,6.042,0,0,0,1.361-1.341C4876.758,2456.328,4876.382,2455.379,4875.845,2455.426Z" transform="translate(-4871.98 -2453.328)" fill="#091d7b"/>
          <path id="Path_25100" data-name="Path 25100" d="M4871.835,2465.883s-1.276-1.788-2.513-1.56S4869.78,2465.849,4871.835,2465.883Z" transform="translate(-4868.303 -2458.734)" fill="#091d7b"/>
          <path id="Path_25101" data-name="Path 25101" d="M4872.343,2471.649s-1.742-2.283-2.822-1.408S4872.343,2471.649,4872.343,2471.649Z" transform="translate(-4868.564 -2462.228)" fill="#091d7b"/>
          <path id="Path_25102" data-name="Path 25102" d="M4877.42,2472.322s2.156-1.24,1.776-2.693S4877.2,2471.036,4877.42,2472.322Z" transform="translate(-4873.478 -2461.732)" fill="#091d7b"/>
          <path id="Path_25103" data-name="Path 25103" d="M4876.114,2464.484s2.229-1.1,1.94-2.577S4875.976,2463.188,4876.114,2464.484Z" transform="translate(-4872.689 -2457.018)" fill="#091d7b"/>
        </g>
        <ellipse id="Ellipse_1953" data-name="Ellipse 1953" cx="11.019" cy="1.751" rx="11.019" ry="1.751" transform="translate(34.79 119.574)" fill="#9faad4" opacity="0.22"/>
        <path id="Path_25104" data-name="Path 25104" d="M4950.4,2520.863c0,.967-27.353,1.751-61.094,1.751s-61.095-.784-61.095-1.751,27.353-1.751,61.095-1.751S4950.4,2519.9,4950.4,2520.863Z" transform="translate(-4804.264 -2405.918)" fill="#9faad4" opacity="0.22"/>
        <ellipse id="Ellipse_1954" data-name="Ellipse 1954" cx="11.019" cy="1.751" rx="11.019" ry="1.751" transform="translate(111.129 121.325)" fill="#9faad4" opacity="0.22"/>
        <ellipse id="Ellipse_1955" data-name="Ellipse 1955" cx="27.179" cy="1.751" rx="27.179" ry="1.751" transform="translate(69.421 115.674)" fill="#9faad4" opacity="0.22"/>
        <ellipse id="Ellipse_1956" data-name="Ellipse 1956" cx="11.019" cy="1.751" rx="11.019" ry="1.751" transform="translate(157.415 117.143)" fill="#9faad4" opacity="0.22"/>
        <g id="Group_12108" data-name="Group 12108" transform="translate(37.03 94.705)">
          <rect id="Rectangle_5195" data-name="Rectangle 5195" width="0.354" height="1.061" fill="#e34899"/>
          <path id="Path_25105" data-name="Path 25105" d="M4862.017,2493.546h-.354v-1.988h.354Zm0-3.976h-.354v-1.988h.354Zm0-3.976h-.354v-1.988h.354Zm0-3.976h-.354v-1.988h.354Z" transform="translate(-4861.663 -2476.58)" fill="#e34899"/>
          <rect id="Rectangle_5196" data-name="Rectangle 5196" width="0.354" height="1.061" transform="translate(0 18.954)" fill="#e34899"/>
        </g>
        <g id="Group_12109" data-name="Group 12109" transform="translate(122.299 96.592)">
          <rect id="Rectangle_5197" data-name="Rectangle 5197" width="0.354" height="1.061" transform="translate(0 0)" fill="#e34899"/>
          <path id="Path_25106" data-name="Path 25106" d="M5080.075,2496.651h-.354v-2.287h.354Zm0-4.573h-.354v-2.286h.354Zm0-4.573h-.354v-2.287h.354Z" transform="translate(-5079.721 -2481.871)" fill="#e34899"/>
          <rect id="Rectangle_5198" data-name="Rectangle 5198" width="0.354" height="1.061" transform="translate(0 17.066)" fill="#e34899"/>
        </g>
        <g id="Group_12110" data-name="Group 12110" transform="translate(35.623 90.087)">
          <path id="Path_25107" data-name="Path 25107" d="M4858.155,2462.1a1.1,1.1,0,0,0,.535.152c.419,0,.55-.268.546-.468,0-.339-.309-.483-.624-.483h-.182v-.245h.182c.238,0,.539-.123.539-.408,0-.193-.124-.364-.423-.364a.89.89,0,0,0-.483.16l-.085-.238a1.151,1.151,0,0,1,.631-.186c.476,0,.691.282.691.576a.6.6,0,0,1-.446.568v.008a.628.628,0,0,1,.539.62c0,.386-.3.724-.88.724a1.226,1.226,0,0,1-.627-.164Z" transform="translate(-4858.066 -2460.022)" fill="#e34899"/>
          <path id="Path_25108" data-name="Path 25108" d="M4864.52,2461.244c0,.82-.3,1.274-.839,1.274-.472,0-.791-.442-.8-1.24s.35-1.256.84-1.256C4864.231,2460.022,4864.52,2460.475,4864.52,2461.244Zm-1.312.037c0,.627.193.984.491.984.334,0,.494-.39.494-1.007,0-.594-.152-.984-.49-.984C4863.417,2460.274,4863.208,2460.624,4863.208,2461.281Z" transform="translate(-4860.999 -2460.022)" fill="#e34899"/>
        </g>
        <g id="Group_12111" data-name="Group 12111" transform="translate(120.815 92.073)">
          <path id="Path_25109" data-name="Path 25109" d="M5075.926,2467.555v-.2l.256-.249c.617-.587.9-.9.9-1.263a.431.431,0,0,0-.479-.471.828.828,0,0,0-.513.2l-.1-.23a1.054,1.054,0,0,1,.684-.246.68.68,0,0,1,.739.7c0,.446-.323.806-.833,1.3l-.193.178v.007h1.084v.271Z" transform="translate(-5075.926 -2465.1)" fill="#e34899"/>
          <path id="Path_25110" data-name="Path 25110" d="M5082.352,2466.323c0,.821-.3,1.274-.839,1.274-.472,0-.791-.442-.8-1.24s.35-1.256.84-1.256C5082.063,2465.1,5082.352,2465.554,5082.352,2466.323Zm-1.311.037c0,.628.192.984.49.984.334,0,.494-.39.494-1.006,0-.595-.152-.985-.49-.985C5081.249,2465.353,5081.041,2465.7,5081.041,2466.36Z" transform="translate(-5078.842 -2465.1)" fill="#e34899"/>
        </g>
        <g id="Group_12112" data-name="Group 12112" transform="translate(158.628 112.796)">
          <path id="Path_25111" data-name="Path 25111" d="M5179.191,2524.129c-.3.114-4.149.048-5.754.015a.365.365,0,0,1-.242-.631,3.708,3.708,0,0,1,2.31-.937,1.294,1.294,0,0,0,1.035-.5,6.454,6.454,0,0,0,.791-2.713c-.012-.17.35-.407.335-.594-.135-1.643,2.331.249,2.331.249a2.07,2.07,0,0,0-.184.755c.016.482-.689,1.7-.552,2.266.021.082.042.161.065.236C5179.631,2523.211,5179.57,2523.984,5179.191,2524.129Z" transform="translate(-5172.901 -2518.095)" fill="#fdd1b1"/>
          <path id="Path_25112" data-name="Path 25112" d="M5178.913,2529.891c-.325.124-4.812.036-6.1.008-.165,0-.23-.176-.156-.325a2.989,2.989,0,0,1,2.531-1.479,1.73,1.73,0,0,0,1.211-.538c.54.033.972.272.961.766-.006.292.913.373,1.627-.525.02.082.042.161.065.236C5179.353,2528.973,5179.291,2529.745,5178.913,2529.891Z" transform="translate(-5172.623 -2523.857)" fill="#202952"/>
        </g>
        <g id="Group_12113" data-name="Group 12113" transform="translate(175.533 112.163)">
          <path id="Path_25113" data-name="Path 25113" d="M5220.222,2521.918a.729.729,0,0,1-.466,1.289h-2.821c-.5,0-1.2-.4-1.065-1.69.008-.079.016-.161.021-.245.048-.627.048-1.361.034-1.973-.018-.721-.054-1.271-.054-1.271l2.149-1.294.428-.258s-.014.088-.034.242a16.33,16.33,0,0,0-.135,2.581,3.3,3.3,0,0,0,.384,1.639.7.7,0,0,0,.289.243A5.09,5.09,0,0,1,5220.222,2521.918Z" transform="translate(-5215.854 -2516.476)" fill="#fdd1b1"/>
          <path id="Path_25114" data-name="Path 25114" d="M5220.94,2529.374a.28.28,0,0,1-.257.392h-3.748c-.5,0-1.2-.4-1.065-1.69.008-.079.016-.161.021-.245.384.246.6.39.929.075a1.909,1.909,0,0,1,1.594-.644c.23.033.283.294.375.375a.618.618,0,0,0,.164.1A3.421,3.421,0,0,1,5220.94,2529.374Z" transform="translate(-5215.854 -2523.035)" fill="#202952"/>
        </g>
        <path id="Path_25115" data-name="Path 25115" d="M5198.776,2456.4v.024q-.316,4.483-.808,9.755h-2.951l-2.1-26.659-7.607,26.659h-3.5s4.158-26.032,6.518-34.1,11.09-.52,11.09-.52.015.239.035.718A231.643,231.643,0,0,1,5198.776,2456.4Z" transform="translate(-5019.59 -2350.652)" fill="#091d7b"/>
        <path id="Path_25116" data-name="Path 25116" d="M5222.077,2462.51c.641-16.082-2-23.234-2-23.234l2.679-.887A231.651,231.651,0,0,1,5222.077,2462.51Z" transform="translate(-5042.891 -2356.761)" opacity="0.15" fill="url(#linear-gradient-5)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_25117" data-name="Path 25117" d="M5165.6,2408l.026.228.12,1.1s-.192-.057-.465-.1a3.8,3.8,0,0,0-1.033-.064c-.038,0-.076.01-.113.016a5.441,5.441,0,0,1-2.968-.267c-.479-.233-.278-.414-.278-.414-1.074-.235-.969-.661-.737-.667.215-.007.746.041.81.046-.458-.073-.887-.282-.879-.45s.223-.193.649-.11a12.26,12.26,0,0,0,2.388.114c.558-.06.319-.214.065-.547s-.652-.829-.425-.973,1.056.828,1.369,1.042c.12.083.368.26.625.446C5165.168,2407.682,5165.6,2408,5165.6,2408Z" transform="translate(-5006.311 -2336.968)" fill="#fdd1b1"/>
        <path id="Path_25118" data-name="Path 25118" d="M5183.051,2397.067c.482,1.345.868,2.7.868,2.7s-1.521,3.982-2.679,4.9a1.041,1.041,0,0,1-.17.117c-.861.463-8.2-2.317-8.2-2.317l.536-2.328,5.756,1.278a.238.238,0,0,0,.287-.182c.262-1.143,1.691-7.028,2.473-7.528C5182.369,2393.419,5182.513,2395.572,5183.051,2397.067Z" transform="translate(-5014.146 -2329.535)" fill="#57abec"/>
        <path id="Path_25119" data-name="Path 25119" d="M5196.085,2402.276c.482,1.344.868,2.7.868,2.7s-1.521,3.982-2.679,4.9c1.443-2.729,1.659-7.642,1.659-7.642Z" transform="translate(-5027.18 -2334.744)" opacity="0.15" fill="url(#linear-gradient-6)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_25120" data-name="Path 25120" d="M5210.34,2394.572a18.021,18.021,0,0,1-.752,2.387,31.866,31.866,0,0,0-1.935,9.674,16.007,16.007,0,0,1-2.52.513,26.283,26.283,0,0,1-8.594-.522c0-6.426-2.13-16.557-.82-17.41s3.9-2.392,6.164-2.59l2.423-.073c5.185,1.108,6.167,2.847,6.167,2.847A10.34,10.34,0,0,1,5210.34,2394.572Z" transform="translate(-5027.8 -2325.193)" fill="#57abec"/>
        <path id="Path_25121" data-name="Path 25121" d="M5212.135,2361.728c-.3,2.321-3.251,4.06-3.251,4.06a17.077,17.077,0,0,0-1.073-3.218c-.554-1.2-1.325-2.312-2.321-2.524-2.279-.489-1.714-2.544-.882-2.908a8.174,8.174,0,0,1,5.228.133,2.424,2.424,0,0,1,1.369,2.275S5212.436,2359.407,5212.135,2361.728Z" transform="translate(-5033.029 -2307.056)" fill="#002b3b"/>
        <path id="Path_25122" data-name="Path 25122" d="M5212.215,2379.542a4.556,4.556,0,0,1-2.433,1.105,1.705,1.705,0,0,1-1.5-.687l.065-1.485.058-1.369,3.514-2.058.01-.008v.024c0,.087,0,.4,0,.762,0,.486.01,1.072.027,1.352C5212,2377.771,5212.215,2379.542,5212.215,2379.542Z" transform="translate(-5035.709 -2318.185)" fill="#fdd1b1"/>
        <path id="Path_25123" data-name="Path 25123" d="M5210.28,2389.995s.43.639.72.689.9-.689.9-.689l-.9-.615Z" transform="translate(-5036.927 -2326.917)" fill="#e34862"/>
        <path id="Path_25124" data-name="Path 25124" d="M5209.335,2404.563l-1.512,2.6-1.045-2.7a67.062,67.062,0,0,1,1.664-13.413l.373.051.655.089S5208.97,2400.012,5209.335,2404.563Z" transform="translate(-5034.795 -2327.934)" fill="#e34862"/>
        <path id="Path_25125" data-name="Path 25125" d="M5212.118,2386.7l.881,1.156,2.427-2.061-.873-.816Z" transform="translate(-5038.046 -2324.236)" fill="#fff"/>
        <path id="Path_25126" data-name="Path 25126" d="M5207.369,2385.984l1.5,1.328-.75,1.156-1.337-1.7Z" transform="translate(-5034.796 -2324.849)" fill="#fff"/>
        <path id="Path_25127" data-name="Path 25127" d="M5225.432,2405.448a17.924,17.924,0,0,1-.752,2.387,31.856,31.856,0,0,0-1.935,9.673,16.035,16.035,0,0,1-2.52.513,1.324,1.324,0,0,1-.124-.377c-.367-2.167,3.289-13.234,3.289-13.234S5224.545,2404.977,5225.432,2405.448Z" transform="translate(-5042.892 -2336.07)" opacity="0.15" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_25128" data-name="Path 25128" d="M5212.068,2375.691c-.012.051-.022.1-.037.147a3.721,3.721,0,0,1-3.584,2.648l.059-1.369,3.514-2.058a.11.11,0,0,1,.01.017A.9.9,0,0,1,5212.068,2375.691Z" transform="translate(-5035.811 -2318.196)" opacity="0.15" fill="url(#linear-gradient-8)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_25129" data-name="Path 25129" d="M5211.445,2368.316c-.069.58-1.058,1.157-1.058,1.157-.765,1.136-2.5,2.716-3.918,1.556-1.176-.965-.621-4.653-.4-5.872.043-.249.075-.395.075-.395a7.414,7.414,0,0,0,2.118.349c1.072,0,1.877.294,1.827,1.486,0,0,0,.007,0,.009a.51.51,0,0,1,0,.082c-.091,1.269-.123,1.395.048,1.411s.363-.269.427-.349C5210.847,2367.4,5211.516,2367.734,5211.445,2368.316Z" transform="translate(-5034.165 -2311.926)" fill="#fdd1b1"/>
        <path id="Path_25130" data-name="Path 25130" d="M5210.6,2366.6a1.421,1.421,0,0,0-1.243-1.215,16.227,16.227,0,0,1-2.777-.225c.043-.249.075-.395.075-.395a7.414,7.414,0,0,0,2.118.349C5209.841,2365.112,5210.647,2365.406,5210.6,2366.6Z" transform="translate(-5034.671 -2311.926)" opacity="0.15" fill="url(#linear-gradient-9)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_25131" data-name="Path 25131" d="M5134.658,2386.022a8.583,8.583,0,1,0-2.459-11.886A8.582,8.582,0,0,0,5134.658,2386.022Z" transform="translate(-4988.519 -2315.277)" fill="#b8e4f2" opacity="0.34"/>
        <g id="Group_12114" data-name="Group 12114" transform="translate(155.237 58.74)" opacity="0.5" style="mix-blend-mode: screen;isolation: isolate">
          <path id="Path_25132" data-name="Path 25132" d="M5166.483,2385.738a.509.509,0,0,0,.081-.222,7.055,7.055,0,0,0-1.712-5.483.514.514,0,0,0-.77.68,6.029,6.029,0,0,1,1.462,4.685.513.513,0,0,0,.939.341Z" transform="translate(-5163.953 -2379.858)" fill="#fff"/>
        </g>
        <g id="Group_12115" data-name="Group 12115" transform="translate(144.882 56.532)" opacity="0.5" style="mix-blend-mode: screen;isolation: isolate">
          <path id="Path_25133" data-name="Path 25133" d="M5147.457,2376.078a.514.514,0,0,0-.147-.711,7.051,7.051,0,0,0-9.754,2.018.514.514,0,0,0,.859.564,6.022,6.022,0,0,1,8.331-1.724A.514.514,0,0,0,5147.457,2376.078Z" transform="translate(-5137.472 -2374.212)" fill="#fff"/>
        </g>
        <rect id="Rectangle_5199" data-name="Rectangle 5199" width="1.099" height="15.9" transform="matrix(-0.549, 0.836, -0.836, -0.549, 171.701, 76.616)" fill="#ffb600"/>
        <g id="Group_12116" data-name="Group 12116" transform="translate(141.781 54.448)">
          <path id="Path_25134" data-name="Path 25134" d="M5133.7,2385.827a9.232,9.232,0,1,1,12.786-2.646A9.244,9.244,0,0,1,5133.7,2385.827Zm.563-.858a8.206,8.206,0,1,0-2.35-11.364A8.215,8.215,0,0,0,5134.264,2384.969Z" transform="translate(-5129.542 -2368.882)" fill="#ffb600"/>
        </g>
        <rect id="Rectangle_5200" data-name="Rectangle 5200" width="2.583" height="13.422" rx="0.574" transform="matrix(-0.549, 0.836, -0.836, -0.549, 174.996, 77.894)" fill="#091d7b"/>
        <g id="Group_12121" data-name="Group 12121" transform="translate(39.327 53.031)">
          <g id="Group_12120" data-name="Group 12120" transform="translate(3.71 3.345)">
            <g id="Group_12117" data-name="Group 12117" transform="translate(6.745)">
              <path id="Path_25135" data-name="Path 25135" d="M4895.493,2374.422a.609.609,0,1,1-.609-.609A.609.609,0,0,1,4895.493,2374.422Z" transform="translate(-4894.275 -2373.813)" fill="#004b6a"/>
            </g>
            <g id="Group_12118" data-name="Group 12118" transform="translate(3.372)">
              <path id="Path_25136" data-name="Path 25136" d="M4886.869,2374.422a.609.609,0,1,1-.609-.609A.609.609,0,0,1,4886.869,2374.422Z" transform="translate(-4885.651 -2373.813)" fill="#004b6a"/>
            </g>
            <g id="Group_12119" data-name="Group 12119">
              <path id="Path_25137" data-name="Path 25137" d="M4878.245,2374.422a.609.609,0,1,1-.609-.609A.609.609,0,0,1,4878.245,2374.422Z" transform="translate(-4877.026 -2373.813)" fill="#004b6a"/>
            </g>
          </g>
          <path id="Path_25138" data-name="Path 25138" d="M4960.021,2371.856v55.471a1.786,1.786,0,0,1-1.785,1.784h-84.1a1.782,1.782,0,0,1-1.785-1.784v-55.471a1.782,1.782,0,0,1,1.785-1.784h84.1l.093,0,.091.007c.028,0,.059.007.088.012l.055.01c.02,0,.039.007.059.011l.043.01.026.007.063.018a1.726,1.726,0,0,1,.175.063,1.439,1.439,0,0,1,.156.076.688.688,0,0,1,.064.037c.022.012.043.025.063.04s.041.027.061.042l.054.04c.021.016.042.032.063.05s.038.032.055.049a1.624,1.624,0,0,1,.149.155l.034.041c.023.029.046.059.067.09s.027.037.038.055a.622.622,0,0,1,.035.056.335.335,0,0,1,.022.04l0,0c.013.023.025.046.037.069s.027.054.039.082.012.027.018.042.02.051.03.077.018.053.026.08a1.728,1.728,0,0,1,.052.221c0,.027.009.052.013.079,0,0,0,.01,0,.015,0,.027,0,.056.007.084a.469.469,0,0,1,0,.053C4960.02,2371.808,4960.021,2371.832,4960.021,2371.856Z" transform="translate(-4870.469 -2368.19)" fill="#dcf4fe"/>
          <path id="Path_25139" data-name="Path 25139" d="M4871.2,2428.063a3.671,3.671,0,0,1-3.667-3.666v-55.471a3.671,3.671,0,0,1,3.667-3.667h84.1c.07,0,.14,0,.208.006.052,0,.105.008.159.014s.138.015.209.027l.081.014.1.019c.035.006.071.015.108.023l.08.02c.046.013.09.025.135.039.11.034.222.074.333.12a3.261,3.261,0,0,1,.34.163c.045.024.09.05.133.076s.1.062.149.095c.032.021.131.091.131.091l.1.078c.031.024.072.057.115.093s.073.064.109.1a3.567,3.567,0,0,1,.309.322l.017.02.625,1.075c0,.013.01.026.015.041.017.045.031.09.045.135l.011.034a3.609,3.609,0,0,1,.1.448c.006.034.011.068.016.1.008.051.014.1.019.151,0,.026.005.053.007.08,0,.054.008.11.009.167,0,.039,0,.078,0,.119V2424.4a3.671,3.671,0,0,1-3.667,3.666Z" transform="translate(-4867.538 -2365.259)" fill="#202952"/>
          <path id="Path_25140" data-name="Path 25140" d="M4960.021,2371.856v55.471a1.786,1.786,0,0,1-1.785,1.784h-84.1a1.782,1.782,0,0,1-1.785-1.784v-55.471a1.782,1.782,0,0,1,1.785-1.784h84.1l.093,0,.091.007c.028,0,.059.007.088.012l.055.01c.02,0,.039.007.059.011l.043.01.026.007.063.018a1.726,1.726,0,0,1,.175.063,1.439,1.439,0,0,1,.156.076.688.688,0,0,1,.064.037c.022.012.043.025.063.04s.041.027.061.042l.054.04c.021.016.042.032.063.05s.038.032.055.049a1.624,1.624,0,0,1,.149.155l.034.041c.023.029.046.059.067.09s.027.037.038.055a.622.622,0,0,1,.035.056.335.335,0,0,1,.022.04l0,0c.013.023.025.046.037.069s.027.054.039.082.012.027.018.042.02.051.03.077.018.053.026.08a1.728,1.728,0,0,1,.052.221c0,.027.009.052.013.079,0,0,0,.01,0,.015,0,.027,0,.056.007.084a.469.469,0,0,1,0,.053C4960.02,2371.808,4960.021,2371.832,4960.021,2371.856Z" transform="translate(-4870.469 -2368.19)" fill="#a9e1f2"/>
          <path id="Rectangle_5201" data-name="Rectangle 5201" d="M0,0H87.669a0,0,0,0,1,0,0V52.183a2.9,2.9,0,0,1-2.9,2.9H2.9a2.9,2.9,0,0,1-2.9-2.9V0A0,0,0,0,1,0,0Z" transform="translate(1.882 5.847)" fill="#fff"/>
          <line id="Line_1516" data-name="Line 1516" y2="0.212" transform="translate(75.289 25.561)" fill="#091d7b"/>
        </g>
        <image id="Rectangle_5202" data-name="Rectangle 5202" width="58.566" height="58.566" transform="translate(53.965 43.615)" xlink:href="data:image/png;base64,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"/>
        <path id="Path_25141" data-name="Path 25141" d="M5205.241,2422.431l.078.2.369.939s-.184,0-.436.02a3.457,3.457,0,0,0-.927.19c-.033.012-.065.028-.1.042a4.971,4.971,0,0,1-2.684.475c-.479-.091-.346-.3-.346-.3-.743-.128-.94-.51-.738-.572a2.921,2.921,0,0,1,.653.006c-.421.045-.851-.036-.884-.187s.151-.224.548-.253a14.869,14.869,0,0,0,2.136-.471c.482-.153.2-.177-.1-.4s-.826-.35-.659-.531,1.209.154,1.536.269c.127.044.388.141.659.244Z" transform="translate(-5030.806 -2346.495)" fill="#fdd1b1"/>
        <path id="Path_25142" data-name="Path 25142" d="M5220.387,2391.878c.933,1,1.583,3.8,1.319,8.225-.083,1.394-.426,4.241-1.3,5.433a1.072,1.072,0,0,1-.135.157c-.709.672-8.52-.091-8.52-.091l-.093-2.387,5.891-.273a.239.239,0,0,0,.229-.25c-.047-1.172-.135-6.149-.068-7.04.158-2.105.206-4.479,1.263-4.605A2.867,2.867,0,0,1,5220.387,2391.878Z" transform="translate(-5037.766 -2327.931)" fill="#57abec"/>
        <g id="Group_12135" data-name="Group 12135" transform="translate(76.157)">
          <g id="Group_12128" data-name="Group 12128" transform="translate(0)">
            <path id="Path_25143" data-name="Path 25143" d="M5000.746,2242.251c-.048,2.233-4.208,2.222-4.208,2.222h-33.145c-2.594-.553-1.783-2.452-.166-2.911.737-.209,1.983-.582,1.874-1.1-.408-1.906-1.348-3.278,1.073-5.535a4.96,4.96,0,0,1,5.209-.21c1.789.939,2.942-2.684,6.825-4.473,2.924-1.348,6.909-.606,7.961,3.894s3.529,1.508,3.946,1c1.21-1.473,4.809-.684,5.369,1.38,1.057,3.891,2.168,3.279,3.1,3.513S5000.771,2241.088,5000.746,2242.251Z" transform="translate(-4961.722 -2229.644)" fill="url(#linear-gradient-10)"/>
            <g id="Group_12127" data-name="Group 12127" transform="translate(10.899 6.571)">
              <g id="Group_12122" data-name="Group 12122" transform="translate(13.683 2.858)">
                <rect id="Rectangle_5203" data-name="Rectangle 5203" width="1.539" height="2.858" fill="#6ecedb"/>
              </g>
              <g id="Group_12123" data-name="Group 12123" transform="translate(10.263 1.753)">
                <rect id="Rectangle_5204" data-name="Rectangle 5204" width="1.539" height="3.962" fill="#6ecedb"/>
              </g>
              <g id="Group_12124" data-name="Group 12124" transform="translate(6.842)">
                <rect id="Rectangle_5205" data-name="Rectangle 5205" width="1.539" height="5.716" fill="#6ecedb"/>
              </g>
              <g id="Group_12125" data-name="Group 12125" transform="translate(3.421 1.155)">
                <rect id="Rectangle_5206" data-name="Rectangle 5206" width="1.539" height="4.561" fill="#6ecedb"/>
              </g>
              <g id="Group_12126" data-name="Group 12126" transform="translate(0 3.592)">
                <rect id="Rectangle_5207" data-name="Rectangle 5207" width="1.539" height="2.124" fill="#6ecedb"/>
              </g>
            </g>
          </g>
          <rect id="Rectangle_5208" data-name="Rectangle 5208" width="38.66" height="55.9" rx="4.962" transform="translate(15.781 24.86)" fill="#8ed2e8" opacity="0.59"/>
          <g id="Group_12129" data-name="Group 12129" transform="translate(24.681 31.452)">
            <path id="Path_25144" data-name="Path 25144" d="M5044.859,2316.4a10.437,10.437,0,0,0-8.811-6.3c-.257-.019-.518-.028-.78-.028a10.424,10.424,0,1,0,9.591,6.325Zm-6.684,9.55a6.172,6.172,0,1,1-2.907-11.617c.155,0,.309.006.461.017a6.172,6.172,0,0,1,2.446,11.6Z" transform="translate(-5024.838 -2310.075)" fill="#fff"/>
            <path id="Path_25145" data-name="Path 25145" d="M5061.821,2316.444l-3.917,1.677a6.174,6.174,0,0,0-5.212-3.726l.318-4.247A10.435,10.435,0,0,1,5061.821,2316.444Z" transform="translate(-5041.8 -2310.119)" fill="#ffb600"/>
            <path id="Path_25146" data-name="Path 25146" d="M5065.939,2343.074a10.455,10.455,0,0,1-4.985,5.91l-2.007-3.758a6.187,6.187,0,0,0,2.949-3.5Z" transform="translate(-5045.608 -2329.352)" fill="#728efc"/>
            <path id="Path_25147" data-name="Path 25147" d="M5038.44,2310.1l-.318,4.247q-.229-.018-.461-.017a6.168,6.168,0,0,0-5.26,2.943c-1.35-.827-2.686-1.645-3.635-2.219a10.418,10.418,0,0,1,8.9-4.982C5037.923,2310.075,5038.184,2310.084,5038.44,2310.1Z" transform="translate(-5027.23 -2310.075)" fill="#ed8efe"/>
          </g>
          <g id="Group_12133" data-name="Group 12133" transform="translate(20.639 59.917)">
            <g id="Group_12130" data-name="Group 12130" transform="translate(0 6.797)">
              <rect id="Rectangle_5209" data-name="Rectangle 5209" width="1.552" height="1.552" rx="0.776" fill="#57abec"/>
            </g>
            <g id="Group_12131" data-name="Group 12131" transform="translate(0 3.399)">
              <rect id="Rectangle_5210" data-name="Rectangle 5210" width="1.552" height="1.552" rx="0.776" fill="#28b5c8"/>
            </g>
            <g id="Group_12132" data-name="Group 12132">
              <rect id="Rectangle_5211" data-name="Rectangle 5211" width="1.552" height="1.552" rx="0.776" fill="#e34862"/>
            </g>
            <rect id="Rectangle_5212" data-name="Rectangle 5212" width="24.298" height="1.552" rx="0.776" transform="translate(4.499)" fill="#fff"/>
            <rect id="Rectangle_5213" data-name="Rectangle 5213" width="24.298" height="1.552" rx="0.776" transform="translate(4.499 3.399)" fill="#fff"/>
            <rect id="Rectangle_5214" data-name="Rectangle 5214" width="24.298" height="1.552" rx="0.776" transform="translate(4.499 6.797)" fill="#fff"/>
          </g>
          <g id="Group_12134" data-name="Group 12134" transform="translate(18.927 18.472)">
            <path id="Path_25148" data-name="Path 25148" d="M5010.124,2287.034v-3.385h.585v3.385Zm0-6.768v-3.385h.585v3.385Z" transform="translate(-5010.124 -2276.881)" fill="#ffb600"/>
            <path id="Path_25149" data-name="Path 25149" d="M5010.125,2313.549V2311.5h.585v1.462h1.462v.585Z" transform="translate(-5010.125 -2297.964)" fill="#ffb600"/>
            <rect id="Rectangle_5215" data-name="Rectangle 5215" width="2.873" height="0.585" transform="translate(4.921 15)" fill="#ffb600"/>
            <rect id="Rectangle_5216" data-name="Rectangle 5216" width="1.755" height="0.585" transform="translate(10.667 15)" fill="#ffb600"/>
          </g>
        </g>
        <g id="Group_12137" data-name="Group 12137" transform="translate(96.6 51.523)">
          <g id="Group_12136" data-name="Group 12136">
            <path id="Path_25150" data-name="Path 25150" d="M5079.335,2397.886s1.97,4.352,2.2,4.993-1.65,7.147-1.65,7.147l.938,1.328s2.957-6.688,3.14-8.887-2.371-9.334-3.39-9.941S5079.335,2397.886,5079.335,2397.886Z" transform="translate(-5053.787 -2380.329)" fill="url(#linear-gradient-11)"/>
            <path id="Path_25151" data-name="Path 25151" d="M5055.032,2475.539a35.748,35.748,0,0,0,0,10.873c.978,5.009,1.833,11.728,1.833,11.728h2.076s-.61-13.866-.488-17.347a24.109,24.109,0,0,1,1.588-7.086Z" transform="translate(-5038.723 -2429.791)" fill="#fdd1b1"/>
            <path id="Path_25152" data-name="Path 25152" d="M5071.938,2473.706s-.336,3.787,2.886,10.75,5.788,12.339,5.788,12.339l1.712-1.069s-4.493-15.21-4.828-17.134a16.16,16.16,0,0,1,.306-4.886Z" transform="translate(-5049.276 -2429.791)" fill="#fdd1b1"/>
            <path id="Path_25153" data-name="Path 25153" d="M5057.353,2534.739l-.106,4.572h-.8v-1.844s-.86,1.762-1.4,1.844c-.345.053-1.7.038-2.82.021a.284.284,0,0,1-.134-.529c1.02-.587,2.5-1.175,2.832-2.9.285-1.493.2-1.256.2-1.256Z" transform="translate(-5037.109 -2466.891)" fill="#37193b"/>
            <path id="Path_25154" data-name="Path 25154" d="M5093.179,2529.551l1.54,4.307-.751.289-.662-1.722s-.171,1.953-.644,2.223c-.3.173-1.574.646-2.625,1.03a.283.283,0,0,1-.314-.446c.742-.914,1.915-2,1.6-3.724-.271-1.5-.264-1.245-.264-1.245Z" transform="translate(-5060.073 -2463.798)" fill="#37193b"/>
            <path id="Path_25155" data-name="Path 25155" d="M5017.833,2410.988a20.352,20.352,0,0,0-3.8.458c-.275.275,1.374.161,1.718.344s-.071,1.413,1.1,1.145a6.192,6.192,0,0,0,1.6-.527Z" transform="translate(-5014.001 -2391.598)" fill="#fdd1b1"/>
            <path id="Path_25156" data-name="Path 25156" d="M5054.953,2423.838l-.764.382s-2.229,1.268-2.778,8.659-3.543,12.217-2.566,13.438,4.214-.061,6.108.305,4.948,2.138,8.368,1.283,2.749-1.038,4.459-1.527-3.054-5.009-3.481-8.43-.917-11.942-4.521-13.729S5054.953,2423.838,5054.953,2423.838Z" transform="translate(-5035.101 -2399.104)" fill="#091d7b"/>
            <path id="Path_25157" data-name="Path 25157" d="M5046.171,2397.778c-.062.209-.128.4-.2.579a36.306,36.306,0,0,0-1.42,5.314,12.832,12.832,0,0,1-5.589,0,10.6,10.6,0,0,0-1.649-4.994c-.734-1.191.412-2.52.137-3.207s-4.856,3.207-5.818,3.39-7.926.779-7.926.779a7.033,7.033,0,0,1,0-1.7,68.724,68.724,0,0,0,7.376-1.42c.6-.366,4.9-5.007,6.045-5.649a12.191,12.191,0,0,1,4.674-1.269c1.685-.191,4.307.367,4.856,1.145C5047.165,2391.467,5046.86,2395.489,5046.171,2397.778Z" transform="translate(-5019.875 -2378.555)" fill="#e34899"/>
            <path id="Path_25158" data-name="Path 25158" d="M5065.025,2371.233a14.113,14.113,0,0,0-.245,4.093,1.307,1.307,0,0,0,1.314,1.16v2.044l2.894.035a12.574,12.574,0,0,0-.663-2.446c-.306-.488-.146-1.423-.269-3.164S5065.025,2371.233,5065.025,2371.233Z" transform="translate(-5044.867 -2367.389)" fill="#fdd1b1"/>
            <path id="Path_25159" data-name="Path 25159" d="M5064.826,2362.314c-.955,1.057-.779,2.811-.122,2.932s2.921.255,2.682,2.6,1.162,2.212.95,4.172.9,5.981,3.245,6.664,4.667-.794,4.728-2.565-1.019-2.565-1.578-4.276.986-2.688,0-4.825-1.952-1.344-3.15-3.3S5066.536,2360.421,5064.826,2362.314Z" transform="translate(-5044.545 -2361.403)" fill="#002b3b"/>
            <path id="Path_25160" data-name="Path 25160" d="M5070.024,2410.552v0c-.062.209-.128.4-.2.579a36.2,36.2,0,0,0-1.42,5.314,12.832,12.832,0,0,1-5.589,0c.023-.99,3.6-.43,4.4-2.263C5068.57,2411.1,5070.024,2410.552,5070.024,2410.552Z" transform="translate(-5043.728 -2391.333)" fill="url(#linear-gradient-12)"/>
          </g>
          <path id="Path_25161" data-name="Path 25161" d="M5067.774,2382.82a6.835,6.835,0,0,0,1.362-.843c.292-.322.016,1.15-1.363,1.563Z" transform="translate(-5046.747 -2373.904)" opacity="0.15" fill="url(#linear-gradient-13)" style="mix-blend-mode: multiply;isolation: isolate"/>
        </g>
        <g id="Group_12142" data-name="Group 12142" transform="translate(29.518 49.308)">
          <g id="Group_12141" data-name="Group 12141" transform="translate(0)">
            <g id="Group_12140" data-name="Group 12140">
              <path id="Path_25162" data-name="Path 25162" d="M4846.765,2392.453c-.147-.645.5-2.287-.077-1.962-1.924,1.088-2.81,4.837-3.348,6.99-1,4.009-1.5,6.438.424,9.174a40.013,40.013,0,0,0,5.191,5.345l.915-3.084s-3.787-4.279-3.948-5.191,1.967-6.322,1.967-6.322Z" transform="translate(-4842.453 -2376.876)" fill="url(#linear-gradient-14)"/>
              <path id="Path_25163" data-name="Path 25163" d="M4867.4,2363.947a12.623,12.623,0,0,1-.348,4.547c-.643,1.765-1.712,1.6-1.739,1.926s.321,3.692.321,3.692l-3.825.24s-.054-3.611,0-4.093-.877-3.852-.024-5.029S4867.4,2363.947,4867.4,2363.947Z" transform="translate(-4854.002 -2360.737)" fill="#fdd1b1"/>
              <path id="Path_25164" data-name="Path 25164" d="M4858.718,2356.809c.749-.562,2.083-1.362,2.992-.961s2.854-.361,3.25,1.415c.376,1.686-1.133,2.22-2.364,2.354s-1.233.482-1.3,1.151-.643.077-1.066.276c-.262.123-.334,1.061.228,1.757s-.479,2.277-1.068,2.357-.938-.191-1.339-.725a6.975,6.975,0,0,1-1.873-5.19A2.206,2.206,0,0,1,4858.718,2356.809Z" transform="translate(-4850.809 -2355.739)" fill="#002b3b"/>
              <g id="Group_12138" data-name="Group 12138" transform="translate(19.367 66.167)">
                <path id="Path_25165" data-name="Path 25165" d="M4892.321,2531.2c.311.118,4.3.05,5.965.016a.379.379,0,0,0,.251-.655,3.852,3.852,0,0,0-2.395-.971,1.34,1.34,0,0,1-1.073-.521,7.5,7.5,0,0,1-.148-2.813c.012-.176-.363-.423-.348-.615.139-1.7-2.416.258-2.416.258a2.132,2.132,0,0,1,.189.782,15.063,15.063,0,0,1-.1,2.349c-.021.085-.043.167-.068.245C4891.864,2530.251,4891.928,2531.051,4892.321,2531.2Z" transform="translate(-4891.979 -2524.947)" fill="#fdd1b1"/>
                <path id="Path_25166" data-name="Path 25166" d="M4892.321,2537.176c.336.128,4.988.037,6.326.008.172,0,.239-.183.163-.337a3.1,3.1,0,0,0-2.624-1.533,1.792,1.792,0,0,1-1.256-.558c-.56.034-1.007.282-1,.794.007.3-.945.386-1.687-.545-.021.085-.043.168-.068.245C4891.864,2536.225,4891.928,2537.024,4892.321,2537.176Z" transform="translate(-4891.979 -2530.92)" fill="#202952"/>
              </g>
              <g id="Group_12139" data-name="Group 12139" transform="translate(6.236 66.167)">
                <path id="Path_25167" data-name="Path 25167" d="M4858.743,2531.2c.311.118,4.3.05,5.965.016a.379.379,0,0,0,.251-.655,3.852,3.852,0,0,0-2.4-.971,1.339,1.339,0,0,1-1.072-.521,7.5,7.5,0,0,1-.149-2.813c.012-.176-.363-.423-.348-.615.139-1.7-2.417.258-2.417.258a2.125,2.125,0,0,1,.19.782,15.063,15.063,0,0,1-.1,2.349c-.021.085-.043.167-.068.245C4858.286,2530.251,4858.35,2531.051,4858.743,2531.2Z" transform="translate(-4858.401 -2524.947)" fill="#fdd1b1"/>
                <path id="Path_25168" data-name="Path 25168" d="M4858.743,2537.176c.336.128,4.989.037,6.327.008.172,0,.239-.183.163-.337a3.1,3.1,0,0,0-2.625-1.533,1.789,1.789,0,0,1-1.254-.558c-.561.034-1.008.282-1,.794.007.3-.945.386-1.687-.545-.021.085-.043.168-.068.245C4858.286,2536.225,4858.35,2537.024,4858.743,2537.176Z" transform="translate(-4858.401 -2530.92)" fill="#202952"/>
              </g>
              <path id="Path_25169" data-name="Path 25169" d="M4867.3,2435.836a20.236,20.236,0,0,0,1.542,10.228c2.313,5.191,5.242,9.97,5.705,11.281s2.621,16.5,2.621,16.5h2.775s1.079-11.23,0-18.065-5.808-18.811-5.808-18.811Z" transform="translate(-4857.507 -2404.515)" fill="#202952"/>
              <path id="Path_25170" data-name="Path 25170" d="M4857.471,2436.967a24.557,24.557,0,0,0-1,9.971c.54,5.859.887,10.485.444,14.571s.558,12.335.558,12.335h2.776s3.777-20.507,3.854-26.674a44.342,44.342,0,0,0-1.387-11.333Z" transform="translate(-4850.918 -2404.515)" fill="#202952"/>
              <path id="Path_25171" data-name="Path 25171" d="M4924.164,2388.391a21.319,21.319,0,0,1,3.54-1.839c.641-.056-.391.557-.752.92s-.641.195-.307.919-.557,1.115-1.421,1.505S4924.164,2388.391,4924.164,2388.391Z" transform="translate(-4892.211 -2374.5)" fill="#fdd1b1"/>
              <path id="Path_25172" data-name="Path 25172" d="M4880.492,2389.648a39.369,39.369,0,0,1-5.168,4.859c-.026.02-.053.041-.078.062l-.175.136a.076.076,0,0,0-.012.007,4.987,4.987,0,0,1-2.316,1.244c-1.49-.13-7.581-3.122-7.837-2.659a12.576,12.576,0,0,0-.194,1.978c-.23,3.725-.475,11.384-.475,11.384h-10.073s-1.953-8.738-2.468-11.306-1.182-6.323-.591-6.938c1.2-1.251,4.645-2.3,6.7-2.3s6.329.448,7.923,1.578,6.4,3.789,7.324,3.84,6.1-3.232,6.1-3.232S4880.542,2389.34,4880.492,2389.648Z" transform="translate(-4847.581 -2374.238)" fill="#728efc"/>
              <path id="Path_25173" data-name="Path 25173" d="M4861.694,2385.164l.255-1a7.718,7.718,0,0,1,3.635-.117l.371.793A36.9,36.9,0,0,0,4861.694,2385.164Z" transform="translate(-4854.17 -2372.867)" fill="#6ecedb"/>
              <path id="Path_25174" data-name="Path 25174" d="M4923.855,2390.667l.808,1.678-.563.483-1.073-1.7Z" transform="translate(-4891.52 -2377.009)" fill="#6ecedb"/>
            </g>
            <path id="Path_25175" data-name="Path 25175" d="M4896.81,2403.066a4.975,4.975,0,0,1-2.315,1.243c-1.49-.129-7.581-3.122-7.837-2.659,0,0-.189-2.109.778-1.844C4888.378,2400.065,4894.156,2404.875,4896.81,2403.066Z" transform="translate(-4869.362 -2382.561)" fill="url(#linear-gradient-15)"/>
          </g>
          <path id="Path_25176" data-name="Path 25176" d="M4867.572,2377.62c.409.134.729.9,1.4.991l-.036.751S4866.573,2377.292,4867.572,2377.62Z" transform="translate(-4857.6 -2369.042)" opacity="0.15" fill="url(#linear-gradient-16)" style="mix-blend-mode: multiply;isolation: isolate"/>
        </g>
      </g>
    </g>
  </g>
</svg>
