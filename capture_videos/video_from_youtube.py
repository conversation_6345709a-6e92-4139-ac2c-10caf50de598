from pydub import AudioSegment

def extract_audio_with_pydub(mp4_path, wav_path):
    try:
        # Convert MP4 to WAV
        AudioSegment.from_file(mp4_path).export(wav_path, format="wav")
        print(f"Audio extracted and saved to: {wav_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

# Example usage
if __name__ == "__main__":
    mp4_path = r"video4_5.mp4"
    wav_path = r"video4_5.wav"
    extract_audio_with_pydub(mp4_path, wav_path)
