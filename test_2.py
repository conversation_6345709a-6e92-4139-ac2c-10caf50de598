import requests
from docx import Document

# Constants
OLLAMA_URL = "http://***********:11434/api/generate"
MODEL = "llama3.2"
DOCX_PATH = "swtichover.docx"

# Step 1: Read .docx text content
def read_docx_text(path):
    doc = Document(path)
    return "\n".join([p.text.strip() for p in doc.paragraphs if p.text.strip()])

# Step 2: Create the prompt
def build_prompt(sop_text):
    return f"""
You are a professional SOP analyst.

Extract only the **Database-level steps** from the SOP below and convert it into a markdown table with these columns:

- Step Number
- Statement (what to be done)
- Command or database query or shell commands or all os commands
- Output

Ensure each step has valid SQL/Linux DB operations, ignoring non-database steps. Format it cleanly.

SOP content:
\"\"\"
{sop_text}
\"\"\"
"""

# Step 3: Send to Ollama API
def query_ollama(prompt):
    payload = {
        "model": MODEL,
        "prompt": prompt,
        "stream": False
    }
    response = requests.post(OLLAMA_URL, json=payload)
    response.raise_for_status()
    return response.json()["response"]

# Step 4: Main workflow
if __name__ == "__main__":
    sop_text = read_docx_text(DOCX_PATH)
    final_prompt = build_prompt(sop_text)
    output = query_ollama(final_prompt)
    print(output)
