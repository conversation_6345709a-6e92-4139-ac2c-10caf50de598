[{"solution": "Always_ON_SB", "actions": ["CheckRole", "CheckAvailabilityMode", "ExecuteAlwaysOnForceFailOver", "CheckRole", "CheckAvailabilityMode"], "description": "Always_ON_SB (Switch Back) ensures high availability by switching to a secondary system during outages and reverting to the primary once restored."}, {"solution": "Always_ON_SO", "actions": ["CheckRole", "CheckAvailabilityMode", "ExecuteAlwaysOnForceFailOver", "CheckRole", "CheckAvailabilityMode"], "description": "Always_ON_SO (Switch Over) ensures uninterrupted service by shifting operations from the primary to a secondary system during maintenance or failures."}, {"solution": "AzurePipeline", "actions": ["CheckAzureDevOpsPipelineExist", "CheckAzureDevOpsPipelineExist", "CheckAzureDevOpsPipelineExist", "CheckAzureDevOpsPipelineExist", "ExecuteRunAzureDevOpsPipeline", "ExecuteRunAzureDevOpsPipeline", "ExecuteRunAzureDevOpsPipeline", "ExecuteRunAzureDevOpsPipeline"], "description": "Azure Pipeline automates code building, testing, and deployment in a CI/CD workflow for fast, reliable software delivery."}, {"solution": "AzureTrafficManagerEndpoint_Automation", "actions": ["EnableDisableATMEndpoint", "EnableDisableATMEndpoint", "CheckEnableDisableATMEndpoint", "CheckEnableDisableATMEndpoint", "EnableDisableATMEndpoint", "EnableDisableATMEndpoint", "CheckEnableDisableATMEndpoint", "CheckEnableDisableATMEndpoint"], "description": "Azure Traffic Manager Endpoint Automation automates endpoint management, optimizing traffic routing, availability, and simplifying updates, scaling, and failover for a seamless user experience."}, {"solution": "Azure_AKS_Deployment_Pod_Count_Service_Automation_API", "actions": ["Check_Azure_Kubernetes_Deployment_Pod_Count", "Scale_UpDown_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Deployment_Pod_Count", "Scale_UpDown_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Service_Exist", "Delete_Azure_Kubernetes_Service"], "description": "Azure AKS Deployment Pod Count Service Automation API automates the scaling and management of pod counts within an Azure Kubernetes Service (AKS) cluster."}, {"solution": "Azure_AKS_Deployment_Pod_Count_Service_Automation_API_withauth", "actions": ["Check_Azure_Kubernetes_Deployment_Pod_Count", "Scale_UpDown_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Deployment_Pod_Count", "Scale_UpDown_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Service_Exist", "Delete_Azure_Kubernetes_Service"], "description": "Azure AKS Deployment Pod Count Service Automation API with Auth automates pod scaling in Azure Kubernetes Service (AKS) while ensuring secure authentication for access."}, {"solution": "Azure_AKS_Deployment_Pod_Count_Service_Automation_New", "actions": ["Check_Azure_Kubernetes_Deployment_Pod_Count", "Scale_UpDown_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Deployment_Pod_Count", "Scale_UpDown_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Service_Exist", "Delete_Azure_Kubernetes_Service"], "description": "Azure AKS Deployment Pod Count Service Automation New refers to a new implementation for automating pod scaling in an Azure Kubernetes Service (AKS) cluster"}, {"solution": "Azure_AKS_Deployment_Pod_Count_Service_Automation", "actions": ["Check_Azure_Kubernetes_Deployment_Pod_Count", "Scale_UpDown_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Deployment_Pod_Count", "Scale_UpDown_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Deployment_Pod_Count", "Check_Azure_Kubernetes_Service_Exist", "Delete_Azure_Kubernetes_Service"], "description": "Azure AKS Deployment Pod Count Service Automation automates the scaling of pods within an Azure Kubernetes Service (AKS) cluster based on workload demands. "}, {"solution": "Azure_Application_Gateway_Automation", "actions": ["CheckApplicationGatewayRulePathexist", "AddApplicationGatewayBackendPoolRulePath", "CheckApplicationGatewayRulePathexist", "RemovePathbasedRulefromBackendPoolRule", "RemovePathbasedRulefromBackendPoolRule", "CheckListenerwithAssociatedRule", "AddListenerHTTPType", "CheckListenerwithAssociatedRule", "AddRoutingRule", "RemoveRoutingRule", "CheckApplicationGatewayOperationalState"], "description": "Azure Application Gateway Automation streamlines the management of Azure Application Gateway, automating load balancing, traffic routing, scaling, SSL certificate management, and backend health monitoring for better performance."}, {"solution": "Azure_Cosmos_FailOver", "actions": ["CheckAzureWriteLocationCosmos", "CheckAzureReadLocationCosmos", "CheckAzureDBAccountProvisioningStatusCosmos", "ExecuteFailoverAzureCosmosDB"], "description": "Azure Cosmos Failover is a feature that automatically switches to a secondary region in case of a failure in the primary region, ensuring high availability and minimizing downtime for Cosmos DB applications."}, {"solution": "Azure_Failback_1", "actions": ["CheckAzureVMstatus", "ExecutePlannedFailover", "CheckUnplannedFailoverCompletedStatus", "CheckCommitFailOver", "ExecuteCommitUnplannedFailover", "CheckUnplannedFailoverCompletedStatus", "CheckRe_Protect", "ExecuteRe_Protect", "CheckRe_ProtectProtectionState"], "description": "Azure Failback 1 refers to the process of restoring operations to the original primary system or region after a failover event, ensuring normal service resumption once the issues are resolved."}, {"solution": "Azure_FailOver", "actions": ["CheckAllowedOperation", "ExecuteUnplannedFailover", "CheckUnplannedFailoverCompletedStatus", "CheckCommitFailOver", "ExecuteCommitUnplannedFailover", "CheckUnplannedFailoverCommittedStatus", "CheckRe_Protect", "ExecuteRe_Protect", "CheckRe_ProtectProtectionState", "AssociatePublicIPAddress", "Wait", "CheckPublicIPAddress", "ExecutePlannedFailover", "CheckPlannedFailoverStatus"], "description": "Azure Failover is a process that automatically switches application traffic to a secondary region or replica in the event of a failure in the primary region, ensuring high availability and minimal downtime."}, {"solution": "Azure_LoadBalancer", "actions": ["AddLoadBalancerToVM", "RemoveLoadBalancerFromVM", "AddProbeHealthToLoadBalancer", "AddProbeHealthToLoadBalancer", "RemoveProbeHealthfromLoadBalancer", "RemoveProbeHealthfromLoadBalancer", "AddLoadBalancerRuleToLoadBalancer", "RemoveLoadBalancerRuleFromLoadBalancer", "AddLoadBalancerInboundNATRuleToLoadBalancer", "AddLoadBalancerInboundNATRuleToLoadBalancer", "RemoveLoadBalancerInboundNATRuleFromLoadBalancer", "RemoveLoadBalancerInboundNATRuleFromLoadBalancer"], "description": "Azure Load Balancer is a cloud-based service that distributes incoming network traffic across multiple servers to ensure high availability, reliability, and optimal performance for applications."}, {"solution": "Azure_LoadBalancer_SPN", "actions": ["AddLoadBalancerToVM", "RemoveLoadBalancerFromVM", "AddProbeHealthToLoadBalancer", "RemoveProbeHealthfromLoadBalancer", "AddLoadBalancerRuleToLoadBalancer", "RemoveLoadBalancerRuleFromLoadBalancer", "AddLoadBalancerInboundNATRuleToLoadBalancer", "RemoveLoadBalancerInboundNATRuleFromLoadBalancer"], "description": "Azure Load Balancer SPN (Service Principal Name) is used to authenticate and authorize access to Azure Load Balancer resources for automated tasks or services, ensuring secure communication and management within Azure environments."}, {"solution": "Azure_MSSQL_Auto_FB", "actions": ["CheckAzureSQLDBRole", "CheckAzureReplicationState", "ExecuteForceFailOverAzure"], "description": "Azure MSSQL Auto-FB (Auto Failback) refers to the automatic process of restoring SQL Server instances to their original primary region after a failover event, ensuring seamless recovery and minimal downtime in Azure SQL environments."}, {"solution": "Azure_MSSQL_Auto_FO", "actions": ["CheckAzureSQLDBRole", "CheckAzureReplicationState", "ExecutePlannedFailOverAzure"], "description": "Azure MSSQL Auto-FO (Auto Failover) is a feature that automatically switches to a secondary replica of an Azure SQL Database or Managed Instance during an outage or failure."}, {"solution": "Azure_MySQL_PaaS_AzAD_SB", "actions": ["CheckAzureMysqlServerExist_InRegion", "CheckAzureMysqlServerExist_InRegion", "CheckAzureMySQLServerRole", "CheckAzureMySQLServerRole", "CheckAzureMySQLServerStatus", "CheckAzureMySQLServerStatus", "PromoteMySQLGeoAsyncReplicaServertoIndependentServer", "DeleteMySQLStandaloneServer", "CreateMySQLGeoAsyncReplicaServer", "CheckAzureMySQLServerRole", "CheckAzureMySQLServerRole"], "description": "Azure MySQL PaaS AzAD SB refers to Azure MySQL Platform-as-a-Service integrated with Azure Active Directory for authentication, with Switch Back functionality to automatically revert to the primary instance after a failover."}, {"solution": "Azure_MySQL_PaaS_AzAD_SO", "actions": ["CheckAzureMysqlServerExist_InRegion", "CheckAzureMysqlServerExist_InRegion", "CheckAzureMySQLServerRole", "CheckAzureMySQLServerRole", "CheckAzureMySQLServerStatus", "CheckAzureMySQLServerStatus", "PromoteMySQLGeoAsyncReplicaServertoIndependentServer", "DeleteMySQLStandaloneServer", "CreateMySQLGeoAsyncReplicaServer", "CheckAzureMySQLServerRole", "CheckAzureMySQLServerRole"], "description": "Azure MySQL PaaS AzAD SO integrates Azure MySQL with Azure Active Directory for authentication and uses Switch Over to transfer operations to a secondary instance during maintenance or failures."}, {"solution": "Azure_Network_Security_Group", "actions": ["CheckNSGNameExist", "CheckNSGNameAssociatetoVM", "DissociateNSGtoVM", "AssociateNSGtoVMsSpecifyNetworkCard", "DissociateNSGtoVM", "AssociateNSGtoVMDefaultNetworkCard", "AddSecurityRuletoNSG", "RemoveSecurityRuletoNSG", "AssociateNSGtoNetworkInterface", "DissociateNSGtoNetworkInterface", "AssignNSGtoVMWithReplaceExistingNSG"], "description": "Azure Network Security Group (NSG) is a resource in Azure that controls inbound and outbound traffic to network interfaces, virtual machines, and subnets, helping secure network resources by applying security rules."}, {"solution": "Azure_OnPremVMwareTOAz_SB", "actions": ["CheckAllowedOperationAzureToPrim", "CheckReplicatedVMProtectionState", "ExecuteUnplannedFailOverOnPrimToAzure", "CheckAllowedOperationAzureToPrim", "ExecuteCommitFailover", "CheckAllowedOperationAzureToPrim", "CheckReplicatedVMProtectionState", "ExecuteReprotectOnPremVMwareToAzure"], "description": "Azure OnPrem VMware to Az_SB refers to migrating VMware VMs to Azure, with SB indicating Switch Back to restore operations to the primary system after failover or migration."}, {"solution": "Azure_OnPremVMwareTOAz_SO", "actions": ["CheckAllowedOperationAzureToPrim", "CheckReplicatedVMProtectionState", "ExecuteUnplannedFailOverOnPrimToAzure", "CheckAllowedOperationAzureToPrim", "ExecuteCommitFailover", "CheckAllowedOperationAzureToPrim", "CheckReplicatedVMProtectionState", "ExecuteReprotectAzureToOnPrim"], "description": "Azure OnPrem VMware to Az_SO refers to migrating VMware VMs from on-premises to Azure, with Switch Over (SO) transferring operations to Azure during migration or failover for continuity."}, {"solution": "Azure_PSQL_PaaS_Flexible_AzSPN_SB_New", "actions": ["CheckAzurePostgreSQLServerExist_InRegion", "CheckAzurePostgreSQLServerExist_InRegion", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerStatusReady", "CheckAzurePostgreSQLServerStatusReady", "SwitchoverPostgreSQLGeoAsyncReplicaServer", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerRole"], "description": "Azure PSQL PaaS Flexible AzSPN SB New refers to a new Azure PostgreSQL PaaS setup with flexible server deployment, Azure Service Principal (AzSPN) for authentication, and Switch Back (SB) for high availability."}, {"solution": "Azure_PSQL_PaaS_Flexible_AzSPN_SO_New", "actions": ["CheckAzurePostgreSQLServerExist_InRegion", "CheckAzurePostgreSQLServerExist_InRegion", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerStatusReady", "CheckAzurePostgreSQLServerStatusReady", "SwitchoverPostgreSQLGeoAsyncReplicaServer", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerRole"], "description": "Azure PSQL PaaS Flexible AzSPN SO New refers to a new Azure PostgreSQL PaaS setup with flexible server deployment, Azure Service Principal (AzSPN) for authentication, and Switch Over (SO) for high availability."}, {"solution": "Azure_PSQL_PaaS_Flexible_SB_New", "actions": ["CheckAzurePostgreSQLServerExist_InRegion", "CheckAzurePostgreSQLServerExist_InRegion", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerStatusReady", "CheckAzurePostgreSQLServerStatusReady", "SwitchoverPostgreSQLGeoAsyncReplicaServer", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerRole"], "description": "Azure PSQL PaaS Flexible SB New refers to a new Azure PostgreSQL PaaS setup with flexible server deployment and Switch Back (SB) functionality for seamless recovery and failover to the primary system."}, {"solution": "Azure_PSQL_PaaS_Flexible_SO_New", "actions": ["CheckAzurePostgreSQLServerExist_InRegion", "CheckAzurePostgreSQLServerExist_InRegion", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerStatusReady", "CheckAzurePostgreSQLServerStatusReady", "SwitchoverPostgreSQLGeoAsyncReplicaServer", "CheckAzurePostgreSQLServerRole", "CheckAzurePostgreSQLServerRole"], "description": "Azure PSQL PaaS Flexible SO New refers to a new Azure PostgreSQL Platform-as-a-Service setup with flexible server deployment, incorporating Switch Over (SO) functionality for seamless operation transfer during failover or maintenance."}, {"solution": "Azure_PSQL_Server_SO", "actions": ["Check_AzurePostgresDB_ServerReadyStatus", "Check_AzurePostgresDB_ServerReadyStatus", "Check_AzurePostgresDBReplicationMasterRole", "Check_AzurePostgresDBReplicationReplicaRole", "AzurePostgresDB_StopReplication", "AzurePostgresDB_RemoveServer", "AzurePostgresDB_CreateReplicaServer"], "description": "Azure PSQL Server SO refers to the Switch Over functionality in Azure PostgreSQL, transferring operations from the primary server to a secondary server during maintenance or failure."}, {"solution": "Azure_Storage_Repli_FailBack", "actions": ["Check_Storage_Account_Type", "Check_Storage_Account_Type", "Check_Storage_Account_Replication_DataLag_with_Input", "Execute_Storage_Account_Failover", "Set_Storage_Account_Replication"], "description": "Azure Storage Replication FailBack refers to the process of restoring operations to the original storage account after a failover event, ensuring data availability and consistency."}, {"solution": "Azure_Storage_Repli_FailOver", "actions": ["Check_Storage_Account_Type", "Check_Storage_Account_Type", "Check_Storage_Account_Replication_DataLag_with_Input", "Execute_Storage_Account_Failover", "Set_Storage_Account_Replication"], "description": "Azure Storage Replication Failover refers to the process of automatically switching to a secondary storage replica during an outage to maintain data availability and minimize downtime."}, {"solution": "Azure_VM_Scale_Set_AutoWF", "actions": ["VerifyVMScaleSetStatus", "StartVMScaleSet", "VerifyVMScaleSetStatus", "StopVMScaleSet", "VerifyVMScaleSetStatus"], "description": "Azure VM Scale Set AutoWF refers to the automatic scaling feature in Azure VM Scale Sets, where the system adjusts the number of virtual machine instances based on workload demands for optimal performance and cost efficiency."}, {"solution": "CrossvCenterVMMigration", "actions": ["CrossvCenterVMMigration", "CrossvCenterVMMigration"], "description": "CrossvCenter VMMigration refers to migrating virtual machines between different vCenter Server instances in VMware environments for seamless resource management."}, {"solution": "DB2_10.5_SwitchBack", "actions": ["ExecuteCheckOSCommand", "ExecuteCheckOSCommand", "DB2IsDatabasePrimary", "DB2IsDatabaseStandby", "DB2IsHADRRolePrimary", "DB2IsHADRRoleStandby", "DB2IsHADRStatePEER", "DB2IsHADRStatePEER", "DB2VerifyLogPosition", "DB2VerifyLogGap", "DB2QuiescDatabase", "DB2IsDatabaseQuiesced", "DB2UnquiescDatabase", "DB2IsDatabaseActive", "DB2Terminate", "ExecuteCheckOSCommand", "ExecuteCheckOSCommand", "ExecuteCheckOSCommand", "DB2IsDatabasePrimary", "DB2IsDatabaseStandby", "DB2IsHADRRolePrimary", "DB2IsHADRRoleStandby", "DB2IsHADRStatePEER", "DB2IsHADRStatePEER", "ExecuteCheckOSCommand"], "description": "DB2_10.5_SwitchBack refers to the Switch Back functionality in DB2 10.5, where operations are returned to the primary database after a failover or migration event for recovery and continuity."}, {"solution": "DB2_10.5_SwitchOver", "actions": ["ExecuteCheckOSCommand", "ExecuteCheckOSCommand", "DB2IsDatabasePrimary", "DB2IsDatabaseStandby", "DB2IsHADRRolePrimary", "DB2IsHADRRoleStandby", "DB2IsHADRStatePEER", "DB2IsHADRStatePEER", "DB2VerifyLogPosition", "DB2VerifyLogGap", "DB2QuiescDatabase", "DB2IsDatabaseQuiesced", "DB2UnquiescDatabase", "DB2IsDatabaseActive", "DB2Terminate", "ExecuteCheckOSCommand", "ExecuteCheckOSCommand", "ExecuteCheckOSCommand", "DB2IsDatabasePrimary", "DB2IsDatabaseStandby", "DB2IsHADRRolePrimary", "DB2IsHADRRoleStandby", "DB2IsHADRStatePEER", "DB2IsHADRStatePEER", "ExecuteCheckOSCommand"], "description": "DB2_10.5_SwitchOver refers to the Switch Over functionality in DB2 10.5, where operations are transferred from the primary database to a standby database for maintenance or failover."}, {"solution": "DT_FullServer-FB", "actions": ["VerifyJobStatus", "DT_StopJob", "DTJobCheck_CanStart", "DT_StartJob", "DisplayAlert", "VerifyFailOver", "ExecuteFailOver", "VerifyJobStatus", "VerifyJobReverse", "ExecuteReverse", "CheckStatusExecuteReverse", "VerifyJobStatus"], "description": "DT_FullServer-FB refers to a full server deployment with Failback functionality, where operations are restored to the original server after a failover event, ensuring system recovery and continuity."}, {"solution": "DT_FullServer_FO", "actions": ["VerifyJobStatus", "DT_StopJob", "DTJobCheck_CanStart", "DT_StartJob", "DisplayAlert", "VerifyFailOver", "ExecuteFailOver", "VerifyJobStatus", "VerifyJobReverse", "ExecuteReverse", "CheckStatusExecuteReverse", "VerifyJobStatus"], "description": "DT_FullServer_FO refers to a full server deployment with Failover functionality, where operations are automatically transferred to a secondary server during a failure to maintain availability and minimize downtime."}, {"solution": "ExchangeDAG_2016_SB", "actions": ["ExchDAG_StartDAGAtPRMailboxServer", "ExchDAG_StartDAGAtPRMailboxServer", "DisplayAlert", "VerifyTestReplicationHealthPassedStatusExceptDatabaseAvailability", "VerifyReplayQueueStatusforAllMailboxDB", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "VerifyServerComponentActiveStateforallComponent", "VerifyPrimaryActiveManagerStateatPR", "MovePrimaryActiveManagertoTarget", "VerifyPrimaryActiveManagerStateatPR", "VerifyDatabaseCopyActivationPolicySettingforMailboxServer", "MoveMailboxDatabaseToDRServer", "MoveMailboxDatabaseToDRServer", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "RedirectMailboxServerMessageQueuetoOtherServer", "CheckTransportSeverQueueStatus"], "description": "ExchangeDAG_2016_SB refers to a Switch Back (SB) functionality in Microsoft Exchange Server 2016's Database Availability Group (DAG), allowing automatic failback to the primary server after a failover event for seamless service restoration."}, {"solution": "ExchangeDAG_2016_SO", "actions": ["VerifyTestReplicationHealthPassedStatusExceptDatabaseAvailability", "VerifyReplayQueueStatusforAllMailboxDB", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "VerifyServerComponentActiveStateforallComponent", "VerifyPrimaryActiveManagerStateatPR", "MovePrimaryActiveManagertoTarget", "VerifyPrimaryActiveManagerStateatPR", "VerifyDatabaseCopyActivationPolicySettingforMailboxServer", "MoveMailboxDatabaseToDRServer", "MoveMailboxDatabaseToDRServer", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "StopDAGatPrimarySiteWithoutConfigurationOnly", "CheckSiteLevelMailboxAllServerStatusunderDAG", "RedirectMailboxServerMessageQueuetoOtherServer", "CheckTransportSeverQueueStatus"], "description": "ExchangeDAG_2016_SO refers to a Switch Over (SO) functionality in Microsoft Exchange Server 2016's Database Availability Group (DAG), where operations are manually or automatically switched to a secondary server for maintenance or failover."}, {"solution": "ExchangeDAG_2019_SB", "actions": ["StartDAGOnMailBoxServer", "StartDAGOnMailBoxServer", "CheckSiteLevelMailboxAllServerStatusunderDAG", "VerifyTestReplicationHealthPassedStatusExceptDatabaseAvailability", "VerifyTestReplicationHealthPassedStatusExceptDatabaseAvailability", "VerifyReplayQueueStatusforAllMailboxDB", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "VerifyPrimaryActiveManagerStateatPR", "MovePrimaryActiveManagertoTarget", "VerifyPrimaryActiveManagerStateatPR", "MoveMailboxDatabaseToDRServer", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "RedirectMailboxServerMessageQueuetoOtherServer", "CheckTransportSeverQueueStatus"], "description": "ExchangeDAG_2019_SB refers to the Switch Back (SB) functionality in Microsoft Exchange Server 2019's Database Availability Group (DAG), allowing failback to the primary server after a failover to restore normal operations."}, {"solution": "ExchangeDAG_2019_SO", "actions": ["VerifyTestReplicationHealthPassedStatusExceptDatabaseAvailability", "VerifyReplayQueueStatusforAllMailboxDB", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "VerifyServerComponentActiveStateforallComponent", "VerifyPrimaryActiveManagerStateatPR", "MovePrimaryActiveManagertoTarget", "VerifyPrimaryActiveManagerStateatPR", "VerifyDatabaseCopyActivationPolicySettingforMailboxServer", "MoveMailboxDatabaseToDRServer", "VerifyMailboxDBMountedStatusatPR_SiteLevel", "VerifyMailboxDBMountedStatusatPR_ServerLevel", "VerifyMailboxDBHealthyStatusatDR_SiteLevel", "VerifyMailboxDBHealthyStatusatDR_ServerLevel", "StopDAGatPrimarySiteWithoutConfigurationOnly", "CheckSiteLevelMailboxAllServerStatusunderDAG", "RedirectMailboxServerMessageQueuetoOtherServer", "CheckTransportSeverQueueStatus"], "description": "ExchangeDAG_2019_SO refers to the Switch Over (SO) functionality in Microsoft Exchange Server 2019's Database Availability Group (DAG), enabling the transfer of operations to a secondary server for maintenance or failover."}, {"solution": "HyperV_Cluster_SB_New", "actions": ["VerifyReplicationState", "VerifyReplicationMode", "CheckVMReplicationMode", "VerifyForwardRepliConnectionconfig", "VerifyReverseRepliConnectionconfig", "StopVM", "CheckVMState", "StartVMFailOver", "VerifyPreparedForFailOverStatus ", "FailsOverReplicaVM", "Verify_FailedOverWaitingCompletion_Status", "ChangeReplicationmode", "VerifyReplicationState", "VerifyReplicationMode", "CheckVMReplicationState", "VerifyReplicationMode", "StartVM", "VerifystatusofVM", "CheckVMState"], "description": "HyperV_Cluster_SB_New refers to a new Switch Back (SB) functionality in a Hyper-V cluster, allowing failback to the primary node after a failover for seamless operation recovery."}, {"solution": "HyperV_Cluster_SO_New", "actions": ["VerifyReplicationState", "VerifyReplicationMode", "VerifyReplicationMode", "VerifyForwardRepliConnectionconfig", "VerifyReverseRepliConnectionconfig", "ShutdownVM", "VerifystatusofVM", "StartFailover ", "VerifyPreparedForFailoverstatus ", "FailsOverReplicaVM", "VerifyFailedOverWaitingCompletionStatus", "ChangeReplicationmode", "VerifyReplicationState", "VerifyReplicationMode", "VerifyReplicationState", "VerifyReplicationMode", "StartVM", "CheckVMState", "VerifystatusofVM"], "description": "HyperV_Cluster_SO_New refers to a new Switch Over (SO) functionality in a Hyper-V cluster, transferring operations to a secondary node for high availability and minimal downtime."}, {"solution": "Linux_MySQL_NLS_SB", "actions": ["VerifyMasterLogFileAndPositiononMasterSlaveServer", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCPL", "ExecuteCheckMysqlDBCommand", "ExecuteMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "StopSlave", "ExecuteCheckMysqlDBCommand", "ExecuteCPL", "ExecuteMysqlDBCommand", "ChangeMASTERToMasterHostAndLogFile", "ExecuteMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand"], "description": "Linux_MySQL_NLS_SB refers to a Switch Back (SB) functionality for MySQL on Linux, enabling failback to the primary server after a failover event for recovery and continuity."}, {"solution": "Linux_MySQL_NLS_SB", "actions": ["VerifyMasterLogFileAndPositiononMasterSlaveServer", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCPL", "ExecuteCheckMysqlDBCommand", "ExecuteMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "StopSlave", "ExecuteCheckMysqlDBCommand", "ExecuteCPL", "ExecuteMysqlDBCommand", "ChangeMASTERToMasterHostAndLogFile", "ExecuteMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand"], "description": "Linux_MySQL_NLS_SB refers to the Switch Back (SB) functionality for MySQL on Linux, restoring operations to the primary server after a failover for seamless recovery."}, {"solution": "Linux_MySQL_NLS_SO", "actions": ["VerifyMasterLogFileAndPositiononMasterSlaveServer", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCPL", "ExecuteCheckMysqlDBCommand", "ExecuteMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "StopSlave", "ExecuteCheckMysqlDBCommand", "ExecuteCPL", "ExecuteMysqlDBCommand", "ChangeMASTERToMasterHostAndLogFile", "ExecuteMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand"], "description": "Linux_MySQL_NLS_SO refers to the Switch Over (SO) functionality for MySQL on Linux, where operations are transferred to a secondary server for maintenance or failover to ensure continuous availability."}, {"solution": "Linux_MySQL_NLS_SO", "actions": ["VerifyMasterLogFileAndPositiononMasterSlaveServer", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCPL", "ExecuteCheckMysqlDBCommand", "ExecuteMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "StopSlave", "ExecuteCheckMysqlDBCommand", "ExecuteCPL", "ExecuteMysqlDBCommand", "ChangeMASTERToMasterHostAndLogFile", "ExecuteMysqlDBCommand", "ExecuteCheckMysqlDBCommand", "ExecuteCheckMysqlDBCommand"], "description": "Linux_MySQL_NLS_SO refers to the Switch Over (SO) functionality for MySQL on Linux, transferring operations to a secondary server for failover or maintenance."}, {"solution": "MongoDB_SB", "actions": ["CheckHealthStatusUP", "CheckHealthStatusUP", "CheckSecondaryState", "CheckPrimaryState", "SetPriorityoftheReplicaSet", "CheckReplicationLagStatus"], "description": "MongoDB_SB refers to the Switch Back functionality in MongoDB, allowing operations to return to the primary server after a failover event for recovery and continuity."}, {"solution": "MongoDB_SO", "actions": ["CheckHealthStatusUP", "CheckHealthStatusUP", "CheckSecondaryState", "CheckPrimaryState", "SetPriorityoftheReplicaSet", "CheckReplicationLagStatus"], "description": "MongoDB_SO refers to the Switch Over functionality in MongoDB, where operations are transferred to a secondary node during maintenance or failure to ensure high availability."}, {"solution": "MSSQL_Mirroring_2K19_SB", "actions": ["CheckSqlServerRunningStates", "CheckDatabaseRoleAndMirrorState", "CheckDatabaseRoleAndMirrorState", "CheckSqlServerRunningStates", "CheckDatabaseRoleAndMirrorState", "CheckDatabaseRoleAndMirrorState", "ExecuteDBClusterChangeOver", "CheckDatabaseRoleAndMirrorState", "CheckDatabaseRoleAndMirrorState"], "description": "MSSQL_Mirroring_2K19_SB refers to the Switch Back (SB) functionality in SQL Server 2019 mirroring, allowing failback to the primary server after a failover to ensure continuity and data recovery."}, {"solution": "MSSQL_Mirror_2K19_SO", "actions": ["CheckSqlServerRunningStates", "CheckSqlServerRunningStates", "CheckDatabaseRoleAndMirrorState", "CheckDatabaseRoleAndMirrorState", "CheckDatabaseRoleAndMirrorState", "CheckDatabaseRoleAndMirrorState", "ExecuteDBClusterChangeOver", "CheckDatabaseRoleAndMirrorState", "CheckDatabaseRoleAndMirrorState"], "description": "MSSQL_Mirror_2K19_SO refers to the Switch Over (SO) functionality in SQL Server 2019 mirroring, where operations are transferred to the secondary server for maintenance or failover."}, {"solution": "MSSQL_NLS_2k14_SB2", "actions": ["LSSQLCheckPrimarySecondaryLogShippingExist", "LSSQLCheckDBEntryOnSecondaryServerExist", "LSSQLCheckPrimaryLogShippingExist", "LSSQLCheckDbEntryOnPrimaryServerExist", "LSSQLDisableJob", "LSSQLDisableJob", "LSSQLDisableJob", "LSSQLRunJob", "LSSQLRunJob", "LSSQLVerifyLogFileSequence", "LSSQLKillDBSessionWithTimeOut", "LSSQLSetDBSingleUserAccessMode", "LSSQLSetDBSingleUserAccessMode", "LSSQLVerifyLogFileSequence", "LSSQLKillDBSessionWithTimeOut", "LSSQLGenerateLastBackUpLog", "LSSQLSetDBSingleUserAccessMode", "LSSQLKillDBSessionWithTimeOut", "LSSQLSetDBSingleUserAccessMode", "LSSQLKillDBSessionWithTimeOut", "LSSQLRestoreLastBackupLogWithRecovery", "LSSQLVerifyLogFileSequence", "LSSQLRemovePrimarySecondaryLogShipping", "LSSQLVerifyPrimarySecondaryLogShippingExist", "LSSQLRemovePrimaryLogShipping", "LSSQLVerifyDBEntryOnSecondaryServerExist", "LSSQLVerifyPrimaryLogShippingExist", "LSSQLRemoveSecondaryLogShipping", "LSSQLVerifyDBEntryOnPrimaryServerExist", "LSSQLRemoveSecondaryJob", "LSSQLRemoveSecondaryJob", "LSSQLRemoveSecondaryJob", "LSSQLSetDBMultiUserAccessMode", "LSSQLExecutePrimaryLogShipping", "LSSQLExecuteSecondaryLogShipping", "LSSQLUpdatingBackupJobWithPRIPAddress", "LSSQLUpdatingCopyJobWithDRIPAddress", "LSSQLUpdatingRestoreJobWithDRIPAddress", "LSSQLFixOrphanUsers"], "description": "MSSQL_NLS_2k14_SB2 refers to a Switch Back (SB) functionality in SQL Server 2014 with NLS (Native Log Shipping), allowing failback to the primary server after a failover event for recovery and continuity."}, {"solution": "MSSQL_NLS_2k14_SO3", "actions": ["LSSQLCheckPrimarySecondaryLogShippingExist", "LSSQLCheckDBEntryOnSecondaryServerExist", "LSSQLCheckPrimaryLogShippingExist", "LSSQLCheckDbEntryOnPrimaryServerExist", "LSSQLDisableJob", "LSSQLDisableJob", "LSSQLDisableJob", "LSSQLRunJob", "LSSQLRunJob", "LSSQLVerifyLogFileSequence", "LSSQLKillDBSessionWithTimeOut", "LSSQLSetDBSingleUserAccessMode", "LSSQLSetDBSingleUserAccessMode", "LSSQLVerifyLogFileSequence", "LSSQLKillDBSessionWithTimeOut", "LSSQLGenerateLastBackUpLog", "LSSQLSetDBMultiUserAccessMode", "LSSQLKillDBSessionWithTimeOut", "LSSQLSetDBSingleUserAccessMode", "LSSQLKillDBSessionWithTimeOut", "LSSQLRestoreLastBackupLogWithRecovery", "LSSQLVerifyLogFileSequence", "LSSQLRemovePrimarySecondaryLogShipping", "LSSQLVerifyPrimarySecondaryLogShippingExist", "LSSQLRemovePrimaryLogShipping", "LSSQLVerifyDBEntryOnSecondaryServerExist", "LSSQLVerifyPrimaryLogShippingExist", "LSSQLRemoveSecondaryLogShipping", "LSSQLVerifyDBEntryOnPrimaryServerExist", "LSSQLRemoveSecondaryJob", "LSSQLRemoveSecondaryJob", "LSSQLRemoveSecondaryJob", "LSSQLSetDBMultiUserAccessMode", "LSSQLExecutePrimaryLogShipping", "LSSQLExecuteSecondaryLogShipping", "LSSQLUpdatingBackupJobWithPRIPAddress", "LSSQLUpdatingCopyJobWithDRIPAddress", "LSSQLUpdatingRestoreJobWithDRIPAddress", "LSSQLFixOrphanUsers"], "description": "MSSQL_NLS_2k14_SO3 refers to a Switch Over (SO) functionality in SQL Server 2014 with NLS (Native Log Shipping), where operations are transferred to a secondary server for failover or maintenance."}, {"solution": "NetAppSnapMirror", "actions": ["VerifySnapMirrorStatus", "VerifyLUNsStatus", "SnapMirrorCheckVolStatus", "SnapMirrorUpdate", "SnapMirrorBreak", "VerifySnapMirrorStatus", "SnapMirrorVolumeRestrict", "InitialiseSnapMirror", "SnapMirrorResync", "SnapMirrorVolumeRestrict", "SnapMirrorVolumeOnline"], "description": "NetApp SnapMirror is a data replication technology used in NetApp storage systems, enabling efficient backup and disaster recovery by replicating data between primary and secondary storage locations."}, {"solution": "NetApp_SnapMirror_WF", "actions": ["VerifyLUNsStatus", "SnapMirrorCheckVolStatus", "SnapMirrorBreak", "SnapMirrorVolumeRestrict", "InitialiseSnapMirror", "SnapMirrorResync", "SnapMirrorVolumeRestrict", "SnapMirrorVolumeOnline", "SnapMirrorUpdate", "VerifySnapMirrorStatus", "VerifySnapMirrorStatus"], "description": "NetApp_SnapMirror_WF refers to the Workflow (WF) of NetApp SnapMirror, automating the process of data replication and synchronization between storage systems for backup and disaster recovery."}, {"solution": "NutanixPD-SB_501", "actions": ["Check_VM_State", "Check_VM_State", "ExecuteVMPowerOff", "ExecuteVMPowerOff", "Check_VM_State", "Check_VM_State", "CheckProtectionDomainStatus", "CheckProtectionDomainStatus", "CheckCGExistInPDForActiveSite", "CheckCGExistInPDForInactiveSite", "CheckVMExistInCGUnderPDForActiveSite", "CheckVMExistInCGUnderPDForActiveSite", "CheckVMExistInCGUnderPDForInactiveSite", "CheckVMExistInCGUnderPDForInactiveSite", "VerifyIfNoReplIsPendingUnderPDForActiveSite", "VerifyIfNoReplIsPendingUnderPDForInactiveSite", "MigrateProtectionDomain", "VerifyIfNoReplIsPendingUnderPDForActiveSite", "VerifyIfNoReplIsPendingUnderPDForInactiveSite", "DisplayAlert", "ExecuteVMPowerOn", "ExecuteVMPowerOn"], "description": "NutanixPD-SB_501 refers to a Switch Back (SB) functionality in Nutanix's Power Distribution (PD) system, likely indicating a failback process to restore operations to the primary system after a failover event."}, {"solution": "NutanixPD-SO_501", "actions": ["Check_VM_State", "Check_VM_State", "ExecuteVMPowerOff", "ExecuteVMPowerOff", "Check_VM_State", "Check_VM_State", "CheckProtectionDomainStatus", "CheckProtectionDomainStatus", "CheckCGExistInPDForActiveSite", "CheckCGExistInPDForInactiveSite", "CheckVMExistInCGUnderPDForActiveSite", "CheckVMExistInCGUnderPDForActiveSite", "CheckVMExistInCGUnderPDForInactiveSite", "CheckVMExistInCGUnderPDForInactiveSite", "VerifyIfNoReplIsPendingUnderPDForActiveSite", "VerifyIfNoReplIsPendingUnderPDForInactiveSite", "MigrateProtectionDomain", "VerifyIfNoReplIsPendingUnderPDForActiveSite", "VerifyIfNoReplIsPendingUnderPDForInactiveSite", "DisplayAlert", "ExecuteVMPowerOn", "ExecuteVMPowerOn"], "description": "NutanixPD-SO_501 refers to a Switch Over (SO) functionality in Nutanix's Power Distribution (PD) system, transferring operations to a secondary system during maintenance or failure to ensure continuous availability."}, {"solution": "NutanixPD-VM-FB_101", "actions": ["CheckProtectionDomainStatusForSite", "CheckProtectionDomainStatusForSite", "CheckConsistencyGroupMemberStatusUnderPD_ActiveSite", "CheckConsistencyGroupMemberStatusUnderPD_InActiveSite", "CheckVMExistInConsistencyGroupUnderPD_ActiveSite", "CheckVMExistInConsistencyGroupUnderPD_InActiveSite", "VerifyIfNoReplicationPendingForPD_ActiveSite", "VerifyIfNoReplicationPendingForPD_InActiveSite", "ProtectionDomainActivationFailoverFromDR", "CheckProtectionDomainStatusForSite"], "description": "NutanixPD-VM-FB_101 refers to the Failback (FB) functionality in Nutanix PD for VMs, where operations return to the primary system after failover for recovery."}, {"solution": "NutanixPD-VM-FO_101", "actions": ["CheckProtectionDomainStatusForSite", "CheckProtectionDomainStatusForSite", "CheckConsistencyGroupMemberStatusUnderPD_ActiveSite", "CheckConsistencyGroupMemberStatusUnderPD_InActiveSite", "CheckVMExistInConsistencyGroupUnderPD_ActiveSite", "CheckVMExistInConsistencyGroupUnderPD_InActiveSite", "VerifyIfNoReplicationPendingForPD_ActiveSite", "VerifyIfNoReplicationPendingForPD_InActiveSite", "ProtectionDomainActivationFailoverFromDR", "CheckProtectionDomainStatusForSite"], "description": "NutanixPD-VM-FO_101 refers to a Failover (FO) functionality in Nutanix PD for virtual machines (VMs), where operations are transferred to a secondary system during failure or maintenance."}, {"solution": "Nutanix_Leap_FB", "actions": ["VerifyNutanixLeapRecoveryPlanName", "VerifyNutanixLeapRecoveryPlanName", "CheckRecoveryPointEntityVMexistAtSourceFailedAZ", "CheckRecoveryPointEntityVMsdoesnotexistAtTargetLocalAZ", "NutanixLeapRecoveryPlanValidateRecoveryPlanTargetSite", "NutanixLeapRecoveryPlanPlannedFailoverTargetSite"], "description": "Nutanix_Leap_FB refers to the Failback (FB) functionality in Nutanix Leap, which restores operations to the primary system after a failover event, ensuring recovery and continuity."}, {"solution": "Nutanix_Leap_FO", "actions": ["VerifyNutanixLeapRecoveryPlanName", "VerifyNutanixLeapRecoveryPlanName", "CheckRecoveryPointEntityVMexistAtSourceFailedAZ", "CheckRecoveryPointEntityVMsdoesnotexistAtTargetLocalAZ", "NutanixLeapRecoveryPlanValidateRecoveryPlanTargetSite", "NutanixLeapRecoveryPlanPlannedFailoverTargetSite"], "description": "Nutanix_Leap_FO refers to the Failover (FO) functionality in Nutanix Leap, where operations are transferred to a secondary system during failure or maintenance to ensure continuous availability."}, {"solution": "ODG12C_SB", "actions": ["DGVerifyDBModeAndRole", "DGCheckUserStatus", "DGJobStatus", "DGVerifyMaxSequenceNumber", "DGSwitchPrimaryToStandby", "DGMountStandby", "DGSwitchStandByToPrimary", "ShutDB", "OpenDatabase", "DGSwitchLogFile", "DGRecoverStandby"], "description": "ODG12C_SB refers to the Switch Back (SB) functionality in Oracle Database 12c, allowing operations to return to the primary database after a failover event for recovery and continuity."}, {"solution": "ODG_12C_SB", "actions": ["DGVerifyDBModeAndRole", "DGCheckUserStatus", "DGJobStatus", "DGVerifyMaxSequenceNumber", "DGSwitchPrimaryToStandby", "DGMountStandby", "DGSwitchStandByToPrimary", "ShutDB", "OpenDatabase", "DGSwitchLogFile", "DGRecoverStandby"], "description": "ODG_12C_SB refers to the Switch Back functionality in Oracle Data Guard 12c, where operations are reverted to the primary database after a failover event."}, {"solution": "ODG_12C_SO", "actions": ["DGVerifyDBModeAndRole", "DGCheckUserStatus", "DGJobStatus", "DGVerifyMaxSequenceNumber", "DGSwitchPrimaryToStandby", "DGMountStandby", "DGSwitchStandByToPrimary", "ShutDB", "OpenDatabase", "DGSwitchLogFile", "DGRecoverStandby"], "description": "ODG_12C_SO refers to the Switch Over functionality in Oracle Data Guard 12c, where operations are transferred from the primary database to a standby database for maintenance or failover."}, {"solution": "ODG_12C_SO", "actions": ["DGVerifyDBModeAndRole", "DGCheckUserStatus", "DGJobStatus", "DGVerifyMaxSequenceNumber", "DGSwitchPrimaryToStandby", "DGMountStandby", "DGSwitchStandByToPrimary", "ShutDB", "OpenDatabase", "DGSwitchLogFile", "DGRecoverStandby"], "description": "ODG_12C_SO refers to the Switch Over functionality in Oracle Data Guard 12c, transferring operations to a standby database for maintenance or failover."}, {"solution": "OracleRsync_SwitchBack", "actions": ["PreShutRedoCtrlScript", "SwitchShutPrimaryDB", "CopyRedoControlFile", "Ora_Rsync_ReplicateStandByTraceFile", "ExecuteOSCommand", "ReplicateRSyncFilePosix", "ReplicateRSyncFoldersPosix", "StartDBReadWrite", "Ora_Rsync_ReplicateStandByControlFile", "ReplicateRSyncFoldersPosix", "StartDBStandBy"], "description": "OracleRsync_SwitchBack refers to the Switch Back functionality in Oracle Rsync, where data synchronization operations revert to the primary system after a failover or migration event for recovery."}, {"solution": "OracleRsync_SwitchOver", "actions": ["PreShutRedoCtrlScript", "SwitchShutPrimaryDB", "CopyRedoControlFile", "Ora_Rsync_ReplicateStandByTraceFile", "ExecuteOSCommand", "ReplicateRSyncFilePosix", "ReplicateRSyncFoldersPosix", "StartDBReadWrite", "Ora_Rsync_ReplicateStandByControlFile", "ReplicateRSyncFoldersPosix", "StartDBStandBy"], "description": "OracleRsync_SwitchOver refers to the Switch Over functionality in Oracle Rsync, transferring data synchronization operations to a secondary system for maintenance or failover."}, {"solution": "Postgres10.5_SwitchBack", "actions": ["VerifyPRReplicationStatus", "VerifyDRReplicationStatus", "VerifyDatabaseClusterStatus", "VerifyDatabaseClusterStatus", "VerifyPRAndDRWalLSNMatches", "CreateRecoveryConfAtPR", "StopPostgreSQLServer", "VerifyPRDBClusterShutdown", "VerifyDRTriggerFilePath", "ExecuteDRFailingOverToStandbyServer", "RestartPostgreSQLServer", "RestartPostgreSQLServer"], "description": "Postgres10.5_SwitchBack refers to the Switch Back functionality in PostgreSQL 10.5, where operations are reverted to the primary database after a failover or migration event for recovery and continuity."}, {"solution": "Postgres10.5_SwitchOver", "actions": ["VerifyPRReplicationStatus", "VerifyDRReplicationStatus", "VerifyDatabaseClusterStatus", "VerifyDatabaseClusterStatus", "VerifyPRAndDRWalLSNMatches", "CreateRecoveryConfAtPR", "StopPostgreSQLServer", "VerifyPRDBClusterShutdown", "VerifyDRTriggerFilePath", "ExecuteDRFailingOverToStandbyServer", "RestartPostgreSQLServer", "RestartPostgreSQLServer"], "description": "Postgres10.5_SwitchOver refers to the Switch Over functionality in PostgreSQL 10.5, where operations are transferred from the primary database to a standby database for maintenance or failover."}, {"solution": "SO_ODG19C_DB_IMP19C", "actions": ["WAITFORWORKFLOWACTION", "DGVerifyDBModeAndRole", "DGCheckUserStatus", "DGJobStatus", "DGVerifyMaxSequenceNumber", "DGSwitchPrimaryToStandby", "DGMountStandby", "DGSwitchStandByToPrimary", "ShutDB", "OpenDatabase", "DGSwitchLogFile", "DGRecoverStandby"], "description": "SO_ODG19C_DB_IMP19C refers to the Switch Over (SO) functionality in Oracle Data Guard 19c, transferring database operations from the primary to a standby database, with an import from version 19c."}, {"solution": "SO_RAC19C_DB_IMP19C", "actions": ["WAITFORWORKFLOWACTION", "DGVerifyDBModeAndRole", "DGVerifyMaxSequenceNumber", "DGSwitchLogFile", "DGShutDownPrimary", "DGSwitchLogFile", "DGSwitchPrimaryToStandby", "DGMountStandby", "DGSwitchStandByToPrimary", "ShutDB", "OpenDatabase", "DGSwitchLogFile", "DGMountStandby", "DGRecoverStandby"], "description": "SO_RAC19C_DB_IMP19C refers to the Switch Over (SO) functionality in Oracle RAC 19c, transferring database operations from the primary to a standby database, with a database import from version 19c."}, {"solution": "SO_Start_GG_Replication_DR", "actions": ["WAITFORWORKFLOWACTION", "WAITFORWORKFLOWACTION", "StartGoldenGateGroup", "CheckGoldenGateGroupStatusIsRunning", "StartGoldenGateGroup", "CheckGoldenGateGroupStatusIsRunning", "StartGoldenGateGroup", "CheckGoldenGateGroupStatusIsRunning"], "description": "SO_Start_GG_Replication_DR refers to starting GoldenGate (GG) Replication in a Disaster Recovery (DR) scenario, where data replication is initiated to ensure continuity of operations during failover or switch over events."}, {"solution": "SO_Stop_GG_Replication_Prod", "actions": ["CheckGoldenGateGroupStatusIsRunning", "CheckGoldenGateGroupStatusIsRunning", "StopGoldenGateGroup", "StopGoldenGateGroup", "CheckGoldenGateGroupStatusIsRunning", "StopGoldenGateGroup"], "description": "SO_Stop_GG_Replication_Prod refers to stopping GoldenGate (GG) Replication in a Production environment, typically during maintenance or a switch over event."}, {"solution": "SRM8.8_SwitchBack", "actions": ["SRMCheckRecoveryPlanState", "SRMCheckProtectionGroupState", "SRMExecuteTestRecoveryPlan", "SRMCheckRecoveryPlanState", "SRMCheckProtectionGroupState", "SRMExecuteCleanupRecoveryPlan", "SRMCheckRecoveryPlanState", "SRMCheckProtectionGroupState", "SRMPerformPlannedMigrationFailover", "SRMCheckProtectionGroupState", "SRMCheckProtectionGroupState", "SRMReProtectRecoveryPlan", "SRMCheckRecoveryPlanState", "SRMCheckProtectionGroupState"], "description": "SRM8.8_SwitchBack refers to the Switch Back functionality in VMware Site Recovery Manager (SRM) 8.8, where operations are returned to the primary site after a failover event for recovery."}, {"solution": "SRM8.8_SwitchOver", "actions": ["SRMCheckRecoveryPlanState", "SRMCheckProtectionGroupState", "SRMExecuteTestRecoveryPlan", "SRMCheckRecoveryPlanState", "SRMCheckProtectionGroupState", "SRMExecuteCleanupRecoveryPlan", "SRMCheckRecoveryPlanState", "SRMCheckProtectionGroupState", "SRMPerformPlannedMigrationFailover", "SRMCheckRecoveryPlanState", "SRMCheckProtectionGroupState", "SRMReProtectRecoveryPlan", "SRMCheckRecoveryPlanState", "SRMCheckProtectionGroupState"], "description": "SRM8.8_SwitchOver refers to the Switch Over functionality in VMware Site Recovery Manager (SRM) 8.8, where operations are transferred from the primary site to a secondary site for maintenance or failover."}, {"solution": "Veeam_FO_FB", "actions": ["FailOverReplicationJob", "FailBackReplicationJob", "FailBackCommitReplicationJob", "CreateReplicationJobFromBackupFiles"], "description": "Veeam_FO_FB refers to the Failover (FO) and Failback (FB) functionality in Veeam, enabling the transfer of operations to a backup site during failover and returning to the primary site after recovery."}, {"solution": "Vmware_Action", "actions": ["CheckVMExist", "VMCheckRunning", "CheckVMToolStatus", "ExecuteCheckVMCommand", "UpdateVMNetworkInfo", "ExecuteCheckVMCommand", "ExecuteCheckVMCommand", "ExecuteCheckVMCommand", "ExecuteCheckVMCommand"], "description": "Vmware_Action refers to a specific task or operation performed within a VMware environment, such as provisioning, configuration, or management of virtual machines, hosts, or storage."}, {"solution": "VMware_Auto1", "actions": ["UnRegisterVM", "RegisterVM", "Vcenter_CheckVMExist", "ExecuteNCheOpVmWarePowerCLICommand", "ExecuteVmWarePowerCLICommand", "ExecuteNCheOpVmWarePowerCLICommand", "Vcenter_VMPowerOn", "Vcenter_VMCheckRunning", "ExecuteNCheOpVmWarePowerCLICommand", "ExecuteNCheOpVmWarePowerCLICommand", "Vcenter_CheckVMToolStatus", "ExecuteNCheOpVmWarePowerCLICommand", "UpdateVMNetworkInfo", "ExecuteNCheOpVmWarePowerCLICommand"], "description": "VMware_Auto1 refers to an automated process or configuration within a VMware environment, typically for tasks like provisioning, scaling, or managing virtual machines or infrastructure automatically."}, {"solution": "VMware_Automation", "actions": ["Vcenter_CheckVMExist", "Vcenter_VMCheckRunning", "Vcenter_CheckVMToolStatus", "WinRemoveGuestVMSystemFromDomain", "ExecuteCheckVMCommand", "WinChangeGuestVMHostName", "ExecuteNCheOpVmWarePowerCLICommand", "WinJoinGuestVMSystemToDomain", "ExecuteCheckVMCommand"], "description": "VMware_Automation refers to using automated tools in VMware environments to manage, provision, and configure virtual machines and infrastructure without manual intervention."}, {"solution": "VMWare_RDM_Disk", "actions": ["CheckLUNPathExistAtVMsHost", "CheckRDMTypeDiskAttachedToVM", "CheckExistingNonRDMTypeDiskAttachedToVM", "AddRDMDiskToVM", "AddExistingNonRDMTypeDiskToVM", "DisplayAlert", "AddRDMDiskToVM", "AddExistingNonRDMTypeDiskToVM"], "description": "VMware_RDM_Disk refers to a Raw Device Mapping in VMware that provides direct access to a physical storage device, bypassing the VMFS for improved performance and compatibility."}, {"solution": "VM_ActionWith_EXE_CMD", "actions": ["Vcenter_PowerOffVM", "CheckVMExist", "CreateLinkedClone", "Vcenter_PowerOnVM", "VMCheckRunning", "CheckVMToolStatus", "ExecuteCheckVMCommand", "ExecuteCheckVMCommand", "ExecuteCheckVMCommand", "ExecuteCheckVMCommand", "ExecuteCheckVMCommand", "ExecuteCheckVMCommand", "ExecuteCheckVMCommand", "Vcenter_PowerOffVM", "RemoveVM", "Vcenter_PowerOnVM"], "description": "VM_ActionWith_EXE_CMD refers to executing a command or script (.exe) on a virtual machine to perform specific actions or tasks within the VM environment."}, {"solution": "VM_DataStore_Mount_Unmount", "actions": ["PowerOffDATASTORE", "ExecuteVmWarePowerCLICommand", "ExecuteNCheOpVmWarePowerCLICommand", "ExecuteNCheOpVmWarePowerCLICommand", "UnRegisterDATASTORE", "CheckDataStoreCanBeUnmount", "UnmounFromAllEsxiHost", "DeAttachLUN", "ReScanVMFSforStorageLUN", "RescanHBAToFindNewStorageLUN", "AtatchLunOnMultipleESXiHost", "ReScanVMFSforStorageLUN", "ReScanVMFSforStorageLUN", "MountDatastoreOnAllEsxiHost", "RegisterVMWithoutESXiHost", "RegisterVMWithoutESXiHost", "PowerOnDATASTORE"], "description": "VM_DataStore_Mount_Unmount refers to the process of attaching (mounting) or detaching (unmounting) a datastore to/from a virtual machine, allowing access to storage resources."}, {"solution": "VM_Updated_EsxiActions", "actions": ["UnmounFromAllEsxiHost", "DeAttachLUN", "AtatchLunOnMultipleESXiHost", "MountDatastoreOnAllEsxiHost", "PowerOffVM", "UnRegisterVM", "RegisterVMWithoutESXiHost", "PowerOnVM"], "description": "VM_Updated_EsxiActions refers to the actions performed on a virtual machine after updating the ESXi host, such as configuration changes or optimizations."}]