Force-enable the node state of a virtual server,
Force-disable the node state of a virtual server,
Enable a Local Traffic Manager (LTM) virtual server,
Disable a Local Traffic Manager (LTM) virtual server,
Check the state of the Local Traffic Manager (LTM) virtual server,
Check if the default route pools name exists in the F5 Load Balancer,
Check if the F5 Load Balancer name exists and is enabled,
Check the API token expiry date,
Migrate the F5 Load Balancer default route pools name change,
Modify NS1 DNS records with multiple A-type IP addresses,
Modify NS1 DNS record of CNAME type,
Modify NS1 DNS record of A-type with a single IP address,
Check if NS1 DNS record is of CNAME type,
Check if NS1 DNS record is of A-type with a single IP address,
Check if NS1 DNS record is of A-type with multiple IP addresses,
Query DNS records using INFO<PERSON>OX,
Modify DNS records using INFOBLOX,
Delete DNS records using INFOBLOX,
View DNS query statistics with INF<PERSON><PERSON>OX,
Add DNS records using INFOBLOX,
Delete a CNAME record,
Enable or disable DNS record mapping,
Check the DNS record status,
Check if a DNS record exists in a specific view and zone,
Check if a DNS zone exists in a DNS view,
Check if a DNS view exists,
Modify a CNAME record,
Add a CNAME record,
Check VLAN configuration,
Check static route configuration,
Update firewall policies,
Monitor OpenShift clusters,
Scale up or down OpenShift pods and deployment counts,
Check the pod deployment count with all pods in ready status,
Scale up or down OpenShift pods in replica sets,
Check the pod replica set count with all pods in ready status,
Scale up or down OpenShift pods in stateful sets,
Check the pod stateful set count with all pods in ready status,
Scale up or down OpenShift machine sets machine count,
Check the OpenShift machine set machine count,
Stop a virtual machine,
Start a virtual machine,
Check the status of a specific virtual machine,
Test pod deployment in OpenShift,
Update the AirGap action in IM (Incident Management),
Enable a port in IM (Incident Management),
Verify if a port is enabled in IM (Incident Management),
Verify if a port is disabled in IM (Incident Management),
Disable a port in IM (Incident Management),
Add a route in Nexus,
