Redis_CheckEmptyArray_DR,
Redis_CheckEmptyArray_PR,
Redis_VerifySingleTableRowCount_DR,
Redis_VerifySingleTableRowCount_PR,
Postgres_StartService,
CheckContent,
AddContentRecovery,
CheckRecoveryFilePrimary,
VerifyRecoveryDone,
RestartService,
CreateTriggerFile,
CheckRecoveryFile,
Postgres_StopService,
VerifyRecoveryStatus,
VerifyServerRuuning,
VerifyPostGresSQL_TriggerFileNameAndPath,
VerifyPostGresSQL_DR_ReplicationStatus,
VerifyPostGresSQL_PR_ReplicationStatus,
VerifyPostGresSQL_DatabaseClusterState,
VerifyPostGresSQLXLogLSNMatching,
StartPostgreSQLServer,
RestartPostgreSQLServer,
ExecuteDRFailingOverToStandbyServer,
VerifyDRTriggerFilePath,
VerifyPRDBClusterShutdown,
StopPostgreSQLServer,
CreateRecoveryConfAtPR,
Verify_PRAndDRWalLSNMatches,
Verify_DatabaseClusterStatus,
ExecutePostgresSQLCommand,
ClusterChangeOver,
PromoteClusterState,
VerifyReplicationStatusDR,
VerifyClusterStatus,
VerifyPostgresServiceStatus,
VerifyCurrentWalLocation,
VerifyPostgreSQLRecoveryStatus,
VerifyReplicationStatusPR,
VerifyClusterState,
PostgresMonitoring,
PG_Recovery_Sed,
StartPostgresSQLService,
PG_RecoveryCat,
StopPostgreSQLService,
PG_RewindSynchronizeTwoCluster,
PG_RecoveryConf_MV,
pg_rewindtest,
CreateStandByFileInSecondarySer,
CustomActionTest,
PowerShellTest,
TestAction,
StartSybaseServer,
VerifySybaseServerIsUp,
VerifySybaseServerIsDown,
SybaseShutDownDB,
SybaseCheckPointDB,
ReplicateSybaseBcpTable,
DumpTransactionLogFilewithStandbyAccess,
SybaseOnlineDBForStandbyAccess,
SybaseOnlineDB,
LoadTransactionLogFile,
ExecuteSybaseISQLCommand,
GenerateSybaseLastTransactionLog,
ExecuteSybaseBcpInCommand,
ExecuteSybaseBcpOutCommand,
VerifyDBStatus,
VerifyBackupServerStatus,
VerifySybaseDataServerStatus,
CopySybaseLastTransactionLog,
ExecuteCheckSybaseISQLcommand,
SybaseReplicationResumeConnection,
SybaseStartReplicationAgentOnPrimary,
SybaseReplicationCheckRoleSwitchStatus,
SybaseReplicationSwitchOverStatus,
SybasePrimaryToStandby,
VerifySybaseReplicationAgentISDown,
SybaseStopReplicationAgentOnPrimary,
VerifySybaseStandbyDBStatus,
VerifySybasePrimaryDBStatus,
test_command_Copy9011,
TestWW_Copy8328,
VerifyDatabaseStatu_Copy3563,
LSSQL_RstreLastBckupLogwthNoRcvryWithPrchck_Copy1761,
LSSQL_EnableLogshippingwithTargetDBRestoring_Copy2143,
LSSQL_RestoreLastlogwithStandbyDR_Copy1751,
LSSQL_RestoreLastlogwithRecoveryDR_Copy6317,
LSSQL_ValidateBackupwithPrimarySecondary_Copy8706,
LSSQL_CopyTailLogBackupDR_Copy4404,
LSSQL_VerifyPrimarybackuptransactionlogExist_Copy4304,
LSSQLExecuteSecondaryLogShipping_Copy6983,
LSSQLExecutePrimaryLogShippingReverse_Copy6024,
VerifyJobNameAssociatedWithLogShipping_Copy9987,
VerifySourceAndDestBackupDirectory_Copy626,
VerifyBackupDirectoryandSharePath_Copy2758,
Execute2DRSitePrimaryLogShipping_Copy3220,
LSSQLRestoreLastBackupLogWithRecovery_Copy8040,
Execute3SiteSecondaryLogShipping_Copy7757,
Execute3DRSitePrimaryLogShipping_Copy2643,
RestoreLogWithStandby_Copy1265,
Verify3DRSiteLogFileSequence_Copy9302,
LSSQLMakeDBWritableLastRestoredFileFailOver_Copy1308,
LSSQLExecuteSecondaryLogShippingJobSchedule_Copy8777,
LSSQLExecutePrimaryLogShippingJobSchedule_Copy504,
SQLNLSCheckDatabaseMode_Copy5504,
SQLNLSCheckDatabaseState_Copy5936,
VerifyDatabaseMirroringStatus_Copy8342,
MirroringFailOver_Copy1092,
KillMSSQLProcessByName_Copy4082,
ILSSQLRemoveRestoreJob_Copy4676,
ILSSQLRemoveCopyJob_Copy8019,
AttachDB_Copy9781,
SetDBOptionSingleUser_Copy2035,
DBOffline_Copy5634,
LSSQLMigrateRoles_Copy5841,
ILSSQLMigrateLogins_Copy6748,
ILSSQLExecuteSecondaryLogShipping_Copy8481,
ILSSQLExecutePrimaryLogShipping_Copy1013,
ILSSQLRemoveBackUpJob_Copy8703,
ILSSQLRunBackUpJob_Copy1312,
LinuxDisMount_Copy8956,
LinuxMount_Copy1579,
ILSSQLMigrateRoles_Copy6694,
LSSQLMigrateLogins_Copy2225,
DetachSQLDatabase_Copy6169,
AttachSQLDatabase_Copy4977,
test_NLS_Copy697,
LSSQL_RemoveSecondaryJob_Copy5188,
LSSQL_Check_DbEntry_OnPrimaryServer_Exist_Copy6825,
LSSQL_Check_DBEntry_OnSecondaryServer_Exist_Copy3771,
LSSQL_Check_PrimaryLogshipping_Exist_Copy1013,
LSSQLCheckPrimarySecondaryLogshippingExist_Copy153,
LSSQLRestoreSecondaryLogShipping_Copy6109,
LSSQLRestorePrimaryLogShipping_Copy8024,
LSSQLRestoreLastBackUpLogWithNoRecovery_Copy5534,
LSSQLSetDBMultiUserAccessMode_Copy3724,
LSSQLGenerateLastBackUpLog_Copy8879,
LSSQLKillAllSession_Copy6006,
MSSQL_NLS_Monitoring_Copy5990,
FixOrphanUsers_Copy144,
ILSSQLSetDRDBInMultiUserAccessMode_Copy7365,
LSSQLUpdatingRestoreJobWithDRIPAddress_Copy7250,
ILSSQLUpdatingRestoreJobWithDRIPAddress_Copy3921,
LSSQLUpdatingCopyJobWithDRIPAddress_Copy1697,
ILSSQLUpdatingCopyJobWithDRIPAddress_Copy7043,
ILSSQLUpdatingBackupJobWithPRIPAddress_Copy6952,
LSSQLUpdatingBackupJobWithPRIPAddress_Copy5431,
LSSQLRemovePrimarySecondaryLogShipping_Copy3349,
ILSSQLRemovePrimaryLogShipping_Copy9087,
LSSQLRemoveSecondaryLogShipping_Copy6454,
ILSSQLRemoveSecondaryLogShipping_Copy9524,
LSSQLRemovePrimaryLogShipping_Copy7851,
ILSSQLRemovePrimarySecondaryLogShipping_Copy3414,
LSSQLRestoreDatabaseWithRecovery_Copy8869,
ILSSQLRestoreDatabaseWithRecovery_Copy3308,
ILSSQLSetDBSingleUserAccessModeDR_Copy2666,
ILSSQLKillSessionDR_Copy1484,
ILSSQLSetDBMultiUserAccessModePrimary_Copy6753,
LSSQLVerifyDBSingleUserAccessMode_Copy3803,
ILSSQLVerifyPRDBSingleUserAccessMode_Copy4681,
LSSQLSetDBSingleUserAccessMode_Copy9415,
ILSSQLSetDBSingleUserAccessModePrimary_Copy4551,
ImportLoginRolesOnDRServer_Copy5070,
ImportLoginsOnDRServer_Copy2279,
ExportLoginRolesFromProductionServer_Copy951,
ExportLoginFromProductionServer_Copy8248,
ILSSQLKillSessionPrimary_Copy3833,
LSSQLFixOrphanUsers_Copy4095,
LSSQLEnableJob_Copy978,
LSSQLGenerateLastBackUpLogWithNoRecovery_Copy9108,
LSSQLKillDBSessionWithTimeOut_Copy8578,
LSSQLRunJob_Copy7046,
LSSQLDisableJob_Copy903,
VerifyLogFileSequence_Copy2486,
RunRestoreJob_Copy5153,
RunCopyJob_Copy7219,
DisableRestoreJob_Copy9208,
DisableCopyJob_Copy5245,
DisableBackUpJob_Copy5566,
BackupJobStatus_Copy9896,
TransactionLogShippingState_Copy5868,
DatabaseAccessMode_Copy5213,
DatabaseRecoveryModel_Copy1968,
VerifyDatabaseRecoveryMode_Copy4754,
VerifyUpdateabilityState_DR_Copy9100,
VerifyUpdateabilityState_PR_Copy8009,
VerifyDatabaseStatus_Copy2448,
ExecuteMSSQLCommand,
ExecuteCheckMSSQLCommand,
CompareSQLServerTableRowCountDR,
CompareSQLServerTableRowCountPR,
UnjoinedDBfromAvailabilityGroup,
RestoreDatabasewithRecoveryDROnly,
RemovePrimaryDatabase,
ModifyAGMode,
JoinSecondaryDBtoAvailabilityGroup,
CheckDBisJoinedorUnjoined,
AddPrimaryDatabasetoAvailabiityGroup,
TestallAvailabilityReplicasHealthProd,
TestallAvailabilityReplicasHealthDR,
SynAlwaysOnFailoverWithSpecificSecondary,
SynAlwaysOnFailoverWithRandomSecondary,
SuspendReplicationAllDBinAvailabilityGrup,
ResumeReplication,
ManualPlannedFailover,
ForceFullyFailover,
CheckSuspendedState,
CheckResumed,
CheckAvailabilityGroupStatusProd,
CheckAvailabilityGroupStatusEnableDisable,
CheckAvailabilityGroupStatusDR,
CheckAllowAllConnectionsProdDR,
CheckAllDatabasesState,
ASynAlwaysOnFailoverWithSpecificSecondary,
ASynAlwaysOnFailoverWithRandomSecondary,
CheckSqlServerRunningStates,
CheckDBRoleAndMirrorState,
ExecuteDBClusterChangeOver,
MSSQL_Mirroring_Monitoring,
ChangeClusterNodeWeightMultiple,
ChangeClusterNodeWeightSingle,
CheckClusterNodeWeightMultiple,
CheckClusterNodeWeightSingle,
ClusterNameAssociatewithGroup,
SqlDSMsSqlRestoreLastLog,
SqlDSMigrateServerRolesDR,
SqlDSMSSqlRestoreLog,
ScanFileFromApplicationServer,
SQLDSMigrateServerRolesPR,
SQLDSMigrateLoggingPR,
SQLDSMigrateLoggingDR,
SQLDSMSSQLRestoreDBWithRecovery,
SQLDSMSSQLKillProcessSec,
SQLDSMSSQLKillProcessPrim,
SQLDSMSSQLGenerateLastLogFC,
SQLDSMSSQLDBOption,
SQLDSDatSyncSQL2000,
MoveCSVFileToDBServer,
FileReplicationApplicationToDBServer,
DownloadResponseFileFromProductionServer,
CreateCSVFileFromRESP,
ApplicationDataSync,
MSSQL_NLS_Monitoring,
FixOrphanUsers,
ILSSQLSetDRDBInMultiUserAccessMode,
LSSQLUpdatingRestoreJobWithDRIPAddress,
ILSSQLUpdatingRestoreJobWithDRIPAddress,
LSSQLUpdatingCopyJobWithDRIPAddress,
ILSSQLUpdatingCopyJobWithDRIPAddress,
ILSSQLUpdatingBackupJobWithPRIPAddress,
LSSQLUpdatingBackupJobWithPRIPAddress,
LSSQLRemovePrimarySecondaryLogShipping,
ILSSQLRemovePrimaryLogShipping,
LSSQLRemoveSecondaryLogShipping,
ILSSQLRemoveSecondaryLogShipping,
LSSQLRemovePrimaryLogShipping,
ILSSQLRemovePrimarySecondaryLogShipping,
LSSQLRestoreDatabaseWithRecovery,
ILSSQLRestoreDatabaseWithRecovery,
ILSSQLSetDBSingleUserAccessModeDR,
ILSSQLKillSessionDR,
ILSSQLSetDBMultiUserAccessModePrimary,
LSSQLVerifyDBSingleUserAccessMode,
ILSSQLVerifyPRDBSingleUserAccessMode,
LSSQLSetDBSingleUserAccessMode,
ILSSQLSetDBSingleUserAccessModePrimary,
ImportLoginRolesOnDRServer,
ImportLoginsOnDRServer,
ExportLoginRolesFromProductionServer,
ExportLoginFromProductionServer,
ILSSQLKillSessionPrimary,
LSSQLFixOrphanUsers,
LSSQLEnableJob,
LSSQLGenerateLastBackUpLogWithNoRecovery,
LSSQLKillDBSessionWithTimeOut,
LSSQLRunJob,
LSSQLDisableJob,
VerifyLogFileSequence,
RunRestoreJob,
RunCopyJob,
DisableRestoreJob,
DisableCopyJob,
DisableBackUpJob,
BackupJobStatus,
TransactionLogShippingState,
DatabaseAccessMode,
DatabaseRecoveryModel,
LSSQLRestoreSecondaryLogShipping,
LSSQLRestorePrimaryLogShipping,
LSSQLGenerateLastBackUpLog,
LSSQLKillAllSession,
LSSQLRestoreLastBackUpLogWithNoRecovery,
LSSQLSetDBMultiUserAccessMode,
LSSQL_RemoveSecondaryJob,
LSSQL_Check_DbEntry_OnPrimaryServer_Exist,
LSSQL_Check_DBEntry_OnSecondaryServer_Exist,
LSSQL_Check_PrimaryLogshipping_Exist,
LSSQLCheckPrimarySecondaryLogshippingExist,
VerifyDatabaseRecoveryMode,
VerifyUpdateabilityState_DR,
VerifyUpdateabilityState_PR,
VerifyDatabaseStatus,
test_NLS,
LSSQL_RstreLastBckupLogwthNoRcvryWithPrchck,
LSSQL_EnableLogshippingwithTargetDBRestoring,
LSSQL_RestoreLastlogwithStandbyDR,
LSSQL_RestoreLastlogwithRecoveryDR,
LSSQL_ValidateBackupwithPrimarySecondary,
LSSQL_CopyTailLogBackupDR,
LSSQL_VerifyPrimarybackuptransactionlogExist,
LSSQLExecuteSecondaryLogShipping,
LSSQLExecutePrimaryLogShippingReverse,
VerifyJobNameAssociatedWithLogShipping,
VerifySourceAndDestBackupDirectory,
VerifyBackupDirectoryandSharePath,
Execute2DRSitePrimaryLogShipping,
LSSQLRestoreLastBackupLogWithRecovery,
Execute3SiteSecondaryLogShipping,
Execute3DRSitePrimaryLogShipping,
RestoreLogWithStandby,
Verify3DRSiteLogFileSequence,
LSSQLMakeDBWritableLastRestoredFileFailOver,
LSSQLExecuteSecondaryLogShippingJobSchedule,
LSSQLExecutePrimaryLogShippingJobSchedule,
SQLNLSCheckDatabaseMode,
SQLNLSCheckDatabaseState,
VerifyDatabaseMirroringStatus,
MirroringFailOver,
KillMSSQLProcessByName,
ILSSQLRemoveRestoreJob,
ILSSQLRemoveCopyJob,
AttachDB,
SetDBOptionSingleUser,
DBOffline,
LSSQLMigrateRoles,
ILSSQLMigrateLogins,
ILSSQLExecuteSecondaryLogShipping,
ILSSQLExecutePrimaryLogShipping,
ILSSQLRemoveBackUpJob,
ILSSQLRunBackUpJob,
LinuxDisMount,
LinuxMount,
ILSSQLMigrateRoles,
LSSQLMigrateLogins,
DetachSQLDatabase,
AttachSQLDatabase,
LSSQL_Verify_DbEntry_OnPrimaryServer_Exist,
LSSQL_Verify_DBEntry_OnSecondaryServer_Exist,
LSSQL_Verify_PrimaryLogshipping_Exist,
LSSQLVerifyPrimarySecondaryLogshippingExist,
SQLNLS_CheckDatabaseMode,
ChangeAvailabilityMode,
ExecuteAlwaysOnNormalFailOver,
PreFlightCheckDatabaseSyncState,
AlwaysON_Montioring,
ResumeDataMovement,
ExecuteAlwaysOnForceFailOver,
CheckAvailabilityMode,
CheckRole,
CheckPriorityState_Custom,
CheckHealthStatusUP,
MongoDBMonitor,
CheckReplicationLagStatus,
SetPriorityOfTheReplicaSet,
CheckMangoDBPrimaryState,
CheckSecondaryState,
DB2TakeOverHADR,
DB2Start,
DB2DeActivateDBStart,
DB2ActivateDB,
DB2SwitchBackDB,
DB2SwitchOverDB,
DB2IsDBUp,
DB2IsHADRActive,
DB2StopHADR,
DB2StartHADRPrimary,
DB2DeActivateDBTerminate,
DB2StartHADRStandBy,
DB2DeactivateDatabase,
DB2Terminate,
DB2IsDatabaseActive,
DB2UnquiesceDatabase,
DB2IsDatabaseQuiesced,
DB2QuiesceDatabase,
DB2VerifyLogGap,
DB2VerifyLogPosition,
DB2IsHADRStatePEER,
DB2IsHADRRoleStandby,
DB2IsHADRRolePrimary,
DB2IsDatabaseStandby,
DB2IsDatabasePrimary,
DB2HADR_Monitoring,
VerifystatusofVM,
Verify_FailedOverWaitingCompletion_Status,
VerifyState,
VerifyReverseRepliConnectionconfig,
VerifyReplicationMode,
VerifyReplicationState,
VerifyPreparedForFailoverstatus,
VerifyHyperVVMVLANIDConnectedStatus,
VerifyHyperVVMDisConnectedStatus,
VerifyHyperVVMConnectedStatus,
VerifyForwardRepliConnectionconfig,
StartVM,
StartFailover,
SingleNetClsterHyprVVMIPAddressWthMacAddress,
ShutdownVM,
SetVLANIDToVirtualSwitchOnHyperVVM,
HyperVResumeReplication,
FailsOverReplicaVM,
DisconnectionToHyperVVM,
ConnectionToVirtualSwitchOnHyperVVM,
Cluster_VerifyPrimaryReplicationMode,
ChangeReplicationmode,
ChangeDNSIP,
ChngeClsterHyprVVMIPAddressWthOutMacAddress,
ChangeClusterHyperVVMIPAddressWithMacAddress,
Cancel_FailOver_Cluster_HyperVVM,
SingleNetClsterHyprVVMIPAddressWthuyMacAdres,
HyperV_Monitoring_Action,
RmoveClstrResorceDpndncyFrmDpndntResorce,
AddClusterResourceDependencyToDpndentResorce,
VerifyClusterResourceDpndncyInDependentRsorce,
RemoveClusterResourceFromClusterOwnerGroup,
AddClusterResourceToClusterOwnerGroup,
VrfyMasterLogFileAndPostiononMstrSlaveServer,
StopSlave,
Slave_SQLRunningStatus,
SlaveIO_State,
SlaveIORunning_Status,
Relay_MasterLogFile,
Read_MasterLogPosition,
MySQLSlaveStatus,
MySQLServicestatus,
MasterRelayMasterlogFile,
MasterReadMasterlogPosition,
Master_LogFile,
ExecuteMysqlDBCommand,
ExecuteCheckMysqlDBCommand,
ExecMasterLogPosition,
ConnectStatus,
ChangeMASTERToMasterHostAndLogFile,
ChangeMasterToMasterHost,
SetGlobalReadOnlyOFF,
SetGlobalReadOnlyON,
FlushLogs,
FlushTables,
VerifyReadWriteOnMasterInstance,
MakeMasterInstanceToReadWrite,
ShowMasterStatus,
StartSlaveInMaster,
CheckSlaveStoppedStatus,
MySQLMonitoring,
StopReplicationInSlaveServer,
MakeMasterInstanceReadOnly,
VerifyReadOnlyOnSlave,
VerifyRepUserConnectivityFromSlaveToMaster,
VerifyReadOnlyOnMaster,
VerifySQLLogBinSetToONMaster,
VerifyReadOnlyOnMasterInstance,
VerifySQLLogPositionMasterAndSlaveServer,
RecoverStandbyDB,
PreShutDB,
PRPreShutRedoCtrlScript,
MountStandbyDatabase,
CreateControlScript,
CreateControlFileFromTraceFile,
BackupRedoControlFile,
AddTempTableSpace,
ASMRestoreStandbyControlFile,
ASMRecoverDatabase,
ASMOpenDatabase,
ASMCreateStandbyControlFile,
StartUp_NoMount,
StartUpMount,
ShutDownStandbyDB,
ShutDownPrimaryDB,
ShutownDRDB,
ReplicateRSyncFoldersPosix,
ReplicateRSyncFilePosix,
Ora_Rsync_ReplicateStandByControlFile,
Ora_Rsync_ReplicateStandByTraceFile,
UpdateDROperationStatus,
VerifyDatabaseState,
MaxDB_StartDatabase,
MaxDB_StopDatabase,
ExecuteCheckCommand,
DGBValidateDB,
DGBSwitchOver,
DGBFailOver,
DGBConvertSnapshotStandby,
DGBConvertPhysicalStandby,
DGBConfigurationIsOK,
OracleDG12C_StartRecovery,
OracleDG12C_StartDB,
OracleDG12C_ShutdownDB,
OracleDG12C_MountDB,
OracleDG12CDGVerifySwitchover,
OracleDG12CDGSwitchover,
StopNodeManagerService,
StopManagedServer,
StopHTTPServer,
StopAdminServer,
StartNodeManagerService,
StartManagedServer,
StartHTTPServer,
StartAdminServer,
VerifyDBModeAfterRevertSnapshot,
VerifyDBRoleAfterRevertSnapshot,
VerifyDBRoleBeforReverteSnapshot,
VerifyDBRoleBeforSnapshot,
CheckFlashBackOff,
ConvertSnapToPhysicalStandby,
VerifyDBRoleAfterSnapshot,
VerifyDBModeAfterSnapshot,
VerifyCurrentScn,
ConvertSnapshotStandby,
VerifyDBFlashBackRetentionTarget,
VerifyDBRecoveryFileDestSize,
VerifyDBRecoveryFileDest,
VerifyDataGuardStatus,
VerifyDBModeBeforSnapshot,
VerifyDBModeBeforRevertSnapshot,
DGWinVerifyMaxSequenceNumber,
DGDatabaseOpen,
DGShutDownPrimary,
DGConnector,
ExecuteSqlCommand,
WhileCheck,
DGMountStandby,
DGJobStatus,
Test_Chatbot,
TestAction_RoleandMode,
srvctl_StartInstance_OOpen,
ODG_Monitor_Action,
AlterDataBaseSwitchOver,
VerifyAlterDBSO,
srvctl_StopDatabase,
srvctl_StartDatabase,
srvctl_StopInstance,
srvctl_StartInstance_OMount,
CheckOpenMode,
AlterDBMount,
StartUpNoMount,
SwitchOverStatus,
ExecuteCheckSqlCommand,
DGSwitchStandByToPrimary,
DGSwitchPrimaryToStandBy,
DGSwitchLogFile,
DGRecoverStandBy,
DGCheckUserStatus,
DGVerifyMaxSequenceNumber,
DGVerifyDBModeAndRole,
DGSwitchLogFileCurrent,
JobStatusInPR,
RecoveryStartInNewDR,
ShutDownDR,
MountNewDRDBStandBy,
ShutDownPrimary,
VerifyArchiveLogSequence,
VerifySwitchoverStatus,
OracleRAC_Monitoring_Action,
OracleDataGuardAPI_SwitchDB,
OracleDataGuardAPICheckDBSyncStatus,
VerifyLogSequence,
SwitchShutPrimaryDB,
SwitchShutPRDBWin_DS,
SwitchShutPRDBWin,
StopOracleListener,
StopListenerWithPassword,
StartUpMountForce,
StartOracleListener,
StartListenerWithPassword,
StartDatabaseNoMount,
StartDatabaseMount,
StartDatabase,
StartDataBaseReadOnly,
StartDBStandByWin_DS,
StartDBStandByWin,
StartDBStandBy,
StartDBReadWriteWin_DS,
StartDBReadWriteWin,
StartDBReadWrite,
ShutStandByDBWin_DS,
ShutDownDataBase,
ShutDB,
RestoreStandbyControlFile,
ReplicateStandByControlFile,
Recover_Stand_By_Database_DS,
RecoverStandbyDatabaseFile,
RecoverDatabase,
PreShutRedoCtrlScript,
OpenDatabase,
KillOracleSessions,
IsCheckPointCountOne,
GenerateRedoCtrlBKPScriptWin_DS,
GenerateRedoCtrlBKPScriptWin,
FlashBackToRestorePoint,
ExecuteSqlScriptWithPassword,
ExecuteSqlScriptWithEnvPassword,
ExecuteRedoCtrlBKPWin_DS,
ExecuteRedoCtrlBKPWin,
ExecuteDBCommand,
DropRestorePoint,
Database_Switch_Log_DS,
CreateTempFileTableSpace,
CreateTempFile,
CreateStandbyControlFile,
CreateRestorePoint,
CreateControlFileScript,
CreateControlFileByScript,
CopyRedoCtrFile,
CompareTableCountPR,
CompareTableCountDR,
CheckNoLoggingOperation,
CheckFlashBackOn,
BackUpControlFile,
AlterDatabaseOpen,
ApplyIncrementalLogs,
AlterSystemSetLogArchiveDestEnable,
AlterSystemSetLogArchiveDestDefer,
AlterSystemLog,
AlterStbyDBtoMaxPerformance,
AlterDatabaseMount,
AlterDatabaseFlashBackOn,
AlterDatabaseFlashBackOff,
AlterDatabaseConvertPhysicalStby,
AlterDatabaseActiveStby,
AlterDBRecoverManagedStbyDBCancel,
ActiveDatabaseReadWrite,
