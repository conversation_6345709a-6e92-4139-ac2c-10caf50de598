VrfyTstRplctionHealthPasdStatsExcptDBAvlblty,
VerifyServerComponentActiveStateforallCmpnent,
VerifyReplayQueueStatusforAllMailboxDB,
VerifyPrimaryActiveManagerStateatPR,
VerifyMailboxDBMountedStatusatPR_SiteLevel,
VerifyMailboxDBMountedStatusatPR_ServerLevel,
VerifyMailboxDBHealthyStatusatDR_SiteLevel,
VerifyMailboxDBHealthyStatusatDR_ServerLevel,
VrfyDBCopyActvatnPolcySetingfrMalboxServr,
VrfyContentIndexStateasHealthyforallMailboxDB,
StopDAGatPrimarySiteWithoutConfigurationOnly,
StopDAGatPrimarySiteWithConfigurationOnly,
StartDAG,
SetDBCopyAutoActivationPolicy,
SendeReceiveConnector,
RestoreDAG,
RedirectMailboxServerMsgQueuetoOtherServer,
MovePrimaryActiveManagertoTarget,
MoveDatabaseWithoutSkip,
MoveDatabaseWithSkip,
MoveActiveMailboxDatabasewithSkipOptions,
Mount_DataBase,
ExchDAGVerifyWitnessServerAndPathConfigured,
ExchDAGVerifyAltrnteWitnesServerAdDirectory,
ExchDAGSetWitnessServerAndDirectory,
ExchDAGSetAlternateWitnessServerAndDirectory,
EnableSendConnector,
EnableMailboxDatabaseCircularLogging,
DismountDatabase,
DisableSendConnector,
DisableMailboxDatabaseCircularLogging,
CheckTransportSeverQueueStatus,
CheckSiteLevelMailboxAllServerStatusunderDAG,
ChkMailboxdatabaseBackupFullIncrementalStatus,
CheckMailboxServerMessageQueueCount,
CheckMailboxDatabaseCopyStatus,
CheckDatabaseCopyAutoActivationPolicy,
CheckAllMailboxDBMountedStatus,
VerifyPassiveMBXReplayQueueLength,
VerifyPassiveMBXDBCopyStatus,
VerifyPassiveMBXDBCopyErrorMessage,
VerifyPassiveMBXDBCopyContent,
VerifyPassiveMBXCopyQueueLength,
VerifyDAGStatusOnline,
VerifyActiveMBXDBCopyStatus,
VerifyActiveMBXDBCopyErrorMessage,
VerifyActiveMBXDBCopyContent,
SuspendWhenReadyToCompleteModeStatus,
SuspendWhenReadyToCompleteMode,
StoppedMailBoxServers,
StartedMailBoxServers,
ResumeMoveMailBoxReq,
RemoveUserMailBoxMoveReqIfExist,
MoveActiveMailboxDatabase,
GetMoveReqStatus,
GetMoveReqStatics,
CheckUserMoveReqCanBeCreated,
CheckUserMailBoxExist,
SetSCRPrerequisite,
SCRStatusWithCopyQueueLength,
ResumeSCR,
MoveMailboxConfiguration,
MountDatabase,
GetMailboxListCountBeforeSwitch,
GetMailboxListCount,
EnableSCR,
DismountMailboxdatabase,
DisableSCRRestorelogs,
CreateStorageGroup,
CompareNewSGMailboxPath,
ChecktargetDBStatus,
CheckTargetDBFileStatus,
AllowFileRestoreToMailboxDB,
VerifyDAGParameterValue,
StopDAGOnMailBoxServer,
StopDAGActiveDirSiteConfigOnly,
StartDAGOnMailBoxServer,
SetPreferredADServer,
SetDatabaseAvailabilityGroup,
RestoreDAGActiveDirSite,
MoveClusterGroup,
Move_ActiveMailBoxDatabase,
CheckMailBoxDBStatusParameterValue,
VerifyReplicationServiceonPAM,
VerifyMailboxServerEntries,
StopDatabaseAvailabilityGroupOnMailboxServer,
StopDatabaseAvailabilityGroup,
SetPublicFolderDatabase,
SetDatabaseCopyAutoActivationPolicyBlocked,
SetDatabaseCopyAutoActivationPolicy,
RestoreDatabaseAvailabilityGroup,
MoveMailboxDatabaseToDRServer,
MoveDefaultofflineAddressBook,
Execute_PowerShellCommand,
ExchDAG_VerifyStarteddMailBoxServer,
VerifyCurntSiteNameAssociatedWthMailbxServr,
ExchDAG_VerifyCheckandStopclusterService,
ExchDAG_StopDAGAtPRMailboxServer,
ExchDAG_StartDAGAtPRMailboxServer,
ExchDAG_SetDAGToSeedALLChanges,
ResumeMailBoxDatabaseCopy,
MountMailboxDatabaseActivePRMailboxServer,
ExchDAG_CheckReplicationHealthonMailboxServer,
CheckPrimaryAlternateFileShareWitnessInUse,
CheckReplicationHealthStatus,
CheckMailboxServerRoleServicesStatus,
CheckHealthyMailboxDatabaseDetails,
CheckDatabaseMountedStatus,
CheckDAGMembershipStatus,
StopIISWebsite,
StopApplicationPool,
StartIISWebsite,
StartApplicationPool,
HitHTTPUrl,
HitCheckHTTPUrl,
ExecuteCheckRestAPICommand,
NoRelication,
DRReady,
DROperationType,
WaitForWorkflowAction,
WaitForParallelAction,
DisplayAlert,
Stop_WorkflowRTO,
Start_WorkflowRTO,
CyberRecoveryExecuteCopyAnalyze,
CyberRecoveryExecuteSecureCopy,
PPRExecuteRecoveryApplicationPPDM,
CR_ChkiflatestcopyAvailableparticularpolcy,
CyberRecovery_CheckStatus,
CyberRecoveryExecuteSecureCopyAnalyze,
PPDM_ExecuteProtectedSingleVM,
PPDM_VMCopyRestoreToAlternetLocationVer1,
PPDMCheckifMSSQLDBBackupcopyexistVer1_2,
PPDM_1CheckVMexistprotectedVer4,
PPDMExecuteMultipleUnProtectedVMBackup,
PPDMExecuteSingleUnProtectedVMBackup,
PPDMRstorVMtoAltrntlocwtlatstBackupcopy,
PPDMRstrMSSQLDBTOALTRNATEMltplDBtoDfultVer1_1,
DELLEMCPPDMMSSQLBRestorMultipleDBVer1_1,
PPDMExecuteProtectedMultipleVM,
RemoveProbeHealthfromLoadBalancer,
RemoveLoadBalancerRuleFromLoadBalancer,
RmveLodBalancrInboudNATRuleFromLoadBalancer,
RemoveLoadBalancerFromVM,
AddProbeHealthToLoadBalancer,
AddLoadBalancerToVM,
AddLoadBalancerRuleToLoadBalancer,
AddLoadBalancerInboundNATRuleToLoadBalancer,
ExecuteReverseReplicationAzureToOnPrim,
ExecuteReprotectOnPrimToAzure,
ExecuteReprotectOnPremVMwareToAzure,
ExecuteReprotectAzureToOnPrim,
ExecutePlannedFailoverCommitOnPrimToAzure,
ExecutePlannedFailoverAzureToOnPrim,
ExecuteCommitFailover,
CheckReplicatedVMProtectionState,
CheckAllowedOperationAzureToPrim,
TCL_Cloud_VM_Instance_PowerON,
TCL_Cloud_VM_Instance_PowerOFF,
Check_TCL_Cloud_VM_InstanceStatus,
Rackware_StartWaveinRMMServer,
Rackware_ExecuteWaveFailOver,
Rackware_ExecuteWaveFailBack,
Rackware_CheckWaveStatus,
Rackware_CheckDRPolicyStatus,
HostSyncWhenTargetExistWithoutWait,
HostSyncWhenTargetExistWithWait,
CrteWavWtHstSetOCIAutoprovsonparamtrs,
CreateHostSyncAutoTrgtntAvlStaticIPWthoutWait,
CreateHostSyncAutoTargetntAvlStatcIPWthWait,
CreateHostSyncAutoTargetntAvlDynmcIPWithoutWt,
OracleCloudVMInstanceAction,
DeattachALLNSGsfromInstanceVNIC,
CreateNSGandAddSecurityRule,
CheckifNSGAttachedtoInstanceVNIC,
CheckOracleCloudVMInstanceStatus,
AddNewNSGtoInstanceVNICReplaceExistingNSGs,
AddNewNSGtoInstanceVNICAppendtoExistingNSGs,
SoftLayer_UpgradeVirtualMachineMemory,
SoftLayer_UpgradeVirtualMachineCPU,
SoftLayer_UpgradeVirtualMachineByIds,
SoftLayer_UpgradeVirtualGuest,
SoftLayer_ProvisionVirtualMachineByIds,
SoftLayer_ProvisionVirtualGuest,
SoftLayer_PowerONVirtualGuest,
SoftLayer_PowerOFFVirtualGuest,
SoftLayer_CheckVirtualGuestPowerON,
SoftLayer_CheckVirtualGuestPowerOFF,
RemoveRoutingRule,
RmvePathbsdRulfrmBckendPoolRul,
ChckListenerwithAssociatedRule,
ChkApplictionGatwyRulPathexist,
ChkApplcationGatwyOpratnlState,
AddRoutingRule,
AddListenerHTTPType,
AdAppltnGtewyBckendPoolRulPath,
OCI_DeleteDNSRecordAType,
OCI_CheckifDNSRecordExistAType,
OCI_CheckifDNSRecordDoesNotExistAType,
OCI_CheckExistingTTLValueDNSRecordAType,
OCI_AddDNSRecordAType,
AzureMySQLExecuteServiceStopStart,
AzureMySQLCheckServiceStartStopStatus,
ExecutePromoteMySQLReplicaServer,
ExecuteCreateMySQLReplicaServer,
ExecuteDeleteMySQLStandaloneServer,
ExecuteDeleteMySQLReplicationReplicaServer,
AzureMysqlMonitoring,
ExecuteDeleteMySQLReplicationSourceServer,
CheckMySQLServerAvailableStatus,
CheckAzureMySQLServerRole,
CheckAzureMySQLServerExistintheRegion,
AzurePostgreSQLExecuteServiceStopStart,
AzurePostgreSQLCheckServiceStopStart,
AzurePostgreSqlMonitoring,
StpVrtulMchneScaleAllInstances,
StpSpcficVrtulMchnSclStInstnce,
StopMySqlReplication,
StrtVrtulMchneScaleAlInstances,
StartSpcfcVrtulMchnScleStInstn,
RemoveSecurityRuletoNSG,
ExecuteUnplannedFailover,
ExctUnplandFailOvrOnPrmToAzure,
ExecuteRe_Protect,
ExecutePlannedFailover,
ExecutePlannedFailOverAzure,
ExecuteForceFailOverAzure,
ExecuteFailoverAzureCosmosDB,
ExecuteCommitUnplannedFailover,
EnableDisableATMEndpoint,
DissociateNSGtoVM,
DissociateNSGtoNetwrkInterface,
DeleteReplicationSourceServer,
DeleteReplicationReplicaServer,
CreateReplicationAZMySql,
ChkVrtualMachineScaleSetStatus,
ChkUnplandFailovrCompltdStatus,
ChkUnplandFailovrComitedStatus,
CkSpcfcVrtulMchneScleSetStatus,
CheckRe_ProtectProtectionState,
CheckRe_Protect,
CheckPublicIPAddress,
CheckPlannedFailoverStatus,
CheckNSGNameExist,
CheckNSGNameAssociatetoVM,
CheckEnableDisableATMEndpoint,
CheckCommitFailOver,
CheckAzureWriteLocationCosmos,
CheckAzureSQLDBRole,
CheckAzureReplicationState,
CheckAzureReadLocationCosmos,
ChkAzDBActProvsningStatsCosmos,
CheckAllowedOperation,
ChkAZMySQLSrvrAvailableStatus,
CheckAZMySQLRole,
Azure_TestFailOver,
Azure_CleanUpFailOver,
AssociatePublicIPAddress,
AssociateNSGtoVMSpcfyNtwrkCard,
AssociateNSGtoVMDfaltNtwrkCard,
AssociateNSGtoNetworkInterface,
AsignNSGtoVMWthRplaceExstngNSG,
AddSecurityRuletoNSG,
VCDVerifyStatus,
VCDPowerOn,
VCDPowerOff,
StopAzureVM,
StartAzureVM,
CheckAzureVMStatus,
CheckAzureVMPowerState,
ChangeAzureVMSize,
Azure_MSSQLPass_Monitoring,
ExecuteSQLDBForcedFailover,
ExecuteSQLDBPlannedFailover,
CheckAZReplicationState,
CheckAZSQLDBPaaSRole,
CheckStorageReplicationDataLagWithInput,
SetStorageAccountReplication,
ExecuteStorageAccountFailover,
CheckStorageAccountType,
Azurestoragereplication_Monitoring,
ExecuteCommitFailoverASR,
ExecuteUnPlannedFailoverASR,
ExecuteReprotectASR,
ExecuteTestFailoverCleanUpASR,
CheckASRTestFailoverStatus,
ExecuteTestFailover,
CheckASRTestFailoverCleanupStatus,
CheckUnplannedFailoverStatus,
CheckCommitFailoverStatus,
CheckASRReProtectStatus,
CheckASRVMProtectionStatus,
RubrikMountVirtualMachine,
MigrateDataStoreAfterMount,
Stop_EC2_Instance,
Start_EC2_Instance,
Modify_EC2_Instance_Size,
Is_EC2_Instance_UP,
Is_EC2_Instance_Down,
Check_EC2_Instance_State,
Upload_To_S3_Bucket,
Upload_Files_To_S3_Bucket,
Download_From_S3_Bucket,
Download_Files_From_S3_Bucket,
CyberRecoveryExecuteCopyAnalyze,
CyberRecoveryExecuteSecureCopy,
PPRExecuteRecoveryApplicationPPDM,
CR_ChkiflatestcopyAvailableparticularpolcy,
CyberRecovery_CheckStatus,
CyberRecoveryExecuteSecureCopyAnalyze,
PPDM_ExecuteProtectedSingleVM,
PPDM_VMCopyRestoreToAlternetLocationVer1,
PPDMCheckifMSSQLDBBackupcopyexistVer1_2,
PPDM_1CheckVMexistprotectedVer4,
PPDMExecuteMultipleUnProtectedVMBackup,
PPDMExecuteSingleUnProtectedVMBackup,
PPDMRstorVMtoAltrntlocwtlatstBackupcopy,
PPDMRstrMSSQLDBTOALTRNATEMltplDBtoDfultVer1_1,
DELLEMCPPDMMSSQLBRestorMultipleDBVer1_1,
PPDMExecuteProtectedMultipleVM,
VBVMWareReplicaFailOverPlanned,
VBRVMwareReplicaUndoFailOver,
VBRVMWareReplicaUndoFailback,
VBRVMWareReplicaFailOverPermanent,
VBRVMWareReplicaFailOverDROnly,
VBRVMWareReplicaFAILBACKToOriginalVM,
FailOverReplicationJob,
FailBackReplicationJob,
FailBackCommitReplicationJob,
CreateReplicationJobUseProdVMState,
CreateReplicationJobFromBackupFiles,
CheckReplicationJobLastState,
CheckReplicationJobLastResult,
CheckReplicationJob,
CheckReplicaMonitoringStatus,
eBDROperations,
UpdateAnAsset,
UnmanageAnAsset,
RemoveAssetInMaintenanceMode,
RefreshAnAsset,
RebootAsset,
PutAssetInMaintenanceMode,
PowerOnAsset,
PowerOffAsset,
ManageAnAsset,
ExecutePlan,
ExecuteOpsCenterCommand,
ExecuteCheckOpsCenterCommand,
HPPowerONBladeServer,
HPPowerOFFBladeServer,
HPCheckPowerStatusOn,
HPCheckPowerStatusOff,
PowerOnOffStatus,
PowerOn,
PowerOff,
MSSQLlinkstatus,
ExecuteTxnQueCheck,
ExecuteStatusNodeCheck,
Execute_SAF_COUNT_CHECK,
ExecuteDRNetCheckStatusState,
ExecuteDRNetCheckRecords,
ExcuteDRNetCheckDistributorFileMapStatusQueue,
ExecuteDRNetCheckCollectorFileMapMode,
ExecuteDRNetCheckAuditModeStatus,
ExecuteBase24Command,
ExecuteBase24CheckCommand,