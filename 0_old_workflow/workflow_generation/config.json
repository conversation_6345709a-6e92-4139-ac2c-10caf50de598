[{"Layer": "App", "actions": [{"action": "Stop IIS", "details": {"executeType": "Lib", "libType": "Stop_Service", "parameter": {"serviceName": "IIS"}}}, {"action": "Service Stop", "details": {"command": "Custom", "serviceName": "Tomcat", "path": "//admin/stop.sh"}}], "replication_type": "i need the solution for azure application gateway automation"}, {"Layer": "Database", "actions": [{"action": "Verify Dataguard status", "details": {}}], "replication_type": ""}, {"Layer": "Network", "actions": [{"action": "Dns Modify", "details": {"ipaddress": "534563456"}}], "replication_type": ""}]