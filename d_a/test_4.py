from ultralytics import YOLO
import cv2
import numpy as np
import time

model = YOLO('yolov8n.pt')

cap = cv2.VideoCapture('Wrongway.mp4')

width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

VEHICLE_CLASSES = [1, 2, 3, 5, 7]


initial_y = {}

fps_start_time = time.time()
frame_count = 0

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    frame_count += 1

    results = model.track(frame, imgsz=640, conf=0.5, persist=True, verbose=False)

    if results[0].boxes.id is not None:
        boxes = results[0].boxes.xyxy.cpu().numpy()
        track_ids = results[0].boxes.id.cpu().numpy().astype(int)

        for box, track_id in zip(boxes, track_ids):
            x1, y1, x2, y2 = box

            current_y = y1

            if track_id not in initial_y:
                initial_y[track_id] = current_y

            if current_y > initial_y[track_id]:

                print(f"{track_id} : Initial - {initial_y}, Current - {current_y}")
                cv2.rectangle(frame,
                              (int(x1), int(y1)),
                              (int(x2), int(y2)),
                              (0, 0, 255), 2)
                cv2.putText(frame,
                            f'WRONG WAY! ID: {track_id}',
                            (int(x1), int(y1) - 10),
                            cv2.FONT_HERSHEY_SIMPLEX,
                            0.9,
                            (0, 0, 255),
                            2)
            else:
                cv2.rectangle(frame,
                              (int(x1), int(y1)),
                              (int(x2), int(y2)),
                              (0, 255, 0), 2)
                cv2.putText(frame,
                            f'ID: {track_id}',
                            (int(x1), int(y1) - 10),
                            cv2.FONT_HERSHEY_SIMPLEX,
                            0.9,
                            (0, 255, 0),
                            2)

    if time.time() - fps_start_time >= 1.0:
        fps = frame_count
        fps_start_time = time.time()
        frame_count = 0
    else:
        fps = 0

    cv2.putText(frame, f'FPS: {fps}', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

    cv2.imshow('Wrong Way Detection', frame)

    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# Cleanup
cap.release()
# output_video.release()
cv2.destroyAllWindows()
