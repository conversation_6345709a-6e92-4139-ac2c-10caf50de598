number_of_student_records = 3
student_record = ["<PERSON> 67 68 69",
"<PERSON><PERSON><PERSON> 70 98 63",
"<PERSON><PERSON> 52 56 60"]
students_dict = {}

for record in student_record:
    data = record.split()
    name = data[0]
    marks = data[1:]
    mark_list = []
    for mark in marks:
        mark_list.append(mark)

    students_dict[name] = mark_list

query = "<PERSON><PERSON>"


for key, value in students_dict.items():
    total_marks = 0
    if key == query:
        for mark in value:
            total_marks += int(mark)
        average_mark = total_marks / len(value)
        print(f"{query}'s average marks: {average_mark:.2f}")





n = int(input("Enter the count :"))

student_marks = {}

for i in range(n):
    user_input = input(f"Student {i} - Enter Name and Marks : ")
    data = user_input.split()
    name = data[0]
    mark_list = data[1:]
    marks = []
    for mark in mark_list:
        marks.append(float(mark))
    student_marks[name] = marks

print(student_marks)