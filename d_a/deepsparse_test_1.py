from deepsparse import compile_model
import cv2
import numpy as np
import time

# Load the compiled DeepSparse model
onnx_path = 'yolov8n.onnx'  # Ensure this path is correct for your exported model
deepsparse_model = compile_model(onnx_path, batch_size=1)

# Video capture
cap = cv2.VideoCapture('Wrongway.mp4')
width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

VEHICLE_CLASSES = [1, 2, 3, 5, 7]
initial_y = {}
fps_start_time = time.time()
frame_count = 0

# Inference loop
while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    frame_count += 1

    # Prepare frame for DeepSparse (resize to model's input size)
    input_frame = cv2.resize(frame, (640, 640))
    input_data = np.transpose(input_frame, (2, 0, 1)).astype(np.float32) / 255.0  # Normalize to [0, 1]
    input_data = np.expand_dims(input_data, axis=0)  # Add batch dimension

    # Run inference
    output = deepsparse_model.run([input_data])

    # Parse and post-process detections
    boxes = output[0]  # Get boxes from DeepSparse output
    track_ids = output[1]  # Simulated track IDs; adapt as needed

    for box, track_id in zip(boxes, track_ids):
        x1, y1, x2, y2 = box
        current_y = y1

        if track_id not in initial_y:
            initial_y[track_id] = current_y

        if current_y > initial_y[track_id]:
            print(f"{track_id} : Initial - {initial_y}, Current - {current_y}")
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 0, 255), 2)
            cv2.putText(frame, f'WRONG WAY! ID: {track_id}', (int(x1), int(y1) - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)
        else:
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
            cv2.putText(frame, f'ID: {track_id}', (int(x1), int(y1) - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)

    # Calculate FPS
    if time.time() - fps_start_time >= 1.0:
        fps = frame_count
        fps_start_time = time.time()
        frame_count = 0
    else:
        fps = 0

    cv2.putText(frame, f'FPS: {fps}', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

    cv2.imshow('Wrong Way Detection', frame)

    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# Cleanup
cap.release()
cv2.destroyAllWindows()
