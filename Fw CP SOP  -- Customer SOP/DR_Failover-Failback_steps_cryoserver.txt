Cryoserver Failover/Failback Full steps:


A- Steps for Mirror mode to DR mode
-------------------------------------

1. First login as normal user (Ex: mnader-mgmt2), to switch as root user, we use
sudo su

2. Stop Postfix services on HQ server
systemctl stop postfix.service
systemctl disable postfix.service


--And to check either postfix service has stopped or not
Run :
systemctl status postfix


3. Stop Cryoserver services on DR server
systemctl stop cryoserver.service fileserver.service tomcat.service derbynet.service

4. Stop Cryoserver services on HQ server
systemctl stop cryoserver.service fileserver.service tomcat.service derbynet.service
systemctl disable cryoserver.service fileserver.service tomcat.service derbynet.service


--in case of need to confirm the all java service is stop running below command:

ps -ef | grep java


5. Run that script on Mirror (DR) server 
cd /opt/cryoserver/cryoserver/groovy/
./run_groovy.sh mirror_dr_switch.groovy -m dr


y for confirming that we have stopped all the Cryoserver services
and then choose 904 as the running storage_node and to choose it, select -
n, n, n, y

6. Start all the Cryoserver services on DR server only.
systemctl start derbynet.service cryoserver.service fileserver.service tomcat.service


7. On DR server Run command:  cryohealthcheck 

to health check the cryoserver as well as run below comamnd:

Comammdn: ps -ef | grep java

to check the running services.





B-Steps for DR mode to Mirror mode
-----------------------------------


1. First login as normal user (ex: mnader-mgmt2), to switch as root user, we use
sudo su

2. Stop all Cryoserver services on DR server except fileserver service 
systemctl stop cryoserver.service tomcat.service derbynet.service

3. Start only and only Fileserver services on DR server
systemctl start fileserver.service

--- And to check, fileserver is running, we can use below cmd
ps -ef | grep fileserver


4. Start only and only Fileserver services on HQ server - 
systemctl enable fileserver.service
systemctl start fileserver.service

--- And to check, fileserver is running, we can use below cmd
ps -ef | grep fileserver


5. Run that script on Mirror(DR)- 
cd /opt/cryoserver/cryoserver/groovy/
./run_groovy.sh mirror_dr_switch.groovy -m mirror 
(this step will auto copy the cryoserver.xml from mirror to primary)

y for confirming that we have stopped all the Cryoserver services
and then copying of storage_nodes to HQ server and for that please select -
y, y, y, y
wait till script completely run of DR server

6. Start the Cryoserver services on HQ server
systemctl enable derbynet.service cryoserver.service fileserver.service tomcat.service
systemctl start derbynet.service cryoserver.service fileserver.service tomcat.service

7. Start the Postfix services on HQ server
systemctl enable postfix.service
systemctl start postfix.service



--And to check either postfix service has started or not
Run :
systemctl status postfix


8.Start the Cryoserver services on DR server.
systemctl start derbynet.service cryoserver.service fileserver.service tomcat.service

9. On HQ server Run command:  
cryohealthcheck 

to health check the cryoserver as well as run below comamnd:

Comammdn: ps -ef | grep java

to check the running services.




