import datetime
import os
import threading
import cv2
import queue
from multiprocessing.pool import ThreadPool
from surveillance_process.Intern_VL.Vdo_ai import InternVL
from surveillance_process.Intern_VL.create_folders import Folder

pool = ThreadPool(processes=1)
root = os.getcwd()


class PlayVideo:

    def __init__(self, source, question, user_id, cam_name, host_url, frame_skip=5):
        self.image = None
        self.cap_line = None
        self.frame = None
        self.response = None
        self.user_id = user_id
        self.cam_name = cam_name
        self.source = source
        self.question = question
        self.q_img = queue.Queue()
        self.original_path = os.path.join(root, "data/output/{}/{}/original".format(user_id, cam_name))
        self.host_url = host_url
        self.filename = None
        self.frame_skip = frame_skip  # Number of frames to skip
        self.frame_counter = 0
        self.result = queue.Queue()

    def im_write(self):
        cv2.imwrite(self.filename, self.frame)

    def vdo_cap(self):

        try:
            if self.source.endswith((".mp4", ".avi")):
                self.cap_line = cv2.VideoCapture(self.source)
            else:
                pass

            while True:
                if self.source.endswith((".mp4", ".avi")):
                    ret, self.frame = self.cap_line.read()
                    if not ret:
                        break
                else:
                    self.frame = self.source

                # Skip frames
                self.frame_counter += 1
                if self.frame_counter % self.frame_skip != 0:
                    continue

                # send frame to visual prediction
                self.response, self.frame = pool.apply_async(InternVL().intern_ai, (self.frame, self.question)).get()

                if not os.path.exists(self.original_path):
                    Folder(self.original_path).create()

                self.filename = self.original_path + "/detected{}.jpeg".format(
                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))

                img_path = self.filename.split(root + "\\")[1]

                self.response["img_path"] = self.host_url + "/" + img_path

                self.result.put(self.response)

                print("image is successfully put int queue")

                writer_thread = threading.Thread(target=self.im_write)
                writer_thread.start()

        except Exception as e:
            print(e)

    def get_result(self):

        return self.result.get()