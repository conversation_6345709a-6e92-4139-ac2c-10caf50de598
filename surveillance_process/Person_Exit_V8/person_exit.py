import cv2
import supervision as sv
import os
import datetime

from processor.logger import exc
from surveillance_process.Person_Exit_V8.polygon_test import PolygonTest
from processor.common import FolderView, CenterFrameText

os.environ["KMP_DUPLICATE_LIB_OK"]="TRUE"


class PersonExit:

    def __init__(self, model, intersect_line_zones, draw_line_zones, image_writer_flag, pe_user_cam_path):
        self.model = model
        self.intersect_line_zones = intersect_line_zones
        self.draw_line_zones = draw_line_zones
        self.image_writer_flag = image_writer_flag
        self.pe_user_cam_path = pe_user_cam_path
        self.original_path = os.path.join(pe_user_cam_path, "original")
        self.detections = None
        self.count = None
        self.frame = None
        self.person_flag = False
        self.writer_flag = False
        self.intersect = None
        self.detected_image_image_path = []
        self.original_image_path = []
        self.object_id = []
        FolderView(self.pe_user_cam_path).createfolder()
        FolderView(self.original_path).createfolder()

    def plots(self):
        try:
            if not self.detections:

                CenterFrameText().put_text_centered(self.frame, "Person Not Present In The Area",
                                                    (0, 0, 255))
                cv2.polylines(self.frame, [self.draw_line_zones], True, (0, 0, 255), 4)

            elif self.intersect:

                cv2.polylines(self.frame, [self.draw_line_zones], True, (0, 220, 0), 4)

            else:

                CenterFrameText().put_text_centered(self.frame, "Person Not Present In The Area",
                                                    (0, 0, 255))
                cv2.polylines(self.frame, [self.draw_line_zones], True, (0, 0, 255), 4)
        except Exception as er:
            exc.exception('Error occurred in plots function in person exit: {}'.format(er))

    def polygon_test(self):
        try:
            self.intersect, self.count = PolygonTest(self.detections, self.intersect_line_zones).point_polygon_test()

            if self.intersect:
                self.plots()
                self.person_flag = False
            else:
                self.plots()
                self.person_flag = True

        except Exception as er:
            exc.exception('Error occurred in polygon test function in person exit: {}'.format(er))

    def write_images(self, detected_image, original_image):

        try:
            if self.image_writer_flag:
                detected_image_image_path = self.pe_user_cam_path + "/person_exist{}.jpeg".format(
                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                self.detected_image_image_path.append(detected_image_image_path)
                original_image_path = self.original_path + "/person_exist{}.jpeg".format(
                    str(datetime.datetime.now().strftime("%d_%m_%Y_TIME_%H_%M_%S")))
                self.original_image_path.append(original_image_path)
                cv2.imwrite(detected_image_image_path, detected_image, [cv2.IMWRITE_JPEG_QUALITY, 50])
                cv2.imwrite(original_image_path, original_image, [cv2.IMWRITE_JPEG_QUALITY, 50])
        except Exception as er:
            exc.exception('Error occurred in write_images function in person exit: {}'.format(er))

    def predict(self, q_img, original_image):
        try:
            elastic_list = []
            writer_flag_list = []
            resized_coordinates_list = []
            self.detected_image_image_path = []
            self.original_image_path = []
            self.writer_flag = False
            self.frame = q_img.get()

            result = self.model.track(source=self.frame, conf=0.5, classes=0, verbose=False, half=True, persist=True)
            result = result[0]

            self.detections = sv.Detections.from_ultralytics(result)

            if self.detections:

                for box, mask, confidence, class_id, tracker_id, class_name in self.detections:

                    x_min, y_min, x_max, y_max = box

                    labels = f"{result.names[class_id]}"

                    cv2.rectangle(self.frame, (int(x_min), int(y_min)), (int(x_max), int(y_max)), (255, 64, 64), 3)
                    self.frame = cv2.putText(self.frame, labels, (int(x_min), int(y_min) - 10),
                                             cv2.FONT_HERSHEY_SIMPLEX, 0.9,
                                             (255, 64, 64), 3)
                    self.polygon_test()
                    if tracker_id not in self.object_id and self.person_flag:
                        self.object_id.append(tracker_id)
                        self.write_images(self.frame, original_image)
                        elastic_list.append({'x': int(x_min), 'y': int(y_min), 'h': int(x_max), 'w': int(y_max)})
                        resized_coordinates_list.append((x_min, x_max, y_min, y_max))
                        writer_flag_list.append(True)
                    else:
                        writer_flag_list.append(False)

                    if True in writer_flag_list:
                        self.writer_flag = True
                    else:
                        self.writer_flag = False

                return self.frame, original_image, elastic_list, self.person_flag, self.writer_flag, self.detected_image_image_path, self.original_image_path, resized_coordinates_list
            else:
                self.plots()
                return self.frame, original_image, elastic_list, self.person_flag, self.writer_flag, self.detected_image_image_path, self.original_image_path, resized_coordinates_list
        except Exception as er:
            exc.exception('Error occurred in predict function in person exit: {}'.format(er))

