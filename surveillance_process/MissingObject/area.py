import numpy as np
import matplotlib.path as mplPath
import cv2


def pointPolygonTest(polygon, test_point):
    poly_path = mplPath.Path(np.array(polygon))
    # point = test_point
    x, y, w, h = test_point
    points = (x, y), (w, h), (w, y), (x, h)
    result = []
    for point in points:
        # print(point, " is in polygon: ", poly_path.contains_point(point))
        included = poly_path.contains_point(point)
        result.append(included)
    if not result.__contains__(True):
        return False
    else:
        return True


class area:
    def __init__(self, contour):
        self.contour = np.array(contour, dtype=np.int32)
        self.count = 0


def checkAreaIntrusion(areas, obj):
    for area in areas:
        area.count = 0
        x = obj[0]
        y = obj[1]
        w = obj[2]
        h = obj[3]
        if pointPolygonTest(area.contour, (x, y, w, h)):
            area.count += 1
    val = area.count

    return val


def drawAreas(img, areas):
    for area in areas:
        cv2.polylines(img, [area.contour], True, (0, 0, 255), 4)
    return img
