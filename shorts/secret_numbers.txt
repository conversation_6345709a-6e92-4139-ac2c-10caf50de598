Ever wondered how websites hide sensitive details like mobile or credit card numbers with stars?
Let's uncover the magic behind this in just 60 seconds with Python!"

To display numbers securely, we replace all but the last four digits with stars.
It's super simple in Python! Here's how:"

python
Copy code
def mask_number(number):
    # Convert the number to a string
    number_str = str(number)
    # Replace all but the last 4 digits with stars
    masked = '*' * (len(number_str) - 4) + number_str[-4:]
    return masked

# Example usage
mobile_number = 9876543210
credit_card = 1234567812345678

print("Masked Mobile:", mask_number(mobile_number))
print("Masked Card:", mask_number(credit_card))
Output:
"Masked Mobile: ******3210"
"Masked Card: ************5678"

"Just a few lines of Python keep your data safe and professional. Follow for more coding tips and tricks in a minute!"