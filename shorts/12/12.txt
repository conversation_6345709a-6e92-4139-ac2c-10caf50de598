How movie ticket booking works behind the scenes in Python!"
Script:

Ever booked a movie ticket and seen the seat turn red instantly?
Here's how that actually works behind the scenes… with Python!

So let’s say available seats are:

seats = ["A1", "A2", "A3", "A4"]  
booked = []  

Now, the moment you tap on a seat — say "A2" —
this line in the backend runs:

if choice not in booked:  
    booked.append(choice)  

This means the seat wasn’t taken, so it gets added to the booked list.
Then the backend returns:

{"status": "booked", "seat": "A2"}  

Now the frontend developers take that response,
and immediately change that seat's color to red in the UI.
Or even freeze it — so others can’t tap on it.

And if you don’t finish payment in time?
That seat is released back to available.
Yup — all this happens in seconds!