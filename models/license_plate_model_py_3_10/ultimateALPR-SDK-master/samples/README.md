 - [android](android) folder contains GUI applications for Android 5.0+.
 - [c++](c++) folder contains command-line sample applications for Windows, Raspberry Pi and Linux. These samples can also be used for Android or iOS but we recommend using the one in [android](android) and [iOS](iOS) folders.
 - [csharp](csharp) folder contains C# command-line sample applications.
 - [iOS](ios) folder contains GUI applications for iOS 8.0+.
 - [java](java) folder contains command-line sample applications for all supported platforms.
 - [python](python) folder contains command-line sample applications for all supported platforms.
 
