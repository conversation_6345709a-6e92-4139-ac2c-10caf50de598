{"metadata": {"total_size": 8293711872}, "weight_map": {"language_model.lm_head.weight": "model-00002-of-00002.safetensors", "language_model.model.embed_tokens.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.18.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.18.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.18.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.18.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.19.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.2.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.20.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.26.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.26.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.26.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.26.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.26.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.26.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.27.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.27.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.27.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.27.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.27.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.27.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.28.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.28.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.28.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.28.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.28.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.28.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.29.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.29.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.29.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.29.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.29.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.29.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.3.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.30.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.30.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.30.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.30.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.30.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.30.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.31.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.31.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.31.mlp.gate_up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.31.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.31.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.31.self_attn.qkv_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.4.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.mlp.gate_up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.self_attn.qkv_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.norm.weight": "model-00002-of-00002.safetensors", "mlp1.0.bias": "model-00002-of-00002.safetensors", "mlp1.0.weight": "model-00002-of-00002.safetensors", "mlp1.1.bias": "model-00002-of-00002.safetensors", "mlp1.1.weight": "model-00002-of-00002.safetensors", "mlp1.3.bias": "model-00002-of-00002.safetensors", "mlp1.3.weight": "model-00002-of-00002.safetensors", "vision_model.embeddings.class_embedding": "model-00001-of-00002.safetensors", "vision_model.embeddings.patch_embedding.bias": "model-00001-of-00002.safetensors", "vision_model.embeddings.patch_embedding.weight": "model-00001-of-00002.safetensors", "vision_model.embeddings.position_embedding": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.0.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.1.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.10.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.11.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.12.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.13.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.14.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.15.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.16.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.17.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.18.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.19.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.2.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.20.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.21.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.22.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.23.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.3.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.4.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.5.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.6.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.7.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.8.norm2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.attn.proj.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.attn.qkv.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.ls1": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.ls2": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.norm1.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.norm1.weight": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.norm2.bias": "model-00001-of-00002.safetensors", "vision_model.encoder.layers.9.norm2.weight": "model-00001-of-00002.safetensors"}}