{"add_bos_token": true, "add_eos_token": false, "added_tokens_decoder": {"0": {"content": "<unk>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "<s>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "2": {"content": "</s>", "lstrip": false, "normalized": false, "rstrip": true, "single_word": false, "special": true}, "32000": {"content": "<|endoftext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "32001": {"content": "<|assistant|>", "lstrip": false, "normalized": false, "rstrip": true, "single_word": false, "special": true}, "32002": {"content": "<|placeholder1|>", "lstrip": false, "normalized": false, "rstrip": true, "single_word": false, "special": true}, "32003": {"content": "<|placeholder2|>", "lstrip": false, "normalized": false, "rstrip": true, "single_word": false, "special": true}, "32004": {"content": "<|placeholder3|>", "lstrip": false, "normalized": false, "rstrip": true, "single_word": false, "special": true}, "32005": {"content": "<|placeholder4|>", "lstrip": false, "normalized": false, "rstrip": true, "single_word": false, "special": true}, "32006": {"content": "<|system|>", "lstrip": false, "normalized": false, "rstrip": true, "single_word": false, "special": true}, "32007": {"content": "<|end|>", "lstrip": false, "normalized": false, "rstrip": true, "single_word": false, "special": true}, "32008": {"content": "<|placeholder5|>", "lstrip": false, "normalized": false, "rstrip": true, "single_word": false, "special": true}, "32009": {"content": "<|placeholder6|>", "lstrip": false, "normalized": false, "rstrip": true, "single_word": false, "special": true}, "32010": {"content": "<|user|>", "lstrip": false, "normalized": false, "rstrip": true, "single_word": false, "special": true}, "32011": {"content": "<img>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "32012": {"content": "</img>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "32013": {"content": "<IMG_CONTEXT>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "32014": {"content": "<quad>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "32015": {"content": "</quad>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "32016": {"content": "<ref>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "32017": {"content": "</ref>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "32018": {"content": "<box>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "32019": {"content": "</box>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "additional_special_tokens": ["<img>", "</img>", "<IMG_CONTEXT>", "<quad>", "</quad>", "<ref>", "</ref>", "<box>", "</box>"], "bos_token": "<s>", "chat_template": "{{ bos_token }}{% for message in messages %}{% if (message['role'] == 'user') %}{{'<|user|>' + '\n' + message['content'] + '<|end|>' + '\n' + '<|assistant|>' + '\n'}}{% elif (message['role'] == 'assistant') %}{{message['content'] + '<|end|>' + '\n'}}{% endif %}{% endfor %}", "clean_up_tokenization_spaces": false, "eos_token": "</s>", "legacy": false, "model_max_length": 8192, "pad_token": "</s>", "padding_side": "right", "sp_model_kwargs": {}, "spaces_between_special_tokens": false, "tokenizer_class": "LlamaTokenizer", "unk_token": "<unk>", "use_default_system_prompt": false}