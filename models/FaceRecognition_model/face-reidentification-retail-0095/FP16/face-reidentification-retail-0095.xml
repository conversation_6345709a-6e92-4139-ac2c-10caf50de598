<?xml version="1.0" ?>
<net name="face-reidentification-retail-0095" version="10">
	<layers>
		<layer id="0" name="0" type="Parameter" version="opset1">
			<data element_type="f16" shape="1, 3, 128, 128"/>
			<output>
				<port id="0" names="0" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>128</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="1" name="data_mul_11616" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="1, 3, 1, 1" size="6"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="2" name="0/scale/Fused_Mul_" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>128</dim>
					<dim>128</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>128</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="3" name="data_add_11618" type="Const" version="opset1">
			<data element_type="f16" offset="6" shape="1, 3, 1, 1" size="6"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="409/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>128</dim>
					<dim>128</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="409" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>128</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="5" name="411/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="12" shape="64, 3, 3, 3" size="3456"/>
			<output>
				<port id="0" names="6" precision="FP16">
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="410" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="2, 2"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>128</dim>
					<dim>128</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="data_add_1162111626" type="Const" version="opset1">
			<data element_type="f16" offset="3468" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="8" name="411/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="411" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="9" name="12" type="Const" version="opset1">
			<data element_type="f32" offset="3596" shape="1" size="4"/>
			<output>
				<port id="0" names="12" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="412" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="412" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="414/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="3600" shape="64, 1, 1, 3, 3" size="1152"/>
			<output>
				<port id="0" names="13" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="413" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="data_add_1162911634" type="Const" version="opset1">
			<data element_type="f16" offset="4752" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="414/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="414" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="15" name="19" type="Const" version="opset1">
			<data element_type="f32" offset="4880" shape="1" size="4"/>
			<output>
				<port id="0" names="19" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="16" name="415" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="415" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="417/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="4884" shape="128, 64, 1, 1" size="16384"/>
			<output>
				<port id="0" names="20" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="416" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="data_add_1163711642" type="Const" version="opset1">
			<data element_type="f16" offset="21268" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="417/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="417" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="21" name="26" type="Const" version="opset1">
			<data element_type="f32" offset="21524" shape="1" size="4"/>
			<output>
				<port id="0" names="26" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="418" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="418" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="23" name="420/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="21528" shape="128, 1, 1, 3, 3" size="2304"/>
			<output>
				<port id="0" names="27" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="24" name="419" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="2, 2"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="25" name="data_add_1164511650" type="Const" version="opset1">
			<data element_type="f16" offset="23832" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="420/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="420" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="33" type="Const" version="opset1">
			<data element_type="f32" offset="24088" shape="1" size="4"/>
			<output>
				<port id="0" names="33" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="421" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="421" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="423/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="24092" shape="64, 128, 1, 1" size="16384"/>
			<output>
				<port id="0" names="34" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="30" name="422" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="data_add_1165311658" type="Const" version="opset1">
			<data element_type="f16" offset="40476" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="32" name="423/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="423,424" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="425" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="32, 32" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="32, 32"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" names="425" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="40" type="Const" version="opset1">
			<data element_type="f16" offset="40604" shape="8, 64, 1, 1" size="1024"/>
			<output>
				<port id="0" names="40" precision="FP16">
					<dim>8</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="426/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>8</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="426/Dims6960" type="Const" version="opset1">
			<data element_type="f16" offset="41628" shape="1, 8, 1, 1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="37" name="426" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="426" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="38" name="44" type="Const" version="opset1">
			<data element_type="f32" offset="41644" shape="1" size="4"/>
			<output>
				<port id="0" names="44" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="39" name="427" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="427" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="40" name="42" type="Const" version="opset1">
			<data element_type="f16" offset="41648" shape="64, 8, 1, 1" size="1024"/>
			<output>
				<port id="0" names="42" precision="FP16">
					<dim>64</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="41" name="428/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="42" name="428/Dims7062" type="Const" version="opset1">
			<data element_type="f16" offset="42672" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="43" name="428" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="428" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="44" name="429" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="429" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="45" name="430" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="430" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="46" name="432/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="42800" shape="128, 64, 1, 1" size="16384"/>
			<output>
				<port id="0" names="45" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="47" name="431" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="48" name="data_add_1166111666" type="Const" version="opset1">
			<data element_type="f16" offset="59184" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="49" name="432/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="432" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="50" name="51" type="Const" version="opset1">
			<data element_type="f32" offset="59440" shape="1" size="4"/>
			<output>
				<port id="0" names="51" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="51" name="433" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="433" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="52" name="435/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="59444" shape="128, 1, 1, 3, 3" size="2304"/>
			<output>
				<port id="0" names="52" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="53" name="434" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="54" name="data_add_1166911674" type="Const" version="opset1">
			<data element_type="f16" offset="61748" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="55" name="435/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="435" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="56" name="58" type="Const" version="opset1">
			<data element_type="f32" offset="62004" shape="1" size="4"/>
			<output>
				<port id="0" names="58" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="57" name="436" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="436" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="58" name="438/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="62008" shape="64, 128, 1, 1" size="16384"/>
			<output>
				<port id="0" names="59" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="59" name="437" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="60" name="data_add_1167711682" type="Const" version="opset1">
			<data element_type="f16" offset="78392" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="61" name="438/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="438,439" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="62" name="440" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="32, 32" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="32, 32"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" names="440" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="63" name="65" type="Const" version="opset1">
			<data element_type="f16" offset="78520" shape="8, 64, 1, 1" size="1024"/>
			<output>
				<port id="0" names="65" precision="FP16">
					<dim>8</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="64" name="441/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>8</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="65" name="441/Dims6918" type="Const" version="opset1">
			<data element_type="f16" offset="79544" shape="1, 8, 1, 1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="66" name="441" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="441" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="67" name="69" type="Const" version="opset1">
			<data element_type="f32" offset="79560" shape="1" size="4"/>
			<output>
				<port id="0" names="69" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="68" name="442" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="442" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="69" name="67" type="Const" version="opset1">
			<data element_type="f16" offset="79564" shape="64, 8, 1, 1" size="1024"/>
			<output>
				<port id="0" names="67" precision="FP16">
					<dim>64</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="70" name="443/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="71" name="443/Dims6990" type="Const" version="opset1">
			<data element_type="f16" offset="80588" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="72" name="443" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="443" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="73" name="444" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="444" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="74" name="445" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="445" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="75" name="446" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="2" names="446" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="76" name="448/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="80716" shape="128, 64, 1, 1" size="16384"/>
			<output>
				<port id="0" names="70" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="77" name="447" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="78" name="data_add_1168511690" type="Const" version="opset1">
			<data element_type="f16" offset="97100" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="79" name="448/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="448" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="80" name="76" type="Const" version="opset1">
			<data element_type="f32" offset="97356" shape="1" size="4"/>
			<output>
				<port id="0" names="76" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="81" name="449" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="449" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="82" name="451/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="97360" shape="128, 1, 1, 3, 3" size="2304"/>
			<output>
				<port id="0" names="77" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="83" name="450" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="84" name="data_add_1169311698" type="Const" version="opset1">
			<data element_type="f16" offset="99664" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="85" name="451/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="451" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="86" name="83" type="Const" version="opset1">
			<data element_type="f32" offset="99920" shape="1" size="4"/>
			<output>
				<port id="0" names="83" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="87" name="452" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="452" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="88" name="454/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="99924" shape="64, 128, 1, 1" size="16384"/>
			<output>
				<port id="0" names="84" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="89" name="453" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="90" name="data_add_1170111706" type="Const" version="opset1">
			<data element_type="f16" offset="116308" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="91" name="454/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="454,455" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="92" name="456" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="32, 32" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="32, 32"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" names="456" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="93" name="90" type="Const" version="opset1">
			<data element_type="f16" offset="116436" shape="8, 64, 1, 1" size="1024"/>
			<output>
				<port id="0" names="90" precision="FP16">
					<dim>8</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="94" name="457/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>8</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="95" name="457/Dims7020" type="Const" version="opset1">
			<data element_type="f16" offset="117460" shape="1, 8, 1, 1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="96" name="457" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="457" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="97" name="94" type="Const" version="opset1">
			<data element_type="f32" offset="117476" shape="1" size="4"/>
			<output>
				<port id="0" names="94" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="98" name="458" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="458" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="99" name="92" type="Const" version="opset1">
			<data element_type="f16" offset="117480" shape="64, 8, 1, 1" size="1024"/>
			<output>
				<port id="0" names="92" precision="FP16">
					<dim>64</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="100" name="459/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="101" name="459/Dims7056" type="Const" version="opset1">
			<data element_type="f16" offset="118504" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="102" name="459" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="459" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="103" name="460" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="460" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="104" name="461" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="461" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="105" name="462" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="2" names="462" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="106" name="464/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="118632" shape="128, 64, 1, 1" size="16384"/>
			<output>
				<port id="0" names="95" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="107" name="463" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="108" name="data_add_1170911714" type="Const" version="opset1">
			<data element_type="f16" offset="135016" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="109" name="464/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="464" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="110" name="101" type="Const" version="opset1">
			<data element_type="f32" offset="135272" shape="1" size="4"/>
			<output>
				<port id="0" names="101" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="111" name="465" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="465" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="112" name="467/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="135276" shape="128, 1, 1, 3, 3" size="2304"/>
			<output>
				<port id="0" names="102" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="113" name="466" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="114" name="data_add_1171711722" type="Const" version="opset1">
			<data element_type="f16" offset="137580" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="115" name="467/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="467" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="116" name="108" type="Const" version="opset1">
			<data element_type="f32" offset="137836" shape="1" size="4"/>
			<output>
				<port id="0" names="108" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="117" name="468" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="468" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="118" name="470/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="137840" shape="64, 128, 1, 1" size="16384"/>
			<output>
				<port id="0" names="109" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="119" name="469" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="120" name="data_add_1172511730" type="Const" version="opset1">
			<data element_type="f16" offset="154224" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="121" name="470/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="470,471" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="122" name="472" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="32, 32" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="32, 32"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" names="472" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="123" name="115" type="Const" version="opset1">
			<data element_type="f16" offset="154352" shape="8, 64, 1, 1" size="1024"/>
			<output>
				<port id="0" names="115" precision="FP16">
					<dim>8</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="124" name="473/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>8</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="125" name="473/Dims6912" type="Const" version="opset1">
			<data element_type="f16" offset="155376" shape="1, 8, 1, 1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="126" name="473" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="473" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="127" name="119" type="Const" version="opset1">
			<data element_type="f32" offset="155392" shape="1" size="4"/>
			<output>
				<port id="0" names="119" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="128" name="474" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="474" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="129" name="117" type="Const" version="opset1">
			<data element_type="f16" offset="155396" shape="64, 8, 1, 1" size="1024"/>
			<output>
				<port id="0" names="117" precision="FP16">
					<dim>64</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="130" name="475/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="131" name="475/Dims6942" type="Const" version="opset1">
			<data element_type="f16" offset="156420" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="132" name="475" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="475" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="133" name="476" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="476" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="134" name="477" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="477" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="135" name="478" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="2" names="478" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="136" name="480/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="156548" shape="128, 64, 1, 1" size="16384"/>
			<output>
				<port id="0" names="120" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="137" name="479" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="138" name="data_add_1173311738" type="Const" version="opset1">
			<data element_type="f16" offset="172932" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="139" name="480/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="480" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="140" name="126" type="Const" version="opset1">
			<data element_type="f32" offset="173188" shape="1" size="4"/>
			<output>
				<port id="0" names="126" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="141" name="481" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="481" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="142" name="483/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="173192" shape="128, 1, 1, 3, 3" size="2304"/>
			<output>
				<port id="0" names="127" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="143" name="482" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="144" name="data_add_1174111746" type="Const" version="opset1">
			<data element_type="f16" offset="175496" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="145" name="483/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="483" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="146" name="133" type="Const" version="opset1">
			<data element_type="f32" offset="175752" shape="1" size="4"/>
			<output>
				<port id="0" names="133" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="147" name="484" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="484" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="148" name="486/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="175756" shape="64, 128, 1, 1" size="16384"/>
			<output>
				<port id="0" names="134" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="149" name="485" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="150" name="data_add_1174911754" type="Const" version="opset1">
			<data element_type="f16" offset="192140" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="151" name="486/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="486,487" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="152" name="488" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="32, 32" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="32, 32"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" names="488" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="153" name="140" type="Const" version="opset1">
			<data element_type="f16" offset="192268" shape="8, 64, 1, 1" size="1024"/>
			<output>
				<port id="0" names="140" precision="FP16">
					<dim>8</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="154" name="489/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>8</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="155" name="489/Dims6894" type="Const" version="opset1">
			<data element_type="f16" offset="193292" shape="1, 8, 1, 1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="156" name="489" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="489" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="157" name="144" type="Const" version="opset1">
			<data element_type="f32" offset="193308" shape="1" size="4"/>
			<output>
				<port id="0" names="144" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="158" name="490" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="490" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="159" name="142" type="Const" version="opset1">
			<data element_type="f16" offset="193312" shape="64, 8, 1, 1" size="1024"/>
			<output>
				<port id="0" names="142" precision="FP16">
					<dim>64</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="160" name="491/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="161" name="491/Dims6948" type="Const" version="opset1">
			<data element_type="f16" offset="194336" shape="1, 64, 1, 1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="162" name="491" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="491" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="163" name="492" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="492" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="164" name="493" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="493" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="165" name="494" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="2" names="494" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="166" name="496/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="194464" shape="256, 64, 1, 1" size="32768"/>
			<output>
				<port id="0" names="145" precision="FP16">
					<dim>256</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="167" name="495" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="168" name="data_add_1175711762" type="Const" version="opset1">
			<data element_type="f16" offset="227232" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="169" name="496/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="496" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="170" name="151" type="Const" version="opset1">
			<data element_type="f32" offset="227744" shape="1" size="4"/>
			<output>
				<port id="0" names="151" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="171" name="497" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="497" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="172" name="499/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="227748" shape="256, 1, 1, 3, 3" size="4608"/>
			<output>
				<port id="0" names="152" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="173" name="498" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="2, 2"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="174" name="data_add_1176511770" type="Const" version="opset1">
			<data element_type="f16" offset="232356" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="175" name="499/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="499" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="176" name="158" type="Const" version="opset1">
			<data element_type="f32" offset="232868" shape="1" size="4"/>
			<output>
				<port id="0" names="158" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="177" name="500" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="500" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="178" name="502/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="232872" shape="128, 256, 1, 1" size="65536"/>
			<output>
				<port id="0" names="159" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="179" name="501" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="180" name="data_add_1177311778" type="Const" version="opset1">
			<data element_type="f16" offset="298408" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="181" name="502/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="502,503" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="182" name="504" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="16, 16" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="16, 16"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" names="504" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="183" name="165" type="Const" version="opset1">
			<data element_type="f16" offset="298664" shape="16, 128, 1, 1" size="4096"/>
			<output>
				<port id="0" names="165" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="184" name="505/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="185" name="505/Dims6924" type="Const" version="opset1">
			<data element_type="f16" offset="302760" shape="1, 16, 1, 1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="186" name="505" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="505" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="187" name="169" type="Const" version="opset1">
			<data element_type="f32" offset="302792" shape="1" size="4"/>
			<output>
				<port id="0" names="169" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="188" name="506" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="506" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="189" name="167" type="Const" version="opset1">
			<data element_type="f16" offset="302796" shape="128, 16, 1, 1" size="4096"/>
			<output>
				<port id="0" names="167" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="190" name="507/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="191" name="507/Dims7026" type="Const" version="opset1">
			<data element_type="f16" offset="306892" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="192" name="507" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="507" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="193" name="508" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="508" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="194" name="509" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="509" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="195" name="511/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="307148" shape="256, 128, 1, 1" size="65536"/>
			<output>
				<port id="0" names="170" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="196" name="510" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="197" name="data_add_1178111786" type="Const" version="opset1">
			<data element_type="f16" offset="372684" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="198" name="511/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="511" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="199" name="176" type="Const" version="opset1">
			<data element_type="f32" offset="373196" shape="1" size="4"/>
			<output>
				<port id="0" names="176" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="200" name="512" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="512" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="201" name="514/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="373200" shape="256, 1, 1, 3, 3" size="4608"/>
			<output>
				<port id="0" names="177" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="202" name="513" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="203" name="data_add_1178911794" type="Const" version="opset1">
			<data element_type="f16" offset="377808" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="204" name="514/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="514" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="205" name="183" type="Const" version="opset1">
			<data element_type="f32" offset="378320" shape="1" size="4"/>
			<output>
				<port id="0" names="183" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="206" name="515" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="515" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="207" name="517/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="378324" shape="128, 256, 1, 1" size="65536"/>
			<output>
				<port id="0" names="184" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="208" name="516" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="209" name="data_add_1179711802" type="Const" version="opset1">
			<data element_type="f16" offset="443860" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="210" name="517/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="517,518" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="211" name="519" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="16, 16" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="16, 16"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" names="519" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="212" name="190" type="Const" version="opset1">
			<data element_type="f16" offset="444116" shape="16, 128, 1, 1" size="4096"/>
			<output>
				<port id="0" names="190" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="213" name="520/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="214" name="520/Dims6996" type="Const" version="opset1">
			<data element_type="f16" offset="448212" shape="1, 16, 1, 1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="215" name="520" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="520" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="216" name="194" type="Const" version="opset1">
			<data element_type="f32" offset="448244" shape="1" size="4"/>
			<output>
				<port id="0" names="194" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="217" name="521" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="521" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="218" name="192" type="Const" version="opset1">
			<data element_type="f16" offset="448248" shape="128, 16, 1, 1" size="4096"/>
			<output>
				<port id="0" names="192" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="219" name="522/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="220" name="522/Dims7002" type="Const" version="opset1">
			<data element_type="f16" offset="452344" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="221" name="522" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="522" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="222" name="523" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="523" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="223" name="524" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="524" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="224" name="525" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="2" names="525" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="225" name="527/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="452600" shape="256, 128, 1, 1" size="65536"/>
			<output>
				<port id="0" names="195" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="226" name="526" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="227" name="data_add_1180511810" type="Const" version="opset1">
			<data element_type="f16" offset="518136" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="228" name="527/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="527" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="229" name="201" type="Const" version="opset1">
			<data element_type="f32" offset="518648" shape="1" size="4"/>
			<output>
				<port id="0" names="201" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="230" name="528" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="528" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="231" name="530/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="518652" shape="256, 1, 1, 3, 3" size="4608"/>
			<output>
				<port id="0" names="202" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="232" name="529" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="233" name="data_add_1181311818" type="Const" version="opset1">
			<data element_type="f16" offset="523260" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="234" name="530/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="530" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="235" name="208" type="Const" version="opset1">
			<data element_type="f32" offset="523772" shape="1" size="4"/>
			<output>
				<port id="0" names="208" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="236" name="531" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="531" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="237" name="533/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="523776" shape="128, 256, 1, 1" size="65536"/>
			<output>
				<port id="0" names="209" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="238" name="532" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="239" name="data_add_1182111826" type="Const" version="opset1">
			<data element_type="f16" offset="589312" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="240" name="533/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="533,534" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="241" name="535" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="16, 16" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="16, 16"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" names="535" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="242" name="215" type="Const" version="opset1">
			<data element_type="f16" offset="589568" shape="16, 128, 1, 1" size="4096"/>
			<output>
				<port id="0" names="215" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="243" name="536/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="244" name="536/Dims6888" type="Const" version="opset1">
			<data element_type="f16" offset="593664" shape="1, 16, 1, 1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="245" name="536" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="536" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="246" name="219" type="Const" version="opset1">
			<data element_type="f32" offset="593696" shape="1" size="4"/>
			<output>
				<port id="0" names="219" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="247" name="537" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="537" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="248" name="217" type="Const" version="opset1">
			<data element_type="f16" offset="593700" shape="128, 16, 1, 1" size="4096"/>
			<output>
				<port id="0" names="217" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="249" name="538/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="250" name="538/Dims6966" type="Const" version="opset1">
			<data element_type="f16" offset="597796" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="251" name="538" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="538" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="252" name="539" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="539" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="253" name="540" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="540" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="254" name="541" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="2" names="541" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="255" name="543/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="598052" shape="256, 128, 1, 1" size="65536"/>
			<output>
				<port id="0" names="220" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="256" name="542" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="257" name="data_add_1182911834" type="Const" version="opset1">
			<data element_type="f16" offset="663588" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="258" name="543/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="543" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="259" name="226" type="Const" version="opset1">
			<data element_type="f32" offset="664100" shape="1" size="4"/>
			<output>
				<port id="0" names="226" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="260" name="544" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="544" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="261" name="546/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="664104" shape="256, 1, 1, 3, 3" size="4608"/>
			<output>
				<port id="0" names="227" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="262" name="545" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="263" name="data_add_1183711842" type="Const" version="opset1">
			<data element_type="f16" offset="668712" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="264" name="546/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="546" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="265" name="233" type="Const" version="opset1">
			<data element_type="f32" offset="669224" shape="1" size="4"/>
			<output>
				<port id="0" names="233" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="266" name="547" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="547" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="267" name="549/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="669228" shape="128, 256, 1, 1" size="65536"/>
			<output>
				<port id="0" names="234" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="268" name="548" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="269" name="data_add_1184511850" type="Const" version="opset1">
			<data element_type="f16" offset="734764" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="270" name="549/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="549,550" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="271" name="551" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="16, 16" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="16, 16"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" names="551" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="272" name="240" type="Const" version="opset1">
			<data element_type="f16" offset="735020" shape="16, 128, 1, 1" size="4096"/>
			<output>
				<port id="0" names="240" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="273" name="552/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="274" name="552/Dims6930" type="Const" version="opset1">
			<data element_type="f16" offset="739116" shape="1, 16, 1, 1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="275" name="552" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="552" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="276" name="244" type="Const" version="opset1">
			<data element_type="f32" offset="739148" shape="1" size="4"/>
			<output>
				<port id="0" names="244" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="277" name="553" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="553" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="278" name="242" type="Const" version="opset1">
			<data element_type="f16" offset="739152" shape="128, 16, 1, 1" size="4096"/>
			<output>
				<port id="0" names="242" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="279" name="554/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="280" name="554/Dims6936" type="Const" version="opset1">
			<data element_type="f16" offset="743248" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="281" name="554" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="554" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="282" name="555" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="555" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="283" name="556" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="556" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="284" name="557" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="2" names="557" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="285" name="559/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="743504" shape="256, 128, 1, 1" size="65536"/>
			<output>
				<port id="0" names="245" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="286" name="558" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="287" name="data_add_1185311858" type="Const" version="opset1">
			<data element_type="f16" offset="809040" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="288" name="559/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="559" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="289" name="251" type="Const" version="opset1">
			<data element_type="f32" offset="809552" shape="1" size="4"/>
			<output>
				<port id="0" names="251" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="290" name="560" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="560" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="291" name="562/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="809556" shape="256, 1, 1, 3, 3" size="4608"/>
			<output>
				<port id="0" names="252" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="292" name="561" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="293" name="data_add_1186111866" type="Const" version="opset1">
			<data element_type="f16" offset="814164" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="294" name="562/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="562" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="295" name="258" type="Const" version="opset1">
			<data element_type="f32" offset="814676" shape="1" size="4"/>
			<output>
				<port id="0" names="258" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="296" name="563" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="563" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="297" name="565/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="814680" shape="128, 256, 1, 1" size="65536"/>
			<output>
				<port id="0" names="259" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="298" name="564" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="299" name="data_add_1186911874" type="Const" version="opset1">
			<data element_type="f16" offset="880216" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="300" name="565/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="565,566" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="301" name="567" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="16, 16" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="16, 16"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" names="567" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="302" name="265" type="Const" version="opset1">
			<data element_type="f16" offset="880472" shape="16, 128, 1, 1" size="4096"/>
			<output>
				<port id="0" names="265" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="303" name="568/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="304" name="568/Dims7008" type="Const" version="opset1">
			<data element_type="f16" offset="884568" shape="1, 16, 1, 1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="305" name="568" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="568" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="306" name="269" type="Const" version="opset1">
			<data element_type="f32" offset="884600" shape="1" size="4"/>
			<output>
				<port id="0" names="269" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="307" name="569" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="569" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="308" name="267" type="Const" version="opset1">
			<data element_type="f16" offset="884604" shape="128, 16, 1, 1" size="4096"/>
			<output>
				<port id="0" names="267" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="309" name="570/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="310" name="570/Dims6906" type="Const" version="opset1">
			<data element_type="f16" offset="888700" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="311" name="570" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="570" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="312" name="571" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="571" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="313" name="572" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="572" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="314" name="573" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="2" names="573" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="315" name="575/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="888956" shape="256, 128, 1, 1" size="65536"/>
			<output>
				<port id="0" names="270" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="316" name="574" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="317" name="data_add_1187711882" type="Const" version="opset1">
			<data element_type="f16" offset="954492" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="318" name="575/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="575" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="319" name="276" type="Const" version="opset1">
			<data element_type="f32" offset="955004" shape="1" size="4"/>
			<output>
				<port id="0" names="276" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="320" name="576" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="576" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="321" name="578/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="955008" shape="256, 1, 1, 3, 3" size="4608"/>
			<output>
				<port id="0" names="277" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="322" name="577" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="323" name="data_add_1188511890" type="Const" version="opset1">
			<data element_type="f16" offset="959616" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="324" name="578/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="578" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="325" name="283" type="Const" version="opset1">
			<data element_type="f32" offset="960128" shape="1" size="4"/>
			<output>
				<port id="0" names="283" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="326" name="579" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="579" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="327" name="581/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="960132" shape="128, 256, 1, 1" size="65536"/>
			<output>
				<port id="0" names="284" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="328" name="580" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="329" name="data_add_1189311898" type="Const" version="opset1">
			<data element_type="f16" offset="1025668" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="330" name="581/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="581,582" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="331" name="583" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="16, 16" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="16, 16"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" names="583" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="332" name="290" type="Const" version="opset1">
			<data element_type="f16" offset="1025924" shape="16, 128, 1, 1" size="4096"/>
			<output>
				<port id="0" names="290" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="333" name="584/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="334" name="584/Dims6984" type="Const" version="opset1">
			<data element_type="f16" offset="1030020" shape="1, 16, 1, 1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="335" name="584" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="584" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="336" name="294" type="Const" version="opset1">
			<data element_type="f32" offset="1030052" shape="1" size="4"/>
			<output>
				<port id="0" names="294" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="337" name="585" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="585" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="338" name="292" type="Const" version="opset1">
			<data element_type="f16" offset="1030056" shape="128, 16, 1, 1" size="4096"/>
			<output>
				<port id="0" names="292" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="339" name="586/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="340" name="586/Dims6978" type="Const" version="opset1">
			<data element_type="f16" offset="1034152" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="341" name="586" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="586" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="342" name="587" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="587" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="343" name="588" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="588" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="344" name="589" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="2" names="589" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="345" name="591/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1034408" shape="256, 128, 1, 1" size="65536"/>
			<output>
				<port id="0" names="295" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="346" name="590" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="347" name="data_add_1190111906" type="Const" version="opset1">
			<data element_type="f16" offset="1099944" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="348" name="591/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="591" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="349" name="301" type="Const" version="opset1">
			<data element_type="f32" offset="1100456" shape="1" size="4"/>
			<output>
				<port id="0" names="301" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="350" name="592" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="592" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="351" name="594/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1100460" shape="256, 1, 1, 3, 3" size="4608"/>
			<output>
				<port id="0" names="302" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="352" name="593" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="353" name="data_add_1190911914" type="Const" version="opset1">
			<data element_type="f16" offset="1105068" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="354" name="594/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="594" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="355" name="308" type="Const" version="opset1">
			<data element_type="f32" offset="1105580" shape="1" size="4"/>
			<output>
				<port id="0" names="308" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="356" name="595" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="595" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="357" name="597/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1105584" shape="128, 256, 1, 1" size="65536"/>
			<output>
				<port id="0" names="309" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="358" name="596" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="359" name="data_add_1191711922" type="Const" version="opset1">
			<data element_type="f16" offset="1171120" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="360" name="597/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="597,598" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="361" name="599" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="16, 16" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="16, 16"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" names="599" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="362" name="315" type="Const" version="opset1">
			<data element_type="f16" offset="1171376" shape="16, 128, 1, 1" size="4096"/>
			<output>
				<port id="0" names="315" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="363" name="600/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="364" name="600/Dims6954" type="Const" version="opset1">
			<data element_type="f16" offset="1175472" shape="1, 16, 1, 1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="365" name="600" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="600" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="366" name="319" type="Const" version="opset1">
			<data element_type="f32" offset="1175504" shape="1" size="4"/>
			<output>
				<port id="0" names="319" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="367" name="601" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="601" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="368" name="317" type="Const" version="opset1">
			<data element_type="f16" offset="1175508" shape="128, 16, 1, 1" size="4096"/>
			<output>
				<port id="0" names="317" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="369" name="602/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="370" name="602/Dims6972" type="Const" version="opset1">
			<data element_type="f16" offset="1179604" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="371" name="602" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="602" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="372" name="603" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="603" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="373" name="604" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="604" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="374" name="605" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="2" names="605" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="375" name="607/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1179860" shape="512, 128, 1, 1" size="131072"/>
			<output>
				<port id="0" names="320" precision="FP16">
					<dim>512</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="376" name="606" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>512</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="377" name="data_add_1192511930" type="Const" version="opset1">
			<data element_type="f16" offset="1310932" shape="1, 512, 1, 1" size="1024"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="378" name="607/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="607" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="379" name="326" type="Const" version="opset1">
			<data element_type="f32" offset="1311956" shape="1" size="4"/>
			<output>
				<port id="0" names="326" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="380" name="608" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="608" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="381" name="610/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1311960" shape="512, 1, 1, 3, 3" size="9216"/>
			<output>
				<port id="0" names="327" precision="FP16">
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="382" name="609" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="2, 2"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="383" name="data_add_1193311938" type="Const" version="opset1">
			<data element_type="f16" offset="1321176" shape="1, 512, 1, 1" size="1024"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="384" name="610/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="610" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="385" name="333" type="Const" version="opset1">
			<data element_type="f32" offset="1322200" shape="1" size="4"/>
			<output>
				<port id="0" names="333" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="386" name="611" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="611" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="387" name="613/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1322204" shape="128, 512, 1, 1" size="131072"/>
			<output>
				<port id="0" names="334" precision="FP16">
					<dim>128</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="388" name="612" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="389" name="data_add_1194111946" type="Const" version="opset1">
			<data element_type="f16" offset="1453276" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="390" name="613/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="613,614" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="391" name="615" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="8, 8" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="8, 8"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</input>
			<output>
				<port id="1" names="615" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="392" name="340" type="Const" version="opset1">
			<data element_type="f16" offset="1453532" shape="16, 128, 1, 1" size="4096"/>
			<output>
				<port id="0" names="340" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="393" name="616/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="394" name="616/Dims7050" type="Const" version="opset1">
			<data element_type="f16" offset="1457628" shape="1, 16, 1, 1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="395" name="616" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="616" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="396" name="344" type="Const" version="opset1">
			<data element_type="f32" offset="1457660" shape="1" size="4"/>
			<output>
				<port id="0" names="344" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="397" name="617" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="617" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="398" name="342" type="Const" version="opset1">
			<data element_type="f16" offset="1457664" shape="128, 16, 1, 1" size="4096"/>
			<output>
				<port id="0" names="342" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="399" name="618/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="400" name="618/Dims7014" type="Const" version="opset1">
			<data element_type="f16" offset="1461760" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="401" name="618" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="618" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="402" name="619" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="619" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="403" name="620" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="620" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="404" name="622/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1462016" shape="256, 128, 1, 1" size="65536"/>
			<output>
				<port id="0" names="345" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="405" name="621" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="406" name="data_add_1194911954" type="Const" version="opset1">
			<data element_type="f16" offset="1527552" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="407" name="622/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="622" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="408" name="351" type="Const" version="opset1">
			<data element_type="f32" offset="1528064" shape="1" size="4"/>
			<output>
				<port id="0" names="351" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="409" name="623" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="623" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="410" name="625/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1528068" shape="256, 1, 1, 3, 3" size="4608"/>
			<output>
				<port id="0" names="352" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="411" name="624" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="412" name="data_add_1195711962" type="Const" version="opset1">
			<data element_type="f16" offset="1532676" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="413" name="625/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="625" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="414" name="358" type="Const" version="opset1">
			<data element_type="f32" offset="1533188" shape="1" size="4"/>
			<output>
				<port id="0" names="358" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="415" name="626" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="626" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="416" name="628/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1533192" shape="128, 256, 1, 1" size="65536"/>
			<output>
				<port id="0" names="359" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="417" name="627" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="418" name="data_add_1196511970" type="Const" version="opset1">
			<data element_type="f16" offset="1598728" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="419" name="628/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="628,629" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="420" name="630" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="8, 8" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="8, 8"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</input>
			<output>
				<port id="1" names="630" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="421" name="365" type="Const" version="opset1">
			<data element_type="f16" offset="1598984" shape="16, 128, 1, 1" size="4096"/>
			<output>
				<port id="0" names="365" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="422" name="631/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="423" name="631/Dims7044" type="Const" version="opset1">
			<data element_type="f16" offset="1603080" shape="1, 16, 1, 1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="424" name="631" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="631" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="425" name="369" type="Const" version="opset1">
			<data element_type="f32" offset="1603112" shape="1" size="4"/>
			<output>
				<port id="0" names="369" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="426" name="632" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="632" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="427" name="367" type="Const" version="opset1">
			<data element_type="f16" offset="1603116" shape="128, 16, 1, 1" size="4096"/>
			<output>
				<port id="0" names="367" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="428" name="633/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="429" name="633/Dims7038" type="Const" version="opset1">
			<data element_type="f16" offset="1607212" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="430" name="633" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="633" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="431" name="634" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="634" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="432" name="635" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="635" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="433" name="636" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</input>
			<output>
				<port id="2" names="636" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="434" name="638/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1607468" shape="256, 128, 1, 1" size="65536"/>
			<output>
				<port id="0" names="370" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="435" name="637" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="436" name="data_add_1197311978" type="Const" version="opset1">
			<data element_type="f16" offset="1673004" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="437" name="638/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="638" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="438" name="376" type="Const" version="opset1">
			<data element_type="f32" offset="1673516" shape="1" size="4"/>
			<output>
				<port id="0" names="376" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="439" name="639" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="639" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="440" name="641/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1673520" shape="256, 1, 1, 3, 3" size="4608"/>
			<output>
				<port id="0" names="377" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="441" name="640" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="442" name="data_add_1198111986" type="Const" version="opset1">
			<data element_type="f16" offset="1678128" shape="1, 256, 1, 1" size="512"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="443" name="641/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="641" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="444" name="383" type="Const" version="opset1">
			<data element_type="f32" offset="1678640" shape="1" size="4"/>
			<output>
				<port id="0" names="383" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="445" name="642" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="642" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="446" name="644/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1678644" shape="128, 256, 1, 1" size="65536"/>
			<output>
				<port id="0" names="384" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="447" name="643" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="448" name="data_add_1198911994" type="Const" version="opset1">
			<data element_type="f16" offset="1744180" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="449" name="644/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="644,645" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="450" name="646" type="AvgPool" version="opset1">
			<data auto_pad="explicit" exclude-pad="true" kernel="8, 8" pads_begin="0, 0" pads_end="0, 0" rounding_type="floor" strides="8, 8"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</input>
			<output>
				<port id="1" names="646" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="451" name="390" type="Const" version="opset1">
			<data element_type="f16" offset="1744436" shape="16, 128, 1, 1" size="4096"/>
			<output>
				<port id="0" names="390" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="452" name="647/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="453" name="647/Dims7032" type="Const" version="opset1">
			<data element_type="f16" offset="1748532" shape="1, 16, 1, 1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="454" name="647" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="647" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="455" name="394" type="Const" version="opset1">
			<data element_type="f32" offset="1748564" shape="1" size="4"/>
			<output>
				<port id="0" names="394" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="456" name="648" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="648" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="457" name="392" type="Const" version="opset1">
			<data element_type="f16" offset="1748568" shape="128, 16, 1, 1" size="4096"/>
			<output>
				<port id="0" names="392" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="458" name="649/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="459" name="649/Dims6900" type="Const" version="opset1">
			<data element_type="f16" offset="1752664" shape="1, 128, 1, 1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="460" name="649" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="649" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="461" name="650" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" names="650" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="462" name="651" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="651" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="463" name="652" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</input>
			<output>
				<port id="2" names="652" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="464" name="654/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1752920" shape="512, 128, 1, 1" size="131072"/>
			<output>
				<port id="0" names="395" precision="FP16">
					<dim>512</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="465" name="653" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>512</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="466" name="data_add_1199712002" type="Const" version="opset1">
			<data element_type="f16" offset="1883992" shape="1, 512, 1, 1" size="1024"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="467" name="654/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="654" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="468" name="401" type="Const" version="opset1">
			<data element_type="f32" offset="1885016" shape="1" size="4"/>
			<output>
				<port id="0" names="401" precision="FP32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="469" name="655" type="PReLU" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="655" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="470" name="657/mean/Fused_Mul__copy" type="Const" version="opset1">
			<data element_type="f16" offset="1885020" shape="512, 1, 1, 8, 8" size="65536"/>
			<output>
				<port id="0" names="402" precision="FP16">
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="471" name="656" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="472" name="data_add_1200512010" type="Const" version="opset1">
			<data element_type="f16" offset="1950556" shape="1, 512, 1, 1" size="1024"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="473" name="657/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="657" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="474" name="408" type="Const" version="opset1">
			<data element_type="f16" offset="1951580" shape="256, 512, 1, 1" size="262144"/>
			<output>
				<port id="0" names="408" precision="FP16">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="475" name="658" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" strides="1, 1"/>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP16">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="658" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="476" name="658/sink_port_0" type="Result" version="opset1">
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="2" to-port="0"/>
		<edge from-layer="1" from-port="0" to-layer="2" to-port="1"/>
		<edge from-layer="2" from-port="2" to-layer="4" to-port="0"/>
		<edge from-layer="3" from-port="0" to-layer="4" to-port="1"/>
		<edge from-layer="4" from-port="2" to-layer="6" to-port="0"/>
		<edge from-layer="5" from-port="0" to-layer="6" to-port="1"/>
		<edge from-layer="6" from-port="2" to-layer="8" to-port="0"/>
		<edge from-layer="7" from-port="0" to-layer="8" to-port="1"/>
		<edge from-layer="8" from-port="2" to-layer="10" to-port="0"/>
		<edge from-layer="9" from-port="0" to-layer="10" to-port="1"/>
		<edge from-layer="10" from-port="2" to-layer="12" to-port="0"/>
		<edge from-layer="11" from-port="0" to-layer="12" to-port="1"/>
		<edge from-layer="12" from-port="2" to-layer="14" to-port="0"/>
		<edge from-layer="13" from-port="0" to-layer="14" to-port="1"/>
		<edge from-layer="14" from-port="2" to-layer="16" to-port="0"/>
		<edge from-layer="15" from-port="0" to-layer="16" to-port="1"/>
		<edge from-layer="16" from-port="2" to-layer="18" to-port="0"/>
		<edge from-layer="17" from-port="0" to-layer="18" to-port="1"/>
		<edge from-layer="18" from-port="2" to-layer="20" to-port="0"/>
		<edge from-layer="19" from-port="0" to-layer="20" to-port="1"/>
		<edge from-layer="20" from-port="2" to-layer="22" to-port="0"/>
		<edge from-layer="21" from-port="0" to-layer="22" to-port="1"/>
		<edge from-layer="22" from-port="2" to-layer="24" to-port="0"/>
		<edge from-layer="23" from-port="0" to-layer="24" to-port="1"/>
		<edge from-layer="24" from-port="2" to-layer="26" to-port="0"/>
		<edge from-layer="25" from-port="0" to-layer="26" to-port="1"/>
		<edge from-layer="26" from-port="2" to-layer="28" to-port="0"/>
		<edge from-layer="27" from-port="0" to-layer="28" to-port="1"/>
		<edge from-layer="28" from-port="2" to-layer="30" to-port="0"/>
		<edge from-layer="29" from-port="0" to-layer="30" to-port="1"/>
		<edge from-layer="30" from-port="2" to-layer="32" to-port="0"/>
		<edge from-layer="31" from-port="0" to-layer="32" to-port="1"/>
		<edge from-layer="32" from-port="2" to-layer="33" to-port="0"/>
		<edge from-layer="32" from-port="2" to-layer="45" to-port="0"/>
		<edge from-layer="33" from-port="1" to-layer="35" to-port="0"/>
		<edge from-layer="34" from-port="0" to-layer="35" to-port="1"/>
		<edge from-layer="35" from-port="2" to-layer="37" to-port="0"/>
		<edge from-layer="36" from-port="0" to-layer="37" to-port="1"/>
		<edge from-layer="37" from-port="2" to-layer="39" to-port="0"/>
		<edge from-layer="38" from-port="0" to-layer="39" to-port="1"/>
		<edge from-layer="39" from-port="2" to-layer="41" to-port="0"/>
		<edge from-layer="40" from-port="0" to-layer="41" to-port="1"/>
		<edge from-layer="41" from-port="2" to-layer="43" to-port="0"/>
		<edge from-layer="42" from-port="0" to-layer="43" to-port="1"/>
		<edge from-layer="43" from-port="2" to-layer="44" to-port="0"/>
		<edge from-layer="44" from-port="1" to-layer="45" to-port="1"/>
		<edge from-layer="45" from-port="2" to-layer="47" to-port="0"/>
		<edge from-layer="45" from-port="2" to-layer="75" to-port="0"/>
		<edge from-layer="46" from-port="0" to-layer="47" to-port="1"/>
		<edge from-layer="47" from-port="2" to-layer="49" to-port="0"/>
		<edge from-layer="48" from-port="0" to-layer="49" to-port="1"/>
		<edge from-layer="49" from-port="2" to-layer="51" to-port="0"/>
		<edge from-layer="50" from-port="0" to-layer="51" to-port="1"/>
		<edge from-layer="51" from-port="2" to-layer="53" to-port="0"/>
		<edge from-layer="52" from-port="0" to-layer="53" to-port="1"/>
		<edge from-layer="53" from-port="2" to-layer="55" to-port="0"/>
		<edge from-layer="54" from-port="0" to-layer="55" to-port="1"/>
		<edge from-layer="55" from-port="2" to-layer="57" to-port="0"/>
		<edge from-layer="56" from-port="0" to-layer="57" to-port="1"/>
		<edge from-layer="57" from-port="2" to-layer="59" to-port="0"/>
		<edge from-layer="58" from-port="0" to-layer="59" to-port="1"/>
		<edge from-layer="59" from-port="2" to-layer="61" to-port="0"/>
		<edge from-layer="60" from-port="0" to-layer="61" to-port="1"/>
		<edge from-layer="61" from-port="2" to-layer="62" to-port="0"/>
		<edge from-layer="61" from-port="2" to-layer="74" to-port="0"/>
		<edge from-layer="62" from-port="1" to-layer="64" to-port="0"/>
		<edge from-layer="63" from-port="0" to-layer="64" to-port="1"/>
		<edge from-layer="64" from-port="2" to-layer="66" to-port="0"/>
		<edge from-layer="65" from-port="0" to-layer="66" to-port="1"/>
		<edge from-layer="66" from-port="2" to-layer="68" to-port="0"/>
		<edge from-layer="67" from-port="0" to-layer="68" to-port="1"/>
		<edge from-layer="68" from-port="2" to-layer="70" to-port="0"/>
		<edge from-layer="69" from-port="0" to-layer="70" to-port="1"/>
		<edge from-layer="70" from-port="2" to-layer="72" to-port="0"/>
		<edge from-layer="71" from-port="0" to-layer="72" to-port="1"/>
		<edge from-layer="72" from-port="2" to-layer="73" to-port="0"/>
		<edge from-layer="73" from-port="1" to-layer="74" to-port="1"/>
		<edge from-layer="74" from-port="2" to-layer="75" to-port="1"/>
		<edge from-layer="75" from-port="2" to-layer="77" to-port="0"/>
		<edge from-layer="75" from-port="2" to-layer="105" to-port="0"/>
		<edge from-layer="76" from-port="0" to-layer="77" to-port="1"/>
		<edge from-layer="77" from-port="2" to-layer="79" to-port="0"/>
		<edge from-layer="78" from-port="0" to-layer="79" to-port="1"/>
		<edge from-layer="79" from-port="2" to-layer="81" to-port="0"/>
		<edge from-layer="80" from-port="0" to-layer="81" to-port="1"/>
		<edge from-layer="81" from-port="2" to-layer="83" to-port="0"/>
		<edge from-layer="82" from-port="0" to-layer="83" to-port="1"/>
		<edge from-layer="83" from-port="2" to-layer="85" to-port="0"/>
		<edge from-layer="84" from-port="0" to-layer="85" to-port="1"/>
		<edge from-layer="85" from-port="2" to-layer="87" to-port="0"/>
		<edge from-layer="86" from-port="0" to-layer="87" to-port="1"/>
		<edge from-layer="87" from-port="2" to-layer="89" to-port="0"/>
		<edge from-layer="88" from-port="0" to-layer="89" to-port="1"/>
		<edge from-layer="89" from-port="2" to-layer="91" to-port="0"/>
		<edge from-layer="90" from-port="0" to-layer="91" to-port="1"/>
		<edge from-layer="91" from-port="2" to-layer="92" to-port="0"/>
		<edge from-layer="91" from-port="2" to-layer="104" to-port="0"/>
		<edge from-layer="92" from-port="1" to-layer="94" to-port="0"/>
		<edge from-layer="93" from-port="0" to-layer="94" to-port="1"/>
		<edge from-layer="94" from-port="2" to-layer="96" to-port="0"/>
		<edge from-layer="95" from-port="0" to-layer="96" to-port="1"/>
		<edge from-layer="96" from-port="2" to-layer="98" to-port="0"/>
		<edge from-layer="97" from-port="0" to-layer="98" to-port="1"/>
		<edge from-layer="98" from-port="2" to-layer="100" to-port="0"/>
		<edge from-layer="99" from-port="0" to-layer="100" to-port="1"/>
		<edge from-layer="100" from-port="2" to-layer="102" to-port="0"/>
		<edge from-layer="101" from-port="0" to-layer="102" to-port="1"/>
		<edge from-layer="102" from-port="2" to-layer="103" to-port="0"/>
		<edge from-layer="103" from-port="1" to-layer="104" to-port="1"/>
		<edge from-layer="104" from-port="2" to-layer="105" to-port="1"/>
		<edge from-layer="105" from-port="2" to-layer="107" to-port="0"/>
		<edge from-layer="105" from-port="2" to-layer="135" to-port="0"/>
		<edge from-layer="106" from-port="0" to-layer="107" to-port="1"/>
		<edge from-layer="107" from-port="2" to-layer="109" to-port="0"/>
		<edge from-layer="108" from-port="0" to-layer="109" to-port="1"/>
		<edge from-layer="109" from-port="2" to-layer="111" to-port="0"/>
		<edge from-layer="110" from-port="0" to-layer="111" to-port="1"/>
		<edge from-layer="111" from-port="2" to-layer="113" to-port="0"/>
		<edge from-layer="112" from-port="0" to-layer="113" to-port="1"/>
		<edge from-layer="113" from-port="2" to-layer="115" to-port="0"/>
		<edge from-layer="114" from-port="0" to-layer="115" to-port="1"/>
		<edge from-layer="115" from-port="2" to-layer="117" to-port="0"/>
		<edge from-layer="116" from-port="0" to-layer="117" to-port="1"/>
		<edge from-layer="117" from-port="2" to-layer="119" to-port="0"/>
		<edge from-layer="118" from-port="0" to-layer="119" to-port="1"/>
		<edge from-layer="119" from-port="2" to-layer="121" to-port="0"/>
		<edge from-layer="120" from-port="0" to-layer="121" to-port="1"/>
		<edge from-layer="121" from-port="2" to-layer="122" to-port="0"/>
		<edge from-layer="121" from-port="2" to-layer="134" to-port="0"/>
		<edge from-layer="122" from-port="1" to-layer="124" to-port="0"/>
		<edge from-layer="123" from-port="0" to-layer="124" to-port="1"/>
		<edge from-layer="124" from-port="2" to-layer="126" to-port="0"/>
		<edge from-layer="125" from-port="0" to-layer="126" to-port="1"/>
		<edge from-layer="126" from-port="2" to-layer="128" to-port="0"/>
		<edge from-layer="127" from-port="0" to-layer="128" to-port="1"/>
		<edge from-layer="128" from-port="2" to-layer="130" to-port="0"/>
		<edge from-layer="129" from-port="0" to-layer="130" to-port="1"/>
		<edge from-layer="130" from-port="2" to-layer="132" to-port="0"/>
		<edge from-layer="131" from-port="0" to-layer="132" to-port="1"/>
		<edge from-layer="132" from-port="2" to-layer="133" to-port="0"/>
		<edge from-layer="133" from-port="1" to-layer="134" to-port="1"/>
		<edge from-layer="134" from-port="2" to-layer="135" to-port="1"/>
		<edge from-layer="135" from-port="2" to-layer="137" to-port="0"/>
		<edge from-layer="135" from-port="2" to-layer="165" to-port="0"/>
		<edge from-layer="136" from-port="0" to-layer="137" to-port="1"/>
		<edge from-layer="137" from-port="2" to-layer="139" to-port="0"/>
		<edge from-layer="138" from-port="0" to-layer="139" to-port="1"/>
		<edge from-layer="139" from-port="2" to-layer="141" to-port="0"/>
		<edge from-layer="140" from-port="0" to-layer="141" to-port="1"/>
		<edge from-layer="141" from-port="2" to-layer="143" to-port="0"/>
		<edge from-layer="142" from-port="0" to-layer="143" to-port="1"/>
		<edge from-layer="143" from-port="2" to-layer="145" to-port="0"/>
		<edge from-layer="144" from-port="0" to-layer="145" to-port="1"/>
		<edge from-layer="145" from-port="2" to-layer="147" to-port="0"/>
		<edge from-layer="146" from-port="0" to-layer="147" to-port="1"/>
		<edge from-layer="147" from-port="2" to-layer="149" to-port="0"/>
		<edge from-layer="148" from-port="0" to-layer="149" to-port="1"/>
		<edge from-layer="149" from-port="2" to-layer="151" to-port="0"/>
		<edge from-layer="150" from-port="0" to-layer="151" to-port="1"/>
		<edge from-layer="151" from-port="2" to-layer="152" to-port="0"/>
		<edge from-layer="151" from-port="2" to-layer="164" to-port="0"/>
		<edge from-layer="152" from-port="1" to-layer="154" to-port="0"/>
		<edge from-layer="153" from-port="0" to-layer="154" to-port="1"/>
		<edge from-layer="154" from-port="2" to-layer="156" to-port="0"/>
		<edge from-layer="155" from-port="0" to-layer="156" to-port="1"/>
		<edge from-layer="156" from-port="2" to-layer="158" to-port="0"/>
		<edge from-layer="157" from-port="0" to-layer="158" to-port="1"/>
		<edge from-layer="158" from-port="2" to-layer="160" to-port="0"/>
		<edge from-layer="159" from-port="0" to-layer="160" to-port="1"/>
		<edge from-layer="160" from-port="2" to-layer="162" to-port="0"/>
		<edge from-layer="161" from-port="0" to-layer="162" to-port="1"/>
		<edge from-layer="162" from-port="2" to-layer="163" to-port="0"/>
		<edge from-layer="163" from-port="1" to-layer="164" to-port="1"/>
		<edge from-layer="164" from-port="2" to-layer="165" to-port="1"/>
		<edge from-layer="165" from-port="2" to-layer="167" to-port="0"/>
		<edge from-layer="166" from-port="0" to-layer="167" to-port="1"/>
		<edge from-layer="167" from-port="2" to-layer="169" to-port="0"/>
		<edge from-layer="168" from-port="0" to-layer="169" to-port="1"/>
		<edge from-layer="169" from-port="2" to-layer="171" to-port="0"/>
		<edge from-layer="170" from-port="0" to-layer="171" to-port="1"/>
		<edge from-layer="171" from-port="2" to-layer="173" to-port="0"/>
		<edge from-layer="172" from-port="0" to-layer="173" to-port="1"/>
		<edge from-layer="173" from-port="2" to-layer="175" to-port="0"/>
		<edge from-layer="174" from-port="0" to-layer="175" to-port="1"/>
		<edge from-layer="175" from-port="2" to-layer="177" to-port="0"/>
		<edge from-layer="176" from-port="0" to-layer="177" to-port="1"/>
		<edge from-layer="177" from-port="2" to-layer="179" to-port="0"/>
		<edge from-layer="178" from-port="0" to-layer="179" to-port="1"/>
		<edge from-layer="179" from-port="2" to-layer="181" to-port="0"/>
		<edge from-layer="180" from-port="0" to-layer="181" to-port="1"/>
		<edge from-layer="181" from-port="2" to-layer="182" to-port="0"/>
		<edge from-layer="181" from-port="2" to-layer="194" to-port="0"/>
		<edge from-layer="182" from-port="1" to-layer="184" to-port="0"/>
		<edge from-layer="183" from-port="0" to-layer="184" to-port="1"/>
		<edge from-layer="184" from-port="2" to-layer="186" to-port="0"/>
		<edge from-layer="185" from-port="0" to-layer="186" to-port="1"/>
		<edge from-layer="186" from-port="2" to-layer="188" to-port="0"/>
		<edge from-layer="187" from-port="0" to-layer="188" to-port="1"/>
		<edge from-layer="188" from-port="2" to-layer="190" to-port="0"/>
		<edge from-layer="189" from-port="0" to-layer="190" to-port="1"/>
		<edge from-layer="190" from-port="2" to-layer="192" to-port="0"/>
		<edge from-layer="191" from-port="0" to-layer="192" to-port="1"/>
		<edge from-layer="192" from-port="2" to-layer="193" to-port="0"/>
		<edge from-layer="193" from-port="1" to-layer="194" to-port="1"/>
		<edge from-layer="194" from-port="2" to-layer="196" to-port="0"/>
		<edge from-layer="194" from-port="2" to-layer="224" to-port="0"/>
		<edge from-layer="195" from-port="0" to-layer="196" to-port="1"/>
		<edge from-layer="196" from-port="2" to-layer="198" to-port="0"/>
		<edge from-layer="197" from-port="0" to-layer="198" to-port="1"/>
		<edge from-layer="198" from-port="2" to-layer="200" to-port="0"/>
		<edge from-layer="199" from-port="0" to-layer="200" to-port="1"/>
		<edge from-layer="200" from-port="2" to-layer="202" to-port="0"/>
		<edge from-layer="201" from-port="0" to-layer="202" to-port="1"/>
		<edge from-layer="202" from-port="2" to-layer="204" to-port="0"/>
		<edge from-layer="203" from-port="0" to-layer="204" to-port="1"/>
		<edge from-layer="204" from-port="2" to-layer="206" to-port="0"/>
		<edge from-layer="205" from-port="0" to-layer="206" to-port="1"/>
		<edge from-layer="206" from-port="2" to-layer="208" to-port="0"/>
		<edge from-layer="207" from-port="0" to-layer="208" to-port="1"/>
		<edge from-layer="208" from-port="2" to-layer="210" to-port="0"/>
		<edge from-layer="209" from-port="0" to-layer="210" to-port="1"/>
		<edge from-layer="210" from-port="2" to-layer="211" to-port="0"/>
		<edge from-layer="210" from-port="2" to-layer="223" to-port="0"/>
		<edge from-layer="211" from-port="1" to-layer="213" to-port="0"/>
		<edge from-layer="212" from-port="0" to-layer="213" to-port="1"/>
		<edge from-layer="213" from-port="2" to-layer="215" to-port="0"/>
		<edge from-layer="214" from-port="0" to-layer="215" to-port="1"/>
		<edge from-layer="215" from-port="2" to-layer="217" to-port="0"/>
		<edge from-layer="216" from-port="0" to-layer="217" to-port="1"/>
		<edge from-layer="217" from-port="2" to-layer="219" to-port="0"/>
		<edge from-layer="218" from-port="0" to-layer="219" to-port="1"/>
		<edge from-layer="219" from-port="2" to-layer="221" to-port="0"/>
		<edge from-layer="220" from-port="0" to-layer="221" to-port="1"/>
		<edge from-layer="221" from-port="2" to-layer="222" to-port="0"/>
		<edge from-layer="222" from-port="1" to-layer="223" to-port="1"/>
		<edge from-layer="223" from-port="2" to-layer="224" to-port="1"/>
		<edge from-layer="224" from-port="2" to-layer="254" to-port="0"/>
		<edge from-layer="224" from-port="2" to-layer="226" to-port="0"/>
		<edge from-layer="225" from-port="0" to-layer="226" to-port="1"/>
		<edge from-layer="226" from-port="2" to-layer="228" to-port="0"/>
		<edge from-layer="227" from-port="0" to-layer="228" to-port="1"/>
		<edge from-layer="228" from-port="2" to-layer="230" to-port="0"/>
		<edge from-layer="229" from-port="0" to-layer="230" to-port="1"/>
		<edge from-layer="230" from-port="2" to-layer="232" to-port="0"/>
		<edge from-layer="231" from-port="0" to-layer="232" to-port="1"/>
		<edge from-layer="232" from-port="2" to-layer="234" to-port="0"/>
		<edge from-layer="233" from-port="0" to-layer="234" to-port="1"/>
		<edge from-layer="234" from-port="2" to-layer="236" to-port="0"/>
		<edge from-layer="235" from-port="0" to-layer="236" to-port="1"/>
		<edge from-layer="236" from-port="2" to-layer="238" to-port="0"/>
		<edge from-layer="237" from-port="0" to-layer="238" to-port="1"/>
		<edge from-layer="238" from-port="2" to-layer="240" to-port="0"/>
		<edge from-layer="239" from-port="0" to-layer="240" to-port="1"/>
		<edge from-layer="240" from-port="2" to-layer="241" to-port="0"/>
		<edge from-layer="240" from-port="2" to-layer="253" to-port="0"/>
		<edge from-layer="241" from-port="1" to-layer="243" to-port="0"/>
		<edge from-layer="242" from-port="0" to-layer="243" to-port="1"/>
		<edge from-layer="243" from-port="2" to-layer="245" to-port="0"/>
		<edge from-layer="244" from-port="0" to-layer="245" to-port="1"/>
		<edge from-layer="245" from-port="2" to-layer="247" to-port="0"/>
		<edge from-layer="246" from-port="0" to-layer="247" to-port="1"/>
		<edge from-layer="247" from-port="2" to-layer="249" to-port="0"/>
		<edge from-layer="248" from-port="0" to-layer="249" to-port="1"/>
		<edge from-layer="249" from-port="2" to-layer="251" to-port="0"/>
		<edge from-layer="250" from-port="0" to-layer="251" to-port="1"/>
		<edge from-layer="251" from-port="2" to-layer="252" to-port="0"/>
		<edge from-layer="252" from-port="1" to-layer="253" to-port="1"/>
		<edge from-layer="253" from-port="2" to-layer="254" to-port="1"/>
		<edge from-layer="254" from-port="2" to-layer="256" to-port="0"/>
		<edge from-layer="254" from-port="2" to-layer="284" to-port="0"/>
		<edge from-layer="255" from-port="0" to-layer="256" to-port="1"/>
		<edge from-layer="256" from-port="2" to-layer="258" to-port="0"/>
		<edge from-layer="257" from-port="0" to-layer="258" to-port="1"/>
		<edge from-layer="258" from-port="2" to-layer="260" to-port="0"/>
		<edge from-layer="259" from-port="0" to-layer="260" to-port="1"/>
		<edge from-layer="260" from-port="2" to-layer="262" to-port="0"/>
		<edge from-layer="261" from-port="0" to-layer="262" to-port="1"/>
		<edge from-layer="262" from-port="2" to-layer="264" to-port="0"/>
		<edge from-layer="263" from-port="0" to-layer="264" to-port="1"/>
		<edge from-layer="264" from-port="2" to-layer="266" to-port="0"/>
		<edge from-layer="265" from-port="0" to-layer="266" to-port="1"/>
		<edge from-layer="266" from-port="2" to-layer="268" to-port="0"/>
		<edge from-layer="267" from-port="0" to-layer="268" to-port="1"/>
		<edge from-layer="268" from-port="2" to-layer="270" to-port="0"/>
		<edge from-layer="269" from-port="0" to-layer="270" to-port="1"/>
		<edge from-layer="270" from-port="2" to-layer="271" to-port="0"/>
		<edge from-layer="270" from-port="2" to-layer="283" to-port="0"/>
		<edge from-layer="271" from-port="1" to-layer="273" to-port="0"/>
		<edge from-layer="272" from-port="0" to-layer="273" to-port="1"/>
		<edge from-layer="273" from-port="2" to-layer="275" to-port="0"/>
		<edge from-layer="274" from-port="0" to-layer="275" to-port="1"/>
		<edge from-layer="275" from-port="2" to-layer="277" to-port="0"/>
		<edge from-layer="276" from-port="0" to-layer="277" to-port="1"/>
		<edge from-layer="277" from-port="2" to-layer="279" to-port="0"/>
		<edge from-layer="278" from-port="0" to-layer="279" to-port="1"/>
		<edge from-layer="279" from-port="2" to-layer="281" to-port="0"/>
		<edge from-layer="280" from-port="0" to-layer="281" to-port="1"/>
		<edge from-layer="281" from-port="2" to-layer="282" to-port="0"/>
		<edge from-layer="282" from-port="1" to-layer="283" to-port="1"/>
		<edge from-layer="283" from-port="2" to-layer="284" to-port="1"/>
		<edge from-layer="284" from-port="2" to-layer="286" to-port="0"/>
		<edge from-layer="284" from-port="2" to-layer="314" to-port="0"/>
		<edge from-layer="285" from-port="0" to-layer="286" to-port="1"/>
		<edge from-layer="286" from-port="2" to-layer="288" to-port="0"/>
		<edge from-layer="287" from-port="0" to-layer="288" to-port="1"/>
		<edge from-layer="288" from-port="2" to-layer="290" to-port="0"/>
		<edge from-layer="289" from-port="0" to-layer="290" to-port="1"/>
		<edge from-layer="290" from-port="2" to-layer="292" to-port="0"/>
		<edge from-layer="291" from-port="0" to-layer="292" to-port="1"/>
		<edge from-layer="292" from-port="2" to-layer="294" to-port="0"/>
		<edge from-layer="293" from-port="0" to-layer="294" to-port="1"/>
		<edge from-layer="294" from-port="2" to-layer="296" to-port="0"/>
		<edge from-layer="295" from-port="0" to-layer="296" to-port="1"/>
		<edge from-layer="296" from-port="2" to-layer="298" to-port="0"/>
		<edge from-layer="297" from-port="0" to-layer="298" to-port="1"/>
		<edge from-layer="298" from-port="2" to-layer="300" to-port="0"/>
		<edge from-layer="299" from-port="0" to-layer="300" to-port="1"/>
		<edge from-layer="300" from-port="2" to-layer="301" to-port="0"/>
		<edge from-layer="300" from-port="2" to-layer="313" to-port="0"/>
		<edge from-layer="301" from-port="1" to-layer="303" to-port="0"/>
		<edge from-layer="302" from-port="0" to-layer="303" to-port="1"/>
		<edge from-layer="303" from-port="2" to-layer="305" to-port="0"/>
		<edge from-layer="304" from-port="0" to-layer="305" to-port="1"/>
		<edge from-layer="305" from-port="2" to-layer="307" to-port="0"/>
		<edge from-layer="306" from-port="0" to-layer="307" to-port="1"/>
		<edge from-layer="307" from-port="2" to-layer="309" to-port="0"/>
		<edge from-layer="308" from-port="0" to-layer="309" to-port="1"/>
		<edge from-layer="309" from-port="2" to-layer="311" to-port="0"/>
		<edge from-layer="310" from-port="0" to-layer="311" to-port="1"/>
		<edge from-layer="311" from-port="2" to-layer="312" to-port="0"/>
		<edge from-layer="312" from-port="1" to-layer="313" to-port="1"/>
		<edge from-layer="313" from-port="2" to-layer="314" to-port="1"/>
		<edge from-layer="314" from-port="2" to-layer="316" to-port="0"/>
		<edge from-layer="314" from-port="2" to-layer="344" to-port="0"/>
		<edge from-layer="315" from-port="0" to-layer="316" to-port="1"/>
		<edge from-layer="316" from-port="2" to-layer="318" to-port="0"/>
		<edge from-layer="317" from-port="0" to-layer="318" to-port="1"/>
		<edge from-layer="318" from-port="2" to-layer="320" to-port="0"/>
		<edge from-layer="319" from-port="0" to-layer="320" to-port="1"/>
		<edge from-layer="320" from-port="2" to-layer="322" to-port="0"/>
		<edge from-layer="321" from-port="0" to-layer="322" to-port="1"/>
		<edge from-layer="322" from-port="2" to-layer="324" to-port="0"/>
		<edge from-layer="323" from-port="0" to-layer="324" to-port="1"/>
		<edge from-layer="324" from-port="2" to-layer="326" to-port="0"/>
		<edge from-layer="325" from-port="0" to-layer="326" to-port="1"/>
		<edge from-layer="326" from-port="2" to-layer="328" to-port="0"/>
		<edge from-layer="327" from-port="0" to-layer="328" to-port="1"/>
		<edge from-layer="328" from-port="2" to-layer="330" to-port="0"/>
		<edge from-layer="329" from-port="0" to-layer="330" to-port="1"/>
		<edge from-layer="330" from-port="2" to-layer="331" to-port="0"/>
		<edge from-layer="330" from-port="2" to-layer="343" to-port="0"/>
		<edge from-layer="331" from-port="1" to-layer="333" to-port="0"/>
		<edge from-layer="332" from-port="0" to-layer="333" to-port="1"/>
		<edge from-layer="333" from-port="2" to-layer="335" to-port="0"/>
		<edge from-layer="334" from-port="0" to-layer="335" to-port="1"/>
		<edge from-layer="335" from-port="2" to-layer="337" to-port="0"/>
		<edge from-layer="336" from-port="0" to-layer="337" to-port="1"/>
		<edge from-layer="337" from-port="2" to-layer="339" to-port="0"/>
		<edge from-layer="338" from-port="0" to-layer="339" to-port="1"/>
		<edge from-layer="339" from-port="2" to-layer="341" to-port="0"/>
		<edge from-layer="340" from-port="0" to-layer="341" to-port="1"/>
		<edge from-layer="341" from-port="2" to-layer="342" to-port="0"/>
		<edge from-layer="342" from-port="1" to-layer="343" to-port="1"/>
		<edge from-layer="343" from-port="2" to-layer="344" to-port="1"/>
		<edge from-layer="344" from-port="2" to-layer="374" to-port="0"/>
		<edge from-layer="344" from-port="2" to-layer="346" to-port="0"/>
		<edge from-layer="345" from-port="0" to-layer="346" to-port="1"/>
		<edge from-layer="346" from-port="2" to-layer="348" to-port="0"/>
		<edge from-layer="347" from-port="0" to-layer="348" to-port="1"/>
		<edge from-layer="348" from-port="2" to-layer="350" to-port="0"/>
		<edge from-layer="349" from-port="0" to-layer="350" to-port="1"/>
		<edge from-layer="350" from-port="2" to-layer="352" to-port="0"/>
		<edge from-layer="351" from-port="0" to-layer="352" to-port="1"/>
		<edge from-layer="352" from-port="2" to-layer="354" to-port="0"/>
		<edge from-layer="353" from-port="0" to-layer="354" to-port="1"/>
		<edge from-layer="354" from-port="2" to-layer="356" to-port="0"/>
		<edge from-layer="355" from-port="0" to-layer="356" to-port="1"/>
		<edge from-layer="356" from-port="2" to-layer="358" to-port="0"/>
		<edge from-layer="357" from-port="0" to-layer="358" to-port="1"/>
		<edge from-layer="358" from-port="2" to-layer="360" to-port="0"/>
		<edge from-layer="359" from-port="0" to-layer="360" to-port="1"/>
		<edge from-layer="360" from-port="2" to-layer="361" to-port="0"/>
		<edge from-layer="360" from-port="2" to-layer="373" to-port="0"/>
		<edge from-layer="361" from-port="1" to-layer="363" to-port="0"/>
		<edge from-layer="362" from-port="0" to-layer="363" to-port="1"/>
		<edge from-layer="363" from-port="2" to-layer="365" to-port="0"/>
		<edge from-layer="364" from-port="0" to-layer="365" to-port="1"/>
		<edge from-layer="365" from-port="2" to-layer="367" to-port="0"/>
		<edge from-layer="366" from-port="0" to-layer="367" to-port="1"/>
		<edge from-layer="367" from-port="2" to-layer="369" to-port="0"/>
		<edge from-layer="368" from-port="0" to-layer="369" to-port="1"/>
		<edge from-layer="369" from-port="2" to-layer="371" to-port="0"/>
		<edge from-layer="370" from-port="0" to-layer="371" to-port="1"/>
		<edge from-layer="371" from-port="2" to-layer="372" to-port="0"/>
		<edge from-layer="372" from-port="1" to-layer="373" to-port="1"/>
		<edge from-layer="373" from-port="2" to-layer="374" to-port="1"/>
		<edge from-layer="374" from-port="2" to-layer="376" to-port="0"/>
		<edge from-layer="375" from-port="0" to-layer="376" to-port="1"/>
		<edge from-layer="376" from-port="2" to-layer="378" to-port="0"/>
		<edge from-layer="377" from-port="0" to-layer="378" to-port="1"/>
		<edge from-layer="378" from-port="2" to-layer="380" to-port="0"/>
		<edge from-layer="379" from-port="0" to-layer="380" to-port="1"/>
		<edge from-layer="380" from-port="2" to-layer="382" to-port="0"/>
		<edge from-layer="381" from-port="0" to-layer="382" to-port="1"/>
		<edge from-layer="382" from-port="2" to-layer="384" to-port="0"/>
		<edge from-layer="383" from-port="0" to-layer="384" to-port="1"/>
		<edge from-layer="384" from-port="2" to-layer="386" to-port="0"/>
		<edge from-layer="385" from-port="0" to-layer="386" to-port="1"/>
		<edge from-layer="386" from-port="2" to-layer="388" to-port="0"/>
		<edge from-layer="387" from-port="0" to-layer="388" to-port="1"/>
		<edge from-layer="388" from-port="2" to-layer="390" to-port="0"/>
		<edge from-layer="389" from-port="0" to-layer="390" to-port="1"/>
		<edge from-layer="390" from-port="2" to-layer="403" to-port="0"/>
		<edge from-layer="390" from-port="2" to-layer="391" to-port="0"/>
		<edge from-layer="391" from-port="1" to-layer="393" to-port="0"/>
		<edge from-layer="392" from-port="0" to-layer="393" to-port="1"/>
		<edge from-layer="393" from-port="2" to-layer="395" to-port="0"/>
		<edge from-layer="394" from-port="0" to-layer="395" to-port="1"/>
		<edge from-layer="395" from-port="2" to-layer="397" to-port="0"/>
		<edge from-layer="396" from-port="0" to-layer="397" to-port="1"/>
		<edge from-layer="397" from-port="2" to-layer="399" to-port="0"/>
		<edge from-layer="398" from-port="0" to-layer="399" to-port="1"/>
		<edge from-layer="399" from-port="2" to-layer="401" to-port="0"/>
		<edge from-layer="400" from-port="0" to-layer="401" to-port="1"/>
		<edge from-layer="401" from-port="2" to-layer="402" to-port="0"/>
		<edge from-layer="402" from-port="1" to-layer="403" to-port="1"/>
		<edge from-layer="403" from-port="2" to-layer="433" to-port="0"/>
		<edge from-layer="403" from-port="2" to-layer="405" to-port="0"/>
		<edge from-layer="404" from-port="0" to-layer="405" to-port="1"/>
		<edge from-layer="405" from-port="2" to-layer="407" to-port="0"/>
		<edge from-layer="406" from-port="0" to-layer="407" to-port="1"/>
		<edge from-layer="407" from-port="2" to-layer="409" to-port="0"/>
		<edge from-layer="408" from-port="0" to-layer="409" to-port="1"/>
		<edge from-layer="409" from-port="2" to-layer="411" to-port="0"/>
		<edge from-layer="410" from-port="0" to-layer="411" to-port="1"/>
		<edge from-layer="411" from-port="2" to-layer="413" to-port="0"/>
		<edge from-layer="412" from-port="0" to-layer="413" to-port="1"/>
		<edge from-layer="413" from-port="2" to-layer="415" to-port="0"/>
		<edge from-layer="414" from-port="0" to-layer="415" to-port="1"/>
		<edge from-layer="415" from-port="2" to-layer="417" to-port="0"/>
		<edge from-layer="416" from-port="0" to-layer="417" to-port="1"/>
		<edge from-layer="417" from-port="2" to-layer="419" to-port="0"/>
		<edge from-layer="418" from-port="0" to-layer="419" to-port="1"/>
		<edge from-layer="419" from-port="2" to-layer="432" to-port="0"/>
		<edge from-layer="419" from-port="2" to-layer="420" to-port="0"/>
		<edge from-layer="420" from-port="1" to-layer="422" to-port="0"/>
		<edge from-layer="421" from-port="0" to-layer="422" to-port="1"/>
		<edge from-layer="422" from-port="2" to-layer="424" to-port="0"/>
		<edge from-layer="423" from-port="0" to-layer="424" to-port="1"/>
		<edge from-layer="424" from-port="2" to-layer="426" to-port="0"/>
		<edge from-layer="425" from-port="0" to-layer="426" to-port="1"/>
		<edge from-layer="426" from-port="2" to-layer="428" to-port="0"/>
		<edge from-layer="427" from-port="0" to-layer="428" to-port="1"/>
		<edge from-layer="428" from-port="2" to-layer="430" to-port="0"/>
		<edge from-layer="429" from-port="0" to-layer="430" to-port="1"/>
		<edge from-layer="430" from-port="2" to-layer="431" to-port="0"/>
		<edge from-layer="431" from-port="1" to-layer="432" to-port="1"/>
		<edge from-layer="432" from-port="2" to-layer="433" to-port="1"/>
		<edge from-layer="433" from-port="2" to-layer="463" to-port="0"/>
		<edge from-layer="433" from-port="2" to-layer="435" to-port="0"/>
		<edge from-layer="434" from-port="0" to-layer="435" to-port="1"/>
		<edge from-layer="435" from-port="2" to-layer="437" to-port="0"/>
		<edge from-layer="436" from-port="0" to-layer="437" to-port="1"/>
		<edge from-layer="437" from-port="2" to-layer="439" to-port="0"/>
		<edge from-layer="438" from-port="0" to-layer="439" to-port="1"/>
		<edge from-layer="439" from-port="2" to-layer="441" to-port="0"/>
		<edge from-layer="440" from-port="0" to-layer="441" to-port="1"/>
		<edge from-layer="441" from-port="2" to-layer="443" to-port="0"/>
		<edge from-layer="442" from-port="0" to-layer="443" to-port="1"/>
		<edge from-layer="443" from-port="2" to-layer="445" to-port="0"/>
		<edge from-layer="444" from-port="0" to-layer="445" to-port="1"/>
		<edge from-layer="445" from-port="2" to-layer="447" to-port="0"/>
		<edge from-layer="446" from-port="0" to-layer="447" to-port="1"/>
		<edge from-layer="447" from-port="2" to-layer="449" to-port="0"/>
		<edge from-layer="448" from-port="0" to-layer="449" to-port="1"/>
		<edge from-layer="449" from-port="2" to-layer="462" to-port="0"/>
		<edge from-layer="449" from-port="2" to-layer="450" to-port="0"/>
		<edge from-layer="450" from-port="1" to-layer="452" to-port="0"/>
		<edge from-layer="451" from-port="0" to-layer="452" to-port="1"/>
		<edge from-layer="452" from-port="2" to-layer="454" to-port="0"/>
		<edge from-layer="453" from-port="0" to-layer="454" to-port="1"/>
		<edge from-layer="454" from-port="2" to-layer="456" to-port="0"/>
		<edge from-layer="455" from-port="0" to-layer="456" to-port="1"/>
		<edge from-layer="456" from-port="2" to-layer="458" to-port="0"/>
		<edge from-layer="457" from-port="0" to-layer="458" to-port="1"/>
		<edge from-layer="458" from-port="2" to-layer="460" to-port="0"/>
		<edge from-layer="459" from-port="0" to-layer="460" to-port="1"/>
		<edge from-layer="460" from-port="2" to-layer="461" to-port="0"/>
		<edge from-layer="461" from-port="1" to-layer="462" to-port="1"/>
		<edge from-layer="462" from-port="2" to-layer="463" to-port="1"/>
		<edge from-layer="463" from-port="2" to-layer="465" to-port="0"/>
		<edge from-layer="464" from-port="0" to-layer="465" to-port="1"/>
		<edge from-layer="465" from-port="2" to-layer="467" to-port="0"/>
		<edge from-layer="466" from-port="0" to-layer="467" to-port="1"/>
		<edge from-layer="467" from-port="2" to-layer="469" to-port="0"/>
		<edge from-layer="468" from-port="0" to-layer="469" to-port="1"/>
		<edge from-layer="469" from-port="2" to-layer="471" to-port="0"/>
		<edge from-layer="470" from-port="0" to-layer="471" to-port="1"/>
		<edge from-layer="471" from-port="2" to-layer="473" to-port="0"/>
		<edge from-layer="472" from-port="0" to-layer="473" to-port="1"/>
		<edge from-layer="473" from-port="2" to-layer="475" to-port="0"/>
		<edge from-layer="474" from-port="0" to-layer="475" to-port="1"/>
		<edge from-layer="475" from-port="2" to-layer="476" to-port="0"/>
	</edges>
	<meta_data>
		<MO_version value="2021.4.0-3827-c5b65f2cb1d-releases/2021/4"/>
		<cli_parameters>
			<caffe_parser_path value="DIR"/>
			<data_type value="FP16"/>
			<disable_nhwc_to_nchw value="False"/>
			<disable_omitting_optional value="False"/>
			<disable_resnet_optimization value="False"/>
			<disable_weights_compression value="False"/>
			<enable_concat_optimization value="False"/>
			<enable_flattening_nested_params value="False"/>
			<enable_ssd_gluoncv value="False"/>
			<extensions value="DIR"/>
			<framework value="onnx"/>
			<freeze_placeholder_with_value value="{}"/>
			<generate_deprecated_IR_V7 value="False"/>
			<input value="0"/>
			<input_model value="DIR/Mobilenet_se_focal_121000.onnx"/>
			<input_model_is_text value="False"/>
			<input_shape value="[1,3,128,128]"/>
			<k value="DIR/CustomLayersMapping.xml"/>
			<keep_shape_ops value="True"/>
			<legacy_ir_generation value="False"/>
			<legacy_mxnet_model value="False"/>
			<log_level value="ERROR"/>
			<mean_scale_values value="{'0': {'mean': None, 'scale': array([255.])}}"/>
			<mean_values value="()"/>
			<model_name value="face-reidentification-retail-0095"/>
			<output_dir value="DIR"/>
			<placeholder_data_types value="{}"/>
			<placeholder_shapes value="{'0': array([  1,   3, 128, 128])}"/>
			<progress value="False"/>
			<remove_memory value="False"/>
			<remove_output_softmax value="False"/>
			<reverse_input_channels value="True"/>
			<save_params_from_nd value="False"/>
			<scale_values value="0[255.0]"/>
			<silent value="False"/>
			<static_shape value="False"/>
			<stream_output value="False"/>
			<transform value=""/>
			<unset unset_cli_parameters="batch, counts, disable_fusing, disable_gfusing, finegrain_fusing, input_checkpoint, input_meta_graph, input_proto, input_symbol, mean_file, mean_file_offsets, move_to_preprocess, nd_prefix_name, output, pretrained_model_name, saved_model_dir, saved_model_tags, scale, tensorboard_logdir, tensorflow_custom_layer_libraries, tensorflow_custom_operations_config_update, tensorflow_object_detection_api_pipeline_config, tensorflow_use_custom_operations_config, transformations_config"/>
		</cli_parameters>
	</meta_data>
</net>
