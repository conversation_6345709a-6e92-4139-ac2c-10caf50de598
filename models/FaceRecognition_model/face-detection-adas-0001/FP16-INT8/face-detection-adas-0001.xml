<?xml version="1.0" ?>
<net name="mobilenet_ssd_672x384" version="10">
	<layers>
		<layer id="0" name="2816282010332" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="1" name="2817282110662" type="Const" version="opset1">
			<data element_type="f16" offset="2" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="2" name="2818282210140" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="3" name="2819282310104" type="Const" version="opset1">
			<data element_type="f16" offset="2" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="4" name="1996200011199" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="5" name="199720019873" type="Const" version="opset1">
			<data element_type="f16" offset="4" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="6" name="1998200210290" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="7" name="1999200310854" type="Const" version="opset1">
			<data element_type="f16" offset="4" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="8" name="2336234010053" type="Const" version="opset1">
			<data element_type="f16" offset="6" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="9" name="2337234110392" type="Const" version="opset1">
			<data element_type="f16" offset="454" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="2338234210983" type="Const" version="opset1">
			<data element_type="f16" offset="6" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="2339234310023" type="Const" version="opset1">
			<data element_type="f16" offset="454" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="185618609972" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="13" name="1857186110119" type="Const" version="opset1">
			<data element_type="f16" offset="902" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="14" name="1858186211355" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="15" name="1859186310218" type="Const" version="opset1">
			<data element_type="f16" offset="902" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="16" name="2556256010011" type="Const" version="opset1">
			<data element_type="f16" offset="904" shape="1,248,1,1" size="496"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="2557256110500" type="Const" version="opset1">
			<data element_type="f16" offset="1400" shape="1,248,1,1" size="496"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="2558256211403" type="Const" version="opset1">
			<data element_type="f16" offset="904" shape="1,248,1,1" size="496"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="2559256310731" type="Const" version="opset1">
			<data element_type="f16" offset="1400" shape="1,248,1,1" size="496"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="245624609828" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="21" name="2457246110158" type="Const" version="opset1">
			<data element_type="f16" offset="1896" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="22" name="245824629858" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="23" name="2459246311271" type="Const" version="opset1">
			<data element_type="f16" offset="1896" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="24" name="1976198011127" type="Const" version="opset1">
			<data element_type="f16" offset="1898" shape="1,304,1,1" size="608"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="25" name="1977198110761" type="Const" version="opset1">
			<data element_type="f16" offset="2506" shape="1,304,1,1" size="608"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="1978198211397" type="Const" version="opset1">
			<data element_type="f16" offset="1898" shape="1,304,1,1" size="608"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="1979198310137" type="Const" version="opset1">
			<data element_type="f16" offset="2506" shape="1,304,1,1" size="608"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="2736274011121" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="29" name="2737274111340" type="Const" version="opset1">
			<data element_type="f16" offset="3114" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="30" name="2738274210383" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="31" name="2739274310752" type="Const" version="opset1">
			<data element_type="f16" offset="3114" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="32" name="2376238011040" type="Const" version="opset1">
			<data element_type="f16" offset="3116" shape="1,328,1,1" size="656"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="2377238110605" type="Const" version="opset1">
			<data element_type="f16" offset="3772" shape="1,328,1,1" size="656"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="2378238210590" type="Const" version="opset1">
			<data element_type="f16" offset="3116" shape="1,328,1,1" size="656"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="2379238310629" type="Const" version="opset1">
			<data element_type="f16" offset="3772" shape="1,328,1,1" size="656"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="1776178011292" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="37" name="1777178111481" type="Const" version="opset1">
			<data element_type="f16" offset="4428" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="38" name="177817829948" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="39" name="1779178310821" type="Const" version="opset1">
			<data element_type="f16" offset="4428" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="40" name="2756276010893" type="Const" version="opset1">
			<data element_type="f16" offset="4430" shape="1,360,1,1" size="720"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="41" name="2757276110602" type="Const" version="opset1">
			<data element_type="f16" offset="5150" shape="1,360,1,1" size="720"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="42" name="2758276210548" type="Const" version="opset1">
			<data element_type="f16" offset="4430" shape="1,360,1,1" size="720"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="43" name="2759276310131" type="Const" version="opset1">
			<data element_type="f16" offset="5150" shape="1,360,1,1" size="720"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="44" name="205620609825" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="45" name="2057206110479" type="Const" version="opset1">
			<data element_type="f16" offset="5870" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="46" name="205820629813" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="47" name="2059206310326" type="Const" version="opset1">
			<data element_type="f16" offset="5870" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="48" name="2776278011310" type="Const" version="opset1">
			<data element_type="f16" offset="5872" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="49" name="2777278111154" type="Const" version="opset1">
			<data element_type="f16" offset="6336" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="50" name="2778278210692" type="Const" version="opset1">
			<data element_type="f16" offset="5872" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="51" name="277927839978" type="Const" version="opset1">
			<data element_type="f16" offset="6336" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="52" name="1716172011304" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="53" name="1717172110791" type="Const" version="opset1">
			<data element_type="f16" offset="6800" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="54" name="1718172211070" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="55" name="1719172310623" type="Const" version="opset1">
			<data element_type="f16" offset="6800" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="56" name="2276228010809" type="Const" version="opset1">
			<data element_type="f16" offset="5872" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="57" name="2277228110800" type="Const" version="opset1">
			<data element_type="f16" offset="6802" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="58" name="2278228210410" type="Const" version="opset1">
			<data element_type="f16" offset="5872" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="59" name="2279228310971" type="Const" version="opset1">
			<data element_type="f16" offset="6802" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="60" name="217621809996" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="61" name="2177218110377" type="Const" version="opset1">
			<data element_type="f16" offset="7266" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="62" name="2178218211346" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="63" name="217921839993" type="Const" version="opset1">
			<data element_type="f16" offset="7266" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="64" name="2096210010698" type="Const" version="opset1">
			<data element_type="f16" offset="7268" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="65" name="2097210110965" type="Const" version="opset1">
			<data element_type="f16" offset="7508" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="66" name="2098210210929" type="Const" version="opset1">
			<data element_type="f16" offset="7268" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="67" name="2099210310959" type="Const" version="opset1">
			<data element_type="f16" offset="7508" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="68" name="1936194011094" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="69" name="1937194111295" type="Const" version="opset1">
			<data element_type="f16" offset="7748" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="70" name="1938194210125" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="71" name="1939194310362" type="Const" version="opset1">
			<data element_type="f16" offset="7748" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="72" name="161616209960" type="Const" version="opset1">
			<data element_type="f16" offset="7750" shape="1,104,1,1" size="208"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="73" name="1617162110476" type="Const" version="opset1">
			<data element_type="f16" offset="7958" shape="1,104,1,1" size="208"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="74" name="1618162211157" type="Const" version="opset1">
			<data element_type="f16" offset="7750" shape="1,104,1,1" size="208"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="75" name="1619162311412" type="Const" version="opset1">
			<data element_type="f16" offset="7958" shape="1,104,1,1" size="208"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="76" name="2576258011463" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="77" name="2577258111103" type="Const" version="opset1">
			<data element_type="f16" offset="8166" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="78" name="2578258210707" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="79" name="2579258311361" type="Const" version="opset1">
			<data element_type="f16" offset="8166" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="80" name="2496250010347" type="Const" version="opset1">
			<data element_type="f16" offset="8168" shape="1,56,1,1" size="112"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="81" name="2497250111442" type="Const" version="opset1">
			<data element_type="f16" offset="8280" shape="1,56,1,1" size="112"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="82" name="2498250211406" type="Const" version="opset1">
			<data element_type="f16" offset="8168" shape="1,56,1,1" size="112"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="83" name="2499250311052" type="Const" version="opset1">
			<data element_type="f16" offset="8280" shape="1,56,1,1" size="112"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="84" name="231623209984" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="85" name="2317232110017" type="Const" version="opset1">
			<data element_type="f16" offset="8392" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="86" name="2318232211010" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="87" name="2319232310092" type="Const" version="opset1">
			<data element_type="f16" offset="8392" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="88" name="2296230011499" type="Const" version="opset1">
			<data element_type="f16" offset="8394" shape="1,24,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="89" name="229723019999" type="Const" version="opset1">
			<data element_type="f16" offset="8442" shape="1,24,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="90" name="2298230211448" type="Const" version="opset1">
			<data element_type="f16" offset="8394" shape="1,24,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="91" name="2299230311244" type="Const" version="opset1">
			<data element_type="f16" offset="8442" shape="1,24,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="92" name="2396240010128" type="Const" version="opset1">
			<data element_type="f16" offset="8490" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="93" name="2397240110797" type="Const" version="opset1">
			<data element_type="f16" offset="8492" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="94" name="239824029891" type="Const" version="opset1">
			<data element_type="f16" offset="8490" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="95" name="2399240310143" type="Const" version="opset1">
			<data element_type="f16" offset="8492" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="96" name="data" type="Parameter" version="opset1">
			<data element_type="f16" shape="1,3,384,672"/>
			<output>
				<port id="0" names="data" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
			</output>
		</layer>
		<layer id="97" name="data_mul_119702137748010617" type="Const" version="opset1">
			<data element_type="f16" offset="8494" shape="1,1,1,1" size="2"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="98" name="data/scale/Fused_Mul_" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
			</output>
		</layer>
		<layer id="99" name="data_add_119722151848210047" type="Const" version="opset1">
			<data element_type="f16" offset="8496" shape="1,3,1,1" size="6"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="100" name="data/mean/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>3</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
			</output>
		</layer>
		<layer id="101" name="conv1/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
			</output>
		</layer>
		<layer id="102" name="conv1/bn/mean/Fused_Mul__copy4845088/quantized722211007" type="Const" version="opset1">
			<data element_type="i8" offset="8502" shape="24,3,3,3" size="648"/>
			<output>
				<port id="0" precision="I8">
					<dim>24</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="103" name="conv1/bn/mean/Fused_Mul__copy4845088/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>24</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="104" name="conv1/fq_weights_1/zero_point723510770" type="Const" version="opset1">
			<data element_type="f16" offset="9150" shape="24,1,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="105" name="conv1/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>24</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="106" name="conv1/fq_weights_1/scale72309849" type="Const" version="opset1">
			<data element_type="f16" offset="9198" shape="24,1,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="107" name="conv1/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>24</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="108" name="conv1" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="109" name="data_add_119751198048610350" type="Const" version="opset1">
			<data element_type="f16" offset="9246" shape="1,24,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="110" name="conv1/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv1" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="111" name="relu1" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv1" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="112" name="conv2_1/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="113" name="8645/value86479930" type="Const" version="opset1">
			<data element_type="i64" offset="9294" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="114" name="conv2_1/dw/bn/mean/Fused_Mul__copy4895090/quantized578211256" type="Const" version="opset1">
			<data element_type="i8" offset="9334" shape="24,1,3,3" size="216"/>
			<output>
				<port id="0" precision="I8">
					<dim>24</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="115" name="conv2_1/dw/bn/mean/Fused_Mul__copy4895090/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="116" name="conv2_1/dw/fq_weights_1/zero_point57959837" type="Const" version="opset1">
			<data element_type="f16" offset="9550" shape="24,1,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="117" name="conv2_1/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="118" name="conv2_1/dw/fq_weights_1/scale579010041" type="Const" version="opset1">
			<data element_type="f16" offset="9598" shape="24,1,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="119" name="conv2_1/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="120" name="8645" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="121" name="conv2_1/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="122" name="data_add_119831198849111133" type="Const" version="opset1">
			<data element_type="f16" offset="9646" shape="1,24,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="123" name="conv2_1/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv2_1/dw" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="124" name="relu2_1/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv2_1/dw" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="125" name="conv2_1/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="126" name="conv2_1/sep/bn/mean/Fused_Mul__copy4945092/quantized731811229" type="Const" version="opset1">
			<data element_type="i8" offset="9694" shape="56,24,1,1" size="1344"/>
			<output>
				<port id="0" precision="I8">
					<dim>56</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="127" name="conv2_1/sep/bn/mean/Fused_Mul__copy4945092/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>56</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>56</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="128" name="conv2_1/sep/fq_weights_1/zero_point733110710" type="Const" version="opset1">
			<data element_type="f16" offset="11038" shape="56,1,1,1" size="112"/>
			<output>
				<port id="0" precision="FP16">
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="129" name="conv2_1/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>56</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>56</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="130" name="conv2_1/sep/fq_weights_1/scale732611139" type="Const" version="opset1">
			<data element_type="f16" offset="11150" shape="56,1,1,1" size="112"/>
			<output>
				<port id="0" precision="FP16">
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="131" name="conv2_1/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>56</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>56</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="132" name="conv2_1/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
				<port id="1">
					<dim>56</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="133" name="data_add_119911199649610923" type="Const" version="opset1">
			<data element_type="f16" offset="11262" shape="1,56,1,1" size="112"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="134" name="conv2_1/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>56</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv2_1/sep" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="135" name="relu2_1/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>56</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv2_1/sep" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="136" name="conv2_2/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>56</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
			</output>
		</layer>
		<layer id="137" name="8613/value861511277" type="Const" version="opset1">
			<data element_type="i64" offset="11374" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="138" name="conv2_2/dw/bn/mean/Fused_Mul__copy4995094/quantized604611418" type="Const" version="opset1">
			<data element_type="i8" offset="11414" shape="56,1,3,3" size="504"/>
			<output>
				<port id="0" precision="I8">
					<dim>56</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="139" name="conv2_2/dw/bn/mean/Fused_Mul__copy4995094/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>56</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>56</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="140" name="conv2_2/dw/fq_weights_1/zero_point605911307" type="Const" version="opset1">
			<data element_type="f16" offset="11918" shape="56,1,1,1" size="112"/>
			<output>
				<port id="0" precision="FP16">
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="141" name="conv2_2/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>56</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>56</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="142" name="conv2_2/dw/fq_weights_1/scale60549834" type="Const" version="opset1">
			<data element_type="f16" offset="12030" shape="56,1,1,1" size="112"/>
			<output>
				<port id="0" precision="FP16">
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="143" name="conv2_2/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>56</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>56</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="144" name="8613" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>56</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="145" name="conv2_2/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>56</dim>
					<dim>192</dim>
					<dim>336</dim>
				</port>
				<port id="1">
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="146" name="data_add_119991200450110494" type="Const" version="opset1">
			<data element_type="f16" offset="12142" shape="1,56,1,1" size="112"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="147" name="conv2_2/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>56</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv2_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="148" name="relu2_2/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>56</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv2_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="149" name="conv2_2/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>56</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>56</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="150" name="conv2_2/sep/bn/mean/Fused_Mul__copy5045096/quantized686210566" type="Const" version="opset1">
			<data element_type="i8" offset="12254" shape="104,56,1,1" size="5824"/>
			<output>
				<port id="0" precision="I8">
					<dim>104</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="151" name="conv2_2/sep/bn/mean/Fused_Mul__copy5045096/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>104</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>104</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="152" name="conv2_2/sep/fq_weights_1/zero_point687510530" type="Const" version="opset1">
			<data element_type="f16" offset="18078" shape="104,1,1,1" size="208"/>
			<output>
				<port id="0" precision="FP16">
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="153" name="conv2_2/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>104</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>104</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="154" name="conv2_2/sep/fq_weights_1/scale687010437" type="Const" version="opset1">
			<data element_type="f16" offset="18286" shape="104,1,1,1" size="208"/>
			<output>
				<port id="0" precision="FP16">
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="155" name="conv2_2/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>104</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>104</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="156" name="conv2_2/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>56</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1">
					<dim>104</dim>
					<dim>56</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="157" name="data_add_120071201250610941" type="Const" version="opset1">
			<data element_type="f16" offset="18494" shape="1,104,1,1" size="208"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="158" name="conv2_2/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv2_2/sep" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="159" name="relu2_2/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv2_2/sep" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="160" name="conv3_1/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="161" name="8633/value863510323" type="Const" version="opset1">
			<data element_type="i64" offset="18702" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="162" name="conv3_1/dw/bn/mean/Fused_Mul__copy5095098/quantized71749810" type="Const" version="opset1">
			<data element_type="i8" offset="18742" shape="104,1,3,3" size="936"/>
			<output>
				<port id="0" precision="I8">
					<dim>104</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="163" name="conv3_1/dw/bn/mean/Fused_Mul__copy5095098/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>104</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>104</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="164" name="conv3_1/dw/fq_weights_1/zero_point718711334" type="Const" version="opset1">
			<data element_type="f16" offset="19678" shape="104,1,1,1" size="208"/>
			<output>
				<port id="0" precision="FP16">
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="165" name="conv3_1/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>104</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>104</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="166" name="conv3_1/dw/fq_weights_1/scale718211247" type="Const" version="opset1">
			<data element_type="f16" offset="19886" shape="104,1,1,1" size="208"/>
			<output>
				<port id="0" precision="FP16">
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="167" name="conv3_1/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>104</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>104</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="168" name="8633" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>104</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="169" name="conv3_1/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1">
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="170" name="data_add_120151202051110704" type="Const" version="opset1">
			<data element_type="f16" offset="20094" shape="1,104,1,1" size="208"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="171" name="conv3_1/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv3_1/dw" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="172" name="relu3_1/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv3_1/dw" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="173" name="conv3_1/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="174" name="conv3_1/sep/bn/mean/Fused_Mul__copy5145100/quantized616610578" type="Const" version="opset1">
			<data element_type="i8" offset="20302" shape="120,104,1,1" size="12480"/>
			<output>
				<port id="0" precision="I8">
					<dim>120</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="175" name="conv3_1/sep/bn/mean/Fused_Mul__copy5145100/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>120</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="176" name="conv3_1/sep/fq_weights_1/zero_point617910668" type="Const" version="opset1">
			<data element_type="f16" offset="32782" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="177" name="conv3_1/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="178" name="conv3_1/sep/fq_weights_1/scale617411151" type="Const" version="opset1">
			<data element_type="f16" offset="33022" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="179" name="conv3_1/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="180" name="conv3_1/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>104</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>104</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="181" name="data_add_120231202851610071" type="Const" version="opset1">
			<data element_type="f16" offset="33262" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="182" name="conv3_1/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv3_1/sep" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="183" name="relu3_1/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv3_1/sep" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="184" name="conv3_2/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
			</output>
		</layer>
		<layer id="185" name="8605/value860710188" type="Const" version="opset1">
			<data element_type="i64" offset="33502" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="186" name="conv3_2/dw/bn/mean/Fused_Mul__copy5195102/quantized628611073" type="Const" version="opset1">
			<data element_type="i8" offset="33542" shape="120,1,3,3" size="1080"/>
			<output>
				<port id="0" precision="I8">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="187" name="conv3_2/dw/bn/mean/Fused_Mul__copy5195102/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="188" name="conv3_2/dw/fq_weights_1/zero_point629910455" type="Const" version="opset1">
			<data element_type="f16" offset="34622" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="189" name="conv3_2/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="190" name="conv3_2/dw/fq_weights_1/scale629411367" type="Const" version="opset1">
			<data element_type="f16" offset="34862" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="191" name="conv3_2/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="192" name="8605" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="193" name="conv3_2/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>96</dim>
					<dim>168</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="194" name="data_add_120311203652110305" type="Const" version="opset1">
			<data element_type="f16" offset="35102" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="195" name="conv3_2/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv3_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="196" name="relu3_2/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv3_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="197" name="conv3_2/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="198" name="conv3_2/sep/bn/mean/Fused_Mul__copy5245104/quantized635810560" type="Const" version="opset1">
			<data element_type="i8" offset="35342" shape="232,120,1,1" size="27840"/>
			<output>
				<port id="0" precision="I8">
					<dim>232</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="199" name="conv3_2/sep/bn/mean/Fused_Mul__copy5245104/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>232</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="200" name="conv3_2/sep/fq_weights_1/zero_point63719963" type="Const" version="opset1">
			<data element_type="f16" offset="63182" shape="232,1,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="201" name="conv3_2/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>232</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="202" name="conv3_2/sep/fq_weights_1/scale636610203" type="Const" version="opset1">
			<data element_type="f16" offset="63646" shape="232,1,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="203" name="conv3_2/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>232</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="204" name="conv3_2/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="205" name="data_add_120391204452610518" type="Const" version="opset1">
			<data element_type="f16" offset="64110" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="206" name="conv3_2/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv3_2/sep" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="207" name="relu3_2/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv3_2/sep" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="208" name="conv4_1/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="209" name="8625/value862710938" type="Const" version="opset1">
			<data element_type="i64" offset="64574" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="210" name="conv4_1/dw/bn/mean/Fused_Mul__copy5295106/quantized592610890" type="Const" version="opset1">
			<data element_type="i8" offset="64614" shape="232,1,3,3" size="2088"/>
			<output>
				<port id="0" precision="I8">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="211" name="conv4_1/dw/bn/mean/Fused_Mul__copy5295106/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="212" name="conv4_1/dw/fq_weights_1/zero_point593910389" type="Const" version="opset1">
			<data element_type="f16" offset="66702" shape="232,1,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="213" name="conv4_1/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="214" name="conv4_1/dw/fq_weights_1/scale593411238" type="Const" version="opset1">
			<data element_type="f16" offset="67166" shape="232,1,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="215" name="conv4_1/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="216" name="8625" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="217" name="conv4_1/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="218" name="data_add_120471205253110401" type="Const" version="opset1">
			<data element_type="f16" offset="67630" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="219" name="conv4_1/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_1/dw" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="220" name="relu4_1/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv4_1/dw" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="221" name="conv4_1/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="222" name="conv4_1/sep/bn/mean/Fused_Mul__copy5345108/quantized727010935" type="Const" version="opset1">
			<data element_type="i8" offset="68094" shape="232,232,1,1" size="53824"/>
			<output>
				<port id="0" precision="I8">
					<dim>232</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="223" name="conv4_1/sep/bn/mean/Fused_Mul__copy5345108/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>232</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="224" name="conv4_1/sep/fq_weights_1/zero_point728310050" type="Const" version="opset1">
			<data element_type="f16" offset="121918" shape="232,1,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="225" name="conv4_1/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>232</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="226" name="conv4_1/sep/fq_weights_1/scale727810545" type="Const" version="opset1">
			<data element_type="f16" offset="122382" shape="232,1,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="227" name="conv4_1/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>232</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="228" name="conv4_1/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="229" name="data_add_120551206053611322" type="Const" version="opset1">
			<data element_type="f16" offset="122846" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="230" name="conv4_1/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_1/sep" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="231" name="relu4_1/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv4_1/sep" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="232" name="conv4_2/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
			</output>
		</layer>
		<layer id="233" name="8597/value859910539" type="Const" version="opset1">
			<data element_type="i64" offset="64574" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="234" name="conv4_2/dw/bn/mean/Fused_Mul__copy5395110/quantized580610341" type="Const" version="opset1">
			<data element_type="i8" offset="123310" shape="232,1,3,3" size="2088"/>
			<output>
				<port id="0" precision="I8">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="235" name="conv4_2/dw/bn/mean/Fused_Mul__copy5395110/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="236" name="conv4_2/dw/fq_weights_1/zero_point581911400" type="Const" version="opset1">
			<data element_type="f16" offset="125398" shape="232,1,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="237" name="conv4_2/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="238" name="conv4_2/dw/fq_weights_1/scale58149903" type="Const" version="opset1">
			<data element_type="f16" offset="125862" shape="232,1,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="239" name="conv4_2/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="240" name="8597" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>232</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="241" name="conv4_2/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>48</dim>
					<dim>84</dim>
				</port>
				<port id="1">
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="242" name="data_add_120631206854110107" type="Const" version="opset1">
			<data element_type="f16" offset="126326" shape="1,232,1,1" size="464"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="243" name="conv4_2/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="244" name="relu4_2/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv4_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="245" name="conv4_2/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>232</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="246" name="conv4_2/sep/bn/mean/Fused_Mul__copy5445112/quantized674210440" type="Const" version="opset1">
			<data element_type="i8" offset="126790" shape="360,232,1,1" size="83520"/>
			<output>
				<port id="0" precision="I8">
					<dim>360</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="247" name="conv4_2/sep/bn/mean/Fused_Mul__copy5445112/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>360</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>360</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="248" name="conv4_2/sep/fq_weights_1/zero_point675510161" type="Const" version="opset1">
			<data element_type="f16" offset="210310" shape="360,1,1,1" size="720"/>
			<output>
				<port id="0" precision="FP16">
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="249" name="conv4_2/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>360</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>360</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="250" name="conv4_2/sep/fq_weights_1/scale675010968" type="Const" version="opset1">
			<data element_type="f16" offset="211030" shape="360,1,1,1" size="720"/>
			<output>
				<port id="0" precision="FP16">
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="251" name="conv4_2/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>360</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>360</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="252" name="conv4_2/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>232</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>360</dim>
					<dim>232</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="253" name="data_add_120711207654611043" type="Const" version="opset1">
			<data element_type="f16" offset="211750" shape="1,360,1,1" size="720"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="254" name="conv4_2/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_2/sep" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="255" name="relu4_2/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv4_2/sep" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="256" name="conv5_1/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="257" name="8617/value861910473" type="Const" version="opset1">
			<data element_type="i64" offset="212470" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="258" name="conv5_1/dw/bn/mean/Fused_Mul__copy5495114/quantized712610077" type="Const" version="opset1">
			<data element_type="i8" offset="212510" shape="360,1,3,3" size="3240"/>
			<output>
				<port id="0" precision="I8">
					<dim>360</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="259" name="conv5_1/dw/bn/mean/Fused_Mul__copy5495114/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>360</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>360</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="260" name="conv5_1/dw/fq_weights_1/zero_point713910251" type="Const" version="opset1">
			<data element_type="f16" offset="215750" shape="360,1,1,1" size="720"/>
			<output>
				<port id="0" precision="FP16">
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="261" name="conv5_1/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>360</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>360</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="262" name="conv5_1/dw/fq_weights_1/scale713410881" type="Const" version="opset1">
			<data element_type="f16" offset="216470" shape="360,1,1,1" size="720"/>
			<output>
				<port id="0" precision="FP16">
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="263" name="conv5_1/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>360</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>360</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="264" name="8617" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>360</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="265" name="conv5_1/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="266" name="data_add_12079120845519990" type="Const" version="opset1">
			<data element_type="f16" offset="217190" shape="1,360,1,1" size="720"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="267" name="conv5_1/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_1/dw" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="268" name="relu5_1/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_1/dw" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="269" name="conv5_1/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="270" name="conv5_1/sep/bn/mean/Fused_Mul__copy5545116/quantized643010122" type="Const" version="opset1">
			<data element_type="i8" offset="217910" shape="328,360,1,1" size="118080"/>
			<output>
				<port id="0" precision="I8">
					<dim>328</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="271" name="conv5_1/sep/bn/mean/Fused_Mul__copy5545116/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>328</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>328</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="272" name="conv5_1/sep/fq_weights_1/zero_point644310242" type="Const" version="opset1">
			<data element_type="f16" offset="335990" shape="328,1,1,1" size="656"/>
			<output>
				<port id="0" precision="FP16">
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="273" name="conv5_1/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>328</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>328</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="274" name="conv5_1/sep/fq_weights_1/scale643810527" type="Const" version="opset1">
			<data element_type="f16" offset="336646" shape="328,1,1,1" size="656"/>
			<output>
				<port id="0" precision="FP16">
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="275" name="conv5_1/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>328</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>328</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="276" name="conv5_1/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>360</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>328</dim>
					<dim>360</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="277" name="data_add_120871209255610743" type="Const" version="opset1">
			<data element_type="f16" offset="337302" shape="1,328,1,1" size="656"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="278" name="conv5_1/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_1/sep" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="279" name="relu5_1/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_1/sep" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="280" name="conv5_2/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="281" name="8589/value859111169" type="Const" version="opset1">
			<data element_type="i64" offset="337958" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="282" name="conv5_2/dw/bn/mean/Fused_Mul__copy5595118/quantized62149888" type="Const" version="opset1">
			<data element_type="i8" offset="337998" shape="328,1,3,3" size="2952"/>
			<output>
				<port id="0" precision="I8">
					<dim>328</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="283" name="conv5_2/dw/bn/mean/Fused_Mul__copy5595118/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>328</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>328</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="284" name="conv5_2/dw/fq_weights_1/zero_point622710320" type="Const" version="opset1">
			<data element_type="f16" offset="340950" shape="328,1,1,1" size="656"/>
			<output>
				<port id="0" precision="FP16">
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="285" name="conv5_2/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>328</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>328</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="286" name="conv5_2/dw/fq_weights_1/scale62229864" type="Const" version="opset1">
			<data element_type="f16" offset="341606" shape="328,1,1,1" size="656"/>
			<output>
				<port id="0" precision="FP16">
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="287" name="conv5_2/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>328</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>328</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="288" name="8589" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>328</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="289" name="conv5_2/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="290" name="data_add_120951210056111115" type="Const" version="opset1">
			<data element_type="f16" offset="342262" shape="1,328,1,1" size="656"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="291" name="conv5_2/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="292" name="relu5_2/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="293" name="conv5_2/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="294" name="conv5_2/sep/bn/mean/Fused_Mul__copy5645120/quantized669410149" type="Const" version="opset1">
			<data element_type="i8" offset="342918" shape="304,328,1,1" size="99712"/>
			<output>
				<port id="0" precision="I8">
					<dim>304</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="295" name="conv5_2/sep/bn/mean/Fused_Mul__copy5645120/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>304</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>304</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="296" name="conv5_2/sep/fq_weights_1/zero_point670710098" type="Const" version="opset1">
			<data element_type="f16" offset="442630" shape="304,1,1,1" size="608"/>
			<output>
				<port id="0" precision="FP16">
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="297" name="conv5_2/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>304</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>304</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="298" name="conv5_2/sep/fq_weights_1/scale670211424" type="Const" version="opset1">
			<data element_type="f16" offset="443238" shape="304,1,1,1" size="608"/>
			<output>
				<port id="0" precision="FP16">
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="299" name="conv5_2/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>304</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>304</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="300" name="conv5_2/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>328</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>304</dim>
					<dim>328</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="301" name="data_add_121031210856611136" type="Const" version="opset1">
			<data element_type="f16" offset="443846" shape="1,304,1,1" size="608"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="302" name="conv5_2/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_2/sep" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="303" name="relu5_2/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_2/sep" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="304" name="conv5_3/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="305" name="8641/value864310512" type="Const" version="opset1">
			<data element_type="i64" offset="444454" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="306" name="conv5_3/dw/bn/mean/Fused_Mul__copy5695122/quantized626210314" type="Const" version="opset1">
			<data element_type="i8" offset="444494" shape="304,1,3,3" size="2736"/>
			<output>
				<port id="0" precision="I8">
					<dim>304</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="307" name="conv5_3/dw/bn/mean/Fused_Mul__copy5695122/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>304</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>304</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="308" name="conv5_3/dw/fq_weights_1/zero_point627510620" type="Const" version="opset1">
			<data element_type="f16" offset="447230" shape="304,1,1,1" size="608"/>
			<output>
				<port id="0" precision="FP16">
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="309" name="conv5_3/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>304</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>304</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="310" name="conv5_3/dw/fq_weights_1/scale627010806" type="Const" version="opset1">
			<data element_type="f16" offset="447838" shape="304,1,1,1" size="608"/>
			<output>
				<port id="0" precision="FP16">
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="311" name="conv5_3/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>304</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>304</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="312" name="8641" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>304</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="313" name="conv5_3/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="314" name="data_add_12111121165719936" type="Const" version="opset1">
			<data element_type="f16" offset="448446" shape="1,304,1,1" size="608"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="315" name="conv5_3/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_3/dw" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="316" name="relu5_3/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_3/dw" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="317" name="conv5_3/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="318" name="conv5_3/sep/bn/mean/Fused_Mul__copy5745124/quantized681410116" type="Const" version="opset1">
			<data element_type="i8" offset="449054" shape="248,304,1,1" size="75392"/>
			<output>
				<port id="0" precision="I8">
					<dim>248</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="319" name="conv5_3/sep/bn/mean/Fused_Mul__copy5745124/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>248</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>248</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="320" name="conv5_3/sep/fq_weights_1/zero_point682711508" type="Const" version="opset1">
			<data element_type="f16" offset="524446" shape="248,1,1,1" size="496"/>
			<output>
				<port id="0" precision="FP16">
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="321" name="conv5_3/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>248</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>248</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="322" name="conv5_3/sep/fq_weights_1/scale682210146" type="Const" version="opset1">
			<data element_type="f16" offset="524942" shape="248,1,1,1" size="496"/>
			<output>
				<port id="0" precision="FP16">
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="323" name="conv5_3/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>248</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>248</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="324" name="conv5_3/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>304</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>248</dim>
					<dim>304</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="325" name="data_add_121191212457610359" type="Const" version="opset1">
			<data element_type="f16" offset="525438" shape="1,248,1,1" size="496"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="326" name="conv5_3/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_3/sep" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="327" name="relu5_3/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_3/sep" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="328" name="conv5_4/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="329" name="8593/value859510596" type="Const" version="opset1">
			<data element_type="i64" offset="525934" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="330" name="conv5_4/dw/bn/mean/Fused_Mul__copy5795126/quantized633410737" type="Const" version="opset1">
			<data element_type="i8" offset="525974" shape="248,1,3,3" size="2232"/>
			<output>
				<port id="0" precision="I8">
					<dim>248</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="331" name="conv5_4/dw/bn/mean/Fused_Mul__copy5795126/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>248</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>248</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="332" name="conv5_4/dw/fq_weights_1/zero_point634710302" type="Const" version="opset1">
			<data element_type="f16" offset="528206" shape="248,1,1,1" size="496"/>
			<output>
				<port id="0" precision="FP16">
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="333" name="conv5_4/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>248</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>248</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="334" name="conv5_4/dw/fq_weights_1/scale634211268" type="Const" version="opset1">
			<data element_type="f16" offset="528702" shape="248,1,1,1" size="496"/>
			<output>
				<port id="0" precision="FP16">
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="335" name="conv5_4/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>248</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>248</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="336" name="8593" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>248</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="337" name="conv5_4/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="338" name="data_add_121271213258110191" type="Const" version="opset1">
			<data element_type="f16" offset="529198" shape="1,248,1,1" size="496"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="339" name="conv5_4/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_4/dw" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="340" name="relu5_4/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_4/dw" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="341" name="conv5_4/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="342" name="conv5_4/sep/bn/mean/Fused_Mul__copy5845128/quantized662210866" type="Const" version="opset1">
			<data element_type="i8" offset="529694" shape="224,248,1,1" size="55552"/>
			<output>
				<port id="0" precision="I8">
					<dim>224</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="343" name="conv5_4/sep/bn/mean/Fused_Mul__copy5845128/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>224</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="344" name="conv5_4/sep/fq_weights_1/zero_point663510599" type="Const" version="opset1">
			<data element_type="f16" offset="585246" shape="224,1,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="345" name="conv5_4/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>224</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="346" name="conv5_4/sep/fq_weights_1/scale663010533" type="Const" version="opset1">
			<data element_type="f16" offset="585694" shape="224,1,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="347" name="conv5_4/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>224</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="348" name="conv5_4/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>248</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>248</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="349" name="data_add_121351214058610794" type="Const" version="opset1">
			<data element_type="f16" offset="586142" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="350" name="conv5_4/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_4/sep" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="351" name="relu5_4/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_4/sep" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="352" name="conv5_5/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="353" name="8609/value861110482" type="Const" version="opset1">
			<data element_type="i64" offset="586590" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="354" name="conv5_5/dw/bn/mean/Fused_Mul__copy5895130/quantized652610428" type="Const" version="opset1">
			<data element_type="i8" offset="586630" shape="224,1,3,3" size="2016"/>
			<output>
				<port id="0" precision="I8">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="355" name="conv5_5/dw/bn/mean/Fused_Mul__copy5895130/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="356" name="conv5_5/dw/fq_weights_1/zero_point653910398" type="Const" version="opset1">
			<data element_type="f16" offset="588646" shape="224,1,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="357" name="conv5_5/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="358" name="conv5_5/dw/fq_weights_1/scale653411457" type="Const" version="opset1">
			<data element_type="f16" offset="589094" shape="224,1,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="359" name="conv5_5/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="360" name="8609" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="361" name="conv5_5/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="362" name="data_add_121431214859110830" type="Const" version="opset1">
			<data element_type="f16" offset="589542" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="363" name="conv5_5/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_5/dw" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="364" name="relu5_5/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_5/dw" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="365" name="conv5_5/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="366" name="conv5_5/sep/bn/mean/Fused_Mul__copy5945132/quantized645411382" type="Const" version="opset1">
			<data element_type="i8" offset="589990" shape="184,224,1,1" size="41216"/>
			<output>
				<port id="0" precision="I8">
					<dim>184</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="367" name="conv5_5/sep/bn/mean/Fused_Mul__copy5945132/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>184</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>184</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="368" name="conv5_5/sep/fq_weights_1/zero_point64679846" type="Const" version="opset1">
			<data element_type="f16" offset="631206" shape="184,1,1,1" size="368"/>
			<output>
				<port id="0" precision="FP16">
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="369" name="conv5_5/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>184</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>184</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="370" name="conv5_5/sep/fq_weights_1/scale646211100" type="Const" version="opset1">
			<data element_type="f16" offset="631574" shape="184,1,1,1" size="368"/>
			<output>
				<port id="0" precision="FP16">
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="371" name="conv5_5/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>184</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>184</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="372" name="conv5_5/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>184</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="373" name="data_add_121511215659611124" type="Const" version="opset1">
			<data element_type="f16" offset="631942" shape="1,184,1,1" size="368"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="374" name="conv5_5/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_5/sep" precision="FP16">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="375" name="relu5_5/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_5/sep" precision="FP16">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="376" name="conv4_3_0_norm_mbox_loc/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="377" name="4205995134/quantized595011211" type="Const" version="opset1">
			<data element_type="i8" offset="632310" shape="16,184,3,3" size="26496"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="378" name="4205995134/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="379" name="conv4_3_0_norm_mbox_loc/WithoutBiases/fq_weights_1/zero_point596311385" type="Const" version="opset1">
			<data element_type="f16" offset="658806" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="380" name="conv4_3_0_norm_mbox_loc/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="381" name="conv4_3_0_norm_mbox_loc/WithoutBiases/fq_weights_1/scale595811430" type="Const" version="opset1">
			<data element_type="f16" offset="658838" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="382" name="conv4_3_0_norm_mbox_loc/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="383" name="conv4_3_0_norm_mbox_loc/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="384" name="conv4_3_0_norm_mbox_loc/Dims71856019831" type="Const" version="opset1">
			<data element_type="f16" offset="658870" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="385" name="conv4_3_0_norm_mbox_loc" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_0_norm_mbox_loc" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="386" name="637603" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="387" name="conv4_3_0_norm_mbox_loc_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_0_norm_mbox_loc_perm" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>42</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="388" name="666/shapes_concat60511091" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="389" name="conv4_3_0_norm_mbox_loc_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>42</dim>
					<dim>16</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_0_norm_mbox_loc_flat" precision="FP16">
					<dim>1</dim>
					<dim>16128</dim>
				</port>
			</output>
		</layer>
		<layer id="390" name="4466075138/quantized693411250" type="Const" version="opset1">
			<data element_type="i8" offset="658950" shape="16,184,3,3" size="26496"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="391" name="4466075138/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="392" name="conv4_3_norm_mbox_loc/WithoutBiases/fq_weights_1/zero_point694710671" type="Const" version="opset1">
			<data element_type="f16" offset="685446" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="393" name="conv4_3_norm_mbox_loc/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="394" name="conv4_3_norm_mbox_loc/WithoutBiases/fq_weights_1/scale694210221" type="Const" version="opset1">
			<data element_type="f16" offset="685478" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="395" name="conv4_3_norm_mbox_loc/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="396" name="conv4_3_norm_mbox_loc/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>184</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="397" name="conv4_3_norm_mbox_loc/Dims714960910380" type="Const" version="opset1">
			<data element_type="f16" offset="685510" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="398" name="conv4_3_norm_mbox_loc" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_norm_mbox_loc" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="399" name="640611" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="400" name="conv4_3_norm_mbox_loc_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_norm_mbox_loc_perm" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>42</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="401" name="641/shapes_concat61310215" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="402" name="conv4_3_norm_mbox_loc_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>42</dim>
					<dim>16</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_norm_mbox_loc_flat" precision="FP16">
					<dim>1</dim>
					<dim>16128</dim>
				</port>
			</output>
		</layer>
		<layer id="403" name="2876288011067" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="404" name="2877288111034" type="Const" version="opset1">
			<data element_type="f16" offset="685542" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="405" name="2878288211325" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="406" name="2879288311319" type="Const" version="opset1">
			<data element_type="f16" offset="685542" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="407" name="2076208010080" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="408" name="2077208111502" type="Const" version="opset1">
			<data element_type="f16" offset="685544" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="409" name="2078208211472" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="410" name="2079208311184" type="Const" version="opset1">
			<data element_type="f16" offset="685544" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="411" name="2896290010635" type="Const" version="opset1">
			<data element_type="f16" offset="6" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="412" name="2897290111208" type="Const" version="opset1">
			<data element_type="f16" offset="685546" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="413" name="2898290211358" type="Const" version="opset1">
			<data element_type="f16" offset="6" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="414" name="2899290310914" type="Const" version="opset1">
			<data element_type="f16" offset="685546" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="415" name="2636264010443" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="416" name="2637264110782" type="Const" version="opset1">
			<data element_type="f16" offset="685994" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="417" name="2638264210101" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="418" name="2639264310278" type="Const" version="opset1">
			<data element_type="f16" offset="685994" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="419" name="8637/value863911388" type="Const" version="opset1">
			<data element_type="i64" offset="685996" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="420" name="conv5_6/dw/bn/mean/Fused_Mul__copy6155142/quantized676610110" type="Const" version="opset1">
			<data element_type="i8" offset="686036" shape="184,1,3,3" size="1656"/>
			<output>
				<port id="0" precision="I8">
					<dim>184</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="421" name="conv5_6/dw/bn/mean/Fused_Mul__copy6155142/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>184</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>184</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="422" name="conv5_6/dw/fq_weights_1/zero_point677910920" type="Const" version="opset1">
			<data element_type="f16" offset="687692" shape="184,1,1,1" size="368"/>
			<output>
				<port id="0" precision="FP16">
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="423" name="conv5_6/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>184</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>184</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="424" name="conv5_6/dw/fq_weights_1/scale677410344" type="Const" version="opset1">
			<data element_type="f16" offset="688060" shape="184,1,1,1" size="368"/>
			<output>
				<port id="0" precision="FP16">
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="425" name="conv5_6/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>184</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>184</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="426" name="8637" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>184</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="427" name="conv5_6/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>184</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="428" name="data_add_121751218061710452" type="Const" version="opset1">
			<data element_type="f16" offset="688428" shape="1,184,1,1" size="368"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="429" name="conv5_6/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_6/dw" precision="FP16">
					<dim>1</dim>
					<dim>184</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="430" name="relu5_6/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_6/dw" precision="FP16">
					<dim>1</dim>
					<dim>184</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="431" name="conv5_6/sep/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>184</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="432" name="conv5_6/sep/bn/mean/Fused_Mul__copy6205144/quantized715010998" type="Const" version="opset1">
			<data element_type="i8" offset="688796" shape="224,184,1,1" size="41216"/>
			<output>
				<port id="0" precision="I8">
					<dim>224</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="433" name="conv5_6/sep/bn/mean/Fused_Mul__copy6205144/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>224</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="434" name="conv5_6/sep/WithoutBiases/fq_weights_1/zero_point716310779" type="Const" version="opset1">
			<data element_type="f16" offset="730012" shape="224,1,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="435" name="conv5_6/sep/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>224</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="436" name="conv5_6/sep/WithoutBiases/fq_weights_1/scale715810851" type="Const" version="opset1">
			<data element_type="f16" offset="730460" shape="224,1,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="437" name="conv5_6/sep/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>224</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="438" name="conv5_6/sep/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="439" name="data_add_121831218862210755" type="Const" version="opset1">
			<data element_type="f16" offset="730908" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="440" name="conv5_6/sep/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_6/sep" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="441" name="relu5_6/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_6/sep" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="442" name="conv6/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="443" name="8649/value865110230" type="Const" version="opset1">
			<data element_type="i64" offset="586590" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="444" name="conv6/dw/bn/mean/Fused_Mul__copy6255146/quantized611811001" type="Const" version="opset1">
			<data element_type="i8" offset="731356" shape="224,1,3,3" size="2016"/>
			<output>
				<port id="0" precision="I8">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="445" name="conv6/dw/bn/mean/Fused_Mul__copy6255146/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="446" name="conv6/dw/fq_weights_1/zero_point613111316" type="Const" version="opset1">
			<data element_type="f16" offset="733372" shape="224,1,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="447" name="conv6/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="448" name="conv6/dw/fq_weights_1/scale61269921" type="Const" version="opset1">
			<data element_type="f16" offset="733820" shape="224,1,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="449" name="conv6/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="450" name="8649" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>224</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="451" name="conv6/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="452" name="data_add_121911219662711235" type="Const" version="opset1">
			<data element_type="f16" offset="734268" shape="1,224,1,1" size="448"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="453" name="conv6/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6/dw" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="454" name="relu6/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv6/dw" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="455" name="conv6/sep/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="456" name="conv6/sep/bn/mean/Fused_Mul__copy6305148/quantized602210656" type="Const" version="opset1">
			<data element_type="i8" offset="734716" shape="128,224,1,1" size="28672"/>
			<output>
				<port id="0" precision="I8">
					<dim>128</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="457" name="conv6/sep/bn/mean/Fused_Mul__copy6305148/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>128</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="458" name="conv6/sep/fq_weights_1/zero_point60359819" type="Const" version="opset1">
			<data element_type="f16" offset="763388" shape="128,1,1,1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="459" name="conv6/sep/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>128</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>128</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="460" name="conv6/sep/fq_weights_1/scale60309942" type="Const" version="opset1">
			<data element_type="f16" offset="763644" shape="128,1,1,1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="461" name="conv6/sep/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>128</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>128</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="462" name="conv6/sep" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>224</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>128</dim>
					<dim>224</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="463" name="data_add_121991220463210068" type="Const" version="opset1">
			<data element_type="f16" offset="763900" shape="1,128,1,1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="464" name="conv6/sep/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6/sep" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="465" name="relu6/sep" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv6/sep" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="466" name="conv6/sep/reduce/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="467" name="3176355150/quantized707810944" type="Const" version="opset1">
			<data element_type="i8" offset="764156" shape="24,128,3,3" size="27648"/>
			<output>
				<port id="0" precision="I8">
					<dim>24</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="468" name="3176355150/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>24</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="469" name="fc7_mbox_loc/WithoutBiases/fq_weights_1/zero_point709110659" type="Const" version="opset1">
			<data element_type="f16" offset="791804" shape="24,1,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="470" name="fc7_mbox_loc/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>24</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="471" name="fc7_mbox_loc/WithoutBiases/fq_weights_1/scale708610035" type="Const" version="opset1">
			<data element_type="f16" offset="791852" shape="24,1,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="472" name="fc7_mbox_loc/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>24</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="473" name="fc7_mbox_loc/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="474" name="fc7_mbox_loc/Dims71256379897" type="Const" version="opset1">
			<data element_type="f16" offset="791900" shape="1,24,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="475" name="fc7_mbox_loc" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="fc7_mbox_loc" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="476" name="632639" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="477" name="fc7_mbox_loc_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="fc7_mbox_loc_perm" precision="FP16">
					<dim>1</dim>
					<dim>12</dim>
					<dim>21</dim>
					<dim>24</dim>
				</port>
			</output>
		</layer>
		<layer id="478" name="686/shapes_concat64110461" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="479" name="fc7_mbox_loc_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>12</dim>
					<dim>21</dim>
					<dim>24</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="fc7_mbox_loc_flat" precision="FP16">
					<dim>1</dim>
					<dim>6048</dim>
				</port>
			</output>
		</layer>
		<layer id="480" name="2676268010557" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="481" name="2677268110503" type="Const" version="opset1">
			<data element_type="f16" offset="791948" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="482" name="2678268210293" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="483" name="2679268310947" type="Const" version="opset1">
			<data element_type="f16" offset="791948" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="484" name="2216222010284" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="485" name="2217222111085" type="Const" version="opset1">
			<data element_type="f16" offset="791950" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="486" name="2218222211232" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="487" name="2219222311145" type="Const" version="opset1">
			<data element_type="f16" offset="791950" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="488" name="2696270010467" type="Const" version="opset1">
			<data element_type="f16" offset="7268" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="489" name="2697270111466" type="Const" version="opset1">
			<data element_type="f16" offset="791952" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="490" name="2698270210353" type="Const" version="opset1">
			<data element_type="f16" offset="7268" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="491" name="2699270310953" type="Const" version="opset1">
			<data element_type="f16" offset="791952" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="492" name="conv6_1/bn/mean/Fused_Mul__copy6435154/quantized609410728" type="Const" version="opset1">
			<data element_type="i8" offset="792192" shape="120,128,1,1" size="15360"/>
			<output>
				<port id="0" precision="I8">
					<dim>120</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="493" name="conv6_1/bn/mean/Fused_Mul__copy6435154/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>120</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="494" name="conv6_1/fq_weights_1/zero_point610710776" type="Const" version="opset1">
			<data element_type="f16" offset="807552" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="495" name="conv6_1/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="496" name="conv6_1/fq_weights_1/scale610211337" type="Const" version="opset1">
			<data element_type="f16" offset="807792" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="497" name="conv6_1/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="498" name="conv6_1" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="499" name="data_add_122151222064510569" type="Const" version="opset1">
			<data element_type="f16" offset="808032" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="500" name="conv6_1/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6_1" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="501" name="relu6_1" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv6_1" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="502" name="conv6_2/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="503" name="8629/value863110803" type="Const" version="opset1">
			<data element_type="i64" offset="33502" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="504" name="conv6_2/dw/bn/mean/Fused_Mul__copy6485156/quantized64069807" type="Const" version="opset1">
			<data element_type="i8" offset="808272" shape="120,1,3,3" size="1080"/>
			<output>
				<port id="0" precision="I8">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="505" name="conv6_2/dw/bn/mean/Fused_Mul__copy6485156/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="506" name="conv6_2/dw/fq_weights_1/zero_point641911283" type="Const" version="opset1">
			<data element_type="f16" offset="809352" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="507" name="conv6_2/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="508" name="conv6_2/dw/fq_weights_1/scale641410593" type="Const" version="opset1">
			<data element_type="f16" offset="809592" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="509" name="conv6_2/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="510" name="8629" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="511" name="conv6_2/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="512" name="data_add_122231222865010173" type="Const" version="opset1">
			<data element_type="f16" offset="809832" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="513" name="conv6_2/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="514" name="relu6_2/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv6_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="515" name="conv6_2_new/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="516" name="conv6_2/bn/mean/Fused_Mul__copy6535158/quantized659810644" type="Const" version="opset1">
			<data element_type="i8" offset="810072" shape="152,120,1,1" size="18240"/>
			<output>
				<port id="0" precision="I8">
					<dim>152</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="517" name="conv6_2/bn/mean/Fused_Mul__copy6535158/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>152</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>152</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="518" name="conv6_2_new/fq_weights_1/zero_point661111079" type="Const" version="opset1">
			<data element_type="f16" offset="828312" shape="152,1,1,1" size="304"/>
			<output>
				<port id="0" precision="FP16">
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="519" name="conv6_2_new/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>152</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>152</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="520" name="conv6_2_new/fq_weights_1/scale660610422" type="Const" version="opset1">
			<data element_type="f16" offset="828616" shape="152,1,1,1" size="304"/>
			<output>
				<port id="0" precision="FP16">
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="521" name="conv6_2_new/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>152</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>152</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="522" name="conv6_2_new" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>152</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>152</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="523" name="data_add_122311223665511364" type="Const" version="opset1">
			<data element_type="f16" offset="828920" shape="1,152,1,1" size="304"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="524" name="conv6_2/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>152</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6_2" precision="FP16">
					<dim>1</dim>
					<dim>152</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="525" name="relu6_2" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>152</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv6_2" precision="FP16">
					<dim>1</dim>
					<dim>152</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="526" name="conv6_2/reduce/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>152</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>152</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="527" name="3196585160/quantized599811415" type="Const" version="opset1">
			<data element_type="i8" offset="829224" shape="24,152,3,3" size="32832"/>
			<output>
				<port id="0" precision="I8">
					<dim>24</dim>
					<dim>152</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="528" name="3196585160/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>152</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>24</dim>
					<dim>152</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="529" name="conv6_2_mbox_loc/WithoutBiases/fq_weights_1/zero_point601110608" type="Const" version="opset1">
			<data element_type="f16" offset="862056" shape="24,1,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="530" name="conv6_2_mbox_loc/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>152</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>24</dim>
					<dim>152</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="531" name="conv6_2_mbox_loc/WithoutBiases/fq_weights_1/scale600610032" type="Const" version="opset1">
			<data element_type="f16" offset="862104" shape="24,1,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="532" name="conv6_2_mbox_loc/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>152</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>24</dim>
					<dim>152</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="533" name="conv6_2_mbox_loc/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>152</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>152</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="534" name="conv6_2_mbox_loc/Dims70956609918" type="Const" version="opset1">
			<data element_type="f16" offset="862152" shape="1,24,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="535" name="conv6_2_mbox_loc" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6_2_mbox_loc" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="536" name="629662" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="537" name="conv6_2_mbox_loc_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6_2_mbox_loc_perm" precision="FP16">
					<dim>1</dim>
					<dim>6</dim>
					<dim>11</dim>
					<dim>24</dim>
				</port>
			</output>
		</layer>
		<layer id="538" name="696/shapes_concat6649804" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="539" name="conv6_2_mbox_loc_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>6</dim>
					<dim>11</dim>
					<dim>24</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6_2_mbox_loc_flat" precision="FP16">
					<dim>1</dim>
					<dim>1584</dim>
				</port>
			</output>
		</layer>
		<layer id="540" name="2436244011343" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="541" name="243724419954" type="Const" version="opset1">
			<data element_type="f16" offset="862200" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="542" name="2438244210773" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="543" name="2439244310653" type="Const" version="opset1">
			<data element_type="f16" offset="862200" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="544" name="2236224010029" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="545" name="2237224111061" type="Const" version="opset1">
			<data element_type="f16" offset="862202" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="546" name="2238224211313" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="547" name="2239224311196" type="Const" version="opset1">
			<data element_type="f16" offset="862202" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="548" name="1956196010686" type="Const" version="opset1">
			<data element_type="f16" offset="862204" shape="1,112,1,1" size="224"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="549" name="1957196110059" type="Const" version="opset1">
			<data element_type="f16" offset="862428" shape="1,112,1,1" size="224"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="550" name="1958196210542" type="Const" version="opset1">
			<data element_type="f16" offset="862204" shape="1,112,1,1" size="224"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="551" name="1959196310095" type="Const" version="opset1">
			<data element_type="f16" offset="862428" shape="1,112,1,1" size="224"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="552" name="conv7_1/bn/mean/Fused_Mul__copy6665164/quantized587811055" type="Const" version="opset1">
			<data element_type="i8" offset="862652" shape="112,152,1,1" size="17024"/>
			<output>
				<port id="0" precision="I8">
					<dim>112</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="553" name="conv7_1/bn/mean/Fused_Mul__copy6665164/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>112</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>112</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="554" name="conv7_1/fq_weights_1/zero_point589110506" type="Const" version="opset1">
			<data element_type="f16" offset="879676" shape="112,1,1,1" size="224"/>
			<output>
				<port id="0" precision="FP16">
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="555" name="conv7_1/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>112</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>112</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="556" name="conv7_1/fq_weights_1/scale588610296" type="Const" version="opset1">
			<data element_type="f16" offset="879900" shape="112,1,1,1" size="224"/>
			<output>
				<port id="0" precision="FP16">
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="557" name="conv7_1/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>112</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>112</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="558" name="conv7_1" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>152</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>112</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="559" name="data_add_122471225266810986" type="Const" version="opset1">
			<data element_type="f16" offset="880124" shape="1,112,1,1" size="224"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="560" name="conv7_1/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>112</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv7_1" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="561" name="relu7_1" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>112</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv7_1" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="562" name="conv7_2/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>112</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="563" name="8601/value860310062" type="Const" version="opset1">
			<data element_type="i64" offset="880348" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="564" name="conv7_2/dw/bn/mean/Fused_Mul__copy6715166/quantized695811202" type="Const" version="opset1">
			<data element_type="i8" offset="880388" shape="112,1,3,3" size="1008"/>
			<output>
				<port id="0" precision="I8">
					<dim>112</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="565" name="conv7_2/dw/bn/mean/Fused_Mul__copy6715166/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>112</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>112</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="566" name="conv7_2/dw/fq_weights_1/zero_point697110575" type="Const" version="opset1">
			<data element_type="f16" offset="881396" shape="112,1,1,1" size="224"/>
			<output>
				<port id="0" precision="FP16">
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="567" name="conv7_2/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>112</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>112</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="568" name="conv7_2/dw/fq_weights_1/scale696610989" type="Const" version="opset1">
			<data element_type="f16" offset="881620" shape="112,1,1,1" size="224"/>
			<output>
				<port id="0" precision="FP16">
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="569" name="conv7_2/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>112</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>112</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="570" name="8601" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>112</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="571" name="conv7_2/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>112</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="572" name="data_add_122551226067311178" type="Const" version="opset1">
			<data element_type="f16" offset="881844" shape="1,112,1,1" size="224"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="573" name="conv7_2/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>112</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv7_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="574" name="relu7_2/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>112</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv7_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="575" name="conv7_2_new/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>112</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>112</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="576" name="conv7_2/bn/mean/Fused_Mul__copy6765168/quantized650210812" type="Const" version="opset1">
			<data element_type="i8" offset="882068" shape="168,112,1,1" size="18816"/>
			<output>
				<port id="0" precision="I8">
					<dim>168</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="577" name="conv7_2/bn/mean/Fused_Mul__copy6765168/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>168</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>168</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="578" name="conv7_2_new/fq_weights_1/zero_point651510425" type="Const" version="opset1">
			<data element_type="f16" offset="900884" shape="168,1,1,1" size="336"/>
			<output>
				<port id="0" precision="FP16">
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="579" name="conv7_2_new/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>168</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>168</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="580" name="conv7_2_new/fq_weights_1/scale651010788" type="Const" version="opset1">
			<data element_type="f16" offset="901220" shape="168,1,1,1" size="336"/>
			<output>
				<port id="0" precision="FP16">
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="581" name="conv7_2_new/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>168</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>168</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="582" name="conv7_2_new" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>112</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>168</dim>
					<dim>112</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="583" name="data_add_122631226867810491" type="Const" version="opset1">
			<data element_type="f16" offset="901556" shape="1,168,1,1" size="336"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="584" name="conv7_2/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv7_2" precision="FP16">
					<dim>1</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="585" name="relu7_2" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv7_2" precision="FP16">
					<dim>1</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="586" name="conv7_2/reduce/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="587" name="4446815170/quantized67189843" type="Const" version="opset1">
			<data element_type="i8" offset="901892" shape="24,168,3,3" size="36288"/>
			<output>
				<port id="0" precision="I8">
					<dim>24</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="588" name="4446815170/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>24</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="589" name="conv7_2_mbox_loc/WithoutBiases/fq_weights_1/zero_point673110281" type="Const" version="opset1">
			<data element_type="f16" offset="938180" shape="24,1,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="590" name="conv7_2_mbox_loc/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>24</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="591" name="conv7_2_mbox_loc/WithoutBiases/fq_weights_1/scale672611373" type="Const" version="opset1">
			<data element_type="f16" offset="938228" shape="24,1,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="592" name="conv7_2_mbox_loc/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>24</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>24</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="593" name="conv7_2_mbox_loc/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>24</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="594" name="conv7_2_mbox_loc/Dims707768311088" type="Const" version="opset1">
			<data element_type="f16" offset="938276" shape="1,24,1,1" size="48"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="595" name="conv7_2_mbox_loc" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>24</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv7_2_mbox_loc" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="596" name="631685" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="597" name="conv7_2_mbox_loc_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv7_2_mbox_loc_perm" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>6</dim>
					<dim>24</dim>
				</port>
			</output>
		</layer>
		<layer id="598" name="711/shapes_concat68710908" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="599" name="conv7_2_mbox_loc_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>6</dim>
					<dim>24</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv7_2_mbox_loc_flat" precision="FP16">
					<dim>1</dim>
					<dim>432</dim>
				</port>
			</output>
		</layer>
		<layer id="600" name="2476248011022" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="601" name="2477248111496" type="Const" version="opset1">
			<data element_type="f16" offset="938324" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="602" name="2478248210014" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="603" name="2479248310551" type="Const" version="opset1">
			<data element_type="f16" offset="938324" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="604" name="2536254010248" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="605" name="2537254111286" type="Const" version="opset1">
			<data element_type="f16" offset="938326" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="606" name="253825429939" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="607" name="2539254310515" type="Const" version="opset1">
			<data element_type="f16" offset="938326" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="608" name="1696170010368" type="Const" version="opset1">
			<data element_type="f16" offset="7268" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="609" name="1697170111520" type="Const" version="opset1">
			<data element_type="f16" offset="938328" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="610" name="1698170210026" type="Const" version="opset1">
			<data element_type="f16" offset="7268" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="611" name="1699170311175" type="Const" version="opset1">
			<data element_type="f16" offset="938328" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="612" name="conv8_1/bn/mean/Fused_Mul__copy6895174/quantized710210005" type="Const" version="opset1">
			<data element_type="i8" offset="938568" shape="120,168,1,1" size="20160"/>
			<output>
				<port id="0" precision="I8">
					<dim>120</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="613" name="conv8_1/bn/mean/Fused_Mul__copy6895174/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>120</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="614" name="conv8_1/fq_weights_1/zero_point711511394" type="Const" version="opset1">
			<data element_type="f16" offset="958728" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="615" name="conv8_1/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="616" name="conv8_1/fq_weights_1/scale711010263" type="Const" version="opset1">
			<data element_type="f16" offset="958968" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="617" name="conv8_1/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="618" name="conv8_1" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="619" name="data_add_12279122846919924" type="Const" version="opset1">
			<data element_type="f16" offset="959208" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="620" name="conv8_1/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv8_1" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="621" name="relu8_1" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv8_1" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="622" name="conv8_2/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="623" name="8585/value858710758" type="Const" version="opset1">
			<data element_type="i64" offset="33502" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="624" name="conv8_2/dw/bn/mean/Fused_Mul__copy6945176/quantized667011253" type="Const" version="opset1">
			<data element_type="i8" offset="959448" shape="120,1,3,3" size="1080"/>
			<output>
				<port id="0" precision="I8">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="625" name="conv8_2/dw/bn/mean/Fused_Mul__copy6945176/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="626" name="conv8_2/dw/fq_weights_1/zero_point668310836" type="Const" version="opset1">
			<data element_type="f16" offset="960528" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="627" name="conv8_2/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="628" name="conv8_2/dw/fq_weights_1/scale667811166" type="Const" version="opset1">
			<data element_type="f16" offset="960768" shape="120,1,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="629" name="conv8_2/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="630" name="8585" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>120</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="631" name="conv8_2/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="632" name="data_add_122871229269610134" type="Const" version="opset1">
			<data element_type="f16" offset="961008" shape="1,120,1,1" size="240"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="633" name="conv8_2/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv8_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="634" name="relu8_2/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv8_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="635" name="conv8_2_new/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>120</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="636" name="conv8_2/bn/mean/Fused_Mul__copy6995178/quantized59029987" type="Const" version="opset1">
			<data element_type="i8" offset="961248" shape="200,120,1,1" size="24000"/>
			<output>
				<port id="0" precision="I8">
					<dim>200</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="637" name="conv8_2/bn/mean/Fused_Mul__copy6995178/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>200</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>200</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="638" name="conv8_2_new/fq_weights_1/zero_point591510611" type="Const" version="opset1">
			<data element_type="f16" offset="985248" shape="200,1,1,1" size="400"/>
			<output>
				<port id="0" precision="FP16">
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="639" name="conv8_2_new/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>200</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>200</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="640" name="conv8_2_new/fq_weights_1/scale591010317" type="Const" version="opset1">
			<data element_type="f16" offset="985648" shape="200,1,1,1" size="400"/>
			<output>
				<port id="0" precision="FP16">
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="641" name="conv8_2_new/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>200</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>200</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="642" name="conv8_2_new" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>120</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>200</dim>
					<dim>120</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>200</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="643" name="data_add_122951230070111451" type="Const" version="opset1">
			<data element_type="f16" offset="986048" shape="1,200,1,1" size="400"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="644" name="conv8_2/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>200</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv8_2" precision="FP16">
					<dim>1</dim>
					<dim>200</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="645" name="relu8_2" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>200</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv8_2" precision="FP16">
					<dim>1</dim>
					<dim>200</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="646" name="conv8_2/reduce/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>200</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>200</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="647" name="4887045180/quantized607010842" type="Const" version="opset1">
			<data element_type="i8" offset="986448" shape="16,200,3,3" size="28800"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>200</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="648" name="4887045180/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>200</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>200</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="649" name="conv8_2_mbox_loc/WithoutBiases/fq_weights_1/zero_point608311505" type="Const" version="opset1">
			<data element_type="f16" offset="1015248" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="650" name="conv8_2_mbox_loc/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>200</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>200</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="651" name="conv8_2_mbox_loc/WithoutBiases/fq_weights_1/scale607810872" type="Const" version="opset1">
			<data element_type="f16" offset="1015280" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="652" name="conv8_2_mbox_loc/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>200</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>200</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="653" name="conv8_2_mbox_loc/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>200</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>200</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="654" name="conv8_2_mbox_loc/Dims719770610887" type="Const" version="opset1">
			<data element_type="f16" offset="1015312" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="655" name="conv8_2_mbox_loc" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv8_2_mbox_loc" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="656" name="627708" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="657" name="conv8_2_mbox_loc_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv8_2_mbox_loc_perm" precision="FP16">
					<dim>1</dim>
					<dim>2</dim>
					<dim>3</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="658" name="676/shapes_concat71010713" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="659" name="conv8_2_mbox_loc_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>3</dim>
					<dim>16</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv8_2_mbox_loc_flat" precision="FP16">
					<dim>1</dim>
					<dim>96</dim>
				</port>
			</output>
		</layer>
		<layer id="660" name="2136214010200" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="661" name="213721419801" type="Const" version="opset1">
			<data element_type="f16" offset="1015344" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="662" name="2138214211025" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="663" name="2139214310641" type="Const" version="opset1">
			<data element_type="f16" offset="1015344" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="664" name="1836184010848" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="665" name="1837184111331" type="Const" version="opset1">
			<data element_type="f16" offset="1015346" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="666" name="1838184210311" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="667" name="183918439855" type="Const" version="opset1">
			<data element_type="f16" offset="1015346" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="668" name="2196220011280" type="Const" version="opset1">
			<data element_type="f16" offset="1015348" shape="1,64,1,1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="669" name="2197220110038" type="Const" version="opset1">
			<data element_type="f16" offset="1015476" shape="1,64,1,1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="670" name="2198220211205" type="Const" version="opset1">
			<data element_type="f16" offset="1015348" shape="1,64,1,1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="671" name="2199220310734" type="Const" version="opset1">
			<data element_type="f16" offset="1015476" shape="1,64,1,1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="672" name="conv9_1/bn/mean/Fused_Mul__copy7125184/quantized691010179" type="Const" version="opset1">
			<data element_type="i8" offset="1015604" shape="64,200,1,1" size="12800"/>
			<output>
				<port id="0" precision="I8">
					<dim>64</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="673" name="conv9_1/bn/mean/Fused_Mul__copy7125184/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>64</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="674" name="conv9_1/fq_weights_1/zero_point692310182" type="Const" version="opset1">
			<data element_type="f16" offset="1028404" shape="64,1,1,1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="675" name="conv9_1/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>64</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>64</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="676" name="conv9_1/fq_weights_1/scale691810239" type="Const" version="opset1">
			<data element_type="f16" offset="1028532" shape="64,1,1,1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="677" name="conv9_1/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>64</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>64</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="678" name="conv9_1" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>200</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>64</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="679" name="data_add_123111231671410572" type="Const" version="opset1">
			<data element_type="f16" offset="1028660" shape="1,64,1,1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="680" name="conv9_1/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv9_1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="681" name="relu9_1" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv9_1" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="682" name="conv9_2/dw/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="683" name="8621/value862310677" type="Const" version="opset1">
			<data element_type="i64" offset="1028788" shape="5" size="40"/>
			<output>
				<port id="0" precision="I64">
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="684" name="conv9_2/dw/bn/mean/Fused_Mul__copy7175186/quantized585410155" type="Const" version="opset1">
			<data element_type="i8" offset="1028828" shape="64,1,3,3" size="576"/>
			<output>
				<port id="0" precision="I8">
					<dim>64</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="685" name="conv9_2/dw/bn/mean/Fused_Mul__copy7175186/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>64</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="686" name="conv9_2/dw/fq_weights_1/zero_point58679951" type="Const" version="opset1">
			<data element_type="f16" offset="1029404" shape="64,1,1,1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="687" name="conv9_2/dw/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>64</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="688" name="conv9_2/dw/fq_weights_1/scale586211478" type="Const" version="opset1">
			<data element_type="f16" offset="1029532" shape="64,1,1,1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="689" name="conv9_2/dw/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>64</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="690" name="8621" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>64</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="691" name="conv9_2/dw" type="GroupConvolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="692" name="data_add_123191232471910911" type="Const" version="opset1">
			<data element_type="f16" offset="1029660" shape="1,64,1,1" size="128"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="693" name="conv9_2/dw/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv9_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="694" name="relu9_2/dw" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv9_2/dw" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="695" name="conv9_2_new/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="696" name="conv9_2/bn/mean/Fused_Mul__copy7225188/quantized614210227" type="Const" version="opset1">
			<data element_type="i8" offset="1029788" shape="128,64,1,1" size="8192"/>
			<output>
				<port id="0" precision="I8">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="697" name="conv9_2/bn/mean/Fused_Mul__copy7225188/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="698" name="conv9_2_new/fq_weights_1/zero_point615511223" type="Const" version="opset1">
			<data element_type="f16" offset="1037980" shape="128,1,1,1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="699" name="conv9_2_new/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="700" name="conv9_2_new/fq_weights_1/scale61509867" type="Const" version="opset1">
			<data element_type="f16" offset="1038236" shape="128,1,1,1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="701" name="conv9_2_new/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="702" name="conv9_2_new" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>128</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="703" name="data_add_123271233272410245" type="Const" version="opset1">
			<data element_type="f16" offset="1038492" shape="1,128,1,1" size="256"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="704" name="conv9_2/bn/variance/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv9_2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="705" name="relu9_2" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv9_2" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="706" name="conv9_2/reduce/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="707" name="5777275190/quantized705410197" type="Const" version="opset1">
			<data element_type="i8" offset="1038748" shape="16,128,3,3" size="18432"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="708" name="5777275190/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="709" name="conv9_2_mbox_loc/WithoutBiases/fq_weights_1/zero_point706710674" type="Const" version="opset1">
			<data element_type="f16" offset="1057180" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="710" name="conv9_2_mbox_loc/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="711" name="conv9_2_mbox_loc/WithoutBiases/fq_weights_1/scale706210338" type="Const" version="opset1">
			<data element_type="f16" offset="1057212" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="712" name="conv9_2_mbox_loc/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="713" name="conv9_2_mbox_loc/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="714" name="conv9_2_mbox_loc/Dims71617299927" type="Const" version="opset1">
			<data element_type="f16" offset="1057244" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="715" name="conv9_2_mbox_loc" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv9_2_mbox_loc" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="716" name="634731" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="717" name="conv9_2_mbox_loc_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv9_2_mbox_loc_perm" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>2</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="718" name="651/shapes_concat73311031" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="719" name="conv9_2_mbox_loc_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>2</dim>
					<dim>16</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv9_2_mbox_loc_flat" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="720" name="mbox_loc" type="Concat" version="opset1">
			<data axis="1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16128</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16128</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>6048</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>1584</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>432</dim>
				</port>
				<port id="5">
					<dim>1</dim>
					<dim>96</dim>
				</port>
				<port id="6">
					<dim>1</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="7" names="mbox_loc" precision="FP16">
					<dim>1</dim>
					<dim>40448</dim>
				</port>
			</output>
		</layer>
		<layer id="721" name="2416242010815" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="722" name="2417242111004" type="Const" version="opset1">
			<data element_type="f16" offset="1057276" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="723" name="241824229870" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="724" name="2419242310065" type="Const" version="opset1">
			<data element_type="f16" offset="1057276" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="725" name="1816182010614" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="726" name="181718219822" type="Const" version="opset1">
			<data element_type="f16" offset="1057278" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="727" name="1818182211490" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="728" name="1819182311226" type="Const" version="opset1">
			<data element_type="f16" offset="1057278" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="729" name="5757365194/quantized688611262" type="Const" version="opset1">
			<data element_type="i8" offset="1057280" shape="16,184,1,1" size="2944"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="730" name="5757365194/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="731" name="conv5_5/sep/reduce/WithoutBiases/fq_weights_1/zero_point689910701" type="Const" version="opset1">
			<data element_type="f16" offset="1060224" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="732" name="conv5_5/sep/reduce/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="733" name="conv5_5/sep/reduce/WithoutBiases/fq_weights_1/scale689411181" type="Const" version="opset1">
			<data element_type="f16" offset="1060256" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="734" name="conv5_5/sep/reduce/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="735" name="conv5_5/sep/reduce/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>184</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="736" name="conv5_5/sep/reduce/Dims720973810236" type="Const" version="opset1">
			<data element_type="f16" offset="1060288" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="737" name="conv5_5/sep/reduce" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv5_5/sep/reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="738" name="conv5_5/sep/reduced/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv5_5/sep/reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="739" name="pre_conv4_3_0_norm_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="740" name="pre_conv4_3_0_norm_mbox_conf/bn/mean/Fused_Mul__copy7415196/quantized638211298" type="Const" version="opset1">
			<data element_type="i8" offset="1060320" shape="16,16,3,3" size="2304"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="741" name="pre_conv4_3_0_norm_mbox_conf/bn/mean/Fused_Mul__copy7415196/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="742" name="pre_conv4_3_0_norm_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point639510356" type="Const" version="opset1">
			<data element_type="f16" offset="1062624" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="743" name="pre_conv4_3_0_norm_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="744" name="pre_conv4_3_0_norm_mbox_conf_64/WithoutBiases/fq_weights_1/scale639010206" type="Const" version="opset1">
			<data element_type="f16" offset="1062656" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="745" name="pre_conv4_3_0_norm_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="746" name="pre_conv4_3_0_norm_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="747" name="data_add_12167121727439912" type="Const" version="opset1">
			<data element_type="f16" offset="1062688" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="748" name="pre_conv4_3_0_norm_mbox_conf_64/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="pre_conv4_3_0_norm_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="749" name="pre_conv4_3_0_norm_mbox_conf/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="pre_conv4_3_0_norm_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="750" name="conv4_3_0_norm_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="751" name="4607465198/quantized583011487" type="Const" version="opset1">
			<data element_type="i8" offset="1062720" shape="8,16,3,3" size="1152"/>
			<output>
				<port id="0" precision="I8">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="752" name="4607465198/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="753" name="conv4_3_0_norm_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point584311214" type="Const" version="opset1">
			<data element_type="f16" offset="1063872" shape="8,1,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="754" name="conv4_3_0_norm_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="755" name="conv4_3_0_norm_mbox_conf_64/WithoutBiases/fq_weights_1/scale583810083" type="Const" version="opset1">
			<data element_type="f16" offset="1063888" shape="8,1,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="756" name="conv4_3_0_norm_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="757" name="conv4_3_0_norm_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="758" name="conv4_3_0_norm_mbox_conf_64/Dims719174810449" type="Const" version="opset1">
			<data element_type="f16" offset="1063904" shape="1,8,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="759" name="conv4_3_0_norm_mbox_conf_64" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>8</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_0_norm_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="760" name="633750" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="761" name="conv4_3_0_norm_mbox_conf_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>8</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_0_norm_mbox_conf_perm" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>42</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="762" name="706/shapes_concat75210827" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="763" name="conv4_3_0_norm_mbox_conf_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>42</dim>
					<dim>8</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_0_norm_mbox_conf_flat" precision="FP16">
					<dim>1</dim>
					<dim>8064</dim>
				</port>
			</output>
		</layer>
		<layer id="764" name="2036204011193" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="765" name="2037204111469" type="Const" version="opset1">
			<data element_type="f16" offset="1063920" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="766" name="2038204210680" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="767" name="2039204310725" type="Const" version="opset1">
			<data element_type="f16" offset="1063920" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="768" name="pre_conv4_3_norm_mbox_conf/bn/mean/Fused_Mul__copy7545202/quantized65509981" type="Const" version="opset1">
			<data element_type="i8" offset="1063922" shape="16,16,3,3" size="2304"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="769" name="pre_conv4_3_norm_mbox_conf/bn/mean/Fused_Mul__copy7545202/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="770" name="pre_conv4_3_norm_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point656311439" type="Const" version="opset1">
			<data element_type="f16" offset="1066226" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="771" name="pre_conv4_3_norm_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="772" name="pre_conv4_3_norm_mbox_conf_64/WithoutBiases/fq_weights_1/scale655810404" type="Const" version="opset1">
			<data element_type="f16" offset="1066258" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="773" name="pre_conv4_3_norm_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="774" name="pre_conv4_3_norm_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="775" name="data_add_121591216475610767" type="Const" version="opset1">
			<data element_type="f16" offset="1066290" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="776" name="pre_conv4_3_norm_mbox_conf_64/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="pre_conv4_3_norm_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="777" name="pre_conv4_3_norm_mbox_conf/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" names="pre_conv4_3_norm_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="778" name="conv4_3_norm_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="779" name="3067595204/quantized664610170" type="Const" version="opset1">
			<data element_type="i8" offset="1066322" shape="8,16,3,3" size="1152"/>
			<output>
				<port id="0" precision="I8">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="780" name="3067595204/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="781" name="conv4_3_norm_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point665910584" type="Const" version="opset1">
			<data element_type="f16" offset="1067474" shape="8,1,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="782" name="conv4_3_norm_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="783" name="conv4_3_norm_mbox_conf_64/WithoutBiases/fq_weights_1/scale665411190" type="Const" version="opset1">
			<data element_type="f16" offset="1067490" shape="8,1,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="784" name="conv4_3_norm_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="785" name="conv4_3_norm_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="786" name="conv4_3_norm_mbox_conf_64/Dims717976110497" type="Const" version="opset1">
			<data element_type="f16" offset="1067506" shape="1,8,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="787" name="conv4_3_norm_mbox_conf_64" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>8</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_norm_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</output>
		</layer>
		<layer id="788" name="636763" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="789" name="conv4_3_norm_mbox_conf_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>8</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_norm_mbox_conf_perm" precision="FP16">
					<dim>1</dim>
					<dim>24</dim>
					<dim>42</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="790" name="646/shapes_concat76510695" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="791" name="conv4_3_norm_mbox_conf_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>42</dim>
					<dim>8</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_norm_mbox_conf_flat" precision="FP16">
					<dim>1</dim>
					<dim>8064</dim>
				</port>
			</output>
		</layer>
		<layer id="792" name="2356236010902" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="793" name="2357236110224" type="Const" version="opset1">
			<data element_type="f16" offset="1067522" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="794" name="235823629861" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="795" name="2359236311289" type="Const" version="opset1">
			<data element_type="f16" offset="1067522" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="796" name="2116212010044" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="797" name="211721219966" type="Const" version="opset1">
			<data element_type="f16" offset="1067524" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="798" name="2118212211376" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="799" name="2119212310470" type="Const" version="opset1">
			<data element_type="f16" offset="1067524" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="800" name="3897675208/quantized647810395" type="Const" version="opset1">
			<data element_type="i8" offset="1067526" shape="16,128,1,1" size="2048"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="801" name="3897675208/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="802" name="conv6/sep/reduce/WithoutBiases/fq_weights_1/zero_point649110554" type="Const" version="opset1">
			<data element_type="f16" offset="1069574" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="803" name="conv6/sep/reduce/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="804" name="conv6/sep/reduce/WithoutBiases/fq_weights_1/scale648610167" type="Const" version="opset1">
			<data element_type="f16" offset="1069606" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="805" name="conv6/sep/reduce/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="806" name="conv6/sep/reduce/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="807" name="conv6/sep/reduce/Dims714376911514" type="Const" version="opset1">
			<data element_type="f16" offset="1069638" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="808" name="conv6/sep/reduce" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6/sep/reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="809" name="conv6/sep/reduced/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv6/sep/reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="810" name="pre_fc7_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="811" name="pre_fc7_mbox_conf/bn/mean/Fused_Mul__copy7725210/quantized597410740" type="Const" version="opset1">
			<data element_type="i8" offset="1069670" shape="16,16,3,3" size="2304"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="812" name="pre_fc7_mbox_conf/bn/mean/Fused_Mul__copy7725210/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="813" name="pre_fc7_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point598711130" type="Const" version="opset1">
			<data element_type="f16" offset="1071974" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="814" name="pre_fc7_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="815" name="pre_fc7_mbox_conf_64/WithoutBiases/fq_weights_1/scale598210434" type="Const" version="opset1">
			<data element_type="f16" offset="1072006" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="816" name="pre_fc7_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="817" name="pre_fc7_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="818" name="data_add_122071221277411172" type="Const" version="opset1">
			<data element_type="f16" offset="1072038" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="819" name="pre_fc7_mbox_conf_64/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="pre_fc7_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="820" name="pre_fc7_mbox_conf/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</input>
			<output>
				<port id="1" names="pre_fc7_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="821" name="fc7_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="822" name="5707775212/quantized700610824" type="Const" version="opset1">
			<data element_type="i8" offset="1072070" shape="12,16,3,3" size="1728"/>
			<output>
				<port id="0" precision="I8">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="823" name="5707775212/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="824" name="fc7_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point701910995" type="Const" version="opset1">
			<data element_type="f16" offset="1073798" shape="12,1,1,1" size="24"/>
			<output>
				<port id="0" precision="FP16">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="825" name="fc7_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="826" name="fc7_mbox_conf_64/WithoutBiases/fq_weights_1/scale70149840" type="Const" version="opset1">
			<data element_type="f16" offset="1073822" shape="12,1,1,1" size="24"/>
			<output>
				<port id="0" precision="FP16">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="827" name="fc7_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="828" name="fc7_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>12</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="829" name="fc7_mbox_conf_64/Dims710777910905" type="Const" version="opset1">
			<data element_type="f16" offset="1073846" shape="1,12,1,1" size="24"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="830" name="fc7_mbox_conf_64" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>12</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="fc7_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>12</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</output>
		</layer>
		<layer id="831" name="639781" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="832" name="fc7_mbox_conf_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>12</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="fc7_mbox_conf_perm" precision="FP16">
					<dim>1</dim>
					<dim>12</dim>
					<dim>21</dim>
					<dim>12</dim>
				</port>
			</output>
		</layer>
		<layer id="833" name="691/shapes_concat78310845" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="834" name="fc7_mbox_conf_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>12</dim>
					<dim>21</dim>
					<dim>12</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="fc7_mbox_conf_flat" precision="FP16">
					<dim>1</dim>
					<dim>3024</dim>
				</port>
			</output>
		</layer>
		<layer id="835" name="2656266010002" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="836" name="2657266110932" type="Const" version="opset1">
			<data element_type="f16" offset="1073870" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="837" name="2658266210884" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="838" name="2659266310056" type="Const" version="opset1">
			<data element_type="f16" offset="1073870" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="839" name="2596260010785" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="840" name="2597260110524" type="Const" version="opset1">
			<data element_type="f16" offset="1073872" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="841" name="2598260210209" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="842" name="2599260310650" type="Const" version="opset1">
			<data element_type="f16" offset="1073872" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="843" name="4387855216/quantized679011058" type="Const" version="opset1">
			<data element_type="i8" offset="1073874" shape="16,152,1,1" size="2432"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="844" name="4387855216/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="845" name="conv6_2/reduce/WithoutBiases/fq_weights_1/zero_point68039882" type="Const" version="opset1">
			<data element_type="f16" offset="1076306" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="846" name="conv6_2/reduce/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="847" name="conv6_2/reduce/WithoutBiases/fq_weights_1/scale679810950" type="Const" version="opset1">
			<data element_type="f16" offset="1076338" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="848" name="conv6_2/reduce/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="849" name="conv6_2/reduce/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>152</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>152</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="850" name="conv6_2/reduce/Dims70717879975" type="Const" version="opset1">
			<data element_type="f16" offset="1076370" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="851" name="conv6_2/reduce" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6_2/reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="852" name="conv6_2/reduced/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv6_2/reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="853" name="pre_conv6_2_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="854" name="pre_conv6_2_mbox_conf/bn/mean/Fused_Mul__copy7905218/quantized734211097" type="Const" version="opset1">
			<data element_type="i8" offset="1076402" shape="16,16,3,3" size="2304"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="855" name="pre_conv6_2_mbox_conf/bn/mean/Fused_Mul__copy7905218/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="856" name="pre_conv6_2_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point735511112" type="Const" version="opset1">
			<data element_type="f16" offset="1078706" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="857" name="pre_conv6_2_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="858" name="pre_conv6_2_mbox_conf_64/WithoutBiases/fq_weights_1/scale735011160" type="Const" version="opset1">
			<data element_type="f16" offset="1078738" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="859" name="pre_conv6_2_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="860" name="pre_conv6_2_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="861" name="data_add_12239122447929909" type="Const" version="opset1">
			<data element_type="f16" offset="1078770" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="862" name="pre_conv6_2_mbox_conf_64/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="pre_conv6_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="863" name="pre_conv6_2_mbox_conf/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</input>
			<output>
				<port id="1" names="pre_conv6_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="864" name="conv6_2_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="865" name="4087955220/quantized623810683" type="Const" version="opset1">
			<data element_type="i8" offset="1078802" shape="12,16,3,3" size="1728"/>
			<output>
				<port id="0" precision="I8">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="866" name="4087955220/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="867" name="conv6_2_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point625111241" type="Const" version="opset1">
			<data element_type="f16" offset="1080530" shape="12,1,1,1" size="24"/>
			<output>
				<port id="0" precision="FP16">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="868" name="conv6_2_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="869" name="conv6_2_mbox_conf_64/WithoutBiases/fq_weights_1/scale624611370" type="Const" version="opset1">
			<data element_type="f16" offset="1080554" shape="12,1,1,1" size="24"/>
			<output>
				<port id="0" precision="FP16">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="870" name="conv6_2_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="871" name="conv6_2_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>12</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="872" name="conv6_2_mbox_conf_64/Dims705979710899" type="Const" version="opset1">
			<data element_type="f16" offset="1080578" shape="1,12,1,1" size="24"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="873" name="conv6_2_mbox_conf_64" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>12</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>12</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</output>
		</layer>
		<layer id="874" name="638799" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="875" name="conv6_2_mbox_conf_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>12</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6_2_mbox_conf_perm" precision="FP16">
					<dim>1</dim>
					<dim>6</dim>
					<dim>11</dim>
					<dim>12</dim>
				</port>
			</output>
		</layer>
		<layer id="876" name="701/shapes_concat80110275" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="877" name="conv6_2_mbox_conf_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>6</dim>
					<dim>11</dim>
					<dim>12</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6_2_mbox_conf_flat" precision="FP16">
					<dim>1</dim>
					<dim>792</dim>
				</port>
			</output>
		</layer>
		<layer id="878" name="2856286010335" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="879" name="2857286110647" type="Const" version="opset1">
			<data element_type="f16" offset="1080602" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="880" name="285828629969" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="881" name="2859286311349" type="Const" version="opset1">
			<data element_type="f16" offset="1080602" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="882" name="1636164011142" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="883" name="163716419945" type="Const" version="opset1">
			<data element_type="f16" offset="1080604" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="884" name="1638164211187" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="885" name="1639164311433" type="Const" version="opset1">
			<data element_type="f16" offset="1080604" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="886" name="4248035224/quantized683810269" type="Const" version="opset1">
			<data element_type="i8" offset="1080606" shape="16,168,1,1" size="2688"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="887" name="4248035224/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="888" name="conv7_2/reduce/WithoutBiases/fq_weights_1/zero_point685111436" type="Const" version="opset1">
			<data element_type="f16" offset="1083294" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="889" name="conv7_2/reduce/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="890" name="conv7_2/reduce/WithoutBiases/fq_weights_1/scale684610875" type="Const" version="opset1">
			<data element_type="f16" offset="1083326" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="891" name="conv7_2/reduce/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="892" name="conv7_2/reduce/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>168</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="893" name="conv7_2/reduce/Dims706580510587" type="Const" version="opset1">
			<data element_type="f16" offset="1083358" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="894" name="conv7_2/reduce" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv7_2_reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="895" name="conv7_2_reduced/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv7_2_reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="896" name="pre_conv7_2_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="897" name="pre_conv7_2_mbox_conf/bn/mean/Fused_Mul__copy8085226/quantized703011028" type="Const" version="opset1">
			<data element_type="i8" offset="1083390" shape="16,16,3,3" size="2304"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="898" name="pre_conv7_2_mbox_conf/bn/mean/Fused_Mul__copy8085226/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="899" name="pre_conv7_2_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point704310626" type="Const" version="opset1">
			<data element_type="f16" offset="1085694" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="900" name="pre_conv7_2_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="901" name="pre_conv7_2_mbox_conf_64/WithoutBiases/fq_weights_1/scale703810764" type="Const" version="opset1">
			<data element_type="f16" offset="1085726" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="902" name="pre_conv7_2_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="903" name="pre_conv7_2_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="904" name="data_add_122711227681010299" type="Const" version="opset1">
			<data element_type="f16" offset="1085758" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="905" name="pre_conv7_2_mbox_conf_64/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="pre_conv7_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="906" name="pre_conv7_2_mbox_conf/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</input>
			<output>
				<port id="1" names="pre_conv7_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="907" name="conv7_2_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="908" name="5328135228/quantized698210020" type="Const" version="opset1">
			<data element_type="i8" offset="1085790" shape="12,16,3,3" size="1728"/>
			<output>
				<port id="0" precision="I8">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="909" name="5328135228/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="910" name="conv7_2_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point699510521" type="Const" version="opset1">
			<data element_type="f16" offset="1087518" shape="12,1,1,1" size="24"/>
			<output>
				<port id="0" precision="FP16">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="911" name="conv7_2_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="912" name="conv7_2_mbox_conf_64/WithoutBiases/fq_weights_1/scale699011493" type="Const" version="opset1">
			<data element_type="f16" offset="1087542" shape="12,1,1,1" size="24"/>
			<output>
				<port id="0" precision="FP16">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="913" name="conv7_2_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="914" name="conv7_2_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>12</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>12</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="915" name="conv7_2_mbox_conf_64/Dims720381511445" type="Const" version="opset1">
			<data element_type="f16" offset="1087566" shape="1,12,1,1" size="24"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="916" name="conv7_2_mbox_conf_64" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>12</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>12</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv7_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>12</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</output>
		</layer>
		<layer id="917" name="630817" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="918" name="conv7_2_mbox_conf_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>12</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv7_2_mbox_conf_perm" precision="FP16">
					<dim>1</dim>
					<dim>3</dim>
					<dim>6</dim>
					<dim>12</dim>
				</port>
			</output>
		</layer>
		<layer id="919" name="656/shapes_concat81910977" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="920" name="conv7_2_mbox_conf_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>6</dim>
					<dim>12</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv7_2_mbox_conf_flat" precision="FP16">
					<dim>1</dim>
					<dim>216</dim>
				</port>
			</output>
		</layer>
		<layer id="921" name="2016202010212" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="922" name="2017202110878" type="Const" version="opset1">
			<data element_type="f16" offset="1087590" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="923" name="2018202210287" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="924" name="2019202310536" type="Const" version="opset1">
			<data element_type="f16" offset="1087590" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="925" name="1756176011109" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="926" name="1757176110926" type="Const" version="opset1">
			<data element_type="f16" offset="1087592" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="927" name="175817629933" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="928" name="1759176311259" type="Const" version="opset1">
			<data element_type="f16" offset="1087592" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="929" name="2958215232/quantized631010089" type="Const" version="opset1">
			<data element_type="i8" offset="1087594" shape="16,200,1,1" size="3200"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="930" name="2958215232/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="931" name="conv8_2/reduce/WithoutBiases/fq_weights_1/zero_point632310164" type="Const" version="opset1">
			<data element_type="f16" offset="1090794" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="932" name="conv8_2/reduce/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="933" name="conv8_2/reduce/WithoutBiases/fq_weights_1/scale63189957" type="Const" version="opset1">
			<data element_type="f16" offset="1090826" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="934" name="conv8_2/reduce/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="935" name="conv8_2/reduce/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>200</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>200</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="936" name="conv8_2/reduce/Dims705382310719" type="Const" version="opset1">
			<data element_type="f16" offset="1090858" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="937" name="conv8_2/reduce" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv8_2_reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="938" name="conv8_2_reduced/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv8_2_reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="939" name="pre_conv8_2_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="940" name="pre_conv8_2_mbox_conf/bn/mean/Fused_Mul__copy8265234/quantized719810365" type="Const" version="opset1">
			<data element_type="i8" offset="1090890" shape="16,16,3,3" size="2304"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="941" name="pre_conv8_2_mbox_conf/bn/mean/Fused_Mul__copy8265234/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="942" name="pre_conv8_2_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point721110419" type="Const" version="opset1">
			<data element_type="f16" offset="1093194" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="943" name="pre_conv8_2_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="944" name="pre_conv8_2_mbox_conf_64/WithoutBiases/fq_weights_1/scale720610464" type="Const" version="opset1">
			<data element_type="f16" offset="1093226" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="945" name="pre_conv8_2_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="946" name="pre_conv8_2_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="947" name="data_add_123031230882810371" type="Const" version="opset1">
			<data element_type="f16" offset="1093258" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="948" name="pre_conv8_2_mbox_conf_64/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="pre_conv8_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="949" name="pre_conv8_2_mbox_conf/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" names="pre_conv8_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="950" name="conv8_2_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="951" name="3338315236/quantized657410665" type="Const" version="opset1">
			<data element_type="i8" offset="1093290" shape="8,16,3,3" size="1152"/>
			<output>
				<port id="0" precision="I8">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="952" name="3338315236/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="953" name="conv8_2_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point658710272" type="Const" version="opset1">
			<data element_type="f16" offset="1094442" shape="8,1,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="954" name="conv8_2_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="955" name="conv8_2_mbox_conf_64/WithoutBiases/fq_weights_1/scale658210869" type="Const" version="opset1">
			<data element_type="f16" offset="1094458" shape="8,1,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="956" name="conv8_2_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="957" name="conv8_2_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="958" name="conv8_2_mbox_conf_64/Dims713183310638" type="Const" version="opset1">
			<data element_type="f16" offset="1094474" shape="1,8,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="959" name="conv8_2_mbox_conf_64" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>8</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv8_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="960" name="635835" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="961" name="conv8_2_mbox_conf_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>8</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv8_2_mbox_conf_perm" precision="FP16">
					<dim>1</dim>
					<dim>2</dim>
					<dim>3</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="962" name="681/shapes_concat83711046" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="963" name="conv8_2_mbox_conf_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>3</dim>
					<dim>8</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv8_2_mbox_conf_flat" precision="FP16">
					<dim>1</dim>
					<dim>48</dim>
				</port>
			</output>
		</layer>
		<layer id="964" name="1676168010860" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="965" name="1677168111517" type="Const" version="opset1">
			<data element_type="f16" offset="1094490" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="966" name="167816829876" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="967" name="1679168310485" type="Const" version="opset1">
			<data element_type="f16" offset="1094490" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="968" name="2796280010839" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="969" name="2797280110488" type="Const" version="opset1">
			<data element_type="f16" offset="1094492" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="970" name="2798280211484" type="Const" version="opset1">
			<data element_type="f16" offset="0" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="971" name="2799280311352" type="Const" version="opset1">
			<data element_type="f16" offset="1094492" shape="" size="2"/>
			<output>
				<port id="0" precision="FP16"/>
			</output>
		</layer>
		<layer id="972" name="4658395240/quantized724610458" type="Const" version="opset1">
			<data element_type="i8" offset="1094494" shape="16,128,1,1" size="2048"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="973" name="4658395240/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="974" name="conv9_2/reduce/WithoutBiases/fq_weights_1/zero_point725910386" type="Const" version="opset1">
			<data element_type="f16" offset="1096542" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="975" name="conv9_2/reduce/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="976" name="conv9_2/reduce/WithoutBiases/fq_weights_1/scale725410746" type="Const" version="opset1">
			<data element_type="f16" offset="1096574" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="977" name="conv9_2/reduce/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="978" name="conv9_2/reduce/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="979" name="conv9_2/reduce/Dims708384111064" type="Const" version="opset1">
			<data element_type="f16" offset="1096606" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="980" name="conv9_2/reduce" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv9_2_reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="981" name="conv9_2_reduced/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="1" names="conv9_2_reduced" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="982" name="pre_conv9_2_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="983" name="pre_conv9_2_mbox_conf/bn/mean/Fused_Mul__copy8445242/quantized729410260" type="Const" version="opset1">
			<data element_type="i8" offset="1096638" shape="16,16,3,3" size="2304"/>
			<output>
				<port id="0" precision="I8">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="984" name="pre_conv9_2_mbox_conf/bn/mean/Fused_Mul__copy8445242/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="985" name="pre_conv9_2_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point730711016" type="Const" version="opset1">
			<data element_type="f16" offset="1098942" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="986" name="pre_conv9_2_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="987" name="pre_conv9_2_mbox_conf_64/WithoutBiases/fq_weights_1/scale730211265" type="Const" version="opset1">
			<data element_type="f16" offset="1098974" shape="16,1,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="988" name="pre_conv9_2_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="989" name="pre_conv9_2_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="990" name="data_add_123351234084610833" type="Const" version="opset1">
			<data element_type="f16" offset="1099006" shape="1,16,1,1" size="32"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="991" name="pre_conv9_2_mbox_conf_64/Fused_Add_" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="pre_conv9_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="992" name="pre_conv9_2_mbox_conf/relu" type="ReLU" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="1" names="pre_conv9_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="993" name="conv9_2_mbox_conf_64/WithoutBiases/fq_input_0" type="FakeQuantize" version="opset1">
			<data auto_broadcast="numpy" levels="256"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1"/>
				<port id="2"/>
				<port id="3"/>
				<port id="4"/>
			</input>
			<output>
				<port id="5" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="994" name="5988495244/quantized619010233" type="Const" version="opset1">
			<data element_type="i8" offset="1099038" shape="8,16,3,3" size="1152"/>
			<output>
				<port id="0" precision="I8">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="995" name="5988495244/quantized/to_f16" type="Convert" version="opset1">
			<data destination_type="f16"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="996" name="conv9_2_mbox_conf_64/WithoutBiases/fq_weights_1/zero_point620310194" type="Const" version="opset1">
			<data element_type="f16" offset="1100190" shape="8,1,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="997" name="conv9_2_mbox_conf_64/WithoutBiases/fq_weights_1/minus_zp" type="Subtract" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="998" name="conv9_2_mbox_conf_64/WithoutBiases/fq_weights_1/scale619810416" type="Const" version="opset1">
			<data element_type="f16" offset="1100206" shape="8,1,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="999" name="conv9_2_mbox_conf_64/WithoutBiases/fq_weights_1/mulpiply_by_scale" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="1000" name="conv9_2_mbox_conf_64/WithoutBiases" type="Convolution" version="opset1">
			<data auto_pad="explicit" dilations="1,1" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1001" name="conv9_2_mbox_conf_64/Dims711385110266" type="Const" version="opset1">
			<data element_type="f16" offset="1100222" shape="1,8,1,1" size="16"/>
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1002" name="conv9_2_mbox_conf_64" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv9_2_mbox_conf_64" precision="FP16">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1003" name="628853" type="Const" version="opset1">
			<data element_type="i64" offset="658902" shape="4" size="32"/>
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1004" name="conv9_2_mbox_conf_perm" type="Transpose" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv9_2_mbox_conf_perm" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>2</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="1005" name="661/shapes_concat8559852" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1006" name="conv9_2_mbox_conf_flat" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>2</dim>
					<dim>8</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv9_2_mbox_conf_flat" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="1007" name="mbox_conf_16" type="Concat" version="opset1">
			<data axis="1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>8064</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>8064</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>3024</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>792</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>216</dim>
				</port>
				<port id="5">
					<dim>1</dim>
					<dim>48</dim>
				</port>
				<port id="6">
					<dim>1</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="7" names="mbox_conf_16" precision="FP16">
					<dim>1</dim>
					<dim>20224</dim>
				</port>
			</output>
		</layer>
		<layer id="1008" name="62685811106" type="Const" version="opset1">
			<data element_type="i64" offset="1100238" shape="3" size="24"/>
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="1009" name="mbox_conf_reshape" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>20224</dim>
				</port>
				<port id="1">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" names="mbox_conf_reshape" precision="FP16">
					<dim>1</dim>
					<dim>10112</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1010" name="mbox_conf_softmax" type="SoftMax" version="opset1">
			<data axis="2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>10112</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="1" names="mbox_conf_softmax" precision="FP16">
					<dim>1</dim>
					<dim>10112</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1011" name="671/shapes_concat8619894" type="Const" version="opset1">
			<data element_type="i64" offset="658934" shape="2" size="16"/>
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1012" name="mbox_conf_flatten" type="Reshape" version="opset1">
			<data special_zero="true"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>10112</dim>
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" names="mbox_conf_flatten" precision="FP16">
					<dim>1</dim>
					<dim>20224</dim>
				</port>
			</output>
		</layer>
		<layer id="1013" name="conv4_3_0_norm_mbox_priorbox/0_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1014" name="conv4_3_0_norm_mbox_priorbox/ss_begin1561421752864" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1015" name="conv4_3_0_norm_mbox_priorbox/ss_end1561521338865" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1016" name="conv4_3_0_norm_mbox_priorbox/ss_stride1561621548866" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1017" name="conv4_3_0_norm_mbox_priorbox/ss_0_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1018" name="conv4_3_0_norm_mbox_priorbox/1_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1019" name="conv4_3_0_norm_mbox_priorbox/ss_begin1561421611869" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1020" name="conv4_3_0_norm_mbox_priorbox/ss_end1561521242870" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1021" name="conv4_3_0_norm_mbox_priorbox/ss_stride1561621335871" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1022" name="conv4_3_0_norm_mbox_priorbox/ss_1_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1023" name="conv4_3_0_norm_mbox_priorbox/naked_not_unsqueezed" type="PriorBox" version="opset1">
			<data aspect_ratio="2" clip="0" density="" fixed_ratio="" fixed_size="" flip="1" max_size="38.4" min_size="16" offset="0.5" scale_all_sizes="true" step="16" variance="0.1,0.1,0.2,0.2"/>
			<input>
				<port id="0">
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>16128</dim>
				</port>
			</output>
		</layer>
		<layer id="1024" name="conv4_3_0_norm_mbox_priorbox/unsqueeze/value156242135087410632" type="Const" version="opset1">
			<data element_type="i64" offset="1100286" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1025" name="conv4_3_0_norm_mbox_priorbox" type="Unsqueeze" version="opset1">
			<input>
				<port id="0">
					<dim>2</dim>
					<dim>16128</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_0_norm_mbox_priorbox" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>16128</dim>
				</port>
			</output>
		</layer>
		<layer id="1026" name="conv4_3_norm_mbox_priorbox/0_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>184</dim>
					<dim>24</dim>
					<dim>42</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1027" name="conv4_3_norm_mbox_priorbox/ss_begin1568621533877" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1028" name="conv4_3_norm_mbox_priorbox/ss_end1568721395878" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1029" name="conv4_3_norm_mbox_priorbox/ss_stride1568821347879" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1030" name="conv4_3_norm_mbox_priorbox/ss_0_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1031" name="conv4_3_norm_mbox_priorbox/1_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1032" name="conv4_3_norm_mbox_priorbox/ss_begin1568621275882" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1033" name="conv4_3_norm_mbox_priorbox/ss_end1568721719883" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1034" name="conv4_3_norm_mbox_priorbox/ss_stride1568821245884" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1035" name="conv4_3_norm_mbox_priorbox/ss_1_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1036" name="conv4_3_norm_mbox_priorbox/naked_not_unsqueezed" type="PriorBox" version="opset1">
			<data aspect_ratio="2" clip="0" density="" fixed_ratio="" fixed_size="" flip="1" max_size="76.8" min_size="38.4" offset="0.5" scale_all_sizes="true" step="16" variance="0.1,0.1,0.2,0.2"/>
			<input>
				<port id="0">
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>16128</dim>
				</port>
			</output>
		</layer>
		<layer id="1037" name="conv4_3_norm_mbox_priorbox/unsqueeze/value156962165688710818" type="Const" version="opset1">
			<data element_type="i64" offset="1100286" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1038" name="conv4_3_norm_mbox_priorbox" type="Unsqueeze" version="opset1">
			<input>
				<port id="0">
					<dim>2</dim>
					<dim>16128</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv4_3_norm_mbox_priorbox" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>16128</dim>
				</port>
			</output>
		</layer>
		<layer id="1039" name="fc7_mbox_priorbox/0_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>21</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1040" name="fc7_mbox_priorbox/ss_begin1566821662890" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1041" name="fc7_mbox_priorbox/ss_end1566921374891" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1042" name="fc7_mbox_priorbox/ss_stride1567021353892" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1043" name="fc7_mbox_priorbox/ss_0_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1044" name="fc7_mbox_priorbox/1_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1045" name="fc7_mbox_priorbox/ss_begin1566821542895" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1046" name="fc7_mbox_priorbox/ss_end1566921236896" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1047" name="fc7_mbox_priorbox/ss_stride1567021620897" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1048" name="fc7_mbox_priorbox/ss_1_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1049" name="fc7_mbox_priorbox/naked_not_unsqueezed" type="PriorBox" version="opset1">
			<data aspect_ratio="2,3" clip="0" density="" fixed_ratio="" fixed_size="" flip="1" max_size="142.08" min_size="76.8" offset="0.5" scale_all_sizes="true" step="32" variance="0.1,0.1,0.2,0.2"/>
			<input>
				<port id="0">
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>6048</dim>
				</port>
			</output>
		</layer>
		<layer id="1050" name="fc7_mbox_priorbox/unsqueeze/value156782176490010980" type="Const" version="opset1">
			<data element_type="i64" offset="1100286" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1051" name="fc7_mbox_priorbox" type="Unsqueeze" version="opset1">
			<input>
				<port id="0">
					<dim>2</dim>
					<dim>6048</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="fc7_mbox_priorbox" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>6048</dim>
				</port>
			</output>
		</layer>
		<layer id="1052" name="conv6_2_mbox_priorbox/0_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>152</dim>
					<dim>6</dim>
					<dim>11</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1053" name="conv6_2_mbox_priorbox/ss_begin1559621392903" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1054" name="conv6_2_mbox_priorbox/ss_end1559721221904" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1055" name="conv6_2_mbox_priorbox/ss_stride1559821296905" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1056" name="conv6_2_mbox_priorbox/ss_0_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1057" name="conv6_2_mbox_priorbox/1_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1058" name="conv6_2_mbox_priorbox/ss_begin1559621284908" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1059" name="conv6_2_mbox_priorbox/ss_end1559721311909" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1060" name="conv6_2_mbox_priorbox/ss_stride1559821410910" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1061" name="conv6_2_mbox_priorbox/ss_1_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1062" name="conv6_2_mbox_priorbox/naked_not_unsqueezed" type="PriorBox" version="opset1">
			<data aspect_ratio="2,3" clip="0" density="" fixed_ratio="" fixed_size="" flip="1" max_size="207.36" min_size="142.08" offset="0.5" scale_all_sizes="true" step="64" variance="0.1,0.1,0.2,0.2"/>
			<input>
				<port id="0">
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>1584</dim>
				</port>
			</output>
		</layer>
		<layer id="1063" name="conv6_2_mbox_priorbox/unsqueeze/value156062118891311220" type="Const" version="opset1">
			<data element_type="i64" offset="1100286" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1064" name="conv6_2_mbox_priorbox" type="Unsqueeze" version="opset1">
			<input>
				<port id="0">
					<dim>2</dim>
					<dim>1584</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv6_2_mbox_priorbox" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>1584</dim>
				</port>
			</output>
		</layer>
		<layer id="1065" name="conv7_2_mbox_priorbox/0_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>168</dim>
					<dim>3</dim>
					<dim>6</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1066" name="conv7_2_mbox_priorbox/ss_begin1557821743916" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1067" name="conv7_2_mbox_priorbox/ss_end1557921287917" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1068" name="conv7_2_mbox_priorbox/ss_stride1558021401918" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1069" name="conv7_2_mbox_priorbox/ss_0_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1070" name="conv7_2_mbox_priorbox/1_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1071" name="conv7_2_mbox_priorbox/ss_begin1557821602921" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1072" name="conv7_2_mbox_priorbox/ss_end1557921695922" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1073" name="conv7_2_mbox_priorbox/ss_stride1558021419923" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1074" name="conv7_2_mbox_priorbox/ss_1_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1075" name="conv7_2_mbox_priorbox/naked_not_unsqueezed" type="PriorBox" version="opset1">
			<data aspect_ratio="2,3" clip="0" density="" fixed_ratio="" fixed_size="" flip="1" max_size="272.64" min_size="207.36" offset="0.5" scale_all_sizes="true" step="100" variance="0.1,0.1,0.2,0.2"/>
			<input>
				<port id="0">
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>432</dim>
				</port>
			</output>
		</layer>
		<layer id="1076" name="conv7_2_mbox_priorbox/unsqueeze/value155882165392611409" type="Const" version="opset1">
			<data element_type="i64" offset="1100286" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1077" name="conv7_2_mbox_priorbox" type="Unsqueeze" version="opset1">
			<input>
				<port id="0">
					<dim>2</dim>
					<dim>432</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv7_2_mbox_priorbox" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>432</dim>
				</port>
			</output>
		</layer>
		<layer id="1078" name="conv8_2_mbox_priorbox/0_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>200</dim>
					<dim>2</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1079" name="conv8_2_mbox_priorbox/ss_begin1563221272929" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1080" name="conv8_2_mbox_priorbox/ss_end1563321404930" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1081" name="conv8_2_mbox_priorbox/ss_stride1563421422931" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1082" name="conv8_2_mbox_priorbox/ss_0_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1083" name="conv8_2_mbox_priorbox/1_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1084" name="conv8_2_mbox_priorbox/ss_begin1563221629934" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1085" name="conv8_2_mbox_priorbox/ss_end1563321437935" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1086" name="conv8_2_mbox_priorbox/ss_stride1563421755936" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1087" name="conv8_2_mbox_priorbox/ss_1_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1088" name="conv8_2_mbox_priorbox/naked_not_unsqueezed" type="PriorBox" version="opset1">
			<data aspect_ratio="2" clip="0" density="" fixed_ratio="" fixed_size="" flip="1" max_size="337.92" min_size="272.64" offset="0.5" scale_all_sizes="true" step="150" variance="0.1,0.1,0.2,0.2"/>
			<input>
				<port id="0">
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>96</dim>
				</port>
			</output>
		</layer>
		<layer id="1089" name="conv8_2_mbox_priorbox/unsqueeze/value156422146793910074" type="Const" version="opset1">
			<data element_type="i64" offset="1100286" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1090" name="conv8_2_mbox_priorbox" type="Unsqueeze" version="opset1">
			<input>
				<port id="0">
					<dim>2</dim>
					<dim>96</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv8_2_mbox_priorbox" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>96</dim>
				</port>
			</output>
		</layer>
		<layer id="1091" name="conv9_2_mbox_priorbox/0_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1092" name="conv9_2_mbox_priorbox/ss_begin1565021308942" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1093" name="conv9_2_mbox_priorbox/ss_end1565121554943" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1094" name="conv9_2_mbox_priorbox/ss_stride1565221641944" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1095" name="conv9_2_mbox_priorbox/ss_0_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1096" name="conv9_2_mbox_priorbox/1_port" type="ShapeOf" version="opset3">
			<data output_type="i64"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>672</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="1097" name="conv9_2_mbox_priorbox/ss_begin1565021566947" type="Const" version="opset1">
			<data element_type="i64" offset="1100262" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1098" name="conv9_2_mbox_priorbox/ss_end1565121587948" type="Const" version="opset1">
			<data element_type="i64" offset="1100270" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1099" name="conv9_2_mbox_priorbox/ss_stride1565221254949" type="Const" version="opset1">
			<data element_type="i64" offset="1100278" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1100" name="conv9_2_mbox_priorbox/ss_1_port" type="StridedSlice" version="opset1">
			<data begin_mask="0" ellipsis_mask="0" end_mask="1" new_axis_mask="0" shrink_axis_mask="0"/>
			<input>
				<port id="0">
					<dim>4</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
				<port id="2">
					<dim>1</dim>
				</port>
				<port id="3">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="1101" name="conv9_2_mbox_priorbox/naked_not_unsqueezed" type="PriorBox" version="opset1">
			<data aspect_ratio="2" clip="0" density="" fixed_ratio="" fixed_size="" flip="1" max_size="403.2" min_size="337.92" offset="0.5" scale_all_sizes="true" step="300" variance="0.1,0.1,0.2,0.2"/>
			<input>
				<port id="0">
					<dim>2</dim>
				</port>
				<port id="1">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="1102" name="conv9_2_mbox_priorbox/unsqueeze/value156602158495211511" type="Const" version="opset1">
			<data element_type="i64" offset="1100286" shape="1" size="8"/>
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="1103" name="conv9_2_mbox_priorbox" type="Unsqueeze" version="opset1">
			<input>
				<port id="0">
					<dim>2</dim>
					<dim>32</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" names="conv9_2_mbox_priorbox" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="1104" name="mbox_priorbox" type="Concat" version="opset1">
			<data axis="2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>16128</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>2</dim>
					<dim>16128</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>2</dim>
					<dim>6048</dim>
				</port>
				<port id="3">
					<dim>1</dim>
					<dim>2</dim>
					<dim>1584</dim>
				</port>
				<port id="4">
					<dim>1</dim>
					<dim>2</dim>
					<dim>432</dim>
				</port>
				<port id="5">
					<dim>1</dim>
					<dim>2</dim>
					<dim>96</dim>
				</port>
				<port id="6">
					<dim>1</dim>
					<dim>2</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="7" names="mbox_priorbox" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>40448</dim>
				</port>
			</output>
		</layer>
		<layer id="1105" name="detection_out" type="DetectionOutput" version="opset1">
			<data background_label_id="0" clip_after_nms="false" clip_before_nms="false" code_type="caffe.PriorBoxParameter.CENTER_SIZE" confidence_threshold="0.009999999776482582" decrease_label_id="false" input_height="1" input_width="1" keep_top_k="200" nms_threshold="0.44999998807907104" normalized="true" num_classes="2" objectness_score="0" share_location="true" top_k="400" variance_encoded_in_target="false"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>40448</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>20224</dim>
				</port>
				<port id="2">
					<dim>1</dim>
					<dim>2</dim>
					<dim>40448</dim>
				</port>
			</input>
			<output>
				<port id="3" names="detection_out" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>200</dim>
					<dim>7</dim>
				</port>
			</output>
		</layer>
		<layer id="1106" name="detection_out/sink_port_0" type="Result" version="opset1">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>200</dim>
					<dim>7</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="96" from-port="0" to-layer="98" to-port="0"/>
		<edge from-layer="97" from-port="0" to-layer="98" to-port="1"/>
		<edge from-layer="98" from-port="2" to-layer="100" to-port="0"/>
		<edge from-layer="99" from-port="0" to-layer="100" to-port="1"/>
		<edge from-layer="100" from-port="2" to-layer="101" to-port="0"/>
		<edge from-layer="92" from-port="0" to-layer="101" to-port="1"/>
		<edge from-layer="93" from-port="0" to-layer="101" to-port="2"/>
		<edge from-layer="94" from-port="0" to-layer="101" to-port="3"/>
		<edge from-layer="95" from-port="0" to-layer="101" to-port="4"/>
		<edge from-layer="102" from-port="0" to-layer="103" to-port="0"/>
		<edge from-layer="103" from-port="1" to-layer="105" to-port="0"/>
		<edge from-layer="104" from-port="0" to-layer="105" to-port="1"/>
		<edge from-layer="105" from-port="2" to-layer="107" to-port="0"/>
		<edge from-layer="106" from-port="0" to-layer="107" to-port="1"/>
		<edge from-layer="101" from-port="5" to-layer="108" to-port="0"/>
		<edge from-layer="107" from-port="2" to-layer="108" to-port="1"/>
		<edge from-layer="108" from-port="2" to-layer="110" to-port="0"/>
		<edge from-layer="109" from-port="0" to-layer="110" to-port="1"/>
		<edge from-layer="110" from-port="2" to-layer="111" to-port="0"/>
		<edge from-layer="111" from-port="1" to-layer="112" to-port="0"/>
		<edge from-layer="88" from-port="0" to-layer="112" to-port="1"/>
		<edge from-layer="89" from-port="0" to-layer="112" to-port="2"/>
		<edge from-layer="90" from-port="0" to-layer="112" to-port="3"/>
		<edge from-layer="91" from-port="0" to-layer="112" to-port="4"/>
		<edge from-layer="114" from-port="0" to-layer="115" to-port="0"/>
		<edge from-layer="115" from-port="1" to-layer="117" to-port="0"/>
		<edge from-layer="116" from-port="0" to-layer="117" to-port="1"/>
		<edge from-layer="117" from-port="2" to-layer="119" to-port="0"/>
		<edge from-layer="118" from-port="0" to-layer="119" to-port="1"/>
		<edge from-layer="119" from-port="2" to-layer="120" to-port="0"/>
		<edge from-layer="113" from-port="0" to-layer="120" to-port="1"/>
		<edge from-layer="112" from-port="5" to-layer="121" to-port="0"/>
		<edge from-layer="120" from-port="2" to-layer="121" to-port="1"/>
		<edge from-layer="121" from-port="2" to-layer="123" to-port="0"/>
		<edge from-layer="122" from-port="0" to-layer="123" to-port="1"/>
		<edge from-layer="123" from-port="2" to-layer="124" to-port="0"/>
		<edge from-layer="124" from-port="1" to-layer="125" to-port="0"/>
		<edge from-layer="84" from-port="0" to-layer="125" to-port="1"/>
		<edge from-layer="85" from-port="0" to-layer="125" to-port="2"/>
		<edge from-layer="86" from-port="0" to-layer="125" to-port="3"/>
		<edge from-layer="87" from-port="0" to-layer="125" to-port="4"/>
		<edge from-layer="126" from-port="0" to-layer="127" to-port="0"/>
		<edge from-layer="127" from-port="1" to-layer="129" to-port="0"/>
		<edge from-layer="128" from-port="0" to-layer="129" to-port="1"/>
		<edge from-layer="129" from-port="2" to-layer="131" to-port="0"/>
		<edge from-layer="130" from-port="0" to-layer="131" to-port="1"/>
		<edge from-layer="125" from-port="5" to-layer="132" to-port="0"/>
		<edge from-layer="131" from-port="2" to-layer="132" to-port="1"/>
		<edge from-layer="132" from-port="2" to-layer="134" to-port="0"/>
		<edge from-layer="133" from-port="0" to-layer="134" to-port="1"/>
		<edge from-layer="134" from-port="2" to-layer="135" to-port="0"/>
		<edge from-layer="135" from-port="1" to-layer="136" to-port="0"/>
		<edge from-layer="80" from-port="0" to-layer="136" to-port="1"/>
		<edge from-layer="81" from-port="0" to-layer="136" to-port="2"/>
		<edge from-layer="82" from-port="0" to-layer="136" to-port="3"/>
		<edge from-layer="83" from-port="0" to-layer="136" to-port="4"/>
		<edge from-layer="138" from-port="0" to-layer="139" to-port="0"/>
		<edge from-layer="139" from-port="1" to-layer="141" to-port="0"/>
		<edge from-layer="140" from-port="0" to-layer="141" to-port="1"/>
		<edge from-layer="141" from-port="2" to-layer="143" to-port="0"/>
		<edge from-layer="142" from-port="0" to-layer="143" to-port="1"/>
		<edge from-layer="143" from-port="2" to-layer="144" to-port="0"/>
		<edge from-layer="137" from-port="0" to-layer="144" to-port="1"/>
		<edge from-layer="136" from-port="5" to-layer="145" to-port="0"/>
		<edge from-layer="144" from-port="2" to-layer="145" to-port="1"/>
		<edge from-layer="145" from-port="2" to-layer="147" to-port="0"/>
		<edge from-layer="146" from-port="0" to-layer="147" to-port="1"/>
		<edge from-layer="147" from-port="2" to-layer="148" to-port="0"/>
		<edge from-layer="148" from-port="1" to-layer="149" to-port="0"/>
		<edge from-layer="76" from-port="0" to-layer="149" to-port="1"/>
		<edge from-layer="77" from-port="0" to-layer="149" to-port="2"/>
		<edge from-layer="78" from-port="0" to-layer="149" to-port="3"/>
		<edge from-layer="79" from-port="0" to-layer="149" to-port="4"/>
		<edge from-layer="150" from-port="0" to-layer="151" to-port="0"/>
		<edge from-layer="151" from-port="1" to-layer="153" to-port="0"/>
		<edge from-layer="152" from-port="0" to-layer="153" to-port="1"/>
		<edge from-layer="153" from-port="2" to-layer="155" to-port="0"/>
		<edge from-layer="154" from-port="0" to-layer="155" to-port="1"/>
		<edge from-layer="149" from-port="5" to-layer="156" to-port="0"/>
		<edge from-layer="155" from-port="2" to-layer="156" to-port="1"/>
		<edge from-layer="156" from-port="2" to-layer="158" to-port="0"/>
		<edge from-layer="157" from-port="0" to-layer="158" to-port="1"/>
		<edge from-layer="158" from-port="2" to-layer="159" to-port="0"/>
		<edge from-layer="159" from-port="1" to-layer="160" to-port="0"/>
		<edge from-layer="72" from-port="0" to-layer="160" to-port="1"/>
		<edge from-layer="73" from-port="0" to-layer="160" to-port="2"/>
		<edge from-layer="74" from-port="0" to-layer="160" to-port="3"/>
		<edge from-layer="75" from-port="0" to-layer="160" to-port="4"/>
		<edge from-layer="162" from-port="0" to-layer="163" to-port="0"/>
		<edge from-layer="163" from-port="1" to-layer="165" to-port="0"/>
		<edge from-layer="164" from-port="0" to-layer="165" to-port="1"/>
		<edge from-layer="165" from-port="2" to-layer="167" to-port="0"/>
		<edge from-layer="166" from-port="0" to-layer="167" to-port="1"/>
		<edge from-layer="167" from-port="2" to-layer="168" to-port="0"/>
		<edge from-layer="161" from-port="0" to-layer="168" to-port="1"/>
		<edge from-layer="160" from-port="5" to-layer="169" to-port="0"/>
		<edge from-layer="168" from-port="2" to-layer="169" to-port="1"/>
		<edge from-layer="169" from-port="2" to-layer="171" to-port="0"/>
		<edge from-layer="170" from-port="0" to-layer="171" to-port="1"/>
		<edge from-layer="171" from-port="2" to-layer="172" to-port="0"/>
		<edge from-layer="172" from-port="1" to-layer="173" to-port="0"/>
		<edge from-layer="68" from-port="0" to-layer="173" to-port="1"/>
		<edge from-layer="69" from-port="0" to-layer="173" to-port="2"/>
		<edge from-layer="70" from-port="0" to-layer="173" to-port="3"/>
		<edge from-layer="71" from-port="0" to-layer="173" to-port="4"/>
		<edge from-layer="174" from-port="0" to-layer="175" to-port="0"/>
		<edge from-layer="175" from-port="1" to-layer="177" to-port="0"/>
		<edge from-layer="176" from-port="0" to-layer="177" to-port="1"/>
		<edge from-layer="177" from-port="2" to-layer="179" to-port="0"/>
		<edge from-layer="178" from-port="0" to-layer="179" to-port="1"/>
		<edge from-layer="173" from-port="5" to-layer="180" to-port="0"/>
		<edge from-layer="179" from-port="2" to-layer="180" to-port="1"/>
		<edge from-layer="180" from-port="2" to-layer="182" to-port="0"/>
		<edge from-layer="181" from-port="0" to-layer="182" to-port="1"/>
		<edge from-layer="182" from-port="2" to-layer="183" to-port="0"/>
		<edge from-layer="183" from-port="1" to-layer="184" to-port="0"/>
		<edge from-layer="64" from-port="0" to-layer="184" to-port="1"/>
		<edge from-layer="65" from-port="0" to-layer="184" to-port="2"/>
		<edge from-layer="66" from-port="0" to-layer="184" to-port="3"/>
		<edge from-layer="67" from-port="0" to-layer="184" to-port="4"/>
		<edge from-layer="186" from-port="0" to-layer="187" to-port="0"/>
		<edge from-layer="187" from-port="1" to-layer="189" to-port="0"/>
		<edge from-layer="188" from-port="0" to-layer="189" to-port="1"/>
		<edge from-layer="189" from-port="2" to-layer="191" to-port="0"/>
		<edge from-layer="190" from-port="0" to-layer="191" to-port="1"/>
		<edge from-layer="191" from-port="2" to-layer="192" to-port="0"/>
		<edge from-layer="185" from-port="0" to-layer="192" to-port="1"/>
		<edge from-layer="184" from-port="5" to-layer="193" to-port="0"/>
		<edge from-layer="192" from-port="2" to-layer="193" to-port="1"/>
		<edge from-layer="193" from-port="2" to-layer="195" to-port="0"/>
		<edge from-layer="194" from-port="0" to-layer="195" to-port="1"/>
		<edge from-layer="195" from-port="2" to-layer="196" to-port="0"/>
		<edge from-layer="196" from-port="1" to-layer="197" to-port="0"/>
		<edge from-layer="60" from-port="0" to-layer="197" to-port="1"/>
		<edge from-layer="61" from-port="0" to-layer="197" to-port="2"/>
		<edge from-layer="62" from-port="0" to-layer="197" to-port="3"/>
		<edge from-layer="63" from-port="0" to-layer="197" to-port="4"/>
		<edge from-layer="198" from-port="0" to-layer="199" to-port="0"/>
		<edge from-layer="199" from-port="1" to-layer="201" to-port="0"/>
		<edge from-layer="200" from-port="0" to-layer="201" to-port="1"/>
		<edge from-layer="201" from-port="2" to-layer="203" to-port="0"/>
		<edge from-layer="202" from-port="0" to-layer="203" to-port="1"/>
		<edge from-layer="197" from-port="5" to-layer="204" to-port="0"/>
		<edge from-layer="203" from-port="2" to-layer="204" to-port="1"/>
		<edge from-layer="204" from-port="2" to-layer="206" to-port="0"/>
		<edge from-layer="205" from-port="0" to-layer="206" to-port="1"/>
		<edge from-layer="206" from-port="2" to-layer="207" to-port="0"/>
		<edge from-layer="207" from-port="1" to-layer="208" to-port="0"/>
		<edge from-layer="56" from-port="0" to-layer="208" to-port="1"/>
		<edge from-layer="57" from-port="0" to-layer="208" to-port="2"/>
		<edge from-layer="58" from-port="0" to-layer="208" to-port="3"/>
		<edge from-layer="59" from-port="0" to-layer="208" to-port="4"/>
		<edge from-layer="210" from-port="0" to-layer="211" to-port="0"/>
		<edge from-layer="211" from-port="1" to-layer="213" to-port="0"/>
		<edge from-layer="212" from-port="0" to-layer="213" to-port="1"/>
		<edge from-layer="213" from-port="2" to-layer="215" to-port="0"/>
		<edge from-layer="214" from-port="0" to-layer="215" to-port="1"/>
		<edge from-layer="215" from-port="2" to-layer="216" to-port="0"/>
		<edge from-layer="209" from-port="0" to-layer="216" to-port="1"/>
		<edge from-layer="208" from-port="5" to-layer="217" to-port="0"/>
		<edge from-layer="216" from-port="2" to-layer="217" to-port="1"/>
		<edge from-layer="217" from-port="2" to-layer="219" to-port="0"/>
		<edge from-layer="218" from-port="0" to-layer="219" to-port="1"/>
		<edge from-layer="219" from-port="2" to-layer="220" to-port="0"/>
		<edge from-layer="220" from-port="1" to-layer="221" to-port="0"/>
		<edge from-layer="52" from-port="0" to-layer="221" to-port="1"/>
		<edge from-layer="53" from-port="0" to-layer="221" to-port="2"/>
		<edge from-layer="54" from-port="0" to-layer="221" to-port="3"/>
		<edge from-layer="55" from-port="0" to-layer="221" to-port="4"/>
		<edge from-layer="222" from-port="0" to-layer="223" to-port="0"/>
		<edge from-layer="223" from-port="1" to-layer="225" to-port="0"/>
		<edge from-layer="224" from-port="0" to-layer="225" to-port="1"/>
		<edge from-layer="225" from-port="2" to-layer="227" to-port="0"/>
		<edge from-layer="226" from-port="0" to-layer="227" to-port="1"/>
		<edge from-layer="221" from-port="5" to-layer="228" to-port="0"/>
		<edge from-layer="227" from-port="2" to-layer="228" to-port="1"/>
		<edge from-layer="228" from-port="2" to-layer="230" to-port="0"/>
		<edge from-layer="229" from-port="0" to-layer="230" to-port="1"/>
		<edge from-layer="230" from-port="2" to-layer="231" to-port="0"/>
		<edge from-layer="231" from-port="1" to-layer="232" to-port="0"/>
		<edge from-layer="48" from-port="0" to-layer="232" to-port="1"/>
		<edge from-layer="49" from-port="0" to-layer="232" to-port="2"/>
		<edge from-layer="50" from-port="0" to-layer="232" to-port="3"/>
		<edge from-layer="51" from-port="0" to-layer="232" to-port="4"/>
		<edge from-layer="234" from-port="0" to-layer="235" to-port="0"/>
		<edge from-layer="235" from-port="1" to-layer="237" to-port="0"/>
		<edge from-layer="236" from-port="0" to-layer="237" to-port="1"/>
		<edge from-layer="237" from-port="2" to-layer="239" to-port="0"/>
		<edge from-layer="238" from-port="0" to-layer="239" to-port="1"/>
		<edge from-layer="239" from-port="2" to-layer="240" to-port="0"/>
		<edge from-layer="233" from-port="0" to-layer="240" to-port="1"/>
		<edge from-layer="232" from-port="5" to-layer="241" to-port="0"/>
		<edge from-layer="240" from-port="2" to-layer="241" to-port="1"/>
		<edge from-layer="241" from-port="2" to-layer="243" to-port="0"/>
		<edge from-layer="242" from-port="0" to-layer="243" to-port="1"/>
		<edge from-layer="243" from-port="2" to-layer="244" to-port="0"/>
		<edge from-layer="244" from-port="1" to-layer="245" to-port="0"/>
		<edge from-layer="44" from-port="0" to-layer="245" to-port="1"/>
		<edge from-layer="45" from-port="0" to-layer="245" to-port="2"/>
		<edge from-layer="46" from-port="0" to-layer="245" to-port="3"/>
		<edge from-layer="47" from-port="0" to-layer="245" to-port="4"/>
		<edge from-layer="246" from-port="0" to-layer="247" to-port="0"/>
		<edge from-layer="247" from-port="1" to-layer="249" to-port="0"/>
		<edge from-layer="248" from-port="0" to-layer="249" to-port="1"/>
		<edge from-layer="249" from-port="2" to-layer="251" to-port="0"/>
		<edge from-layer="250" from-port="0" to-layer="251" to-port="1"/>
		<edge from-layer="245" from-port="5" to-layer="252" to-port="0"/>
		<edge from-layer="251" from-port="2" to-layer="252" to-port="1"/>
		<edge from-layer="252" from-port="2" to-layer="254" to-port="0"/>
		<edge from-layer="253" from-port="0" to-layer="254" to-port="1"/>
		<edge from-layer="254" from-port="2" to-layer="255" to-port="0"/>
		<edge from-layer="255" from-port="1" to-layer="256" to-port="0"/>
		<edge from-layer="40" from-port="0" to-layer="256" to-port="1"/>
		<edge from-layer="41" from-port="0" to-layer="256" to-port="2"/>
		<edge from-layer="42" from-port="0" to-layer="256" to-port="3"/>
		<edge from-layer="43" from-port="0" to-layer="256" to-port="4"/>
		<edge from-layer="258" from-port="0" to-layer="259" to-port="0"/>
		<edge from-layer="259" from-port="1" to-layer="261" to-port="0"/>
		<edge from-layer="260" from-port="0" to-layer="261" to-port="1"/>
		<edge from-layer="261" from-port="2" to-layer="263" to-port="0"/>
		<edge from-layer="262" from-port="0" to-layer="263" to-port="1"/>
		<edge from-layer="263" from-port="2" to-layer="264" to-port="0"/>
		<edge from-layer="257" from-port="0" to-layer="264" to-port="1"/>
		<edge from-layer="256" from-port="5" to-layer="265" to-port="0"/>
		<edge from-layer="264" from-port="2" to-layer="265" to-port="1"/>
		<edge from-layer="265" from-port="2" to-layer="267" to-port="0"/>
		<edge from-layer="266" from-port="0" to-layer="267" to-port="1"/>
		<edge from-layer="267" from-port="2" to-layer="268" to-port="0"/>
		<edge from-layer="268" from-port="1" to-layer="269" to-port="0"/>
		<edge from-layer="36" from-port="0" to-layer="269" to-port="1"/>
		<edge from-layer="37" from-port="0" to-layer="269" to-port="2"/>
		<edge from-layer="38" from-port="0" to-layer="269" to-port="3"/>
		<edge from-layer="39" from-port="0" to-layer="269" to-port="4"/>
		<edge from-layer="270" from-port="0" to-layer="271" to-port="0"/>
		<edge from-layer="271" from-port="1" to-layer="273" to-port="0"/>
		<edge from-layer="272" from-port="0" to-layer="273" to-port="1"/>
		<edge from-layer="273" from-port="2" to-layer="275" to-port="0"/>
		<edge from-layer="274" from-port="0" to-layer="275" to-port="1"/>
		<edge from-layer="269" from-port="5" to-layer="276" to-port="0"/>
		<edge from-layer="275" from-port="2" to-layer="276" to-port="1"/>
		<edge from-layer="276" from-port="2" to-layer="278" to-port="0"/>
		<edge from-layer="277" from-port="0" to-layer="278" to-port="1"/>
		<edge from-layer="278" from-port="2" to-layer="279" to-port="0"/>
		<edge from-layer="279" from-port="1" to-layer="280" to-port="0"/>
		<edge from-layer="32" from-port="0" to-layer="280" to-port="1"/>
		<edge from-layer="33" from-port="0" to-layer="280" to-port="2"/>
		<edge from-layer="34" from-port="0" to-layer="280" to-port="3"/>
		<edge from-layer="35" from-port="0" to-layer="280" to-port="4"/>
		<edge from-layer="282" from-port="0" to-layer="283" to-port="0"/>
		<edge from-layer="283" from-port="1" to-layer="285" to-port="0"/>
		<edge from-layer="284" from-port="0" to-layer="285" to-port="1"/>
		<edge from-layer="285" from-port="2" to-layer="287" to-port="0"/>
		<edge from-layer="286" from-port="0" to-layer="287" to-port="1"/>
		<edge from-layer="287" from-port="2" to-layer="288" to-port="0"/>
		<edge from-layer="281" from-port="0" to-layer="288" to-port="1"/>
		<edge from-layer="280" from-port="5" to-layer="289" to-port="0"/>
		<edge from-layer="288" from-port="2" to-layer="289" to-port="1"/>
		<edge from-layer="289" from-port="2" to-layer="291" to-port="0"/>
		<edge from-layer="290" from-port="0" to-layer="291" to-port="1"/>
		<edge from-layer="291" from-port="2" to-layer="292" to-port="0"/>
		<edge from-layer="292" from-port="1" to-layer="293" to-port="0"/>
		<edge from-layer="28" from-port="0" to-layer="293" to-port="1"/>
		<edge from-layer="29" from-port="0" to-layer="293" to-port="2"/>
		<edge from-layer="30" from-port="0" to-layer="293" to-port="3"/>
		<edge from-layer="31" from-port="0" to-layer="293" to-port="4"/>
		<edge from-layer="294" from-port="0" to-layer="295" to-port="0"/>
		<edge from-layer="295" from-port="1" to-layer="297" to-port="0"/>
		<edge from-layer="296" from-port="0" to-layer="297" to-port="1"/>
		<edge from-layer="297" from-port="2" to-layer="299" to-port="0"/>
		<edge from-layer="298" from-port="0" to-layer="299" to-port="1"/>
		<edge from-layer="293" from-port="5" to-layer="300" to-port="0"/>
		<edge from-layer="299" from-port="2" to-layer="300" to-port="1"/>
		<edge from-layer="300" from-port="2" to-layer="302" to-port="0"/>
		<edge from-layer="301" from-port="0" to-layer="302" to-port="1"/>
		<edge from-layer="302" from-port="2" to-layer="303" to-port="0"/>
		<edge from-layer="303" from-port="1" to-layer="304" to-port="0"/>
		<edge from-layer="24" from-port="0" to-layer="304" to-port="1"/>
		<edge from-layer="25" from-port="0" to-layer="304" to-port="2"/>
		<edge from-layer="26" from-port="0" to-layer="304" to-port="3"/>
		<edge from-layer="27" from-port="0" to-layer="304" to-port="4"/>
		<edge from-layer="306" from-port="0" to-layer="307" to-port="0"/>
		<edge from-layer="307" from-port="1" to-layer="309" to-port="0"/>
		<edge from-layer="308" from-port="0" to-layer="309" to-port="1"/>
		<edge from-layer="309" from-port="2" to-layer="311" to-port="0"/>
		<edge from-layer="310" from-port="0" to-layer="311" to-port="1"/>
		<edge from-layer="311" from-port="2" to-layer="312" to-port="0"/>
		<edge from-layer="305" from-port="0" to-layer="312" to-port="1"/>
		<edge from-layer="304" from-port="5" to-layer="313" to-port="0"/>
		<edge from-layer="312" from-port="2" to-layer="313" to-port="1"/>
		<edge from-layer="313" from-port="2" to-layer="315" to-port="0"/>
		<edge from-layer="314" from-port="0" to-layer="315" to-port="1"/>
		<edge from-layer="315" from-port="2" to-layer="316" to-port="0"/>
		<edge from-layer="316" from-port="1" to-layer="317" to-port="0"/>
		<edge from-layer="20" from-port="0" to-layer="317" to-port="1"/>
		<edge from-layer="21" from-port="0" to-layer="317" to-port="2"/>
		<edge from-layer="22" from-port="0" to-layer="317" to-port="3"/>
		<edge from-layer="23" from-port="0" to-layer="317" to-port="4"/>
		<edge from-layer="318" from-port="0" to-layer="319" to-port="0"/>
		<edge from-layer="319" from-port="1" to-layer="321" to-port="0"/>
		<edge from-layer="320" from-port="0" to-layer="321" to-port="1"/>
		<edge from-layer="321" from-port="2" to-layer="323" to-port="0"/>
		<edge from-layer="322" from-port="0" to-layer="323" to-port="1"/>
		<edge from-layer="317" from-port="5" to-layer="324" to-port="0"/>
		<edge from-layer="323" from-port="2" to-layer="324" to-port="1"/>
		<edge from-layer="324" from-port="2" to-layer="326" to-port="0"/>
		<edge from-layer="325" from-port="0" to-layer="326" to-port="1"/>
		<edge from-layer="326" from-port="2" to-layer="327" to-port="0"/>
		<edge from-layer="327" from-port="1" to-layer="328" to-port="0"/>
		<edge from-layer="16" from-port="0" to-layer="328" to-port="1"/>
		<edge from-layer="17" from-port="0" to-layer="328" to-port="2"/>
		<edge from-layer="18" from-port="0" to-layer="328" to-port="3"/>
		<edge from-layer="19" from-port="0" to-layer="328" to-port="4"/>
		<edge from-layer="330" from-port="0" to-layer="331" to-port="0"/>
		<edge from-layer="331" from-port="1" to-layer="333" to-port="0"/>
		<edge from-layer="332" from-port="0" to-layer="333" to-port="1"/>
		<edge from-layer="333" from-port="2" to-layer="335" to-port="0"/>
		<edge from-layer="334" from-port="0" to-layer="335" to-port="1"/>
		<edge from-layer="335" from-port="2" to-layer="336" to-port="0"/>
		<edge from-layer="329" from-port="0" to-layer="336" to-port="1"/>
		<edge from-layer="328" from-port="5" to-layer="337" to-port="0"/>
		<edge from-layer="336" from-port="2" to-layer="337" to-port="1"/>
		<edge from-layer="337" from-port="2" to-layer="339" to-port="0"/>
		<edge from-layer="338" from-port="0" to-layer="339" to-port="1"/>
		<edge from-layer="339" from-port="2" to-layer="340" to-port="0"/>
		<edge from-layer="340" from-port="1" to-layer="341" to-port="0"/>
		<edge from-layer="12" from-port="0" to-layer="341" to-port="1"/>
		<edge from-layer="13" from-port="0" to-layer="341" to-port="2"/>
		<edge from-layer="14" from-port="0" to-layer="341" to-port="3"/>
		<edge from-layer="15" from-port="0" to-layer="341" to-port="4"/>
		<edge from-layer="342" from-port="0" to-layer="343" to-port="0"/>
		<edge from-layer="343" from-port="1" to-layer="345" to-port="0"/>
		<edge from-layer="344" from-port="0" to-layer="345" to-port="1"/>
		<edge from-layer="345" from-port="2" to-layer="347" to-port="0"/>
		<edge from-layer="346" from-port="0" to-layer="347" to-port="1"/>
		<edge from-layer="341" from-port="5" to-layer="348" to-port="0"/>
		<edge from-layer="347" from-port="2" to-layer="348" to-port="1"/>
		<edge from-layer="348" from-port="2" to-layer="350" to-port="0"/>
		<edge from-layer="349" from-port="0" to-layer="350" to-port="1"/>
		<edge from-layer="350" from-port="2" to-layer="351" to-port="0"/>
		<edge from-layer="351" from-port="1" to-layer="352" to-port="0"/>
		<edge from-layer="8" from-port="0" to-layer="352" to-port="1"/>
		<edge from-layer="9" from-port="0" to-layer="352" to-port="2"/>
		<edge from-layer="10" from-port="0" to-layer="352" to-port="3"/>
		<edge from-layer="11" from-port="0" to-layer="352" to-port="4"/>
		<edge from-layer="354" from-port="0" to-layer="355" to-port="0"/>
		<edge from-layer="355" from-port="1" to-layer="357" to-port="0"/>
		<edge from-layer="356" from-port="0" to-layer="357" to-port="1"/>
		<edge from-layer="357" from-port="2" to-layer="359" to-port="0"/>
		<edge from-layer="358" from-port="0" to-layer="359" to-port="1"/>
		<edge from-layer="359" from-port="2" to-layer="360" to-port="0"/>
		<edge from-layer="353" from-port="0" to-layer="360" to-port="1"/>
		<edge from-layer="352" from-port="5" to-layer="361" to-port="0"/>
		<edge from-layer="360" from-port="2" to-layer="361" to-port="1"/>
		<edge from-layer="361" from-port="2" to-layer="363" to-port="0"/>
		<edge from-layer="362" from-port="0" to-layer="363" to-port="1"/>
		<edge from-layer="363" from-port="2" to-layer="364" to-port="0"/>
		<edge from-layer="364" from-port="1" to-layer="365" to-port="0"/>
		<edge from-layer="4" from-port="0" to-layer="365" to-port="1"/>
		<edge from-layer="5" from-port="0" to-layer="365" to-port="2"/>
		<edge from-layer="6" from-port="0" to-layer="365" to-port="3"/>
		<edge from-layer="7" from-port="0" to-layer="365" to-port="4"/>
		<edge from-layer="366" from-port="0" to-layer="367" to-port="0"/>
		<edge from-layer="367" from-port="1" to-layer="369" to-port="0"/>
		<edge from-layer="368" from-port="0" to-layer="369" to-port="1"/>
		<edge from-layer="369" from-port="2" to-layer="371" to-port="0"/>
		<edge from-layer="370" from-port="0" to-layer="371" to-port="1"/>
		<edge from-layer="365" from-port="5" to-layer="372" to-port="0"/>
		<edge from-layer="371" from-port="2" to-layer="372" to-port="1"/>
		<edge from-layer="372" from-port="2" to-layer="374" to-port="0"/>
		<edge from-layer="373" from-port="0" to-layer="374" to-port="1"/>
		<edge from-layer="374" from-port="2" to-layer="375" to-port="0"/>
		<edge from-layer="375" from-port="1" to-layer="376" to-port="0"/>
		<edge from-layer="0" from-port="0" to-layer="376" to-port="1"/>
		<edge from-layer="1" from-port="0" to-layer="376" to-port="2"/>
		<edge from-layer="2" from-port="0" to-layer="376" to-port="3"/>
		<edge from-layer="3" from-port="0" to-layer="376" to-port="4"/>
		<edge from-layer="377" from-port="0" to-layer="378" to-port="0"/>
		<edge from-layer="378" from-port="1" to-layer="380" to-port="0"/>
		<edge from-layer="379" from-port="0" to-layer="380" to-port="1"/>
		<edge from-layer="380" from-port="2" to-layer="382" to-port="0"/>
		<edge from-layer="381" from-port="0" to-layer="382" to-port="1"/>
		<edge from-layer="376" from-port="5" to-layer="383" to-port="0"/>
		<edge from-layer="382" from-port="2" to-layer="383" to-port="1"/>
		<edge from-layer="383" from-port="2" to-layer="385" to-port="0"/>
		<edge from-layer="384" from-port="0" to-layer="385" to-port="1"/>
		<edge from-layer="385" from-port="2" to-layer="387" to-port="0"/>
		<edge from-layer="386" from-port="0" to-layer="387" to-port="1"/>
		<edge from-layer="387" from-port="2" to-layer="389" to-port="0"/>
		<edge from-layer="388" from-port="0" to-layer="389" to-port="1"/>
		<edge from-layer="390" from-port="0" to-layer="391" to-port="0"/>
		<edge from-layer="391" from-port="1" to-layer="393" to-port="0"/>
		<edge from-layer="392" from-port="0" to-layer="393" to-port="1"/>
		<edge from-layer="393" from-port="2" to-layer="395" to-port="0"/>
		<edge from-layer="394" from-port="0" to-layer="395" to-port="1"/>
		<edge from-layer="376" from-port="5" to-layer="396" to-port="0"/>
		<edge from-layer="395" from-port="2" to-layer="396" to-port="1"/>
		<edge from-layer="396" from-port="2" to-layer="398" to-port="0"/>
		<edge from-layer="397" from-port="0" to-layer="398" to-port="1"/>
		<edge from-layer="398" from-port="2" to-layer="400" to-port="0"/>
		<edge from-layer="399" from-port="0" to-layer="400" to-port="1"/>
		<edge from-layer="400" from-port="2" to-layer="402" to-port="0"/>
		<edge from-layer="401" from-port="0" to-layer="402" to-port="1"/>
		<edge from-layer="420" from-port="0" to-layer="421" to-port="0"/>
		<edge from-layer="421" from-port="1" to-layer="423" to-port="0"/>
		<edge from-layer="422" from-port="0" to-layer="423" to-port="1"/>
		<edge from-layer="423" from-port="2" to-layer="425" to-port="0"/>
		<edge from-layer="424" from-port="0" to-layer="425" to-port="1"/>
		<edge from-layer="425" from-port="2" to-layer="426" to-port="0"/>
		<edge from-layer="419" from-port="0" to-layer="426" to-port="1"/>
		<edge from-layer="376" from-port="5" to-layer="427" to-port="0"/>
		<edge from-layer="426" from-port="2" to-layer="427" to-port="1"/>
		<edge from-layer="427" from-port="2" to-layer="429" to-port="0"/>
		<edge from-layer="428" from-port="0" to-layer="429" to-port="1"/>
		<edge from-layer="429" from-port="2" to-layer="430" to-port="0"/>
		<edge from-layer="430" from-port="1" to-layer="431" to-port="0"/>
		<edge from-layer="415" from-port="0" to-layer="431" to-port="1"/>
		<edge from-layer="416" from-port="0" to-layer="431" to-port="2"/>
		<edge from-layer="417" from-port="0" to-layer="431" to-port="3"/>
		<edge from-layer="418" from-port="0" to-layer="431" to-port="4"/>
		<edge from-layer="432" from-port="0" to-layer="433" to-port="0"/>
		<edge from-layer="433" from-port="1" to-layer="435" to-port="0"/>
		<edge from-layer="434" from-port="0" to-layer="435" to-port="1"/>
		<edge from-layer="435" from-port="2" to-layer="437" to-port="0"/>
		<edge from-layer="436" from-port="0" to-layer="437" to-port="1"/>
		<edge from-layer="431" from-port="5" to-layer="438" to-port="0"/>
		<edge from-layer="437" from-port="2" to-layer="438" to-port="1"/>
		<edge from-layer="438" from-port="2" to-layer="440" to-port="0"/>
		<edge from-layer="439" from-port="0" to-layer="440" to-port="1"/>
		<edge from-layer="440" from-port="2" to-layer="441" to-port="0"/>
		<edge from-layer="441" from-port="1" to-layer="442" to-port="0"/>
		<edge from-layer="411" from-port="0" to-layer="442" to-port="1"/>
		<edge from-layer="412" from-port="0" to-layer="442" to-port="2"/>
		<edge from-layer="413" from-port="0" to-layer="442" to-port="3"/>
		<edge from-layer="414" from-port="0" to-layer="442" to-port="4"/>
		<edge from-layer="444" from-port="0" to-layer="445" to-port="0"/>
		<edge from-layer="445" from-port="1" to-layer="447" to-port="0"/>
		<edge from-layer="446" from-port="0" to-layer="447" to-port="1"/>
		<edge from-layer="447" from-port="2" to-layer="449" to-port="0"/>
		<edge from-layer="448" from-port="0" to-layer="449" to-port="1"/>
		<edge from-layer="449" from-port="2" to-layer="450" to-port="0"/>
		<edge from-layer="443" from-port="0" to-layer="450" to-port="1"/>
		<edge from-layer="442" from-port="5" to-layer="451" to-port="0"/>
		<edge from-layer="450" from-port="2" to-layer="451" to-port="1"/>
		<edge from-layer="451" from-port="2" to-layer="453" to-port="0"/>
		<edge from-layer="452" from-port="0" to-layer="453" to-port="1"/>
		<edge from-layer="453" from-port="2" to-layer="454" to-port="0"/>
		<edge from-layer="454" from-port="1" to-layer="455" to-port="0"/>
		<edge from-layer="407" from-port="0" to-layer="455" to-port="1"/>
		<edge from-layer="408" from-port="0" to-layer="455" to-port="2"/>
		<edge from-layer="409" from-port="0" to-layer="455" to-port="3"/>
		<edge from-layer="410" from-port="0" to-layer="455" to-port="4"/>
		<edge from-layer="456" from-port="0" to-layer="457" to-port="0"/>
		<edge from-layer="457" from-port="1" to-layer="459" to-port="0"/>
		<edge from-layer="458" from-port="0" to-layer="459" to-port="1"/>
		<edge from-layer="459" from-port="2" to-layer="461" to-port="0"/>
		<edge from-layer="460" from-port="0" to-layer="461" to-port="1"/>
		<edge from-layer="455" from-port="5" to-layer="462" to-port="0"/>
		<edge from-layer="461" from-port="2" to-layer="462" to-port="1"/>
		<edge from-layer="462" from-port="2" to-layer="464" to-port="0"/>
		<edge from-layer="463" from-port="0" to-layer="464" to-port="1"/>
		<edge from-layer="464" from-port="2" to-layer="465" to-port="0"/>
		<edge from-layer="465" from-port="1" to-layer="466" to-port="0"/>
		<edge from-layer="403" from-port="0" to-layer="466" to-port="1"/>
		<edge from-layer="404" from-port="0" to-layer="466" to-port="2"/>
		<edge from-layer="405" from-port="0" to-layer="466" to-port="3"/>
		<edge from-layer="406" from-port="0" to-layer="466" to-port="4"/>
		<edge from-layer="467" from-port="0" to-layer="468" to-port="0"/>
		<edge from-layer="468" from-port="1" to-layer="470" to-port="0"/>
		<edge from-layer="469" from-port="0" to-layer="470" to-port="1"/>
		<edge from-layer="470" from-port="2" to-layer="472" to-port="0"/>
		<edge from-layer="471" from-port="0" to-layer="472" to-port="1"/>
		<edge from-layer="466" from-port="5" to-layer="473" to-port="0"/>
		<edge from-layer="472" from-port="2" to-layer="473" to-port="1"/>
		<edge from-layer="473" from-port="2" to-layer="475" to-port="0"/>
		<edge from-layer="474" from-port="0" to-layer="475" to-port="1"/>
		<edge from-layer="475" from-port="2" to-layer="477" to-port="0"/>
		<edge from-layer="476" from-port="0" to-layer="477" to-port="1"/>
		<edge from-layer="477" from-port="2" to-layer="479" to-port="0"/>
		<edge from-layer="478" from-port="0" to-layer="479" to-port="1"/>
		<edge from-layer="492" from-port="0" to-layer="493" to-port="0"/>
		<edge from-layer="493" from-port="1" to-layer="495" to-port="0"/>
		<edge from-layer="494" from-port="0" to-layer="495" to-port="1"/>
		<edge from-layer="495" from-port="2" to-layer="497" to-port="0"/>
		<edge from-layer="496" from-port="0" to-layer="497" to-port="1"/>
		<edge from-layer="466" from-port="5" to-layer="498" to-port="0"/>
		<edge from-layer="497" from-port="2" to-layer="498" to-port="1"/>
		<edge from-layer="498" from-port="2" to-layer="500" to-port="0"/>
		<edge from-layer="499" from-port="0" to-layer="500" to-port="1"/>
		<edge from-layer="500" from-port="2" to-layer="501" to-port="0"/>
		<edge from-layer="501" from-port="1" to-layer="502" to-port="0"/>
		<edge from-layer="488" from-port="0" to-layer="502" to-port="1"/>
		<edge from-layer="489" from-port="0" to-layer="502" to-port="2"/>
		<edge from-layer="490" from-port="0" to-layer="502" to-port="3"/>
		<edge from-layer="491" from-port="0" to-layer="502" to-port="4"/>
		<edge from-layer="504" from-port="0" to-layer="505" to-port="0"/>
		<edge from-layer="505" from-port="1" to-layer="507" to-port="0"/>
		<edge from-layer="506" from-port="0" to-layer="507" to-port="1"/>
		<edge from-layer="507" from-port="2" to-layer="509" to-port="0"/>
		<edge from-layer="508" from-port="0" to-layer="509" to-port="1"/>
		<edge from-layer="509" from-port="2" to-layer="510" to-port="0"/>
		<edge from-layer="503" from-port="0" to-layer="510" to-port="1"/>
		<edge from-layer="502" from-port="5" to-layer="511" to-port="0"/>
		<edge from-layer="510" from-port="2" to-layer="511" to-port="1"/>
		<edge from-layer="511" from-port="2" to-layer="513" to-port="0"/>
		<edge from-layer="512" from-port="0" to-layer="513" to-port="1"/>
		<edge from-layer="513" from-port="2" to-layer="514" to-port="0"/>
		<edge from-layer="514" from-port="1" to-layer="515" to-port="0"/>
		<edge from-layer="484" from-port="0" to-layer="515" to-port="1"/>
		<edge from-layer="485" from-port="0" to-layer="515" to-port="2"/>
		<edge from-layer="486" from-port="0" to-layer="515" to-port="3"/>
		<edge from-layer="487" from-port="0" to-layer="515" to-port="4"/>
		<edge from-layer="516" from-port="0" to-layer="517" to-port="0"/>
		<edge from-layer="517" from-port="1" to-layer="519" to-port="0"/>
		<edge from-layer="518" from-port="0" to-layer="519" to-port="1"/>
		<edge from-layer="519" from-port="2" to-layer="521" to-port="0"/>
		<edge from-layer="520" from-port="0" to-layer="521" to-port="1"/>
		<edge from-layer="515" from-port="5" to-layer="522" to-port="0"/>
		<edge from-layer="521" from-port="2" to-layer="522" to-port="1"/>
		<edge from-layer="522" from-port="2" to-layer="524" to-port="0"/>
		<edge from-layer="523" from-port="0" to-layer="524" to-port="1"/>
		<edge from-layer="524" from-port="2" to-layer="525" to-port="0"/>
		<edge from-layer="525" from-port="1" to-layer="526" to-port="0"/>
		<edge from-layer="480" from-port="0" to-layer="526" to-port="1"/>
		<edge from-layer="481" from-port="0" to-layer="526" to-port="2"/>
		<edge from-layer="482" from-port="0" to-layer="526" to-port="3"/>
		<edge from-layer="483" from-port="0" to-layer="526" to-port="4"/>
		<edge from-layer="527" from-port="0" to-layer="528" to-port="0"/>
		<edge from-layer="528" from-port="1" to-layer="530" to-port="0"/>
		<edge from-layer="529" from-port="0" to-layer="530" to-port="1"/>
		<edge from-layer="530" from-port="2" to-layer="532" to-port="0"/>
		<edge from-layer="531" from-port="0" to-layer="532" to-port="1"/>
		<edge from-layer="526" from-port="5" to-layer="533" to-port="0"/>
		<edge from-layer="532" from-port="2" to-layer="533" to-port="1"/>
		<edge from-layer="533" from-port="2" to-layer="535" to-port="0"/>
		<edge from-layer="534" from-port="0" to-layer="535" to-port="1"/>
		<edge from-layer="535" from-port="2" to-layer="537" to-port="0"/>
		<edge from-layer="536" from-port="0" to-layer="537" to-port="1"/>
		<edge from-layer="537" from-port="2" to-layer="539" to-port="0"/>
		<edge from-layer="538" from-port="0" to-layer="539" to-port="1"/>
		<edge from-layer="552" from-port="0" to-layer="553" to-port="0"/>
		<edge from-layer="553" from-port="1" to-layer="555" to-port="0"/>
		<edge from-layer="554" from-port="0" to-layer="555" to-port="1"/>
		<edge from-layer="555" from-port="2" to-layer="557" to-port="0"/>
		<edge from-layer="556" from-port="0" to-layer="557" to-port="1"/>
		<edge from-layer="526" from-port="5" to-layer="558" to-port="0"/>
		<edge from-layer="557" from-port="2" to-layer="558" to-port="1"/>
		<edge from-layer="558" from-port="2" to-layer="560" to-port="0"/>
		<edge from-layer="559" from-port="0" to-layer="560" to-port="1"/>
		<edge from-layer="560" from-port="2" to-layer="561" to-port="0"/>
		<edge from-layer="561" from-port="1" to-layer="562" to-port="0"/>
		<edge from-layer="548" from-port="0" to-layer="562" to-port="1"/>
		<edge from-layer="549" from-port="0" to-layer="562" to-port="2"/>
		<edge from-layer="550" from-port="0" to-layer="562" to-port="3"/>
		<edge from-layer="551" from-port="0" to-layer="562" to-port="4"/>
		<edge from-layer="564" from-port="0" to-layer="565" to-port="0"/>
		<edge from-layer="565" from-port="1" to-layer="567" to-port="0"/>
		<edge from-layer="566" from-port="0" to-layer="567" to-port="1"/>
		<edge from-layer="567" from-port="2" to-layer="569" to-port="0"/>
		<edge from-layer="568" from-port="0" to-layer="569" to-port="1"/>
		<edge from-layer="569" from-port="2" to-layer="570" to-port="0"/>
		<edge from-layer="563" from-port="0" to-layer="570" to-port="1"/>
		<edge from-layer="562" from-port="5" to-layer="571" to-port="0"/>
		<edge from-layer="570" from-port="2" to-layer="571" to-port="1"/>
		<edge from-layer="571" from-port="2" to-layer="573" to-port="0"/>
		<edge from-layer="572" from-port="0" to-layer="573" to-port="1"/>
		<edge from-layer="573" from-port="2" to-layer="574" to-port="0"/>
		<edge from-layer="574" from-port="1" to-layer="575" to-port="0"/>
		<edge from-layer="544" from-port="0" to-layer="575" to-port="1"/>
		<edge from-layer="545" from-port="0" to-layer="575" to-port="2"/>
		<edge from-layer="546" from-port="0" to-layer="575" to-port="3"/>
		<edge from-layer="547" from-port="0" to-layer="575" to-port="4"/>
		<edge from-layer="576" from-port="0" to-layer="577" to-port="0"/>
		<edge from-layer="577" from-port="1" to-layer="579" to-port="0"/>
		<edge from-layer="578" from-port="0" to-layer="579" to-port="1"/>
		<edge from-layer="579" from-port="2" to-layer="581" to-port="0"/>
		<edge from-layer="580" from-port="0" to-layer="581" to-port="1"/>
		<edge from-layer="575" from-port="5" to-layer="582" to-port="0"/>
		<edge from-layer="581" from-port="2" to-layer="582" to-port="1"/>
		<edge from-layer="582" from-port="2" to-layer="584" to-port="0"/>
		<edge from-layer="583" from-port="0" to-layer="584" to-port="1"/>
		<edge from-layer="584" from-port="2" to-layer="585" to-port="0"/>
		<edge from-layer="585" from-port="1" to-layer="586" to-port="0"/>
		<edge from-layer="540" from-port="0" to-layer="586" to-port="1"/>
		<edge from-layer="541" from-port="0" to-layer="586" to-port="2"/>
		<edge from-layer="542" from-port="0" to-layer="586" to-port="3"/>
		<edge from-layer="543" from-port="0" to-layer="586" to-port="4"/>
		<edge from-layer="587" from-port="0" to-layer="588" to-port="0"/>
		<edge from-layer="588" from-port="1" to-layer="590" to-port="0"/>
		<edge from-layer="589" from-port="0" to-layer="590" to-port="1"/>
		<edge from-layer="590" from-port="2" to-layer="592" to-port="0"/>
		<edge from-layer="591" from-port="0" to-layer="592" to-port="1"/>
		<edge from-layer="586" from-port="5" to-layer="593" to-port="0"/>
		<edge from-layer="592" from-port="2" to-layer="593" to-port="1"/>
		<edge from-layer="593" from-port="2" to-layer="595" to-port="0"/>
		<edge from-layer="594" from-port="0" to-layer="595" to-port="1"/>
		<edge from-layer="595" from-port="2" to-layer="597" to-port="0"/>
		<edge from-layer="596" from-port="0" to-layer="597" to-port="1"/>
		<edge from-layer="597" from-port="2" to-layer="599" to-port="0"/>
		<edge from-layer="598" from-port="0" to-layer="599" to-port="1"/>
		<edge from-layer="612" from-port="0" to-layer="613" to-port="0"/>
		<edge from-layer="613" from-port="1" to-layer="615" to-port="0"/>
		<edge from-layer="614" from-port="0" to-layer="615" to-port="1"/>
		<edge from-layer="615" from-port="2" to-layer="617" to-port="0"/>
		<edge from-layer="616" from-port="0" to-layer="617" to-port="1"/>
		<edge from-layer="586" from-port="5" to-layer="618" to-port="0"/>
		<edge from-layer="617" from-port="2" to-layer="618" to-port="1"/>
		<edge from-layer="618" from-port="2" to-layer="620" to-port="0"/>
		<edge from-layer="619" from-port="0" to-layer="620" to-port="1"/>
		<edge from-layer="620" from-port="2" to-layer="621" to-port="0"/>
		<edge from-layer="621" from-port="1" to-layer="622" to-port="0"/>
		<edge from-layer="608" from-port="0" to-layer="622" to-port="1"/>
		<edge from-layer="609" from-port="0" to-layer="622" to-port="2"/>
		<edge from-layer="610" from-port="0" to-layer="622" to-port="3"/>
		<edge from-layer="611" from-port="0" to-layer="622" to-port="4"/>
		<edge from-layer="624" from-port="0" to-layer="625" to-port="0"/>
		<edge from-layer="625" from-port="1" to-layer="627" to-port="0"/>
		<edge from-layer="626" from-port="0" to-layer="627" to-port="1"/>
		<edge from-layer="627" from-port="2" to-layer="629" to-port="0"/>
		<edge from-layer="628" from-port="0" to-layer="629" to-port="1"/>
		<edge from-layer="629" from-port="2" to-layer="630" to-port="0"/>
		<edge from-layer="623" from-port="0" to-layer="630" to-port="1"/>
		<edge from-layer="622" from-port="5" to-layer="631" to-port="0"/>
		<edge from-layer="630" from-port="2" to-layer="631" to-port="1"/>
		<edge from-layer="631" from-port="2" to-layer="633" to-port="0"/>
		<edge from-layer="632" from-port="0" to-layer="633" to-port="1"/>
		<edge from-layer="633" from-port="2" to-layer="634" to-port="0"/>
		<edge from-layer="634" from-port="1" to-layer="635" to-port="0"/>
		<edge from-layer="604" from-port="0" to-layer="635" to-port="1"/>
		<edge from-layer="605" from-port="0" to-layer="635" to-port="2"/>
		<edge from-layer="606" from-port="0" to-layer="635" to-port="3"/>
		<edge from-layer="607" from-port="0" to-layer="635" to-port="4"/>
		<edge from-layer="636" from-port="0" to-layer="637" to-port="0"/>
		<edge from-layer="637" from-port="1" to-layer="639" to-port="0"/>
		<edge from-layer="638" from-port="0" to-layer="639" to-port="1"/>
		<edge from-layer="639" from-port="2" to-layer="641" to-port="0"/>
		<edge from-layer="640" from-port="0" to-layer="641" to-port="1"/>
		<edge from-layer="635" from-port="5" to-layer="642" to-port="0"/>
		<edge from-layer="641" from-port="2" to-layer="642" to-port="1"/>
		<edge from-layer="642" from-port="2" to-layer="644" to-port="0"/>
		<edge from-layer="643" from-port="0" to-layer="644" to-port="1"/>
		<edge from-layer="644" from-port="2" to-layer="645" to-port="0"/>
		<edge from-layer="645" from-port="1" to-layer="646" to-port="0"/>
		<edge from-layer="600" from-port="0" to-layer="646" to-port="1"/>
		<edge from-layer="601" from-port="0" to-layer="646" to-port="2"/>
		<edge from-layer="602" from-port="0" to-layer="646" to-port="3"/>
		<edge from-layer="603" from-port="0" to-layer="646" to-port="4"/>
		<edge from-layer="647" from-port="0" to-layer="648" to-port="0"/>
		<edge from-layer="648" from-port="1" to-layer="650" to-port="0"/>
		<edge from-layer="649" from-port="0" to-layer="650" to-port="1"/>
		<edge from-layer="650" from-port="2" to-layer="652" to-port="0"/>
		<edge from-layer="651" from-port="0" to-layer="652" to-port="1"/>
		<edge from-layer="646" from-port="5" to-layer="653" to-port="0"/>
		<edge from-layer="652" from-port="2" to-layer="653" to-port="1"/>
		<edge from-layer="653" from-port="2" to-layer="655" to-port="0"/>
		<edge from-layer="654" from-port="0" to-layer="655" to-port="1"/>
		<edge from-layer="655" from-port="2" to-layer="657" to-port="0"/>
		<edge from-layer="656" from-port="0" to-layer="657" to-port="1"/>
		<edge from-layer="657" from-port="2" to-layer="659" to-port="0"/>
		<edge from-layer="658" from-port="0" to-layer="659" to-port="1"/>
		<edge from-layer="672" from-port="0" to-layer="673" to-port="0"/>
		<edge from-layer="673" from-port="1" to-layer="675" to-port="0"/>
		<edge from-layer="674" from-port="0" to-layer="675" to-port="1"/>
		<edge from-layer="675" from-port="2" to-layer="677" to-port="0"/>
		<edge from-layer="676" from-port="0" to-layer="677" to-port="1"/>
		<edge from-layer="646" from-port="5" to-layer="678" to-port="0"/>
		<edge from-layer="677" from-port="2" to-layer="678" to-port="1"/>
		<edge from-layer="678" from-port="2" to-layer="680" to-port="0"/>
		<edge from-layer="679" from-port="0" to-layer="680" to-port="1"/>
		<edge from-layer="680" from-port="2" to-layer="681" to-port="0"/>
		<edge from-layer="681" from-port="1" to-layer="682" to-port="0"/>
		<edge from-layer="668" from-port="0" to-layer="682" to-port="1"/>
		<edge from-layer="669" from-port="0" to-layer="682" to-port="2"/>
		<edge from-layer="670" from-port="0" to-layer="682" to-port="3"/>
		<edge from-layer="671" from-port="0" to-layer="682" to-port="4"/>
		<edge from-layer="684" from-port="0" to-layer="685" to-port="0"/>
		<edge from-layer="685" from-port="1" to-layer="687" to-port="0"/>
		<edge from-layer="686" from-port="0" to-layer="687" to-port="1"/>
		<edge from-layer="687" from-port="2" to-layer="689" to-port="0"/>
		<edge from-layer="688" from-port="0" to-layer="689" to-port="1"/>
		<edge from-layer="689" from-port="2" to-layer="690" to-port="0"/>
		<edge from-layer="683" from-port="0" to-layer="690" to-port="1"/>
		<edge from-layer="682" from-port="5" to-layer="691" to-port="0"/>
		<edge from-layer="690" from-port="2" to-layer="691" to-port="1"/>
		<edge from-layer="691" from-port="2" to-layer="693" to-port="0"/>
		<edge from-layer="692" from-port="0" to-layer="693" to-port="1"/>
		<edge from-layer="693" from-port="2" to-layer="694" to-port="0"/>
		<edge from-layer="694" from-port="1" to-layer="695" to-port="0"/>
		<edge from-layer="664" from-port="0" to-layer="695" to-port="1"/>
		<edge from-layer="665" from-port="0" to-layer="695" to-port="2"/>
		<edge from-layer="666" from-port="0" to-layer="695" to-port="3"/>
		<edge from-layer="667" from-port="0" to-layer="695" to-port="4"/>
		<edge from-layer="696" from-port="0" to-layer="697" to-port="0"/>
		<edge from-layer="697" from-port="1" to-layer="699" to-port="0"/>
		<edge from-layer="698" from-port="0" to-layer="699" to-port="1"/>
		<edge from-layer="699" from-port="2" to-layer="701" to-port="0"/>
		<edge from-layer="700" from-port="0" to-layer="701" to-port="1"/>
		<edge from-layer="695" from-port="5" to-layer="702" to-port="0"/>
		<edge from-layer="701" from-port="2" to-layer="702" to-port="1"/>
		<edge from-layer="702" from-port="2" to-layer="704" to-port="0"/>
		<edge from-layer="703" from-port="0" to-layer="704" to-port="1"/>
		<edge from-layer="704" from-port="2" to-layer="705" to-port="0"/>
		<edge from-layer="705" from-port="1" to-layer="706" to-port="0"/>
		<edge from-layer="660" from-port="0" to-layer="706" to-port="1"/>
		<edge from-layer="661" from-port="0" to-layer="706" to-port="2"/>
		<edge from-layer="662" from-port="0" to-layer="706" to-port="3"/>
		<edge from-layer="663" from-port="0" to-layer="706" to-port="4"/>
		<edge from-layer="707" from-port="0" to-layer="708" to-port="0"/>
		<edge from-layer="708" from-port="1" to-layer="710" to-port="0"/>
		<edge from-layer="709" from-port="0" to-layer="710" to-port="1"/>
		<edge from-layer="710" from-port="2" to-layer="712" to-port="0"/>
		<edge from-layer="711" from-port="0" to-layer="712" to-port="1"/>
		<edge from-layer="706" from-port="5" to-layer="713" to-port="0"/>
		<edge from-layer="712" from-port="2" to-layer="713" to-port="1"/>
		<edge from-layer="713" from-port="2" to-layer="715" to-port="0"/>
		<edge from-layer="714" from-port="0" to-layer="715" to-port="1"/>
		<edge from-layer="715" from-port="2" to-layer="717" to-port="0"/>
		<edge from-layer="716" from-port="0" to-layer="717" to-port="1"/>
		<edge from-layer="717" from-port="2" to-layer="719" to-port="0"/>
		<edge from-layer="718" from-port="0" to-layer="719" to-port="1"/>
		<edge from-layer="389" from-port="2" to-layer="720" to-port="0"/>
		<edge from-layer="402" from-port="2" to-layer="720" to-port="1"/>
		<edge from-layer="479" from-port="2" to-layer="720" to-port="2"/>
		<edge from-layer="539" from-port="2" to-layer="720" to-port="3"/>
		<edge from-layer="599" from-port="2" to-layer="720" to-port="4"/>
		<edge from-layer="659" from-port="2" to-layer="720" to-port="5"/>
		<edge from-layer="719" from-port="2" to-layer="720" to-port="6"/>
		<edge from-layer="729" from-port="0" to-layer="730" to-port="0"/>
		<edge from-layer="730" from-port="1" to-layer="732" to-port="0"/>
		<edge from-layer="731" from-port="0" to-layer="732" to-port="1"/>
		<edge from-layer="732" from-port="2" to-layer="734" to-port="0"/>
		<edge from-layer="733" from-port="0" to-layer="734" to-port="1"/>
		<edge from-layer="376" from-port="5" to-layer="735" to-port="0"/>
		<edge from-layer="734" from-port="2" to-layer="735" to-port="1"/>
		<edge from-layer="735" from-port="2" to-layer="737" to-port="0"/>
		<edge from-layer="736" from-port="0" to-layer="737" to-port="1"/>
		<edge from-layer="737" from-port="2" to-layer="738" to-port="0"/>
		<edge from-layer="738" from-port="1" to-layer="739" to-port="0"/>
		<edge from-layer="725" from-port="0" to-layer="739" to-port="1"/>
		<edge from-layer="726" from-port="0" to-layer="739" to-port="2"/>
		<edge from-layer="727" from-port="0" to-layer="739" to-port="3"/>
		<edge from-layer="728" from-port="0" to-layer="739" to-port="4"/>
		<edge from-layer="740" from-port="0" to-layer="741" to-port="0"/>
		<edge from-layer="741" from-port="1" to-layer="743" to-port="0"/>
		<edge from-layer="742" from-port="0" to-layer="743" to-port="1"/>
		<edge from-layer="743" from-port="2" to-layer="745" to-port="0"/>
		<edge from-layer="744" from-port="0" to-layer="745" to-port="1"/>
		<edge from-layer="739" from-port="5" to-layer="746" to-port="0"/>
		<edge from-layer="745" from-port="2" to-layer="746" to-port="1"/>
		<edge from-layer="746" from-port="2" to-layer="748" to-port="0"/>
		<edge from-layer="747" from-port="0" to-layer="748" to-port="1"/>
		<edge from-layer="748" from-port="2" to-layer="749" to-port="0"/>
		<edge from-layer="749" from-port="1" to-layer="750" to-port="0"/>
		<edge from-layer="721" from-port="0" to-layer="750" to-port="1"/>
		<edge from-layer="722" from-port="0" to-layer="750" to-port="2"/>
		<edge from-layer="723" from-port="0" to-layer="750" to-port="3"/>
		<edge from-layer="724" from-port="0" to-layer="750" to-port="4"/>
		<edge from-layer="751" from-port="0" to-layer="752" to-port="0"/>
		<edge from-layer="752" from-port="1" to-layer="754" to-port="0"/>
		<edge from-layer="753" from-port="0" to-layer="754" to-port="1"/>
		<edge from-layer="754" from-port="2" to-layer="756" to-port="0"/>
		<edge from-layer="755" from-port="0" to-layer="756" to-port="1"/>
		<edge from-layer="750" from-port="5" to-layer="757" to-port="0"/>
		<edge from-layer="756" from-port="2" to-layer="757" to-port="1"/>
		<edge from-layer="757" from-port="2" to-layer="759" to-port="0"/>
		<edge from-layer="758" from-port="0" to-layer="759" to-port="1"/>
		<edge from-layer="759" from-port="2" to-layer="761" to-port="0"/>
		<edge from-layer="760" from-port="0" to-layer="761" to-port="1"/>
		<edge from-layer="761" from-port="2" to-layer="763" to-port="0"/>
		<edge from-layer="762" from-port="0" to-layer="763" to-port="1"/>
		<edge from-layer="768" from-port="0" to-layer="769" to-port="0"/>
		<edge from-layer="769" from-port="1" to-layer="771" to-port="0"/>
		<edge from-layer="770" from-port="0" to-layer="771" to-port="1"/>
		<edge from-layer="771" from-port="2" to-layer="773" to-port="0"/>
		<edge from-layer="772" from-port="0" to-layer="773" to-port="1"/>
		<edge from-layer="739" from-port="5" to-layer="774" to-port="0"/>
		<edge from-layer="773" from-port="2" to-layer="774" to-port="1"/>
		<edge from-layer="774" from-port="2" to-layer="776" to-port="0"/>
		<edge from-layer="775" from-port="0" to-layer="776" to-port="1"/>
		<edge from-layer="776" from-port="2" to-layer="777" to-port="0"/>
		<edge from-layer="777" from-port="1" to-layer="778" to-port="0"/>
		<edge from-layer="764" from-port="0" to-layer="778" to-port="1"/>
		<edge from-layer="765" from-port="0" to-layer="778" to-port="2"/>
		<edge from-layer="766" from-port="0" to-layer="778" to-port="3"/>
		<edge from-layer="767" from-port="0" to-layer="778" to-port="4"/>
		<edge from-layer="779" from-port="0" to-layer="780" to-port="0"/>
		<edge from-layer="780" from-port="1" to-layer="782" to-port="0"/>
		<edge from-layer="781" from-port="0" to-layer="782" to-port="1"/>
		<edge from-layer="782" from-port="2" to-layer="784" to-port="0"/>
		<edge from-layer="783" from-port="0" to-layer="784" to-port="1"/>
		<edge from-layer="778" from-port="5" to-layer="785" to-port="0"/>
		<edge from-layer="784" from-port="2" to-layer="785" to-port="1"/>
		<edge from-layer="785" from-port="2" to-layer="787" to-port="0"/>
		<edge from-layer="786" from-port="0" to-layer="787" to-port="1"/>
		<edge from-layer="787" from-port="2" to-layer="789" to-port="0"/>
		<edge from-layer="788" from-port="0" to-layer="789" to-port="1"/>
		<edge from-layer="789" from-port="2" to-layer="791" to-port="0"/>
		<edge from-layer="790" from-port="0" to-layer="791" to-port="1"/>
		<edge from-layer="800" from-port="0" to-layer="801" to-port="0"/>
		<edge from-layer="801" from-port="1" to-layer="803" to-port="0"/>
		<edge from-layer="802" from-port="0" to-layer="803" to-port="1"/>
		<edge from-layer="803" from-port="2" to-layer="805" to-port="0"/>
		<edge from-layer="804" from-port="0" to-layer="805" to-port="1"/>
		<edge from-layer="466" from-port="5" to-layer="806" to-port="0"/>
		<edge from-layer="805" from-port="2" to-layer="806" to-port="1"/>
		<edge from-layer="806" from-port="2" to-layer="808" to-port="0"/>
		<edge from-layer="807" from-port="0" to-layer="808" to-port="1"/>
		<edge from-layer="808" from-port="2" to-layer="809" to-port="0"/>
		<edge from-layer="809" from-port="1" to-layer="810" to-port="0"/>
		<edge from-layer="796" from-port="0" to-layer="810" to-port="1"/>
		<edge from-layer="797" from-port="0" to-layer="810" to-port="2"/>
		<edge from-layer="798" from-port="0" to-layer="810" to-port="3"/>
		<edge from-layer="799" from-port="0" to-layer="810" to-port="4"/>
		<edge from-layer="811" from-port="0" to-layer="812" to-port="0"/>
		<edge from-layer="812" from-port="1" to-layer="814" to-port="0"/>
		<edge from-layer="813" from-port="0" to-layer="814" to-port="1"/>
		<edge from-layer="814" from-port="2" to-layer="816" to-port="0"/>
		<edge from-layer="815" from-port="0" to-layer="816" to-port="1"/>
		<edge from-layer="810" from-port="5" to-layer="817" to-port="0"/>
		<edge from-layer="816" from-port="2" to-layer="817" to-port="1"/>
		<edge from-layer="817" from-port="2" to-layer="819" to-port="0"/>
		<edge from-layer="818" from-port="0" to-layer="819" to-port="1"/>
		<edge from-layer="819" from-port="2" to-layer="820" to-port="0"/>
		<edge from-layer="820" from-port="1" to-layer="821" to-port="0"/>
		<edge from-layer="792" from-port="0" to-layer="821" to-port="1"/>
		<edge from-layer="793" from-port="0" to-layer="821" to-port="2"/>
		<edge from-layer="794" from-port="0" to-layer="821" to-port="3"/>
		<edge from-layer="795" from-port="0" to-layer="821" to-port="4"/>
		<edge from-layer="822" from-port="0" to-layer="823" to-port="0"/>
		<edge from-layer="823" from-port="1" to-layer="825" to-port="0"/>
		<edge from-layer="824" from-port="0" to-layer="825" to-port="1"/>
		<edge from-layer="825" from-port="2" to-layer="827" to-port="0"/>
		<edge from-layer="826" from-port="0" to-layer="827" to-port="1"/>
		<edge from-layer="821" from-port="5" to-layer="828" to-port="0"/>
		<edge from-layer="827" from-port="2" to-layer="828" to-port="1"/>
		<edge from-layer="828" from-port="2" to-layer="830" to-port="0"/>
		<edge from-layer="829" from-port="0" to-layer="830" to-port="1"/>
		<edge from-layer="830" from-port="2" to-layer="832" to-port="0"/>
		<edge from-layer="831" from-port="0" to-layer="832" to-port="1"/>
		<edge from-layer="832" from-port="2" to-layer="834" to-port="0"/>
		<edge from-layer="833" from-port="0" to-layer="834" to-port="1"/>
		<edge from-layer="843" from-port="0" to-layer="844" to-port="0"/>
		<edge from-layer="844" from-port="1" to-layer="846" to-port="0"/>
		<edge from-layer="845" from-port="0" to-layer="846" to-port="1"/>
		<edge from-layer="846" from-port="2" to-layer="848" to-port="0"/>
		<edge from-layer="847" from-port="0" to-layer="848" to-port="1"/>
		<edge from-layer="526" from-port="5" to-layer="849" to-port="0"/>
		<edge from-layer="848" from-port="2" to-layer="849" to-port="1"/>
		<edge from-layer="849" from-port="2" to-layer="851" to-port="0"/>
		<edge from-layer="850" from-port="0" to-layer="851" to-port="1"/>
		<edge from-layer="851" from-port="2" to-layer="852" to-port="0"/>
		<edge from-layer="852" from-port="1" to-layer="853" to-port="0"/>
		<edge from-layer="839" from-port="0" to-layer="853" to-port="1"/>
		<edge from-layer="840" from-port="0" to-layer="853" to-port="2"/>
		<edge from-layer="841" from-port="0" to-layer="853" to-port="3"/>
		<edge from-layer="842" from-port="0" to-layer="853" to-port="4"/>
		<edge from-layer="854" from-port="0" to-layer="855" to-port="0"/>
		<edge from-layer="855" from-port="1" to-layer="857" to-port="0"/>
		<edge from-layer="856" from-port="0" to-layer="857" to-port="1"/>
		<edge from-layer="857" from-port="2" to-layer="859" to-port="0"/>
		<edge from-layer="858" from-port="0" to-layer="859" to-port="1"/>
		<edge from-layer="853" from-port="5" to-layer="860" to-port="0"/>
		<edge from-layer="859" from-port="2" to-layer="860" to-port="1"/>
		<edge from-layer="860" from-port="2" to-layer="862" to-port="0"/>
		<edge from-layer="861" from-port="0" to-layer="862" to-port="1"/>
		<edge from-layer="862" from-port="2" to-layer="863" to-port="0"/>
		<edge from-layer="863" from-port="1" to-layer="864" to-port="0"/>
		<edge from-layer="835" from-port="0" to-layer="864" to-port="1"/>
		<edge from-layer="836" from-port="0" to-layer="864" to-port="2"/>
		<edge from-layer="837" from-port="0" to-layer="864" to-port="3"/>
		<edge from-layer="838" from-port="0" to-layer="864" to-port="4"/>
		<edge from-layer="865" from-port="0" to-layer="866" to-port="0"/>
		<edge from-layer="866" from-port="1" to-layer="868" to-port="0"/>
		<edge from-layer="867" from-port="0" to-layer="868" to-port="1"/>
		<edge from-layer="868" from-port="2" to-layer="870" to-port="0"/>
		<edge from-layer="869" from-port="0" to-layer="870" to-port="1"/>
		<edge from-layer="864" from-port="5" to-layer="871" to-port="0"/>
		<edge from-layer="870" from-port="2" to-layer="871" to-port="1"/>
		<edge from-layer="871" from-port="2" to-layer="873" to-port="0"/>
		<edge from-layer="872" from-port="0" to-layer="873" to-port="1"/>
		<edge from-layer="873" from-port="2" to-layer="875" to-port="0"/>
		<edge from-layer="874" from-port="0" to-layer="875" to-port="1"/>
		<edge from-layer="875" from-port="2" to-layer="877" to-port="0"/>
		<edge from-layer="876" from-port="0" to-layer="877" to-port="1"/>
		<edge from-layer="886" from-port="0" to-layer="887" to-port="0"/>
		<edge from-layer="887" from-port="1" to-layer="889" to-port="0"/>
		<edge from-layer="888" from-port="0" to-layer="889" to-port="1"/>
		<edge from-layer="889" from-port="2" to-layer="891" to-port="0"/>
		<edge from-layer="890" from-port="0" to-layer="891" to-port="1"/>
		<edge from-layer="586" from-port="5" to-layer="892" to-port="0"/>
		<edge from-layer="891" from-port="2" to-layer="892" to-port="1"/>
		<edge from-layer="892" from-port="2" to-layer="894" to-port="0"/>
		<edge from-layer="893" from-port="0" to-layer="894" to-port="1"/>
		<edge from-layer="894" from-port="2" to-layer="895" to-port="0"/>
		<edge from-layer="895" from-port="1" to-layer="896" to-port="0"/>
		<edge from-layer="882" from-port="0" to-layer="896" to-port="1"/>
		<edge from-layer="883" from-port="0" to-layer="896" to-port="2"/>
		<edge from-layer="884" from-port="0" to-layer="896" to-port="3"/>
		<edge from-layer="885" from-port="0" to-layer="896" to-port="4"/>
		<edge from-layer="897" from-port="0" to-layer="898" to-port="0"/>
		<edge from-layer="898" from-port="1" to-layer="900" to-port="0"/>
		<edge from-layer="899" from-port="0" to-layer="900" to-port="1"/>
		<edge from-layer="900" from-port="2" to-layer="902" to-port="0"/>
		<edge from-layer="901" from-port="0" to-layer="902" to-port="1"/>
		<edge from-layer="896" from-port="5" to-layer="903" to-port="0"/>
		<edge from-layer="902" from-port="2" to-layer="903" to-port="1"/>
		<edge from-layer="903" from-port="2" to-layer="905" to-port="0"/>
		<edge from-layer="904" from-port="0" to-layer="905" to-port="1"/>
		<edge from-layer="905" from-port="2" to-layer="906" to-port="0"/>
		<edge from-layer="906" from-port="1" to-layer="907" to-port="0"/>
		<edge from-layer="878" from-port="0" to-layer="907" to-port="1"/>
		<edge from-layer="879" from-port="0" to-layer="907" to-port="2"/>
		<edge from-layer="880" from-port="0" to-layer="907" to-port="3"/>
		<edge from-layer="881" from-port="0" to-layer="907" to-port="4"/>
		<edge from-layer="908" from-port="0" to-layer="909" to-port="0"/>
		<edge from-layer="909" from-port="1" to-layer="911" to-port="0"/>
		<edge from-layer="910" from-port="0" to-layer="911" to-port="1"/>
		<edge from-layer="911" from-port="2" to-layer="913" to-port="0"/>
		<edge from-layer="912" from-port="0" to-layer="913" to-port="1"/>
		<edge from-layer="907" from-port="5" to-layer="914" to-port="0"/>
		<edge from-layer="913" from-port="2" to-layer="914" to-port="1"/>
		<edge from-layer="914" from-port="2" to-layer="916" to-port="0"/>
		<edge from-layer="915" from-port="0" to-layer="916" to-port="1"/>
		<edge from-layer="916" from-port="2" to-layer="918" to-port="0"/>
		<edge from-layer="917" from-port="0" to-layer="918" to-port="1"/>
		<edge from-layer="918" from-port="2" to-layer="920" to-port="0"/>
		<edge from-layer="919" from-port="0" to-layer="920" to-port="1"/>
		<edge from-layer="929" from-port="0" to-layer="930" to-port="0"/>
		<edge from-layer="930" from-port="1" to-layer="932" to-port="0"/>
		<edge from-layer="931" from-port="0" to-layer="932" to-port="1"/>
		<edge from-layer="932" from-port="2" to-layer="934" to-port="0"/>
		<edge from-layer="933" from-port="0" to-layer="934" to-port="1"/>
		<edge from-layer="646" from-port="5" to-layer="935" to-port="0"/>
		<edge from-layer="934" from-port="2" to-layer="935" to-port="1"/>
		<edge from-layer="935" from-port="2" to-layer="937" to-port="0"/>
		<edge from-layer="936" from-port="0" to-layer="937" to-port="1"/>
		<edge from-layer="937" from-port="2" to-layer="938" to-port="0"/>
		<edge from-layer="938" from-port="1" to-layer="939" to-port="0"/>
		<edge from-layer="925" from-port="0" to-layer="939" to-port="1"/>
		<edge from-layer="926" from-port="0" to-layer="939" to-port="2"/>
		<edge from-layer="927" from-port="0" to-layer="939" to-port="3"/>
		<edge from-layer="928" from-port="0" to-layer="939" to-port="4"/>
		<edge from-layer="940" from-port="0" to-layer="941" to-port="0"/>
		<edge from-layer="941" from-port="1" to-layer="943" to-port="0"/>
		<edge from-layer="942" from-port="0" to-layer="943" to-port="1"/>
		<edge from-layer="943" from-port="2" to-layer="945" to-port="0"/>
		<edge from-layer="944" from-port="0" to-layer="945" to-port="1"/>
		<edge from-layer="939" from-port="5" to-layer="946" to-port="0"/>
		<edge from-layer="945" from-port="2" to-layer="946" to-port="1"/>
		<edge from-layer="946" from-port="2" to-layer="948" to-port="0"/>
		<edge from-layer="947" from-port="0" to-layer="948" to-port="1"/>
		<edge from-layer="948" from-port="2" to-layer="949" to-port="0"/>
		<edge from-layer="949" from-port="1" to-layer="950" to-port="0"/>
		<edge from-layer="921" from-port="0" to-layer="950" to-port="1"/>
		<edge from-layer="922" from-port="0" to-layer="950" to-port="2"/>
		<edge from-layer="923" from-port="0" to-layer="950" to-port="3"/>
		<edge from-layer="924" from-port="0" to-layer="950" to-port="4"/>
		<edge from-layer="951" from-port="0" to-layer="952" to-port="0"/>
		<edge from-layer="952" from-port="1" to-layer="954" to-port="0"/>
		<edge from-layer="953" from-port="0" to-layer="954" to-port="1"/>
		<edge from-layer="954" from-port="2" to-layer="956" to-port="0"/>
		<edge from-layer="955" from-port="0" to-layer="956" to-port="1"/>
		<edge from-layer="950" from-port="5" to-layer="957" to-port="0"/>
		<edge from-layer="956" from-port="2" to-layer="957" to-port="1"/>
		<edge from-layer="957" from-port="2" to-layer="959" to-port="0"/>
		<edge from-layer="958" from-port="0" to-layer="959" to-port="1"/>
		<edge from-layer="959" from-port="2" to-layer="961" to-port="0"/>
		<edge from-layer="960" from-port="0" to-layer="961" to-port="1"/>
		<edge from-layer="961" from-port="2" to-layer="963" to-port="0"/>
		<edge from-layer="962" from-port="0" to-layer="963" to-port="1"/>
		<edge from-layer="972" from-port="0" to-layer="973" to-port="0"/>
		<edge from-layer="973" from-port="1" to-layer="975" to-port="0"/>
		<edge from-layer="974" from-port="0" to-layer="975" to-port="1"/>
		<edge from-layer="975" from-port="2" to-layer="977" to-port="0"/>
		<edge from-layer="976" from-port="0" to-layer="977" to-port="1"/>
		<edge from-layer="706" from-port="5" to-layer="978" to-port="0"/>
		<edge from-layer="977" from-port="2" to-layer="978" to-port="1"/>
		<edge from-layer="978" from-port="2" to-layer="980" to-port="0"/>
		<edge from-layer="979" from-port="0" to-layer="980" to-port="1"/>
		<edge from-layer="980" from-port="2" to-layer="981" to-port="0"/>
		<edge from-layer="981" from-port="1" to-layer="982" to-port="0"/>
		<edge from-layer="968" from-port="0" to-layer="982" to-port="1"/>
		<edge from-layer="969" from-port="0" to-layer="982" to-port="2"/>
		<edge from-layer="970" from-port="0" to-layer="982" to-port="3"/>
		<edge from-layer="971" from-port="0" to-layer="982" to-port="4"/>
		<edge from-layer="983" from-port="0" to-layer="984" to-port="0"/>
		<edge from-layer="984" from-port="1" to-layer="986" to-port="0"/>
		<edge from-layer="985" from-port="0" to-layer="986" to-port="1"/>
		<edge from-layer="986" from-port="2" to-layer="988" to-port="0"/>
		<edge from-layer="987" from-port="0" to-layer="988" to-port="1"/>
		<edge from-layer="982" from-port="5" to-layer="989" to-port="0"/>
		<edge from-layer="988" from-port="2" to-layer="989" to-port="1"/>
		<edge from-layer="989" from-port="2" to-layer="991" to-port="0"/>
		<edge from-layer="990" from-port="0" to-layer="991" to-port="1"/>
		<edge from-layer="991" from-port="2" to-layer="992" to-port="0"/>
		<edge from-layer="992" from-port="1" to-layer="993" to-port="0"/>
		<edge from-layer="964" from-port="0" to-layer="993" to-port="1"/>
		<edge from-layer="965" from-port="0" to-layer="993" to-port="2"/>
		<edge from-layer="966" from-port="0" to-layer="993" to-port="3"/>
		<edge from-layer="967" from-port="0" to-layer="993" to-port="4"/>
		<edge from-layer="994" from-port="0" to-layer="995" to-port="0"/>
		<edge from-layer="995" from-port="1" to-layer="997" to-port="0"/>
		<edge from-layer="996" from-port="0" to-layer="997" to-port="1"/>
		<edge from-layer="997" from-port="2" to-layer="999" to-port="0"/>
		<edge from-layer="998" from-port="0" to-layer="999" to-port="1"/>
		<edge from-layer="993" from-port="5" to-layer="1000" to-port="0"/>
		<edge from-layer="999" from-port="2" to-layer="1000" to-port="1"/>
		<edge from-layer="1000" from-port="2" to-layer="1002" to-port="0"/>
		<edge from-layer="1001" from-port="0" to-layer="1002" to-port="1"/>
		<edge from-layer="1002" from-port="2" to-layer="1004" to-port="0"/>
		<edge from-layer="1003" from-port="0" to-layer="1004" to-port="1"/>
		<edge from-layer="1004" from-port="2" to-layer="1006" to-port="0"/>
		<edge from-layer="1005" from-port="0" to-layer="1006" to-port="1"/>
		<edge from-layer="763" from-port="2" to-layer="1007" to-port="0"/>
		<edge from-layer="791" from-port="2" to-layer="1007" to-port="1"/>
		<edge from-layer="834" from-port="2" to-layer="1007" to-port="2"/>
		<edge from-layer="877" from-port="2" to-layer="1007" to-port="3"/>
		<edge from-layer="920" from-port="2" to-layer="1007" to-port="4"/>
		<edge from-layer="963" from-port="2" to-layer="1007" to-port="5"/>
		<edge from-layer="1006" from-port="2" to-layer="1007" to-port="6"/>
		<edge from-layer="1007" from-port="7" to-layer="1009" to-port="0"/>
		<edge from-layer="1008" from-port="0" to-layer="1009" to-port="1"/>
		<edge from-layer="1009" from-port="2" to-layer="1010" to-port="0"/>
		<edge from-layer="1010" from-port="1" to-layer="1012" to-port="0"/>
		<edge from-layer="1011" from-port="0" to-layer="1012" to-port="1"/>
		<edge from-layer="375" from-port="1" to-layer="1013" to-port="0"/>
		<edge from-layer="1013" from-port="1" to-layer="1017" to-port="0"/>
		<edge from-layer="1014" from-port="0" to-layer="1017" to-port="1"/>
		<edge from-layer="1015" from-port="0" to-layer="1017" to-port="2"/>
		<edge from-layer="1016" from-port="0" to-layer="1017" to-port="3"/>
		<edge from-layer="100" from-port="2" to-layer="1018" to-port="0"/>
		<edge from-layer="1018" from-port="1" to-layer="1022" to-port="0"/>
		<edge from-layer="1019" from-port="0" to-layer="1022" to-port="1"/>
		<edge from-layer="1020" from-port="0" to-layer="1022" to-port="2"/>
		<edge from-layer="1021" from-port="0" to-layer="1022" to-port="3"/>
		<edge from-layer="1017" from-port="4" to-layer="1023" to-port="0"/>
		<edge from-layer="1022" from-port="4" to-layer="1023" to-port="1"/>
		<edge from-layer="1023" from-port="2" to-layer="1025" to-port="0"/>
		<edge from-layer="1024" from-port="0" to-layer="1025" to-port="1"/>
		<edge from-layer="375" from-port="1" to-layer="1026" to-port="0"/>
		<edge from-layer="1026" from-port="1" to-layer="1030" to-port="0"/>
		<edge from-layer="1027" from-port="0" to-layer="1030" to-port="1"/>
		<edge from-layer="1028" from-port="0" to-layer="1030" to-port="2"/>
		<edge from-layer="1029" from-port="0" to-layer="1030" to-port="3"/>
		<edge from-layer="100" from-port="2" to-layer="1031" to-port="0"/>
		<edge from-layer="1031" from-port="1" to-layer="1035" to-port="0"/>
		<edge from-layer="1032" from-port="0" to-layer="1035" to-port="1"/>
		<edge from-layer="1033" from-port="0" to-layer="1035" to-port="2"/>
		<edge from-layer="1034" from-port="0" to-layer="1035" to-port="3"/>
		<edge from-layer="1030" from-port="4" to-layer="1036" to-port="0"/>
		<edge from-layer="1035" from-port="4" to-layer="1036" to-port="1"/>
		<edge from-layer="1036" from-port="2" to-layer="1038" to-port="0"/>
		<edge from-layer="1037" from-port="0" to-layer="1038" to-port="1"/>
		<edge from-layer="465" from-port="1" to-layer="1039" to-port="0"/>
		<edge from-layer="1039" from-port="1" to-layer="1043" to-port="0"/>
		<edge from-layer="1040" from-port="0" to-layer="1043" to-port="1"/>
		<edge from-layer="1041" from-port="0" to-layer="1043" to-port="2"/>
		<edge from-layer="1042" from-port="0" to-layer="1043" to-port="3"/>
		<edge from-layer="100" from-port="2" to-layer="1044" to-port="0"/>
		<edge from-layer="1044" from-port="1" to-layer="1048" to-port="0"/>
		<edge from-layer="1045" from-port="0" to-layer="1048" to-port="1"/>
		<edge from-layer="1046" from-port="0" to-layer="1048" to-port="2"/>
		<edge from-layer="1047" from-port="0" to-layer="1048" to-port="3"/>
		<edge from-layer="1043" from-port="4" to-layer="1049" to-port="0"/>
		<edge from-layer="1048" from-port="4" to-layer="1049" to-port="1"/>
		<edge from-layer="1049" from-port="2" to-layer="1051" to-port="0"/>
		<edge from-layer="1050" from-port="0" to-layer="1051" to-port="1"/>
		<edge from-layer="525" from-port="1" to-layer="1052" to-port="0"/>
		<edge from-layer="1052" from-port="1" to-layer="1056" to-port="0"/>
		<edge from-layer="1053" from-port="0" to-layer="1056" to-port="1"/>
		<edge from-layer="1054" from-port="0" to-layer="1056" to-port="2"/>
		<edge from-layer="1055" from-port="0" to-layer="1056" to-port="3"/>
		<edge from-layer="100" from-port="2" to-layer="1057" to-port="0"/>
		<edge from-layer="1057" from-port="1" to-layer="1061" to-port="0"/>
		<edge from-layer="1058" from-port="0" to-layer="1061" to-port="1"/>
		<edge from-layer="1059" from-port="0" to-layer="1061" to-port="2"/>
		<edge from-layer="1060" from-port="0" to-layer="1061" to-port="3"/>
		<edge from-layer="1056" from-port="4" to-layer="1062" to-port="0"/>
		<edge from-layer="1061" from-port="4" to-layer="1062" to-port="1"/>
		<edge from-layer="1062" from-port="2" to-layer="1064" to-port="0"/>
		<edge from-layer="1063" from-port="0" to-layer="1064" to-port="1"/>
		<edge from-layer="585" from-port="1" to-layer="1065" to-port="0"/>
		<edge from-layer="1065" from-port="1" to-layer="1069" to-port="0"/>
		<edge from-layer="1066" from-port="0" to-layer="1069" to-port="1"/>
		<edge from-layer="1067" from-port="0" to-layer="1069" to-port="2"/>
		<edge from-layer="1068" from-port="0" to-layer="1069" to-port="3"/>
		<edge from-layer="100" from-port="2" to-layer="1070" to-port="0"/>
		<edge from-layer="1070" from-port="1" to-layer="1074" to-port="0"/>
		<edge from-layer="1071" from-port="0" to-layer="1074" to-port="1"/>
		<edge from-layer="1072" from-port="0" to-layer="1074" to-port="2"/>
		<edge from-layer="1073" from-port="0" to-layer="1074" to-port="3"/>
		<edge from-layer="1069" from-port="4" to-layer="1075" to-port="0"/>
		<edge from-layer="1074" from-port="4" to-layer="1075" to-port="1"/>
		<edge from-layer="1075" from-port="2" to-layer="1077" to-port="0"/>
		<edge from-layer="1076" from-port="0" to-layer="1077" to-port="1"/>
		<edge from-layer="645" from-port="1" to-layer="1078" to-port="0"/>
		<edge from-layer="1078" from-port="1" to-layer="1082" to-port="0"/>
		<edge from-layer="1079" from-port="0" to-layer="1082" to-port="1"/>
		<edge from-layer="1080" from-port="0" to-layer="1082" to-port="2"/>
		<edge from-layer="1081" from-port="0" to-layer="1082" to-port="3"/>
		<edge from-layer="100" from-port="2" to-layer="1083" to-port="0"/>
		<edge from-layer="1083" from-port="1" to-layer="1087" to-port="0"/>
		<edge from-layer="1084" from-port="0" to-layer="1087" to-port="1"/>
		<edge from-layer="1085" from-port="0" to-layer="1087" to-port="2"/>
		<edge from-layer="1086" from-port="0" to-layer="1087" to-port="3"/>
		<edge from-layer="1082" from-port="4" to-layer="1088" to-port="0"/>
		<edge from-layer="1087" from-port="4" to-layer="1088" to-port="1"/>
		<edge from-layer="1088" from-port="2" to-layer="1090" to-port="0"/>
		<edge from-layer="1089" from-port="0" to-layer="1090" to-port="1"/>
		<edge from-layer="705" from-port="1" to-layer="1091" to-port="0"/>
		<edge from-layer="1091" from-port="1" to-layer="1095" to-port="0"/>
		<edge from-layer="1092" from-port="0" to-layer="1095" to-port="1"/>
		<edge from-layer="1093" from-port="0" to-layer="1095" to-port="2"/>
		<edge from-layer="1094" from-port="0" to-layer="1095" to-port="3"/>
		<edge from-layer="100" from-port="2" to-layer="1096" to-port="0"/>
		<edge from-layer="1096" from-port="1" to-layer="1100" to-port="0"/>
		<edge from-layer="1097" from-port="0" to-layer="1100" to-port="1"/>
		<edge from-layer="1098" from-port="0" to-layer="1100" to-port="2"/>
		<edge from-layer="1099" from-port="0" to-layer="1100" to-port="3"/>
		<edge from-layer="1095" from-port="4" to-layer="1101" to-port="0"/>
		<edge from-layer="1100" from-port="4" to-layer="1101" to-port="1"/>
		<edge from-layer="1101" from-port="2" to-layer="1103" to-port="0"/>
		<edge from-layer="1102" from-port="0" to-layer="1103" to-port="1"/>
		<edge from-layer="1025" from-port="2" to-layer="1104" to-port="0"/>
		<edge from-layer="1038" from-port="2" to-layer="1104" to-port="1"/>
		<edge from-layer="1051" from-port="2" to-layer="1104" to-port="2"/>
		<edge from-layer="1064" from-port="2" to-layer="1104" to-port="3"/>
		<edge from-layer="1077" from-port="2" to-layer="1104" to-port="4"/>
		<edge from-layer="1090" from-port="2" to-layer="1104" to-port="5"/>
		<edge from-layer="1103" from-port="2" to-layer="1104" to-port="6"/>
		<edge from-layer="720" from-port="7" to-layer="1105" to-port="0"/>
		<edge from-layer="1012" from-port="2" to-layer="1105" to-port="1"/>
		<edge from-layer="1104" from-port="7" to-layer="1105" to-port="2"/>
		<edge from-layer="1105" from-port="3" to-layer="1106" to-port="0"/>
	</edges>
	<meta_data>
		<MO_version value="custom_HEAD_149c43044cb1e8ed8cd4f3f196b23f7b3f129a36"/>
		<cli_parameters>
			<caffe_parser_path value="DIR"/>
			<data_type value="FP16"/>
			<disable_nhwc_to_nchw value="False"/>
			<disable_omitting_optional value="False"/>
			<disable_resnet_optimization value="False"/>
			<disable_weights_compression value="False"/>
			<enable_concat_optimization value="False"/>
			<enable_flattening_nested_params value="False"/>
			<enable_ssd_gluoncv value="False"/>
			<extensions value="DIR"/>
			<framework value="caffe"/>
			<freeze_placeholder_with_value value="{}"/>
			<generate_deprecated_IR_V7 value="False"/>
			<input value="data"/>
			<input_model value="DIR/model.caffemodel"/>
			<input_model_is_text value="False"/>
			<input_proto value="DIR/deploy.prototxt"/>
			<input_shape value="[1,3,384,672]"/>
			<k value="DIR/CustomLayersMapping.xml"/>
			<keep_shape_ops value="True"/>
			<legacy_ir_generation value="False"/>
			<legacy_mxnet_model value="False"/>
			<log_level value="ERROR"/>
			<mean_scale_values value="{'data': {'mean': array([ 88., 107., 102.]), 'scale': array([76.9231])}}"/>
			<mean_values value="data[88.0,107.0,102.0]"/>
			<model_name value="face-detection-adas-0001"/>
			<output value="['detection_out']"/>
			<output_dir value="DIR"/>
			<placeholder_data_types value="{}"/>
			<placeholder_shapes value="{'data': array([  1,   3, 384, 672])}"/>
			<progress value="False"/>
			<remove_memory value="False"/>
			<remove_output_softmax value="False"/>
			<reverse_input_channels value="False"/>
			<save_params_from_nd value="False"/>
			<scale_values value="data[76.9231]"/>
			<silent value="False"/>
			<static_shape value="False"/>
			<stream_output value="False"/>
			<transform value=""/>
			<unset unset_cli_parameters="batch, counts, disable_fusing, disable_gfusing, finegrain_fusing, input_checkpoint, input_meta_graph, input_symbol, mean_file, mean_file_offsets, move_to_preprocess, nd_prefix_name, pretrained_model_name, saved_model_dir, saved_model_tags, scale, tensorboard_logdir, tensorflow_custom_layer_libraries, tensorflow_custom_operations_config_update, tensorflow_object_detection_api_pipeline_config, tensorflow_use_custom_operations_config, transformations_config"/>
		</cli_parameters>
	</meta_data>
	<quantization_parameters>
		<config>{
		'compression': {
			'algorithms': [
				{
					'name': 'DefaultQuantization',
					'params': {
						'num_samples_for_tuning': 2000,
						'preset': 'performance',
						'stat_subset_size': 300,
						'use_layerwise_tuning': false
					}
				}
			],
			'dump_intermediate_model': true,
			'target_device': 'ANY'
		},
		'engine': {
			'models': [
				{
					'name': 'face-detection-adas-0001',
					'launchers': [
						{
							'framework': 'dlsdk',
							'adapter': 'ssd',
							'device': 'CPU'
						}
					],
					'datasets': [
						{
							'name': 'wider',
							'data_source': 'PATH',
							'annotation_conversion': {
								'converter': 'wider',
								'annotation_file': 'PATH'
							},
							'annotation': 'PATH',
							'dataset_meta': 'PATH',
							'preprocessing': [
								{
									'type': 'resize',
									'dst_width': 672,
									'dst_height': 384
								}
							],
							'postprocessing': [
								{
									'type': 'resize_prediction_boxes'
								},
								{
									'type': 'filter',
									'height_range': 100,
									'apply_to': 'annotation'
								}
							],
							'metrics': [
								{
									'type': 'map',
									'ignore_difficult': true,
									'include_boundaries': false,
									'allow_multiple_matches_per_ignored': true,
									'use_filtered_tp': true
								}
							],
							'_command_line_mapping': {
								'annotation_file': 'PATH'
							}
						}
					]
				}
			],
			'stat_requests_number': null,
			'eval_requests_number': null,
			'type': 'accuracy_checker'
		}
	}</config>
		<version value="1.0"/>
		<cli_params value="{'quantize': None, 'preset': None, 'model': None, 'weights': None, 'name': None, 'ac_config': None, 'max_drop': None, 'evaluate': False, 'output_dir': 'PATH', 'direct_dump': True, 'log_level': 'INFO', 'pbar': False, 'stream_output': False, 'keep_uncompressed_weights': False}"/>
	</quantization_parameters>
</net>
