import pandas as pd
import os
from datetime import datetime

# Directory containing the Excel files
directory = r'.\stock_list'

# List all Excel files in the directory
excel_files = [f for f in os.listdir(directory) if f.endswith('.xlsx')]

# Sort the files by their date part in ascending order
excel_files.sort(key=lambda date: datetime.strptime(date.replace('.xlsx', ''), "%d_%m_%Y"))

# Initialize lists to hold the data from column B and column D of each file
columns_b_data = []
columns_d_data = []
file_names = []

# Load each Excel file and extract the second and fourth columns
for file in excel_files:
    df = pd.read_excel(os.path.join(directory, file))
    col_b = df.iloc[:, 1]
    col_d = df.iloc[:, 3]
    columns_b_data.append(col_b)
    columns_d_data.append(col_d)
    file_names.append(file)

# Find common names across all files
common_names = set(columns_b_data[0])
for col_b in columns_b_data[1:]:
    common_names.intersection_update(set(col_b))

common_names = list(common_names)

# Initialize a DataFrame for the common names
df_common = pd.DataFrame(common_names, columns=['Common Names'])

# Extract date parts from filenames, sort them, and add the 4th column data
date_file_mapping = {}
for col_b, col_d, file_name in zip(columns_b_data, columns_d_data, file_names):
    date_part = file_name.replace('.xlsx', '')
    date_file_mapping[date_part] = col_d

# Sort dates in ascending order
sorted_dates = sorted(date_file_mapping.keys(), key=lambda date: datetime.strptime(date, "%d_%m_%Y"))

# Add the 4th column data to the DataFrame in sorted order
for date in sorted_dates:
    col_d = date_file_mapping[date]
    col_d_data = [col_d[col_b[col_b == name].index[0]] if name in col_b.values else None for name in common_names]
    df_common[date] = col_d_data

# Function to get names common in the last two files but not in the third last file
def find_common_names_last_two_not_in_third_last(columns_b_data):
    # Ensure there are at least three files
    if len(columns_b_data) < 3:
        return []

    # Get the last two columns
    last_two = set(columns_b_data[-1]).intersection(set(columns_b_data[-2]))
    # Get the third last column
    third_last = set(columns_b_data[-3])
    # Find names that are in the last two but not in the third last
    specific_common_names = last_two.difference(third_last)
    return list(specific_common_names)

specific_common_names = find_common_names_last_two_not_in_third_last(columns_b_data)

# Initialize a DataFrame for the specific common names
df_specific_common = pd.DataFrame(specific_common_names, columns=['Common Names'])

# Add the 4th column data to the DataFrame for the specific common names
for date in sorted_dates[-2:]:  # Only for the last two files
    col_d = date_file_mapping[date]
    col_b = columns_b_data[sorted_dates.index(date)]
    col_d_data = [col_d[col_b[col_b == name].index[0]] if name in col_b.values else None for name in specific_common_names]
    df_specific_common[date] = col_d_data

# Function to find new names in the last file
def find_new_names_in_last_file(columns_b_data):
    # Ensure there are at least three files
    if len(columns_b_data) < 3:
        return []

    # Get the last column
    last = set(columns_b_data[-1])
    # Get the second last and third last columns
    second_last = set(columns_b_data[-2])
    third_last = set(columns_b_data[-3])
    # Find names that are in the last column but not in the second and third last columns
    new_names = last.difference(second_last).difference(third_last)
    return list(new_names)

new_names_in_last_file = find_new_names_in_last_file(columns_b_data)

# Initialize a DataFrame for the new names in the last file
df_new_names_last = pd.DataFrame(new_names_in_last_file, columns=['New Names in Last File'])

# Add the 4th column data to the DataFrame for the new names in the last file
last_date = sorted_dates[-1]
col_d_last = date_file_mapping[last_date]
col_b_last = columns_b_data[-1]
col_d_data_last = [col_d_last[col_b_last[col_b_last == name].index[0]] if name in col_b_last.values else None for name in new_names_in_last_file]
df_new_names_last[last_date] = col_d_data_last

# Sort the DataFrame by the values in the last date column in descending order
df_new_names_last.sort_values(by=last_date, ascending=False, inplace=True)

# Get current date
current_date = datetime.now().strftime("%d_%m_%Y")

# Save common names, specific common names, and new names in the last file to a new Excel file with the current date
filename = f'common_names_{current_date}.xlsx'
with pd.ExcelWriter(filename) as writer:
    df_common.to_excel(writer, sheet_name='Common_Stocks', index=False)
    df_specific_common.to_excel(writer, sheet_name='Last_2_days', index=False)
    df_new_names_last.to_excel(writer, sheet_name='New_Stocks', index=False)

# Print the resulting DataFrames for verification
print("Resulting DataFrame (Common_Stocks):")
print(df_common)

print("\nResulting DataFrame (Last_2_days):")
print(df_specific_common)

print("\nResulting DataFrame (New_Stocks):")
print(df_new_names_last)






# import pandas as pd
# import os
# from datetime import datetime
#
# # Directory containing the Excel files
# directory = r'.\stock_list'
#
# # List all Excel files in the directory
# excel_files = [f for f in os.listdir(directory) if f.endswith('.xlsx')]
#
# # Sort the files by their date part in ascending order
# excel_files.sort(key=lambda date: datetime.strptime(date.replace('.xlsx', ''), "%d_%m_%Y"))
#
# # Initialize lists to hold the data from column B and column D of each file
# columns_b_data = []
# columns_d_data = []
# file_names = []
#
# # Load each Excel file and extract the second and fourth columns
# for file in excel_files:
#     df = pd.read_excel(os.path.join(directory, file))
#     col_b = df.iloc[:, 1]
#     col_d = df.iloc[:, 3]
#     columns_b_data.append(col_b)
#     columns_d_data.append(col_d)
#     file_names.append(file)
#
# # Find common names across all files
# common_names = set(columns_b_data[0])
# for col_b in columns_b_data[1:]:
#     common_names.intersection_update(set(col_b))
#
# common_names = list(common_names)
#
# # Initialize a DataFrame for the common names
# df_common = pd.DataFrame(common_names, columns=['Common Names'])
#
# # Extract date parts from filenames, sort them, and add the 4th column data
# date_file_mapping = {}
# for col_b, col_d, file_name in zip(columns_b_data, columns_d_data, file_names):
#     date_part = file_name.replace('.xlsx', '')
#     date_file_mapping[date_part] = col_d
#
# # Sort dates in ascending order
# sorted_dates = sorted(date_file_mapping.keys(), key=lambda date: datetime.strptime(date, "%d_%m_%Y"))
#
# # Add the 4th column data to the DataFrame in sorted order
# for date in sorted_dates:
#     col_d = date_file_mapping[date]
#     col_d_data = [col_d[col_b[col_b == name].index[0]] if name in col_b.values else None for name in common_names]
#     df_common[date] = col_d_data
#
# # Function to get names common in the last two files but not in the third last file
# def find_common_names_last_two_not_in_third_last(columns_b_data):
#     # Ensure there are at least three files
#     if len(columns_b_data) < 3:
#         return []
#
#     # Get the last two columns
#     last_two = set(columns_b_data[-1]).intersection(set(columns_b_data[-2]))
#     # Get the third last column
#     third_last = set(columns_b_data[-3])
#     # Find names that are in the last two but not in the third last
#     specific_common_names = last_two.difference(third_last)
#     return list(specific_common_names)
#
# specific_common_names = find_common_names_last_two_not_in_third_last(columns_b_data)
#
# # Initialize a DataFrame for the specific common names
# df_specific_common = pd.DataFrame(specific_common_names, columns=['Common Names'])
#
# # Add the 4th column data to the DataFrame for the specific common names
# for date in sorted_dates[-2:]:  # Only for the last two files
#     col_d = date_file_mapping[date]
#     col_b = columns_b_data[sorted_dates.index(date)]
#     col_d_data = [col_d[col_b[col_b == name].index[0]] if name in col_b.values else None for name in specific_common_names]
#     df_specific_common[date] = col_d_data
#
# # Get current date
# current_date = datetime.now().strftime("%d_%m_%Y")
#
# # Save common names and specific common names to a new Excel file with the current date
# filename = f'common_names_{current_date}.xlsx'
# with pd.ExcelWriter(filename) as writer:
#     df_common.to_excel(writer, sheet_name='Common Names', index=False)
#     df_specific_common.to_excel(writer, sheet_name='Common in Last 2 Not 3rd', index=False)
#
# # Print the resulting DataFrame for verification
# print("Resulting DataFrame (Common Names):")
# print(df_common)
#
# print("\nResulting DataFrame (Common in Last 2 Not 3rd):")
# print(df_specific_common)
