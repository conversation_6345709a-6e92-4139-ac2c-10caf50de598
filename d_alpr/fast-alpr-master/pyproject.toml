[tool.poetry]
name = "fast-alpr"
version = "0.2.0"
description = "Fast Automatic License Plate Recognition."
authors = ["ankandrew <<EMAIL>>"]
readme = "README.md"
license = "MIT"
keywords = [
    "image-processing",
    "computer-vision",
    "deep-learning",
    "object-detection",
    "plate-detection",
    "license-plate-ocr",
    "onnx",
]
classifiers = [
    "Typing :: Typed",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "Intended Audience :: Science/Research",
    "Operating System :: OS Independent",
    "Topic :: Software Development",
    "Topic :: Scientific/Engineering",
    "Topic :: Software Development :: Libraries",
    "Topic :: Software Development :: Build Tools",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
opencv-python = ">=********"
fast-plate-ocr = "^1.0.0"
open-image-models = "^0.4.0"

onnxruntime = { version = ">=1.19.2", optional = true }
onnxruntime-gpu = { version = ">=1.19.2", optional = true }
onnxruntime-openvino = { version = ">=1.19.2", optional = true }
onnxruntime-directml = { version = ">=1.19.2", optional = true }
onnxruntime-qnn = { version = ">=1.19.2", optional = true }

[tool.poetry.extras]
onnx = ["onnxruntime"]
onnx-gpu = ["onnxruntime-gpu"]
onnx-openvino = ["onnxruntime-openvino"]
onnx-directml = ["onnxruntime-directml"]
onnx-qnn = ["onnxruntime-qnn"]

[tool.poetry.group.test.dependencies]
pytest = "*"

[tool.poetry.group.dev.dependencies]
mypy = "*"
ruff = "*"
pylint = "*"
types-pyyaml = "*"
yamllint = "*"

[tool.poetry.group.docs.dependencies]
mkdocs-material = "*"
mkdocstrings = { version = "*", extras = ["python"] }
mike = "*"

[tool.ruff]
line-length = 100
target-version = "py310"

[tool.ruff.lint]
select = [
    # pycodestyle
    "E",
    "W",
    # Pyflakes
    "F",
    # pep8-naming
    "N",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # flake8-unused-arguments
    "ARG",
    # Pylint
    "PL",
    # Perflint
    "PERF",
    # Ruff-specific rules
    "RUF",
    # pandas-vet
    "PD",
]
ignore = ["N812", "PLR2004", "PD011"]
fixable = ["ALL"]
unfixable = []

[tool.ruff.lint.pylint]
max-args = 8

[tool.ruff.format]
line-ending = "lf"

[tool.mypy]
disable_error_code = "import-untyped"

[tool.pylint.typecheck]
generated-members = ["cv2.*"]
signature-mutators = [
    "click.decorators.option",
    "click.decorators.argument",
    "click.decorators.version_option",
    "click.decorators.help_option",
    "click.decorators.pass_context",
    "click.decorators.confirmation_option"
]

[tool.pylint.format]
max-line-length = 100

[tool.pylint."messages control"]
disable = ["missing-class-docstring", "missing-function-docstring"]

[tool.pylint.design]
max-args = 8
min-public-methods = 1

[tool.pylint.basic]
no-docstring-rgx = "^__|^test_"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
