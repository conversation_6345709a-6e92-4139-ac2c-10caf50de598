name: Test
on:
  workflow_call:  # Allows this workflow to be called by other workflows
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  test:
    name: Test
    strategy:
      fail-fast: false
      matrix:
        python-version: [ '3.10', '3.11', '3.12' ]
        os: [ 'ubuntu-latest' ]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4

      - name: Install poetry
        run: pipx install poetry

      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'poetry'

      - name: Install dependencies
        run: make install

      - name: Check format
        run: make check_format

      - name: Run linters
        run: make lint

      - name: Run tests
        run: make test
