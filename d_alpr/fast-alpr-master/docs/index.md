# FastALPR

[![ALPR Demo Animation](https://raw.githubusercontent.com/ankandrew/fast-alpr/f672fbbec2ddf86aabfc2afc0c45d1fa7612516c/assets/alpr.gif)](https://youtu.be/-TPJot7-HTs?t=652)

## Intro

**FastALPR** is a high-performance, customizable Automatic License Plate Recognition (ALPR) system. We offer fast and
efficient ONNX models by default, but you can easily swap in your own models if needed.

For Optical Character Recognition (**OCR**), we use [fast-plate-ocr](https://github.com/ankandrew/fast-plate-ocr) by
default, and for **license plate detection**, we
use [open-image-models](https://github.com/ankandrew/open-image-models). However, you can integrate any OCR or detection
model of your choice.

## Features

- **🔍 High Accuracy**: Uses advanced models for precise license plate detection and OCR.
- **🔧 Customizable**: Easily switch out detection and OCR models.
- **🚀 Easy to Use**: Quick setup with a simple API.
- **📦 Out-of-the-Box Models**: Includes ready-to-use detection and OCR models
- **⚡ Fast Performance**: Optimized with ONNX Runtime for speed.

<br>
<br>
