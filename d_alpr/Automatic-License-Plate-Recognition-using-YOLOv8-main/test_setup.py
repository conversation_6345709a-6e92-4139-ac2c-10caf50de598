#!/usr/bin/env python3
"""
Test script to verify the ALPR setup is working correctly
"""

import os
import sys
import cv2
import numpy as np

def test_imports():
    """Test if all required packages can be imported"""
    print("Testing imports...")
    
    try:
        from ultralytics import YOLO
        print("✓ ultralytics imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import ultralytics: {e}")
        return False
    
    try:
        import cv2
        print(f"✓ OpenCV imported successfully (version: {cv2.__version__})")
    except ImportError as e:
        print(f"✗ Failed to import OpenCV: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy imported successfully (version: {np.__version__})")
    except ImportError as e:
        print(f"✗ Failed to import NumPy: {e}")
        return False
    
    try:
        import easyocr
        print("✓ EasyOCR imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import EasyOCR: {e}")
        return False
    
    try:
        # Add the parent directory to the path to import sort
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(os.path.dirname(current_dir))
        sys.path.append(parent_dir)
        
        from sort.sort import Sort
        print("✓ SORT imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import SORT: {e}")
        return False
    
    try:
        from util import get_car, read_license_plate, write_csv
        print("✓ Utility functions imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import utility functions: {e}")
        return False
    
    return True

def test_models():
    """Test if model files exist and can be loaded"""
    print("\nTesting models...")
    
    # Check if model files exist
    yolo_model = 'yolov8n.pt'
    plate_model = 'license_plate_detector.pt'
    
    if not os.path.exists(yolo_model):
        print(f"✗ YOLO model not found: {yolo_model}")
        return False
    else:
        print(f"✓ YOLO model found: {yolo_model}")
    
    if not os.path.exists(plate_model):
        print(f"✗ License plate model not found: {plate_model}")
        return False
    else:
        print(f"✓ License plate model found: {plate_model}")
    
    # Try to load models
    try:
        from ultralytics import YOLO
        coco_model = YOLO(yolo_model)
        print("✓ YOLO model loaded successfully")
    except Exception as e:
        print(f"✗ Failed to load YOLO model: {e}")
        return False
    
    try:
        license_plate_detector = YOLO(plate_model)
        print("✓ License plate model loaded successfully")
    except Exception as e:
        print(f"✗ Failed to load license plate model: {e}")
        return False
    
    return True

def test_video_files():
    """Test if video files exist"""
    print("\nTesting video files...")
    
    video_paths = [
        '../input_images/alpr_test.mp4',
        '../input_images/alpr_test_1.mp4',
        '../input_images/license_plate.mp4',
        '../input_images/license_plate_1.mp4'
    ]
    
    found_videos = []
    for video_path in video_paths:
        if os.path.exists(video_path):
            print(f"✓ Video found: {video_path}")
            found_videos.append(video_path)
        else:
            print(f"✗ Video not found: {video_path}")
    
    if not found_videos:
        print("✗ No video files found for testing")
        return False
    
    # Test opening one video file
    try:
        cap = cv2.VideoCapture(found_videos[0])
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print(f"✓ Successfully opened and read from video: {found_videos[0]}")
                print(f"  Frame shape: {frame.shape}")
            else:
                print(f"✗ Could not read frame from video: {found_videos[0]}")
                return False
            cap.release()
        else:
            print(f"✗ Could not open video: {found_videos[0]}")
            return False
    except Exception as e:
        print(f"✗ Error testing video: {e}")
        return False
    
    return True

def test_ocr():
    """Test OCR functionality"""
    print("\nTesting OCR...")
    
    try:
        import easyocr
        reader = easyocr.Reader(['en'], gpu=False)
        
        # Create a simple test image with text
        test_image = np.ones((50, 200, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, 'ABC123', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Convert to grayscale for OCR
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        
        # Test OCR
        results = reader.readtext(gray)
        if results:
            print(f"✓ OCR test successful. Detected: {results[0][1]}")
        else:
            print("✗ OCR test failed - no text detected")
            return False
            
    except Exception as e:
        print(f"✗ OCR test failed: {e}")
        return False
    
    return True

def main():
    """Run all tests"""
    print("=" * 50)
    print("ALPR Setup Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Model Test", test_models),
        ("Video Test", test_video_files),
        ("OCR Test", test_ocr)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 20)
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Your ALPR setup is ready.")
        return True
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
