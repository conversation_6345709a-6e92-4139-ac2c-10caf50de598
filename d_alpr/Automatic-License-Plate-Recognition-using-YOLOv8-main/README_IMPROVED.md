# Improved Automatic License Plate Recognition using YOLOv8

This is an enhanced version of the original ALPR system with better error handling, visualization, and user experience.

## Features

- **Vehicle Detection**: Uses YOLOv8 to detect cars, motorcycles, buses, and trucks
- **License Plate Detection**: Custom trained YOLOv8 model for license plate detection
- **Object Tracking**: SORT algorithm for tracking vehicles across frames
- **OCR**: EasyOCR for reading license plate text
- **Visualization**: Real-time display and video output with annotations
- **Error Handling**: Robust error handling and progress reporting
- **Flexible Input**: Support for various video formats and configurable paths

## Installation

### 1. Clone the Repository

```bash
git clone https://github.com/Muhammad-Zeerak-Khan/Automatic-License-Plate-Recognition-using-YOLOv8.git
cd Automatic-License-Plate-Recognition-using-YOLOv8
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Download Required Models

The repository should include:
- `yolov8n.pt` - YOLOv8 nano model for vehicle detection
- `license_plate_detector.pt` - Custom trained model for license plate detection

If missing, download them from the original repository or train your own.

### 4. Setup SORT Tracking

Clone the SORT repository in the parent directory:

```bash
cd ..
git clone https://github.com/abewley/sort.git
cd Automatic-License-Plate-Recognition-using-YOLOv8
```

## Usage

### Test Setup

First, verify your setup is working correctly:

```bash
python test_setup.py
```

This will check:
- All required packages are installed
- Model files are present and loadable
- Video files are accessible
- OCR functionality is working

### Basic Usage

Run license plate recognition on a video:

```bash
python main.py
```

Or use the improved version:

```bash
python main_improved.py --video ../input_images/alpr_test.mp4 --output results.csv
```

### Advanced Usage

#### With Real-time Display

```bash
python main_improved.py --video ../input_images/alpr_test.mp4 --output results.csv --display
```

#### Save Annotated Video

```bash
python main_improved.py --video ../input_images/alpr_test.mp4 --output results.csv --save-video
```

#### Custom Models

```bash
python main_improved.py --video ../input_images/alpr_test.mp4 --vehicle-model yolov8s.pt --plate-model custom_plate_model.pt
```

### Visualization

Visualize results on the original video:

```bash
python visualize_improved.py --video ../input_images/alpr_test.mp4 --csv results.csv --output annotated_video.mp4
```

### Post-processing

1. **Add Missing Data** (interpolation):
```bash
python add_missing_data.py
```

2. **Original Visualization**:
```bash
python visualize.py
```

## File Structure

```
Automatic-License-Plate-Recognition-using-YOLOv8/
├── main.py                    # Original main script (improved)
├── main_improved.py           # Enhanced main script with OOP design
├── util.py                    # Utility functions
├── visualize.py              # Original visualization script
├── visualize_improved.py     # Enhanced visualization script
├── add_missing_data.py       # Data interpolation script
├── test_setup.py             # Setup verification script
├── requirements.txt          # Python dependencies
├── yolov8n.pt               # Vehicle detection model
├── license_plate_detector.pt # License plate detection model
└── README_IMPROVED.md       # This file
```

## Configuration

### Video Input

The system supports various video formats:
- MP4, AVI, MOV, MKV
- Local files or network streams
- Webcam input (use device index like `0`)

### Model Configuration

- **Vehicle Classes**: Cars (2), Motorcycles (3), Buses (5), Trucks (7)
- **Confidence Thresholds**: Adjustable in the code
- **OCR Languages**: Currently set to English, can be modified in `util.py`

### License Plate Format

The system is configured for a specific license plate format (7 characters: 2 letters, 2 numbers, 3 letters).
Modify the `license_complies_format()` function in `util.py` for different formats.

## Output

### CSV Results

The system generates a CSV file with the following columns:
- `frame_nmr`: Frame number
- `car_id`: Unique car ID from tracking
- `car_bbox`: Vehicle bounding box coordinates
- `license_plate_bbox`: License plate bounding box coordinates
- `license_plate_bbox_score`: License plate detection confidence
- `license_number`: Detected license plate text
- `license_number_score`: OCR confidence score

### Video Output

When using `--save-video`, the system creates an annotated video showing:
- Green boxes around detected vehicles
- Red boxes around detected license plates
- License plate text with confidence scores
- Vehicle tracking IDs

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed and SORT is in the correct location
2. **Model Loading Errors**: Verify model files exist and are not corrupted
3. **Video Loading Errors**: Check video file path and format compatibility
4. **OCR Errors**: EasyOCR requires internet connection for first-time setup

### Performance Tips

1. **GPU Acceleration**: Install CUDA-compatible PyTorch for faster inference
2. **Model Selection**: Use larger models (YOLOv8s, YOLOv8m) for better accuracy
3. **Video Resolution**: Lower resolution videos process faster
4. **Batch Processing**: Process multiple videos in sequence

## Customization

### License Plate Format

Modify `util.py` to support different license plate formats:

```python
def license_complies_format(text):
    # Customize this function for your region's license plate format
    # Current: 2 letters + 2 numbers + 3 letters (e.g., AB12CDE)
    pass
```

### OCR Languages

Change OCR language in `util.py`:

```python
reader = easyocr.Reader(['en', 'es'], gpu=False)  # English and Spanish
```

### Vehicle Classes

Modify vehicle classes in the main script:

```python
vehicles = [2, 3, 5, 7, 1]  # Add person (1) if needed
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Original repository by Muhammad-Zeerak-Khan
- YOLOv8 by Ultralytics
- SORT tracking algorithm by Alex Bewley
- EasyOCR by JaidedAI

## Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test setup script
3. Review the error messages carefully
4. Create an issue with detailed information
