#!/usr/bin/env python3
"""
Real-time License Plate Recognition with Visual Output
Shows detected license plates and OCR results in a window
"""

from ultralytics import YOLO
import cv2
import numpy as np
import os
import sys
import torch
import torch.serialization
import torch.nn as nn
from ultralytics.nn.tasks import DetectionModel

# Allow YOLOv8 model load in PyTorch 2.6+
torch.serialization.add_safe_globals([
    DetectionModel,
    nn.modules.container.Sequential,
    torch.nn.modules.container.Sequential,
    torch.nn.modules.conv.Conv2d,
    torch.nn.modules.batchnorm.BatchNorm2d,
    torch.nn.modules.activation.SiLU,
    torch.nn.modules.pooling.AdaptiveAvgPool2d,
    torch.nn.modules.linear.Linear,
    torch.nn.modules.dropout.Dropout,
    torch.nn.modules.upsampling.Upsample
])

# Add the parent directory to the path to import sort
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(parent_dir)

from sort.sort import Sort
from util import get_car, read_license_plate

def draw_border(img, top_left, bottom_right, color=(0, 255, 0), thickness=10, line_length_x=200, line_length_y=200):
    """Draw a stylized border around a bounding box"""
    x1, y1 = top_left
    x2, y2 = bottom_right

    # Ensure coordinates are within image bounds
    h, w = img.shape[:2]
    x1, y1, x2, y2 = max(0, x1), max(0, y1), min(w, x2), min(h, y2)
    
    # Adjust line lengths based on box size
    box_width = x2 - x1
    box_height = y2 - y1
    line_length_x = min(line_length_x, box_width // 3)
    line_length_y = min(line_length_y, box_height // 3)

    # Top-left corner
    cv2.line(img, (x1, y1), (x1, y1 + line_length_y), color, thickness)
    cv2.line(img, (x1, y1), (x1 + line_length_x, y1), color, thickness)

    # Bottom-left corner
    cv2.line(img, (x1, y2), (x1, y2 - line_length_y), color, thickness)
    cv2.line(img, (x1, y2), (x1 + line_length_x, y2), color, thickness)

    # Top-right corner
    cv2.line(img, (x2, y1), (x2 - line_length_x, y1), color, thickness)
    cv2.line(img, (x2, y1), (x2, y1 + line_length_y), color, thickness)

    # Bottom-right corner
    cv2.line(img, (x2, y2), (x2 - line_length_x, y2), color, thickness)
    cv2.line(img, (x2, y2), (x2, y2 - line_length_y), color, thickness)

    return img

def main():
    # Initialize tracker
    mot_tracker = Sort()

    # Load models
    print("Loading YOLO models...")
    try:
        # Set torch.load to use weights_only=False for compatibility
        import torch
        original_load = torch.load
        torch.load = lambda *args, **kwargs: original_load(*args, **kwargs, weights_only=False)

        coco_model = YOLO('yolov8n.pt')
        license_plate_detector = YOLO('license_plate_detector.pt')

        # Restore original torch.load
        torch.load = original_load

        print("✓ Models loaded successfully!")
    except Exception as e:
        print(f"✗ Error loading models: {e}")
        print("Trying alternative loading method...")
        try:
            # Alternative: Load with explicit weights_only=False
            coco_model = YOLO('yolov8n.pt')
            license_plate_detector = YOLO('license_plate_detector.pt')
            print("✓ Models loaded successfully with alternative method!")
        except Exception as e2:
            print(f"✗ Alternative loading also failed: {e2}")
            return

    # Load video
    video_path = os.path.join('..', 'input_images', 'alpr_test.mp4')
    if not os.path.exists(video_path):
        video_path = r'E:\d_rtsp\d_alpr\input_images\alpr_test.mp4'
        
    cap = cv2.VideoCapture(video_path)
    print(f"Loading video: {video_path}")

    if not cap.isOpened():
        print(f"✗ Error: Could not open video file {video_path}")
        return

    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"Video properties: {width}x{height}, {fps} FPS, {total_frames} frames")
    print("Controls:")
    print("  'q' - Quit")
    print("  'SPACE' - Pause/Resume")
    print("  'r' - Reset to beginning")
    print("=" * 50)

    vehicles = [2, 3, 5, 7]  # COCO IDs for car, motorcycle, bus, truck
    frame_nmr = 0
    paused = False
    detected_plates = {}  # Store detected plates for display

    while True:
        if not paused:
            ret, frame = cap.read()
            if not ret:
                print("End of video reached. Press 'r' to restart or 'q' to quit.")
                paused = True
                continue
            frame_nmr += 1
        else:
            # Use the last frame when paused
            if 'frame' not in locals():
                continue

        # Create display frame
        display_frame = frame.copy()
        
        if not paused:
            try:
                # Detect vehicles
                detections = coco_model(frame, verbose=False)[0]
                detections_ = []
                
                if detections.boxes is not None:
                    for detection in detections.boxes.data.tolist():
                        x1, y1, x2, y2, score, class_id = detection
                        if int(class_id) in vehicles and score > 0.3:  # Confidence threshold
                            detections_.append([x1, y1, x2, y2, score])
                
                # Update tracker
                if len(detections_) > 0:
                    track_ids = mot_tracker.update(np.asarray(detections_))
                else:
                    track_ids = np.empty((0, 5))

                # Detect license plates
                license_plates = license_plate_detector(frame, verbose=False)[0]
                
                if license_plates.boxes is not None:
                    for license_plate in license_plates.boxes.data.tolist():
                        x1, y1, x2, y2, score, class_id = license_plate
                        
                        if score > 0.3:  # Confidence threshold for license plates
                            # Find associated car
                            xcar1, ycar1, xcar2, ycar2, car_id = get_car(license_plate, track_ids)
                            
                            if car_id != -1:
                                # Crop license plate
                                license_plate_crop = frame[int(y1):int(y2), int(x1):int(x2)]
                                
                                if license_plate_crop.size > 0 and license_plate_crop.shape[0] > 10 and license_plate_crop.shape[1] > 10:
                                    # Process license plate
                                    license_plate_crop_gray = cv2.cvtColor(license_plate_crop, cv2.COLOR_BGR2GRAY)
                                    _, license_plate_crop_thresh = cv2.threshold(license_plate_crop_gray, 64, 255, cv2.THRESH_BINARY_INV)
                                    
                                    # Read license plate text
                                    license_plate_text, license_plate_text_score = read_license_plate(license_plate_crop_thresh)
                                    
                                    if license_plate_text is not None and license_plate_text_score > 0.1:
                                        # Store detection
                                        detected_plates[car_id] = {
                                            'car_bbox': [xcar1, ycar1, xcar2, ycar2],
                                            'plate_bbox': [x1, y1, x2, y2],
                                            'text': license_plate_text,
                                            'confidence': license_plate_text_score,
                                            'frame': frame_nmr
                                        }
                                        print(f"Frame {frame_nmr}: Car {car_id} - License Plate: '{license_plate_text}' (Confidence: {license_plate_text_score:.2f})")

            except Exception as e:
                print(f"Error processing frame {frame_nmr}: {e}")

        # Draw vehicle tracks
        if 'track_ids' in locals():
            for track in track_ids:
                x1, y1, x2, y2, track_id = track
                cv2.rectangle(display_frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
                cv2.putText(display_frame, f'Car {int(track_id)}', (int(x1), int(y1)-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # Draw license plate detections
        for car_id, detection in detected_plates.items():
            # Only show recent detections (within last 30 frames)
            if frame_nmr - detection['frame'] <= 30:
                # Draw car bounding box
                xcar1, ycar1, xcar2, ycar2 = detection['car_bbox']
                cv2.rectangle(display_frame, (int(xcar1), int(ycar1)), (int(xcar2), int(ycar2)), (255, 0, 0), 3)
                
                # Draw license plate with stylized border
                x1, y1, x2, y2 = detection['plate_bbox']
                draw_border(display_frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 0, 255), 25, 50, 50)
                
                # Add license plate text with background
                text = f"{detection['text']} ({detection['confidence']:.2f})"
                text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
                
                # Position text above the license plate
                text_x = int(x1)
                text_y = int(y1) - 15
                
                # Ensure text is within frame bounds
                if text_y < text_size[1]:
                    text_y = int(y2) + text_size[1] + 15
                
                # Draw text background
                cv2.rectangle(display_frame, 
                            (text_x - 5, text_y - text_size[1] - 5), 
                            (text_x + text_size[0] + 5, text_y + 5), 
                            (0, 0, 0), -1)
                
                # Draw text
                cv2.putText(display_frame, text, (text_x, text_y), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

        # Add frame info
        info_text = f"Frame: {frame_nmr}/{total_frames}"
        if paused:
            info_text += " [PAUSED]"
        cv2.putText(display_frame, info_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Add detection count
        recent_detections = len([d for d in detected_plates.values() if frame_nmr - d['frame'] <= 30])
        cv2.putText(display_frame, f"Active Detections: {recent_detections}", (10, 70), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

        # Display frame
        cv2.imshow('License Plate Recognition - Real Time', display_frame)
        
        # Handle key presses
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord(' '):  # Spacebar to pause/resume
            paused = not paused
            print("PAUSED" if paused else "RESUMED")
        elif key == ord('r'):  # Reset video
            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            frame_nmr = 0
            detected_plates.clear()
            paused = False
            print("Video reset to beginning")

    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    
    # Print summary
    total_unique_plates = len(set(d['text'] for d in detected_plates.values()))
    print(f"\nSummary:")
    print(f"Total frames processed: {frame_nmr}")
    print(f"Total license plate detections: {len(detected_plates)}")
    print(f"Unique license plates detected: {total_unique_plates}")
    
    if detected_plates:
        print("\nDetected License Plates:")
        for car_id, detection in detected_plates.items():
            print(f"  Car {car_id}: {detection['text']} (Confidence: {detection['confidence']:.2f})")

if __name__ == '__main__':
    main()
