#!/usr/bin/env python3
"""
Improved License Plate Recognition using YOLOv8
Author: AI Assistant
Description: Enhanced version with better error handling, visualization, and configuration
"""

import numpy as np
import cv2
import os
import sys
import argparse
import time
from ultralytics import YOLO
import torch.serialization
from ultralytics.nn.tasks import DetectionModel

# Allow YOLOv8 model load in PyTorch 2.6+
torch.serialization.add_safe_globals([DetectionModel])

# Add the parent directory to the path to import sort
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(parent_dir)

from sort.sort import Sort
from util import get_car, read_license_plate, write_csv

class LicensePlateRecognizer:
    def __init__(self, vehicle_model_path='yolov8n.pt', plate_model_path='license_plate_detector.pt'):
        """
        Initialize the License Plate Recognizer
        
        Args:
            vehicle_model_path (str): Path to vehicle detection model
            plate_model_path (str): Path to license plate detection model
        """
        self.vehicle_model_path = vehicle_model_path
        self.plate_model_path = plate_model_path
        self.mot_tracker = Sort()
        self.results = {}
        self.vehicles = [2, 3, 5, 7]  # COCO IDs for car, motorcycle, bus, truck
        
        # Load models
        self.load_models()
    
    def load_models(self):
        """Load YOLO models for vehicle and license plate detection"""
        try:
            print("Loading YOLO models...")
            self.coco_model = YOLO(self.vehicle_model_path)
            self.license_plate_detector = YOLO(self.plate_model_path)
            print("✓ Models loaded successfully!")
        except Exception as e:
            print(f"✗ Error loading models: {e}")
            raise
    
    def process_video(self, video_path, output_csv='test.csv', display=False, save_video=False):
        """
        Process video for license plate recognition
        
        Args:
            video_path (str): Path to input video file
            output_csv (str): Path to output CSV file
            display (bool): Whether to display video with detections
            save_video (bool): Whether to save output video with annotations
        
        Returns:
            bool: True if successful, False otherwise
        """
        print(f"Starting license plate recognition on: {video_path}")
        
        # Open video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"✗ Error: Could not open video file {video_path}")
            return False
        
        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Video properties: {width}x{height}, {fps} FPS, {total_frames} frames")
        
        # Setup video writer if saving output
        out = None
        if save_video:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            output_video_path = os.path.splitext(output_csv)[0] + '_output.mp4'
            out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
            print(f"Output video will be saved to: {output_video_path}")
        
        frame_nmr = -1
        start_time = time.time()
        
        print("Processing frames...")
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            frame_nmr += 1
            self.results[frame_nmr] = {}
            
            # Print progress every 30 frames
            if frame_nmr % 30 == 0:
                elapsed = time.time() - start_time
                fps_current = frame_nmr / elapsed if elapsed > 0 else 0
                print(f"Processing frame {frame_nmr}/{total_frames} (FPS: {fps_current:.1f})")

            # Detect vehicles
            try:
                detections = self.coco_model(frame)[0]
                detections_ = []
                
                if detections.boxes is not None:
                    for detection in detections.boxes.data.tolist():
                        x1, y1, x2, y2, score, class_id = detection
                        if int(class_id) in self.vehicles:
                            detections_.append([x1, y1, x2, y2, score])
                
                # Update tracker
                if len(detections_) > 0:
                    track_ids = self.mot_tracker.update(np.asarray(detections_))
                else:
                    track_ids = np.empty((0, 5))

            except Exception as e:
                print(f"✗ Error in vehicle detection at frame {frame_nmr}: {e}")
                continue

            # Detect license plates
            try:
                license_plates = self.license_plate_detector(frame)[0]
                
                if license_plates.boxes is not None:
                    for license_plate in license_plates.boxes.data.tolist():
                        x1, y1, x2, y2, score, class_id = license_plate
                        
                        # Find associated car
                        xcar1, ycar1, xcar2, ycar2, car_id = get_car(license_plate, track_ids)
                        
                        if car_id != -1:
                            # Crop license plate
                            license_plate_crop = frame[int(y1):int(y2), int(x1):int(x2), :]
                            
                            if license_plate_crop.size > 0:
                                # Process license plate
                                license_plate_crop_gray = cv2.cvtColor(license_plate_crop, cv2.COLOR_BGR2GRAY)
                                _, license_plate_crop_thresh = cv2.threshold(license_plate_crop_gray, 64, 255, cv2.THRESH_BINARY_INV)
                                
                                # Read license plate text
                                license_plate_text, license_plate_text_score = read_license_plate(license_plate_crop_thresh)
                                
                                if license_plate_text is not None:
                                    self.results[frame_nmr][car_id] = {
                                        'car': {'bbox': [xcar1, ycar1, xcar2, ycar2]},
                                        'license_plate': {
                                            'bbox': [x1, y1, x2, y2],
                                            'text': license_plate_text,
                                            'bbox_score': score,
                                            'text_score': license_plate_text_score
                                        }
                                    }
                                    print(f"Frame {frame_nmr}: Detected license plate '{license_plate_text}' with confidence {license_plate_text_score:.2f}")

            except Exception as e:
                print(f"✗ Error in license plate detection at frame {frame_nmr}: {e}")
                continue
            
            # Visualize detections
            if display or save_video:
                display_frame = self.draw_detections(frame.copy(), track_ids, frame_nmr)
                
                if display:
                    cv2.imshow('License Plate Recognition', display_frame)
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                
                if save_video and out is not None:
                    out.write(display_frame)

        # Cleanup
        cap.release()
        if out is not None:
            out.release()
        if display:
            cv2.destroyAllWindows()
        
        # Save results
        print(f"Processing complete. Saving results to {output_csv}")
        write_csv(self.results, output_csv)
        print(f"✓ Results saved to {output_csv}")
        
        # Print summary
        total_detections = sum(len(frame_data) for frame_data in self.results.values())
        print(f"Summary: Processed {frame_nmr + 1} frames, detected {total_detections} license plates")
        
        return True
    
    def draw_detections(self, frame, track_ids, frame_nmr):
        """
        Draw vehicle tracks and license plate detections on frame
        
        Args:
            frame: Input frame
            track_ids: Vehicle tracking results
            frame_nmr: Current frame number
        
        Returns:
            Annotated frame
        """
        # Draw vehicle tracks
        for track in track_ids:
            x1, y1, x2, y2, track_id = track
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
            cv2.putText(frame, f'Car {int(track_id)}', (int(x1), int(y1)-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # Draw license plate detections
        if frame_nmr in self.results:
            for car_id in self.results[frame_nmr]:
                if 'license_plate' in self.results[frame_nmr][car_id]:
                    lp = self.results[frame_nmr][car_id]['license_plate']
                    x1, y1, x2, y2 = lp['bbox']
                    text = lp['text']
                    confidence = lp['text_score']
                    
                    cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 0, 255), 2)
                    cv2.putText(frame, f'{text} ({confidence:.2f})', (int(x1), int(y1)-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        return frame

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description='License Plate Recognition using YOLOv8')
    parser.add_argument('--video', type=str, default='../input_images/alpr_test.mp4',
                       help='Path to input video file')
    parser.add_argument('--output', type=str, default='test.csv',
                       help='Path to output CSV file')
    parser.add_argument('--display', action='store_true',
                       help='Display video with detections')
    parser.add_argument('--save-video', action='store_true',
                       help='Save output video with annotations')
    parser.add_argument('--vehicle-model', type=str, default='yolov8n.pt',
                       help='Path to vehicle detection model')
    parser.add_argument('--plate-model', type=str, default='license_plate_detector.pt',
                       help='Path to license plate detection model')
    
    args = parser.parse_args()
    
    # Check if video file exists
    if not os.path.exists(args.video):
        print(f"✗ Error: Video file not found: {args.video}")
        print("Available video files:")
        video_dir = os.path.dirname(args.video)
        if os.path.exists(video_dir):
            for f in os.listdir(video_dir):
                if f.endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    print(f"  {os.path.join(video_dir, f)}")
        sys.exit(1)
    
    # Initialize recognizer
    try:
        recognizer = LicensePlateRecognizer(args.vehicle_model, args.plate_model)
    except Exception as e:
        print(f"✗ Failed to initialize recognizer: {e}")
        sys.exit(1)
    
    # Process video
    success = recognizer.process_video(args.video, args.output, args.display, args.save_video)
    
    if success:
        print("✓ License plate recognition completed successfully!")
    else:
        print("✗ License plate recognition failed!")
        sys.exit(1)

if __name__ == '__main__':
    main()
