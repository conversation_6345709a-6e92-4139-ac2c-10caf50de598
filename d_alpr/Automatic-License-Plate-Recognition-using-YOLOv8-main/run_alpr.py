#!/usr/bin/env python3
"""
Simple script to run ALPR with different configurations
"""

import os
import sys
import argparse
import subprocess

def run_test():
    """Run setup test"""
    print("Running setup test...")
    result = subprocess.run([sys.executable, 'test_setup.py'], capture_output=True, text=True)
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    return result.returncode == 0

def run_basic_alpr(video_path):
    """Run basic ALPR without display"""
    print(f"Running basic ALPR on {video_path}...")
    cmd = [sys.executable, 'main_improved.py', '--video', video_path, '--output', 'results.csv']
    result = subprocess.run(cmd)
    return result.returncode == 0

def run_alpr_with_display(video_path):
    """Run ALPR with real-time display"""
    print(f"Running ALPR with display on {video_path}...")
    cmd = [sys.executable, 'main_improved.py', '--video', video_path, '--output', 'results.csv', '--display']
    result = subprocess.run(cmd)
    return result.returncode == 0

def run_alpr_with_video_output(video_path):
    """Run ALPR and save annotated video"""
    print(f"Running ALPR with video output on {video_path}...")
    cmd = [sys.executable, 'main_improved.py', '--video', video_path, '--output', 'results.csv', '--save-video']
    result = subprocess.run(cmd)
    return result.returncode == 0

def run_visualization(video_path, csv_path):
    """Run visualization on existing results"""
    print(f"Running visualization...")
    cmd = [sys.executable, 'visualize_improved.py', '--video', video_path, '--csv', csv_path, '--output', 'annotated_output.mp4']
    result = subprocess.run(cmd)
    return result.returncode == 0

def find_video_files():
    """Find available video files"""
    video_dirs = ['../input_images', '.', '..']
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
    
    found_videos = []
    for video_dir in video_dirs:
        if os.path.exists(video_dir):
            for file in os.listdir(video_dir):
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    found_videos.append(os.path.join(video_dir, file))
    
    return found_videos

def interactive_mode():
    """Interactive mode for easy usage"""
    print("=" * 60)
    print("Automatic License Plate Recognition - Interactive Mode")
    print("=" * 60)
    
    # Test setup first
    print("\n1. Testing setup...")
    if not run_test():
        print("Setup test failed. Please check the errors above.")
        return False
    
    # Find video files
    videos = find_video_files()
    if not videos:
        print("No video files found. Please add video files to the input_images directory.")
        return False
    
    print(f"\n2. Found {len(videos)} video file(s):")
    for i, video in enumerate(videos):
        print(f"   {i+1}. {video}")
    
    # Select video
    while True:
        try:
            choice = input(f"\nSelect video (1-{len(videos)}): ")
            video_idx = int(choice) - 1
            if 0 <= video_idx < len(videos):
                selected_video = videos[video_idx]
                break
            else:
                print("Invalid choice. Please try again.")
        except ValueError:
            print("Please enter a number.")
    
    print(f"\nSelected video: {selected_video}")
    
    # Select mode
    print("\n3. Select processing mode:")
    print("   1. Basic processing (no display)")
    print("   2. Processing with real-time display")
    print("   3. Processing with video output")
    print("   4. Visualization only (requires existing CSV)")
    
    while True:
        try:
            mode = input("Select mode (1-4): ")
            mode_idx = int(mode)
            if 1 <= mode_idx <= 4:
                break
            else:
                print("Invalid choice. Please try again.")
        except ValueError:
            print("Please enter a number.")
    
    # Run selected mode
    print(f"\n4. Running mode {mode_idx}...")
    
    if mode_idx == 1:
        success = run_basic_alpr(selected_video)
    elif mode_idx == 2:
        success = run_alpr_with_display(selected_video)
    elif mode_idx == 3:
        success = run_alpr_with_video_output(selected_video)
    elif mode_idx == 4:
        csv_path = input("Enter path to CSV file (default: results.csv): ").strip()
        if not csv_path:
            csv_path = 'results.csv'
        if not os.path.exists(csv_path):
            print(f"CSV file not found: {csv_path}")
            return False
        success = run_visualization(selected_video, csv_path)
    
    if success:
        print("\n✓ Processing completed successfully!")
        
        # Show output files
        if os.path.exists('results.csv'):
            print(f"✓ Results saved to: results.csv")
        if os.path.exists('results_output.mp4'):
            print(f"✓ Annotated video saved to: results_output.mp4")
        if os.path.exists('annotated_output.mp4'):
            print(f"✓ Visualization video saved to: annotated_output.mp4")
    else:
        print("\n✗ Processing failed!")
    
    return success

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='ALPR Runner Script')
    parser.add_argument('--interactive', action='store_true',
                       help='Run in interactive mode')
    parser.add_argument('--test', action='store_true',
                       help='Run setup test only')
    parser.add_argument('--video', type=str,
                       help='Video file to process')
    parser.add_argument('--mode', type=str, choices=['basic', 'display', 'video', 'viz'],
                       default='basic', help='Processing mode')
    parser.add_argument('--csv', type=str, default='results.csv',
                       help='CSV file for visualization mode')
    
    args = parser.parse_args()
    
    if args.interactive:
        return interactive_mode()
    
    if args.test:
        return run_test()
    
    if not args.video:
        print("Please specify a video file with --video or use --interactive mode")
        return False
    
    if not os.path.exists(args.video):
        print(f"Video file not found: {args.video}")
        return False
    
    # Run based on mode
    if args.mode == 'basic':
        return run_basic_alpr(args.video)
    elif args.mode == 'display':
        return run_alpr_with_display(args.video)
    elif args.mode == 'video':
        return run_alpr_with_video_output(args.video)
    elif args.mode == 'viz':
        if not os.path.exists(args.csv):
            print(f"CSV file not found: {args.csv}")
            return False
        return run_visualization(args.video, args.csv)

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
