from ultralytics import YOLO
import cv2
import numpy as np
import os
import sys

# Add the parent directory to the path to import sort
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(parent_dir)

import util
from sort.sort import Sort
from util import get_car, read_license_plate, write_csv


results = {}

mot_tracker = Sort()

# load models
coco_model = YOLO('yolov8n.pt')
license_plate_detector = YOLO('license_plate_detector.pt')

# load video
video_path = os.path.join('..', 'input_images', 'alpr_test.mp4')
if not os.path.exists(video_path):
    video_path = r'E:\d_rtsp\d_alpr\input_images\alpr_test.mp4'

cap = cv2.VideoCapture(video_path)
print(f"Loading video: {video_path}")

vehicles = [2, 3, 5, 7]

# Get video properties for progress tracking
total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
print(f"Total frames to process: {total_frames}")

# read frames
frame_nmr = -1
ret = True
while ret:
    frame_nmr += 1
    ret, frame = cap.read()
    if ret:
        # Print progress every 30 frames
        if frame_nmr % 30 == 0:
            print(f"Processing frame {frame_nmr}/{total_frames}")
        results[frame_nmr] = {}

        try:
            # detect vehicles
            detections = coco_model(frame)[0]
            detections_ = []

            if detections.boxes is not None:
                for detection in detections.boxes.data.tolist():
                    x1, y1, x2, y2, score, class_id = detection
                    if int(class_id) in vehicles:
                        detections_.append([x1, y1, x2, y2, score])

            # track vehicles
            if len(detections_) > 0:
                track_ids = mot_tracker.update(np.asarray(detections_))
            else:
                track_ids = np.empty((0, 5))
        except Exception as e:
            print(f"Error in vehicle detection at frame {frame_nmr}: {e}")
            continue

        try:
            # detect license plates
            license_plates = license_plate_detector(frame)[0]

            if license_plates.boxes is not None:
                for license_plate in license_plates.boxes.data.tolist():
                    x1, y1, x2, y2, score, class_id = license_plate

                    # assign license plate to car
                    xcar1, ycar1, xcar2, ycar2, car_id = get_car(license_plate, track_ids)

                    if car_id != -1:
                        # crop license plate
                        license_plate_crop = frame[int(y1):int(y2), int(x1): int(x2), :]

                        if license_plate_crop.size > 0:
                            # process license plate
                            license_plate_crop_gray = cv2.cvtColor(license_plate_crop, cv2.COLOR_BGR2GRAY)
                            _, license_plate_crop_thresh = cv2.threshold(license_plate_crop_gray, 64, 255, cv2.THRESH_BINARY_INV)

                            # read license plate number
                            license_plate_text, license_plate_text_score = read_license_plate(license_plate_crop_thresh)

                            if license_plate_text is not None:
                                results[frame_nmr][car_id] = {'car': {'bbox': [xcar1, ycar1, xcar2, ycar2]},
                                                              'license_plate': {'bbox': [x1, y1, x2, y2],
                                                                                'text': license_plate_text,
                                                                                'bbox_score': score,
                                                                                'text_score': license_plate_text_score}}
                                print(f"Frame {frame_nmr}: Detected license plate '{license_plate_text}' with confidence {license_plate_text_score:.2f}")
        except Exception as e:
            print(f"Error in license plate detection at frame {frame_nmr}: {e}")
            continue

# Cleanup
cap.release()

# write results
output_file = './test.csv'
write_csv(results, output_file)
print(f"Results saved to {output_file}")

# Print summary
total_detections = sum(len(frame_data) for frame_data in results.values())
print(f"Summary: Processed {frame_nmr + 1} frames, detected {total_detections} license plates")
print("License plate recognition completed successfully!")