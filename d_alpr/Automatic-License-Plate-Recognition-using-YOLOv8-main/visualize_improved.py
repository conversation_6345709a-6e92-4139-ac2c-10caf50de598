#!/usr/bin/env python3
"""
Improved visualization script for license plate recognition results
"""

import cv2
import pandas as pd
import numpy as np
import ast
import argparse
import os

def draw_border(img, top_left, bottom_right, color=(0, 255, 0), thickness=10, line_length_x=200, line_length_y=200):
    """
    Draw a border around a bounding box
    
    Args:
        img: Input image
        top_left: Top left corner coordinates
        bottom_right: Bottom right corner coordinates
        color: Border color
        thickness: Border thickness
        line_length_x: Length of horizontal lines
        line_length_y: Length of vertical lines
    """
    x1, y1 = top_left
    x2, y2 = bottom_right

    cv2.line(img, (x1, y1), (x1, y1 + line_length_y), color, thickness)  # top-left
    cv2.line(img, (x1, y1), (x1 + line_length_x, y1), color, thickness)

    cv2.line(img, (x1, y2), (x1, y2 - line_length_y), color, thickness)  # bottom-left
    cv2.line(img, (x1, y2), (x1 + line_length_x, y2), color, thickness)

    cv2.line(img, (x2, y1), (x2 - line_length_x, y1), color, thickness)  # top-right
    cv2.line(img, (x2, y1), (x2, y1 + line_length_y), color, thickness)

    cv2.line(img, (x2, y2), (x2 - line_length_x, y2), color, thickness)  # bottom-right
    cv2.line(img, (x2, y2), (x2, y2 - line_length_y), color, thickness)

    return img

def visualize_results(video_path, csv_path, output_path=None, display=True):
    """
    Visualize license plate recognition results on video
    
    Args:
        video_path (str): Path to input video
        csv_path (str): Path to CSV results file
        output_path (str): Path to save output video (optional)
        display (bool): Whether to display video in real-time
    """
    # Read CSV results
    try:
        results = pd.read_csv(csv_path)
        print(f"Loaded {len(results)} detection results from {csv_path}")
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return False

    # Open video
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"Error opening video: {video_path}")
        return False

    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"Video properties: {width}x{height}, {fps} FPS, {total_frames} frames")

    # Setup video writer if output path is provided
    out = None
    if output_path:
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        print(f"Output video will be saved to: {output_path}")

    frame_nmr = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # Get detections for current frame
        frame_results = results[results['frame_nmr'] == frame_nmr]

        for _, row in frame_results.iterrows():
            try:
                # Parse bounding boxes
                car_bbox = ast.literal_eval(row['car_bbox'])
                license_plate_bbox = ast.literal_eval(row['license_plate_bbox'])
                
                # Get license plate info
                license_number = row['license_number']
                license_score = float(row['license_number_score'])
                
                # Draw car bounding box
                cv2.rectangle(frame, 
                            (int(car_bbox[0]), int(car_bbox[1])), 
                            (int(car_bbox[2]), int(car_bbox[3])), 
                            (0, 255, 0), 2)
                
                # Draw license plate bounding box with border
                draw_border(frame, 
                          (int(license_plate_bbox[0]), int(license_plate_bbox[1])), 
                          (int(license_plate_bbox[2]), int(license_plate_bbox[3])), 
                          (0, 0, 255), 25, 
                          line_length_x=200, line_length_y=200)
                
                # Add license plate text
                text = f"{license_number} ({license_score:.2f})"
                text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 1.2, 3)[0]
                
                # Position text above the license plate
                text_x = int(license_plate_bbox[0])
                text_y = int(license_plate_bbox[1]) - 10
                
                # Ensure text is within frame bounds
                if text_y < text_size[1]:
                    text_y = int(license_plate_bbox[3]) + text_size[1] + 10
                
                # Draw text background
                cv2.rectangle(frame, 
                            (text_x, text_y - text_size[1] - 10), 
                            (text_x + text_size[0] + 10, text_y + 5), 
                            (0, 0, 0), -1)
                
                # Draw text
                cv2.putText(frame, text, (text_x + 5, text_y), 
                          cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)
                
                # Add car ID
                car_id = row['car_id']
                cv2.putText(frame, f"Car {car_id}", 
                          (int(car_bbox[0]), int(car_bbox[1]) - 10), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                
            except Exception as e:
                print(f"Error processing detection at frame {frame_nmr}: {e}")
                continue

        # Add frame number
        cv2.putText(frame, f"Frame: {frame_nmr}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        # Display frame
        if display:
            cv2.imshow('License Plate Recognition Results', frame)
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord(' '):  # Spacebar to pause
                cv2.waitKey(0)

        # Write frame to output video
        if out:
            out.write(frame)

        frame_nmr += 1

    # Cleanup
    cap.release()
    if out:
        out.release()
    if display:
        cv2.destroyAllWindows()

    print(f"Visualization complete. Processed {frame_nmr} frames.")
    return True

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description='Visualize License Plate Recognition Results')
    parser.add_argument('--video', type=str, required=True,
                       help='Path to input video file')
    parser.add_argument('--csv', type=str, required=True,
                       help='Path to CSV results file')
    parser.add_argument('--output', type=str, default=None,
                       help='Path to save output video (optional)')
    parser.add_argument('--no-display', action='store_true',
                       help='Do not display video in real-time')
    
    args = parser.parse_args()
    
    # Check if files exist
    if not os.path.exists(args.video):
        print(f"Error: Video file not found: {args.video}")
        return False
    
    if not os.path.exists(args.csv):
        print(f"Error: CSV file not found: {args.csv}")
        return False
    
    # Run visualization
    success = visualize_results(args.video, args.csv, args.output, not args.no_display)
    
    if success:
        print("Visualization completed successfully!")
    else:
        print("Visualization failed!")
    
    return success

if __name__ == '__main__':
    import sys
    success = main()
    sys.exit(0 if success else 1)
