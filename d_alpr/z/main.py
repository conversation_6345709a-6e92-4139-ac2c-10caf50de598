import cv2
import numpy as np
from ultralytics import YOLO
import easyocr

# Load models
plates_detector = YOLO('yolov8n-license-plate.pt')  # pre-trained for plates
ocr_reader = easyocr.Reader(['en'], gpu=False)

def process_frame(frame):
    res = plates_detector(frame, verbose=False)[0]
    annotated = frame.copy()
    for *box, conf, cls in res.boxes.data.tolist():
        x1,y1,x2,y2 = map(int, box)
        crop = frame[y1:y2, x1:x2]
        if crop.size == 0: continue

        # OCR
        ocr_results = ocr_reader.readtext(crop, detail=0)
        text = ocr_results[0] if ocr_results else ''
        cv2.rectangle(annotated, (x1,y1), (x2,y2), (0,255,0), 2)
        cv2.putText(annotated, text, (x1, y1-10),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0,255,0), 2)
    return annotated

if __name__ == "__main__":
    video_path = r"E:\d_rtsp\d_alpr\input_images\Jakarta_traffic.mp4"
    cap = cv2.VideoCapture(video_path)
    # change to video file or RTSP URL
    while True:
        ret, frame = cap.read()
        if not ret: break
        output = process_frame(frame)
        cv2.imshow("Live ALPR", output)
        if cv2.waitKey(1) & 0xFF == ord('q'): break
    cap.release()
    cv2.destroyAllWindows()
