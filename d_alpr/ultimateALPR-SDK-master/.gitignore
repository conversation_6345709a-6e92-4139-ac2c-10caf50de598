libultimatePluginTensorRT.so
binaries/jetson_tftrt/aarch64/libultimate_alpr-sdk.so
assets/models.tensorrt/optimized/*
*.user
*.suo
*.opensdf
*.sdf
*.xcuserdatad
*.DS_Store
/ipch
/x64
*.idb
/Debug
Debug_dynamic
Debug_static
Release
Release_dynamic
Release_static
Debug_sdk
Release_sdk
*.orig
*.tlog
Debug
*.gray
*.yuv
*.rgb
*.ipch
*.opendb
*.db
*.o
*.iws
.idea/tasks.xml
.idea/vcs.xml
.idea/workspace.xml
workspace.xml
local.properties
.gradle
gradle
gradlew
gradlew.bat
build
gen
out
.externalNativeBuild
My Amplifier XE Results*
My Advisor Results*
*.rbg
/.vs/config
/vs_android/.vs
/docs/*.odt#
/vs_android/.workspace

