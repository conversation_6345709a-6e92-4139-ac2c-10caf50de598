/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 2.0.9
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

namespace org.doubango.ultimateAlpr.Sdk {

using System;
using System.Runtime.InteropServices;

public class UltAlprSdkParallelDeliveryCallback : IDisposable {
  private HandleRef swigCPtr;
  protected bool swigCMemOwn;

  internal UltAlprSdkParallelDeliveryCallback(IntPtr cPtr, bool cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = new HandleRef(this, cPtr);
  }

  internal static HandleRef getCPtr(UltAlprSdkParallelDeliveryCallback obj) {
    return (obj == null) ? new HandleRef(null, IntPtr.Zero) : obj.swigCPtr;
  }

  ~UltAlprSdkParallelDeliveryCallback() {
    Dispose();
  }

  public virtual void Dispose() {
    lock(this) {
      if (swigCPtr.Handle != IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          ultimateAlprSdkPINVOKE.delete_UltAlprSdkParallelDeliveryCallback(swigCPtr);
        }
        swigCPtr = new HandleRef(null, IntPtr.Zero);
      }
      GC.SuppressFinalize(this);
    }
  }

  protected UltAlprSdkParallelDeliveryCallback() : this(ultimateAlprSdkPINVOKE.new_UltAlprSdkParallelDeliveryCallback(), true) {
    SwigDirectorConnect();
  }

  public virtual void onNewResult(UltAlprSdkResult newResult) {
    ultimateAlprSdkPINVOKE.UltAlprSdkParallelDeliveryCallback_onNewResult(swigCPtr, UltAlprSdkResult.getCPtr(newResult));
  }

  private void SwigDirectorConnect() {
    if (SwigDerivedClassHasMethod("onNewResult", swigMethodTypes0))
      swigDelegate0 = new SwigDelegateUltAlprSdkParallelDeliveryCallback_0(SwigDirectoronNewResult);
    ultimateAlprSdkPINVOKE.UltAlprSdkParallelDeliveryCallback_director_connect(swigCPtr, swigDelegate0);
  }

  private bool SwigDerivedClassHasMethod(string methodName, Type[] methodTypes) {
    System.Reflection.MethodInfo methodInfo = this.GetType().GetMethod(methodName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance, null, methodTypes, null);
    bool hasDerivedMethod = methodInfo.DeclaringType.IsSubclassOf(typeof(UltAlprSdkParallelDeliveryCallback));
    return hasDerivedMethod;
  }

  private void SwigDirectoronNewResult(IntPtr newResult) {
    onNewResult((newResult == IntPtr.Zero) ? null : new UltAlprSdkResult(newResult, false));
  }

  public delegate void SwigDelegateUltAlprSdkParallelDeliveryCallback_0(IntPtr newResult);

  private SwigDelegateUltAlprSdkParallelDeliveryCallback_0 swigDelegate0;

  private static Type[] swigMethodTypes0 = new Type[] { typeof(UltAlprSdkResult) };
}

}
