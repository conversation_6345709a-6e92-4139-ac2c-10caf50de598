<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A0DECB92-8F4A-4DFE-BDC3-EBB54E85CA18}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>recognizer</RootNamespace>
    <AssemblyName>recognizer</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <UseVSHostingProcess>false</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>false</UseVSHostingProcess>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\..\..\csharp\UltAlprSdkEngine.cs">
      <Link>UltAlprSdkEngine.cs</Link>
    </Compile>
    <Compile Include="..\..\..\csharp\UltAlprSdkParallelDeliveryCallback.cs">
      <Link>UltAlprSdkParallelDeliveryCallback.cs</Link>
    </Compile>
    <Compile Include="..\..\..\csharp\UltAlprSdkResult.cs">
      <Link>UltAlprSdkResult.cs</Link>
    </Compile>
    <Compile Include="..\..\..\csharp\ULTALPR_SDK_IMAGE_TYPE.cs">
      <Link>ULTALPR_SDK_IMAGE_TYPE.cs</Link>
    </Compile>
    <Compile Include="..\..\..\csharp\ultimateAlprSdk.cs">
      <Link>ultimateAlprSdk.cs</Link>
    </Compile>
    <Compile Include="..\..\..\csharp\ultimateAlprSdkPINVOKE.cs">
      <Link>ultimateAlprSdkPINVOKE.cs</Link>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Resources\RecognizerProjectDir.txt" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>xcopy /y /d  "$(ProjectDir)..\..\..\binaries\windows\x86_64\*.dll" "$(ProjectDir)$(OutDir)"
xcopy /y /d  "$(ProjectDir)..\..\..\binaries\windows\x86_64\*.xml" "$(ProjectDir)$(OutDir)"
xcopy /y /d  "$(ProjectDir)..\..\..\binaries\windows\x86_64\*.so" "$(ProjectDir)$(OutDir)"</PostBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PreBuildEvent>echo $(ProjectDir) &gt; "$(ProjectDir)\Resources\RecognizerProjectDir.txt"</PreBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>