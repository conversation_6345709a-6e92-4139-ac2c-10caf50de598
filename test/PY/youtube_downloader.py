# from pytube import YouTube
#
# video_url = "https://www.youtube.com/shorts/JvMh6yshgFo"
#
# yt = YouTube(video_url)
# stream = yt.streams.get_highest_resolution()
# stream.download()
#
# print("Video downloaded successfully.")





import yt_dlp

url = "https://www.youtube.com/shorts/IhvpPKuk8zY"

ydl_opts = {
    'format': 'best',
    'outtmpl': '%(title)s.%(ext)s',  # Save with video title
}

with yt_dlp.YoutubeDL(ydl_opts) as ydl:
    ydl.download([url])
