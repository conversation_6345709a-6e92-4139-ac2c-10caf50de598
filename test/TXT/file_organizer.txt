Have you ever found yourself dealing with a cluttered folder full of files and wished for a quick and efficient way to organize them?
Well, you are in the right place!
In this video, we will walk through a Python script that automates the process of categorizing files based on their types.
And the best part? It can all be done with just one click!
Stay tuned to discover how this script can help you maintain a well-organized file system effortlessly.
Lets jump right in!
We start by importing two modules. 'OS' helps us interact with the operating system, and 'shutil' provides high-level file operations.
import os
import shutil
Now, lets define our source directory.
Here, we set our source directory, the place where all our unorganized files are currently located.
Next, we define a nifty function get_file_type that takes a file name and extracts its type. 
For example, it tells us if a file is a 'jpg' image, a 'docx' document, and so on
To extract the file extension from the given file name. Use the 'split' method.
file_extension equals to file_name dot split and mention how to split. Here we are going to split by dot.
The split method will divide the filename into segments based on 'dot'. For instance, if the file name is "document dot txt," this operation produces a list ['document' and  'txt'].
By using the reverse indexing, minus one, we access the last element of the resulting list, Which represents the file extension.
Next. We create an empty set called 'file_types' to store unique file types. 
Why a set? Well, it ensures that we only have distinct file types, no duplicates.
Next. we get a list of all the files in our source directory using os dot list d i r.
These are the files we want to organize.


