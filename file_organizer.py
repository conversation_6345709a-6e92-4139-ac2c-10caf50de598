# import os
# import shutil
#
# # Define the source and destination directories
# source_directory = r'E:\PythonCodeCamp\test'
# destination_directory = r'E:\PythonCodeCamp\test'
#
# # Create a dictionary to map file types to folder names
# file_type_folders = {
#     'txt': 'TextFiles',
#     'log': 'TextFiles',
#     'json': 'TextFiles',
#     'jpg': 'Images',
#     'png' : 'Images',
#     'webp' : 'Images',
#     'py': 'PythonFile',
#     'pdf': 'Documents',
#     'xlsx': 'Documents',
#     'docx': 'Documents',
#     'exe': 'Apps',
#     'mp3': 'Music',
#     'wav': 'Music',
#     # Add more file types and folder names as needed
# }
#
# # Create folders in the destination directory based on the file types
# for folder_name in file_type_folders.values():
#     folder_path = os.path.join(destination_directory, folder_name)
#     os.makedirs(folder_path, exist_ok=True)
#
# # Function to get the file type (extension)
# def get_file_type(file_name):
#     file_extension = file_name.split('.')[-1]
#     return file_extension
#
# # List files in the source directory
# files = os.listdir(source_directory)
# for file in files:
#     print(file)
#
# # Organize files into folders based on their types
# for file in files:
#     source_file_path = os.path.join(source_directory, file)
#     if os.path.isfile(source_file_path):
#         file_type = get_file_type(file.lower())
#         if file_type in file_type_folders:
#             destination_folder = file_type_folders[file_type]
#             destination_folder_path = os.path.join(destination_directory, destination_folder)
#             shutil.move(source_file_path, os.path.join(destination_folder_path, file))
#
# print("File organization completed.")


import os
import shutil
from collections import defaultdict

# Define the source and destination directories
source_directory = r'E:\PythonCodeCamp\test'
destination_directory = r'E:\PythonCodeCamp\test'

# Map file extensions to folder names
file_type_folders = {
    'txt': 'TextFiles',
    'log': 'TextFiles',
    'json': 'TextFiles',
    'jpg': 'Images',
    'png': 'Images',
    'webp': 'Images',
    'py': 'PythonFile',
    'pdf': 'Documents',
    'xlsx': 'Documents',
    'docx': 'Documents',
    'exe': 'Apps',
    'mp3': 'Music',
    'wav': 'Music',
    'mp4': 'Video',
}

# Function to get file extension
def get_file_type(file_name):
    return file_name.split('.')[-1].lower()

# Group files by their folder based on extension
files_by_folder = defaultdict(list)
files = os.listdir(source_directory)

for file in files:
    source_file_path = os.path.join(source_directory, file)
    if os.path.isfile(source_file_path):
        file_type = get_file_type(file)
        if file_type in file_type_folders:
            folder_name = file_type_folders[file_type]
            files_by_folder[folder_name].append(file)

# Create folders only if files exist and move files
for folder_name, file_list in files_by_folder.items():
    folder_path = os.path.join(destination_directory, folder_name)
    os.makedirs(folder_path, exist_ok=True)
    for file in file_list:
        source_file_path = os.path.join(source_directory, file)
        shutil.move(source_file_path, os.path.join(folder_path, file))

print("File organization completed.")
