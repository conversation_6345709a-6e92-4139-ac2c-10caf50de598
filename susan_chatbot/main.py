import shutil
import requests
import sqlite3 as sql
import os
import random
import re
import jwt
import base64
import time
import shutil
from datetime import datetime, timedelta
from docx2pdf import convert
import uuid
from flask import Flask, request, jsonify, make_response, send_file
from flask import Flask, request, jsonify, session
from flask_cors import CORS
# from flask_session import Session
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyMuPDFLoader
from langchain_community.vectorstores import FAISS
from langchain_ollama import OllamaEmbeddings
# from langchain_community.embeddings import OllamaEmbeddings
from processor.logger import trace, exc
from processor.mistral_gguf import BC<PERSON>lanner
from processor.attached_file_reader import AttachedFileReader
import json
from elevenlabs import ElevenLabs
from elevenlabs.core.api_error import ApiError
from collections import defaultdict
from flask import Flask, request, jsonify, send_file, make_response
import pythoncom
from fpdf import FPDF

import ssl
import os

app = Flask(__name__)
app.secret_key = "b'!9m@S-dThyIlW[pHQbN^'"
app.config['SESSION_PERMANENT'] = True
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=1)
# Session(app)  # Reinitialize Flask-Session

SAVE_FOLDER = 'knowledge_base_documents'
TEMP_FOLDER = 'temp_pdf_cache'
os.makedirs(SAVE_FOLDER, exist_ok=True)
os.makedirs(TEMP_FOLDER, exist_ok=True)

CORS(app, support_credentials=True, origins="*")

if not os.path.isfile('database.db'):
    conn = sql.connect('database.db')
    conn.execute('CREATE TABLE IF NOT EXISTS Donors (Name TEXT NOT NULL, Amount INTEGER NOT NULL, Email TEXT NOT NULL, '
                 '[timestamp] TIMESTAMP)')
    conn.execute('''
      CREATE TABLE IF NOT EXISTS Users (
          UserID INTEGER PRIMARY KEY AUTOINCREMENT, 
          Name TEXT NOT NULL, 
          Email TEXT NOT NULL, 
          Password TEXT NOT NULL, 
          Contact INTEGER NOT NULL,
          UniqueID INTEGER UNIQUE
      )
  ''')
    conn.close()

user_vector_stores = {}
invalid_tokens = set()
user_consultant_instance = {}
session_data = {}

with open('config.json', 'r+') as f:
    config = json.load(f)
    ChatCohere_api = config['ChatCohere_api']
    ElevenLabs_api = config['elvenlabs_api']
    agent_id = config['agent_id']

client = ElevenLabs(api_key=ElevenLabs_api)

if ChatCohere_api:
    from processor.cohere_ai_bcm_api import DocumentRetrievalAssistant

    bcm_assistant_initiate = DocumentRetrievalAssistant()


def clear_folder(folder_path):
    """Deletes all files in the given folder."""
    if os.path.exists(folder_path):
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)  # Delete file
            except Exception as e:
                print(f"Error deleting {file_path}: {e}")


def generate_random_id():
    return random.randint(100000, 999999)


# def load_and_store_pdf(user_id, pdf_path):
#     try:
#         trace.info("Loads a PDF, splits text, and stores vectors in FAISS.")
#         txt_loader = PyMuPDFLoader(pdf_path, extract_tables="csv")
#         txt_documents = txt_loader.load()
#
#         text_splitter = RecursiveCharacterTextSplitter(
#             chunk_size=1024,
#             chunk_overlap=64,
#             separators=['\n\n', '\n', '(?=>\. )', ' ', '']
#         )
#
#         docs = text_splitter.split_documents(txt_documents)
#         # embeddings = OllamaEmbeddings(model="nomic-embed-text:v1.5")
#         embeddings = OllamaEmbeddings(
#             model="nomic-embed-text:v1.5",
#             base_url="http://localhost:11434"  # Specify the Ollama server URL
#         )        # Use local Ollama
#         vectorstore = FAISS.from_documents(docs, embedding=embeddings)
#
#         user_vector_stores[user_id] = vectorstore
#         return True
#     except Exception as ex:
#         print(ex)
#         exc.exception(f"Issue Faced on the load_and_store_pdf function... error : {ex}")
#         return False

# def load_and_store_pdf(user_id, pdf_path):
#     try:
#         trace.info("Loads a PDF, splits text, and stores vectors in FAISS.")
#         txt_loader = PyMuPDFLoader(pdf_path, extract_tables="csv")
#         txt_documents = txt_loader.load()
#
#         text_splitter = RecursiveCharacterTextSplitter(
#             chunk_size=1024,
#             chunk_overlap=64,
#             separators=['\n\n', '\n', '(?=>\. )', ' ', '']
#         )
#
#         docs = text_splitter.split_documents(txt_documents)
#
#         try:
#             embeddings = OllamaEmbeddings(
#                 model="nomic-embed-text:latest",  # Remove version suffix if causing issues
#                 base_url="http://localhost:11434"
#             )
#             vectorstore = FAISS.from_documents(docs, embedding=embeddings)
#             user_vector_stores[user_id] = vectorstore
#             return True
#         except Exception as embed_ex:
#             print(f"Embedding error: {embed_ex}")
#             # Fallback to a different embedding model if available
#             try:
#                 embeddings = OllamaEmbeddings(
#                     model="nomic-embed-text:latest",  # Alternative model
#                     base_url="http://localhost:11434"
#                 )
#                 vectorstore = FAISS.from_documents(docs, embedding=embeddings)
#                 user_vector_stores[user_id] = vectorstore
#                 return True
#             except Exception as fallback_ex:
#                 print(f"Fallback embedding also failed: {fallback_ex}")
#                 return False
#
#     except Exception as ex:
#         print(ex)
#         exc.exception(f"Issue Faced on the load_and_store_pdf function... error : {ex}")
#         return False


def load_and_store_pdf(user_id, pdf_path):
    try:
        trace.info("Loads a PDF, splits text, and stores vectors in FAISS.")
        txt_loader = PyMuPDFLoader(pdf_path, extract_tables="csv")
        txt_documents = txt_loader.load()

        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1024,
            chunk_overlap=64,
            separators=['\n\n', '\n', '(?=>\. )', ' ', '']
        )

        docs = text_splitter.split_documents(txt_documents)

        try:
            # Use the original nomic-embed-text model
            embeddings = OllamaEmbeddings(
                model="nomic-embed-text:latest",
                base_url="http://localhost:11434"
            )

            # Test embedding with a small sample first
            test_embed = embeddings.embed_query("test")
            print(f"Embedding test successful, dimension: {len(test_embed)}")

            vectorstore = FAISS.from_documents(docs, embedding=embeddings)
            user_vector_stores[user_id] = vectorstore
            return True

        except Exception as embed_ex:
            print(f"Embedding error: {embed_ex}")
            print("Make sure 'nomic-embed-text' model is pulled with: ollama pull nomic-embed-text")
            return False

    except Exception as ex:
        print(f"Error in load_and_store_pdf: {ex}")
        exc.exception(f"Issue Faced on the load_and_store_pdf function... error : {ex}")
        return False


@app.route('/register', methods=['GET', 'POST'])
def register():
    try:
        if request.method == 'POST':
            data = request.get_json()
            name = data['nm']
            contact = data['contact']
            email = data['email']
            password = data['password']
            unique_id = generate_random_id()
            with sql.connect("database.db") as con:
                cur = con.cursor()
                # check if User already present
                cur.execute("SELECT Email FROM Users WHERE Email=(?)", [(email)])
                data = cur.fetchall()
                if len(data) > 0:
                    print('User already exists')
                    return jsonify(message="user already exist", code=0)
                else:
                    print("User not found, register new user")
                    trace.info(f"Registering the new user:{data}")
                    cur.execute("INSERT INTO Users (Name,Email,Password,Contact, UniqueID) VALUES (?,?,?,?,?)",
                                (name, email, password, contact, unique_id))
                    return jsonify(message="user registered successfully", code=1, success=True)
        else:
            return jsonify(message="Invalid Request", code=1, success=True)
    except Exception as ex:
        print(ex)
        exc.exception(f"Issue Faced on the register function... error : {ex}")


@app.route('/login', methods=['GET', 'POST'])
def login():
    try:
        if request.method == 'POST':
            with open('config.json', 'r+') as f:
                config = json.load(f)
                agent_id = config['agent_id']
            data = request.get_json()
            email = data['email']
            password = data['password']
            with sql.connect("database.db") as con:
                cur = con.cursor()
                cur.execute("SELECT Email FROM Users WHERE Email=(?)", [(email)])
                data = cur.fetchall()
                if len(data) == 0:
                    return jsonify({'message': 'Email id was not registered', 'status': 'error'})
                else:
                    cur.execute("SELECT Email FROM Users WHERE Email=(?) AND Password=(?)", [(email), (password)])
                    data = cur.fetchall()
                    if len(data) > 0:
                        print('Login Success')
                        # Fetch name of user
                        query_to_execute = "SELECT Name, UniqueID  FROM Users WHERE Email=? AND Password=?"
                        data_to_execute = (email, password)
                        cur.execute(query_to_execute, data_to_execute)
                        login_detail = cur.fetchall()
                        name = login_detail[0][0]
                        user_id = login_detail[0][1]
                        if user_id in session_data:
                            del session_data[user_id]
                        if user_id in user_consultant_instance:
                            del user_consultant_instance[user_id]
                        if user_id in user_vector_stores:
                            del user_vector_stores[user_id]
                        # Store User details in Session and log in user
                        session['name'] = name
                        session['email'] = email
                        session['user_id'] = user_id
                        session['logged_out'] = None
                        session['plan_type'] = None
                        session_data[user_id] = {'plan_type': None}
                        token = jwt.encode({
                            'email': email,
                            'exp': datetime.utcnow() + timedelta(hours=1),
                            'user_id': user_id,
                        }, app.config['SECRET_KEY'], algorithm='HS256')
                        trace.info(f"login user:{name}, user_id:{user_id}, email:{email}")
                        return jsonify({'message': 'Login successful', 'token': token, 'status': 'success',
                                        'agent_id': agent_id}), 200
                    else:
                        print("Invalid password")
                        return jsonify({'message': 'Invalid password', 'status': 'error'}), 401
        else:
            return jsonify(message="Invalid Request", code=1, success=True)
    except Exception as ex:
        print(ex)
        exc.exception(f"Issue Faced on the login function... error : {ex}")


@app.route('/upload', methods=['POST'])
def upload_pdf():
    try:
        global bcm_assistant_initiate
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'Token missing', 'status': 'error'}), 401
        if token in invalid_tokens:
            return jsonify({'message': 'Token has been revoked', 'status': 'error'}), 401
        decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        """Handles PDF upload and initializes vector store for the user."""
        user_id = decode['user_id']  # Use IP as session ID
        if user_id not in session_data:
            return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
        data = request.get_json()
        if not data or "file" not in data:
            return jsonify({"error": "No file uploaded"}), 400
        try:
            pdf_data = base64.b64decode(data["file"])
        except Exception as e:
            return jsonify({"error": f"Invalid base64 data: {str(e)}"}), 400
        chat_type = data["chat_type"]
        file_name = data['file_name']
        # Define the path to save the PDF
        dir_path = f"./data/uploads/{chat_type}/{user_id}"
        file_path = f"{dir_path}/{file_name}"
        if os.path.exists(dir_path):
            for filename in os.listdir(dir_path):
                file_path = os.path.join(dir_path, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.remove(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)  # Delete folder
                except Exception as e:
                    print(f"Error deleting {file_path}: {e}")
        os.makedirs(dir_path, exist_ok=True)
        if os.path.exists(file_path):
            os.remove(file_path)

        # 🔹 Save the new PDF file
        with open(file_path, 'wb') as f:
            f.write(pdf_data)
        flag = None
        if user_id not in session_data:
            session_data[user_id]['plan_type'] = None
        if chat_type == 'BCMConsultant':
            bcm_consultant = BCMPlanner()
            flag = bcm_consultant.read_word_to_list(file_path)
            user_consultant_instance[user_id] = bcm_consultant
            session_data[user_id]['plan_type'] = None
        elif chat_type == 'BCMAssistant':
            if ChatCohere_api:
                bcm_assistant_initiate = DocumentRetrievalAssistant()
                bcm_assistant_initiate.process_pdf(file_path)
                flag = True
            else:
                flag = load_and_store_pdf(user_id, file_path)
            session_data[user_id]['plan_type'] = None
        if flag:
            return jsonify({"message": "Uploaded File processed successfully", "user_id": user_id, "success": True})
        else:
            return jsonify({"message": "Unable to load pdf", "user_id": user_id, "success": False})
    except jwt.ExpiredSignatureError:
        return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': 'Invalid token', 'status': 'expired', "success": False}), 401
    except Exception as ex:
        print(ex)
        exc.exception(f"Issue Faced on the Upload function... error : {ex}")
        return jsonify({'message': 'Please upload valid PDF file', 'status': 'error', "success": False}), 401


@app.route('/chat_response', methods=['POST'])
def ask_question():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'Token missing', 'status': 'error'}), 401
        if token in invalid_tokens:
            return jsonify({'message': 'Token has been revoked', 'status': 'error'}), 401

        decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        user_id = decode['user_id']
        data = request.json
        input_string = data.get("question")
        chat_type = data["chat_type"]
        file_name = data['file_name']
        if file_name:
            save_folder = f"./data/uploads/{chat_type}/{user_id}/attachment"
            os.makedirs(save_folder, exist_ok=True)
            clear_folder(save_folder)
            file_name_1 = os.path.join(save_folder, file_name)
            with open(file_name_1, "wb") as file:
                file.write(base64.b64decode(input_string))
            print(f"File saved as {file_name_1}")
            reader = AttachedFileReader(file_name_1)
            input_string = reader.read_file()
        if not input_string:
            return jsonify({"error": "Query is required"}), 400
        if chat_type == 'BCMConsultant':
            if user_id not in session_data:
                return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
            if input_string == 'NewPlan':
                bcm_consultant = BCMPlanner()
                bcm_consultant.read_word_to_list("data/consultant_doc/BC plan framwork or structure.docx")
                user_consultant_instance[user_id] = bcm_consultant
                session['plan_type'] = 'NewPlan'
                session_data[user_id]['plan_type'] = 'NewPlan'
                # return jsonify({"error": "No document uploaded for this session"}), 400
            bcm_consultant = user_consultant_instance[user_id]
            if input_string == 'How Can You Help Me' or input_string == 'NewPlan':
                input_string = ''
            chat_response, data_type = bcm_consultant.bcp_creation(input_string, session_data[user_id]['plan_type'])
            trace.info(f"User_input:{input_string},Chatbot Response :{chat_response}")
            if data_type == 'docx':
                user_consultant_instance[user_id] = BCMPlanner()
            return jsonify({'message': chat_response, "type": data_type, "success": True})
        elif chat_type == 'BCMAssistant':
            if ChatCohere_api:
                res = bcm_assistant_initiate.answer_question(input_string)
                return jsonify({'message': res, "type": "string", "success": True})
            else:
                if user_id not in user_vector_stores:
                    return jsonify({"error": "No document uploaded for this session"}), 400
                vectorstore = user_vector_stores[user_id]
                results = vectorstore.similarity_search(input_string, k=5)  # Get top 5 matches
                retrieved_text = "\n\n".join([doc.page_content for doc in results])
                formatted_prompt = f"""

                You are Susan, an AI assistant developed and fine-tuned by Perpetuuiti for BCM assistance. Your role is to 
                respond in a direct and conversational manner, like a one-on-one communication, strictly based on the information 
                from the BCM document. 

                - If the user greets with "Hi," "Hello," "Thanks," "Hey there," or any similar greeting, respond politely. - For 
                any other query, provide an answer only based on the BCM document. - If the query is unrelated, inform the user 
                that your role is strictly to provide BCM-related information and you cannot assist with other topics. 

                **Context:** {retrieved_text}  

                **User Query:** {input_string}  

                Respond in a direct and conversational way, making it feel like a real discussion. Keep answers concise and 
                accurate, strictly from the BCM document. """

                ollama_url = "http://localhost:11434/api/generate"
                data = {
                    "model": "gemma2:2b",
                    "prompt": formatted_prompt,
                    "stream": False,
                    "max_tokens": 200,
                    "temperature": 0.7
                }

                try:
                    response = requests.post(ollama_url, json=data)
                    res = response.json()
                    trace.info(f"User_input:{input_string},Chatbot Response :{res['response']}")
                    return jsonify({'message': res["response"], "type": "string", "success": True})
                except Exception as e:
                    return jsonify({"error": str(e)}), 500
    except jwt.ExpiredSignatureError:
        return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': 'Invalid token', 'status': 'expired', "success": False}), 401
    except Exception as ex:
        print(ex)
        exc.exception(f"Issue Faced on the ask_question function... error : {ex}")
        return jsonify({"message": str(ex), 'status': 'error', "success": False}), 500


@app.route("/conversations", methods=["GET"])
def get_conversations():
    try:
        # conversations_response = client.conversational_ai.get_conversations()
        # conversations = conversations_response.conversations
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'Token missing', 'status': 'error'}), 401
        if token in invalid_tokens:
            return jsonify({'message': 'Token has been revoked', 'status': 'error'}), 401

        decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])

        cursor = None
        conversations = []

        while True:
            result = client.conversational_ai.get_conversations(cursor=cursor)
            conversations.extend(result.conversations)
            if not result.next_cursor:
                break
            cursor = result.next_cursor

        total_calls = 0
        answered_calls = 0
        missed_calls = 0
        total_answered_duration_secs = 0

        convo_list = []

        for convo in conversations:
            total_calls += 1

            conversation_id = convo.conversation_id
            agent_name = convo.agent_name or "-"
            status = convo.status
            duration_secs = convo.call_duration_secs or 0
            message_count = convo.message_count
            call_result = convo.call_successful
            start_time_unix = convo.start_time_unix_secs
            start_time = datetime.fromtimestamp(start_time_unix)

            if status.lower() == "failed":
                missed_calls += 1
            elif call_result:
                answered_calls += 1
                total_answered_duration_secs += duration_secs

            caller_number = "-"
            try:
                convo_response = client.conversational_ai.get_conversation(conversation_id=conversation_id)
                convo_dict = convo_response.dict()
                caller_number = convo_dict.get("conversation_initiation_client_data", {}) \
                    .get("dynamic_variables", {}) \
                    .get("system__called_number", "-")
            except:
                pass

            convo_list.append({
                "conversation_id": conversation_id,
                "agent_name": agent_name,
                "caller_number": caller_number,
                "status": status,
                "time": start_time.strftime("%H:%M:%S"),
                "date": start_time.strftime("%Y-%m-%d"),
                "duration_seconds": duration_secs,
                "duration_formatted": str(timedelta(seconds=duration_secs)),
                "message_count": message_count,
                "call_result": call_result
            })

        avg_duration = total_answered_duration_secs / answered_calls if answered_calls > 0 else 0

        answered_percentage = round((answered_calls / total_calls) * 100, 2) if total_calls > 0 else 0
        missed_percentage = round((missed_calls / total_calls) * 100, 2) if total_calls > 0 else 0
        total_percentage = 100  # Always 100% since it's the base

        summary = {
            "total_calls": total_calls,
            "answered_calls": answered_calls,
            "missed_calls": missed_calls,
            "total_answered_duration": str(timedelta(seconds=total_answered_duration_secs)),
            "average_duration": str(timedelta(seconds=int(avg_duration))),
            "percentages": {
                "total_calls_percent": total_percentage,
                "answered_calls_percent": answered_percentage,
                "missed_calls_percent": missed_percentage
            }
        }

        return jsonify({
            "summary": summary,
            "conversations": convo_list
        })
    except jwt.ExpiredSignatureError:
        return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': 'Invalid token', 'status': 'expired', "success": False}), 401
    except Exception as e:
        exc.exception(f"Issue Faced on the logout function... error : {e}")
        return jsonify({
            "error": f"Failed to fetch conversations: {str(e)}"
        }), 500


@app.route("/graph", methods=["POST"])
def get_call_analytics_by_date():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'Token missing', 'status': 'error'}), 401
        if token in invalid_tokens:
            return jsonify({'message': 'Token has been revoked', 'status': 'error'}), 401

        decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        # Get date query param
        date_str = request.args.get("date")
        if not date_str:
            return jsonify({"error": "Missing 'date' query parameter in format YYYY-MM-DD"}), 400

        try:
            target_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            return jsonify({"error": "Invalid date format. Use YYYY-MM-DD"}), 400

        # conversations_response = client.conversational_ai.get_conversations()
        # conversations = conversations_response.conversations

        cursor = None
        conversations = []

        while True:
            result = client.conversational_ai.get_conversations(cursor=cursor)
            conversations.extend(result.conversations)
            if not result.next_cursor:
                break
            cursor = result.next_cursor

        # Hourly stats for that specific day
        hourly_data = defaultdict(lambda: {"total": 0, "answered": 0, "missed": 0})

        for convo in conversations:
            start_time_unix = convo.start_time_unix_secs
            start_time = datetime.fromtimestamp(start_time_unix)

            # Only include if date matches
            if start_time.date() != target_date:
                continue

            hour = start_time.replace(minute=0, second=0, microsecond=0)
            status = convo.status
            call_result = convo.call_successful

            hourly_data[hour]["total"] += 1
            if status.lower() == "failed":
                hourly_data[hour]["missed"] += 1
            elif call_result:
                hourly_data[hour]["answered"] += 1

        # Format call analytics data
        call_analytics = []
        for hour, counts in sorted(hourly_data.items()):
            call_analytics.append({
                "date": hour.isoformat(),
                "value": counts["total"],
                "value2": counts["answered"],
                "value3": counts["missed"]
            })

        return jsonify({
            "date": date_str,
            "call_analytics": call_analytics
        })
    except jwt.ExpiredSignatureError:
        return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': 'Invalid token', 'status': 'expired', "success": False}), 401

    except Exception as e:
        return jsonify({
            "error": f"Failed to fetch data for date {date_str}: {str(e)}"
        }), 500


@app.route("/transcript/<conversation_id>", methods=["POST"])
def get_transcript(conversation_id):
    try:
        # Step 1: Get all conversation summaries
        # conversations_response = client.conversational_ai.get_conversations()
        # conversations = conversations_response.conversations
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'Token missing', 'status': 'error'}), 401
        if token in invalid_tokens:
            return jsonify({'message': 'Token has been revoked', 'status': 'error'}), 401

        decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])

        cursor = None
        conversations = []

        while True:
            result = client.conversational_ai.get_conversations(cursor=cursor)
            conversations.extend(result.conversations)
            if not result.next_cursor:
                break
            cursor = result.next_cursor

        # Step 2: Match the conversation_id to get start time
        convo_summary = next((c for c in conversations if c.conversation_id == conversation_id), None)
        if convo_summary is None:
            return jsonify({"error": "Conversation summary not found"}), 404

        # Step 3: Format the date and time from the start timestamp
        start_time_unix = convo_summary.start_time_unix_secs
        start_time = datetime.fromtimestamp(start_time_unix)
        date_str = start_time.strftime("%Y-%m-%d")
        time_str = start_time.strftime("%H:%M:%S")

        # Step 4: Get the conversation transcript
        convo_response = client.conversational_ai.get_conversation(conversation_id=conversation_id)
        convo_data = convo_response.dict()
        transcript_entries = convo_data.get("transcript", [])

        # Step 5: Structure transcript turns with the new format
        turns = []

        try:
            for i in range(len(transcript_entries)):
                entry = transcript_entries[i]
                role = entry.get("role", "").lower()
                # message = entry.get("message", "").strip()
                message = (entry.get("message") or "").strip()
                time_secs = entry.get("time_in_call_secs")

                # Format the time_secs into MM:SS format
                user_sec = ""
                agent_sec = ""
                if time_secs is not None:
                    minutes = int(time_secs) // 60
                    seconds = int(time_secs) % 60
                    formatted_time = f"{minutes}:{seconds:02d}"

                    if role == "user":
                        user_sec = formatted_time
                    elif role == "agent":
                        agent_sec = formatted_time

                # Find if there's already a turn for this message pair
                turn = None

                # Look for an existing incomplete turn
                for t in turns:
                    # If we find a user message without agent, or vice versa
                    if (role == "user" and t["agent"] and not t["user"]) or \
                            (role == "agent" and t["user"] and not t["agent"]):
                        turn = t
                        break

                # If no existing turn found, create a new one
                if not turn:
                    turn = {
                        "user": "",
                        "user_sec": "",
                        "agent": "",
                        "agent_sec": "",
                        "date": date_str,
                        "time": time_str
                    }
                    turns.append(turn)

                # Update the turn with this message
                if role == "user":
                    turn["user"] = message
                    turn["user_sec"] = user_sec
                elif role == "agent":
                    turn["agent"] = message
                    turn["agent_sec"] = agent_sec
        except Exception as e:
            print(f"Error in get_transcript function: {e}")

        return jsonify({
            "conversation_id": conversation_id,
            "transcript": turns
        })

    except jwt.ExpiredSignatureError:
        return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': 'Invalid token', 'status': 'expired', "success": False}), 401
    except Exception as e:
        return jsonify({"error": f"Failed to fetch transcript: {str(e)}"}), 500


@app.route("/history", methods=["GET"])
def get_conversations_by_date():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'Token missing', 'status': 'error'}), 401
        if token in invalid_tokens:
            return jsonify({'message': 'Token has been revoked', 'status': 'error'}), 401

        decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        # Get date query param
        date_str = request.args.get("date")
        if not date_str:
            return jsonify({"error": "Missing 'date' query parameter in format YYYY-MM-DD"}), 400

        try:
            target_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            return jsonify({"error": "Invalid date format. Use YYYY-MM-DD"}), 400

        # Get all conversations
        # conversations_response = client.conversational_ai.get_conversations()
        # conversations = conversations_response.conversations

        cursor = None
        conversations = []

        while True:
            result = client.conversational_ai.get_conversations(cursor=cursor)
            conversations.extend(result.conversations)
            if not result.next_cursor:
                break
            cursor = result.next_cursor

        # Initialize counters for summary
        total_calls = 0
        answered_calls = 0
        missed_calls = 0
        total_answered_duration_secs = 0
        convo_list = []

        # Filter conversations by date and build response
        for convo in conversations:
            start_time_unix = convo.start_time_unix_secs
            start_time = datetime.fromtimestamp(start_time_unix)

            # Only include conversations from the target date
            if start_time.date() != target_date:
                continue

            # Increment counters
            total_calls += 1
            conversation_id = convo.conversation_id
            agent_name = convo.agent_name or "-"
            status = convo.status
            duration_secs = convo.call_duration_secs or 0
            message_count = convo.message_count
            call_result = convo.call_successful

            if status.lower() == "failed":
                missed_calls += 1
            elif call_result:
                answered_calls += 1
                total_answered_duration_secs += duration_secs

            # Try to get caller number
            caller_number = "-"
            try:
                convo_response = client.conversational_ai.get_conversation(conversation_id=conversation_id)
                convo_dict = convo_response.dict()
                caller_number = convo_dict.get("conversation_initiation_client_data", {}) \
                    .get("dynamic_variables", {}) \
                    .get("system__called_number", "-")
            except:
                pass

            # Add to conversation list
            convo_list.append({
                "conversation_id": conversation_id,
                "agent_name": agent_name,
                "caller_number": caller_number,
                "status": status,
                "time": start_time.strftime("%H:%M:%S"),
                "date": start_time.strftime("%Y-%m-%d"),
                "duration_seconds": duration_secs,
                "duration_formatted": str(timedelta(seconds=duration_secs)),
                "message_count": message_count,
                "call_result": call_result
            })

        # Calculate summary statistics
        avg_duration = total_answered_duration_secs / answered_calls if answered_calls > 0 else 0
        answered_percentage = round((answered_calls / total_calls) * 100, 2) if total_calls > 0 else 0
        missed_percentage = round((missed_calls / total_calls) * 100, 2) if total_calls > 0 else 0
        total_percentage = 100  # Always 100% since it's the base

        # Create summary object
        summary = {
            "total_calls": total_calls,
            "answered_calls": answered_calls,
            "missed_calls": missed_calls,
            "total_answered_duration": str(timedelta(seconds=total_answered_duration_secs)),
            "average_duration": str(timedelta(seconds=int(avg_duration))),
            "percentages": {
                "total_calls_percent": total_percentage,
                "answered_calls_percent": answered_percentage,
                "missed_calls_percent": missed_percentage
            }
        }

        # Get hourly breakdown for chart data
        hourly_data = defaultdict(lambda: {"total": 0, "answered": 0, "missed": 0})
        for convo in convo_list:
            time_obj = datetime.strptime(f"{convo['date']} {convo['time']}", "%Y-%m-%d %H:%M:%S")
            hour = time_obj.replace(minute=0, second=0, microsecond=0)
            hourly_data[hour]["total"] += 1

            if convo["status"].lower() == "failed":
                hourly_data[hour]["missed"] += 1
            elif convo["call_result"]:
                hourly_data[hour]["answered"] += 1

        # Format chart data
        call_analytics = []
        for hour, counts in sorted(hourly_data.items()):
            call_analytics.append({
                "date": hour.isoformat(),
                "value": counts["total"],
                "value2": counts["answered"],
                "value3": counts["missed"]
            })

        phone_number = []
        try:
            result = client.conversational_ai.get_phone_numbers()
            phone_adent_id = result[0].assigned_agent.agent_id
            if phone_adent_id == agent_id:
                phone_number.append(result[0].phone_number)
            else:
                print("Phone number not assigned to the agent.")
        except Exception as e:
            print(f"Error fetching phone numbers: {e}")

        return jsonify({
            "date": date_str,
            "summary": summary,
            "conversations": convo_list,
            "call_analytics": call_analytics,
            "phone_number": phone_number
        })

    except jwt.ExpiredSignatureError:
        return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': 'Invalid token', 'status': 'expired', "success": False}), 401

    except Exception as e:
        return jsonify({
            "error": f"Failed to fetch conversations for date {date_str}: {str(e)}"
        }), 500


def get_unique_filename(folder, base_name, ext):
    try:
        safe_base = re.sub(r'[\\/:"*?<>|]+', '_', base_name)
        filename = f"{safe_base}.{ext}"
        counter = 1
        while os.path.exists(os.path.join(folder, filename)):
            filename = f"{safe_base}_{counter}.{ext}"
            counter += 1
        return filename

    except Exception as ex:
        print(f"Error in get_unique_filename: {ex}")
        return None


def save_file_from_base64(base64_str, original_name):
    try:
        ext = original_name.split('.')[-1]
        base_name = os.path.splitext(original_name)[0]

        file_data = base64.b64decode(base64_str)
        unique_filename = get_unique_filename(SAVE_FOLDER, base_name, ext)
        filepath = os.path.join(SAVE_FOLDER, unique_filename)

        if len(filepath) > 255:
            unique_filename = unique_filename[:240] + "." + ext
            filepath = os.path.join(SAVE_FOLDER, unique_filename)

        with open(filepath, 'wb') as f:
            f.write(file_data)

        print(f"File saved as: {unique_filename}")
        return filepath, unique_filename

    except Exception as e:
        print(f"Error in save_file_from_base64: {e}")
        return None, None


def upload_files_to_knowledge_base(file_paths, client):
    supported_extensions = ['.pdf', '.docx', '.doc', '.txt']
    knowledge_base_items = []

    for file_path in file_paths:
        file_name = os.path.basename(file_path)
        file_extension = os.path.splitext(file_name)[1].lower()

        if os.path.isfile(file_path) and file_extension in supported_extensions:
            try:
                print(f"Uploading {file_name}...")
                with open(file_path, "rb") as file:
                    response = client.conversational_ai.add_to_knowledge_base(
                        file=file,
                        name=file_name
                    )

                document_id = getattr(response, 'id', None)

                if document_id:
                    print(f"Uploaded document ID: {document_id} for {file_name}")
                    knowledge_base_items.append({
                        "type": "file",
                        "id": document_id,
                        "name": file_name,
                        "usage_mode": "auto"
                    })
                    time.sleep(1)
                else:
                    print(f"Failed to get document ID for {file_name}")
            except Exception as e:
                print(f"Error uploading {file_name}: {str(e)}")
        else:
            print(f"Skipping unsupported or non-existent file: {file_path}")

    return knowledge_base_items


@app.route("/kbaddfile", methods=["POST"])
def knowledge_base_add_files():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'Token missing', 'status': 'error'}), 401
        if token in invalid_tokens:
            return jsonify({'message': 'Token has been revoked', 'status': 'error'}), 401

        decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        data = request.get_json()
        if isinstance(data, str):
            data = json.loads(data)
        file_name = data.get("filename")
        base64_file_data = data.get("file")

        if not file_name or not base64_file_data:
            return jsonify({"error": "filename and file (base64) are required"}), 400

        saved_path, unique_filename = save_file_from_base64(base64_file_data, file_name)

        upload_response = upload_files_to_knowledge_base([saved_path], client)
        if not upload_response:
            print(f"Error in upload_files_to_knowledge_base")
            return jsonify({"error": "File not uploaded to knowledge base"}), 500

        return jsonify({
            "status": True,
            "message": "Document added successfully"
        })

    except jwt.ExpiredSignatureError:
        return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': 'Invalid token', 'status': 'expired', "success": False}), 401

    except Exception as e:
        print(f"Error in knowledge_base_add_files: {e}")
        return jsonify({
            "status": False,
            "message": f"Document could not be added: {str(e)}"
        }), 500


@app.route("/kbgetfiles", methods=["GET"])
def knowledge_base_get_file_details():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'Token missing', 'status': 'error'}), 401
        if token in invalid_tokens:
            return jsonify({'message': 'Token has been revoked', 'status': 'error'}), 401

        decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])

        result = client.conversational_ai.get_knowledge_base_list()

        document_details = []

        # change
        for doc in result.documents:
            name = doc.name
            if '.' not in name.split()[-1]:
                name += ".pdf"
            updated_time = datetime.fromtimestamp(doc.metadata.last_updated_at_unix_secs)
            dependent_agent = doc.dependent_agents
            status = False
            if dependent_agent:
                for d_agent in dependent_agent:
                    if d_agent.id == agent_id:
                        status = True
                        break


            doc_detail = {
                "kb_doc_name": name,
                "kb_doc_id": doc.id,
                "kb_doc_uploaded_time": updated_time.strftime("%Y-%m-%d %H:%M:%S"),
                "kb_doc_status": status
            }
            document_details.append(doc_detail)

        return jsonify({
            "status": True,
            "message": "Documents fetched successfully",
            "data": document_details
        })

    except jwt.ExpiredSignatureError:
        return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': 'Invalid token', 'status': 'expired', "success": False}), 401

    except Exception as e:
        print(f"Error in knowledge_base_get_file_details: {e}")
        return jsonify({
            "status": False,
            "message": f"Error fetching documents: {str(e)}",
            "data": []
        }), 500


@app.route("/kbconnectdisconnectdocs", methods=["POST"])
def knowledge_base_connect_disconnect_documents_to_agent():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'Token missing', 'status': 'error'}), 401
        if token in invalid_tokens:
            return jsonify({'message': 'Token has been revoked', 'status': 'error'}), 401

        decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])

        data = request.get_json()
        if isinstance(data, str):
            data = json.loads(data)
        connect_documents_list = data.get("currentDocs")
        connectStatus = data.get("connectStatus")
        status = connectStatus.get("status")

        new_knowledge_base = []
        for doc in connect_documents_list:
            new_document = {
                "type": "file",
                "name": doc["name"],
                "id": doc["id"],
                "usage_mode": "auto"
            }
            new_knowledge_base.append(new_document)

        client.conversational_ai.update_agent(
            agent_id=agent_id,
            conversation_config={
                "agent": {
                    "prompt": {
                        "knowledge_base": new_knowledge_base
                    }
                }
            }
        )

        if status == "connect":
            return jsonify({
                "status": True,
                "message": "Document connected successfully",
                "connectStatus": connectStatus,
            })
        else:
            return jsonify({
                "status": True,
                "message": "Document disconnected successfully",
                "connectStatus": connectStatus,
            })

    except jwt.ExpiredSignatureError:
        return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': 'Invalid token', 'status': 'expired', "success": False}), 401


    except Exception as e:
        print(f"Error in knowledge_base_connect_disconnect_documents_to_agent: {e}")
        return jsonify({
            "status": False,
            "message": f"Document could not be connected: {str(e)}"
        }), 500


@app.route("/kbdeletedoc", methods=["POST"])
def knowledge_base_delete_document():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'Token missing', 'status': 'error'}), 401
        if token in invalid_tokens:
            return jsonify({'message': 'Token has been revoked', 'status': 'error'}), 401

        decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])

        data = request.get_json()
        if isinstance(data, str):
            data = json.loads(data)
        if not data or "id" not in data:
            return jsonify({
                "status": False,
                "message": "Invalid input. 'deleteDocs' or 'id' missing."
            }), 400

        doc_id = data["id"]
        filename = data["name"]

        try:
            client.conversational_ai.delete_knowledge_base_document(documentation_id=doc_id)

            try:
                if filename:
                    file_path = os.path.join(SAVE_FOLDER, filename)
                    if os.path.exists(file_path):
                        os.remove(file_path)
            except Exception as ex:
                print(ex)

        except ApiError as e:
            if e.status_code == 204:
                try:
                    if filename:
                        file_path = os.path.join(SAVE_FOLDER, filename)
                        if os.path.exists(file_path):
                            os.remove(file_path)
                except Exception as ex:
                    print(ex)

                return jsonify({
                    "status": True,
                    "message": "Document deleted successfully",
                })
            else:
                return jsonify({
                    "status": False,
                    "message": f"Document could not be deleted: {str(e)}"
                }), 500

        return jsonify({
            "status": True,
            "message": "Document deleted successfully",
        })

    except jwt.ExpiredSignatureError:
        return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': 'Invalid token', 'status': 'expired', "success": False}), 401

    except Exception as e:
        print(f"Error in knowledge_base_delete_document: {e}")
        return jsonify({
            "status": False,
            "message": f"Document could not be deleted: {str(e)}"
        }), 500


def clear_temp_folder():
    try:
        for filename in os.listdir(TEMP_FOLDER):
            file_path = os.path.join(TEMP_FOLDER, filename)
            if os.path.isfile(file_path):
                try:
                    time.sleep(0.1)
                    os.remove(file_path)
                    print(f"Deleted temp file: {file_path}")
                except Exception as e:
                    print(f"Could not delete {file_path}: {e}")
    except Exception as e:
        print(f"Error in clear_temp_folder: {e}")


def convert_docx_to_pdf(docx_path, pdf_path):
    try:
        pythoncom.CoInitialize()
        convert(docx_path, pdf_path)
    finally:
        pythoncom.CoUninitialize()


def convert_txt_to_pdf(txt_path, pdf_path):
    pdf = FPDF()
    pdf.add_page()
    pdf.set_auto_page_break(auto=True, margin=15)
    pdf.set_font("Arial", size=12)

    with open(txt_path, "r", encoding="utf-8") as file:
        for line in file:
            pdf.multi_cell(0, 10, txt=line.strip())

    pdf.output(pdf_path)


# elevenlabs==1.59.0
@app.route("/kbviewdoc", methods=["POST"])
def knowledge_base_view_document():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': 'Token missing', 'status': 'error'}), 401
        if token in invalid_tokens:
            return jsonify({'message': 'Token has been revoked', 'status': 'error'}), 401

        decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])

        data = request.get_json()
        if isinstance(data, str):
            data = json.loads(data)

        doc_id = data.get("id")
        original_name = data.get("name")

        if not doc_id or not original_name:
            return jsonify({"status": False, "message": "Missing document ID or name."}), 400

        # Fetch content
        doc_content = client.conversational_ai.get_knowledge_base_document_by_id(documentation_id=doc_id)
        show_content = doc_content.extracted_inner_html

        # Prepare file
        ext = original_name.split('.')[-1].lower()
        base_name = os.path.splitext(original_name)[0]
        safe_base = re.sub(r'[\\/:"*?<>|]+', '_', base_name)
        file_name = f"{safe_base}.{ext}"
        file_path = os.path.join(SAVE_FOLDER, file_name)

        # Save file if not already
        if not os.path.exists(file_path):
            with open(file_path, "wb") as f:
                f.write(doc_content.content)

        # Convert if needed
        if ext == "pdf":
            file_url = f"/knowledge_base_documents/{file_name}"
        elif ext in ["docx", "txt"]:
            clear_temp_folder()
            temp_pdf_name = f"{uuid.uuid4()}.pdf"
            temp_pdf_path = os.path.join(TEMP_FOLDER, temp_pdf_name)
            if ext == "docx":
                convert_docx_to_pdf(file_path, temp_pdf_path)
            elif ext == "txt":
                convert_txt_to_pdf(file_path, temp_pdf_path)
            file_url = f"/serve_temp/{temp_pdf_name}"
        else:
            return jsonify({"status": False, "message": "Unsupported file type"}), 400

        return jsonify({
            "status": True,
            "showContent": show_content,
            "showFilePath": file_url
        })

    except jwt.ExpiredSignatureError:
        return jsonify({'message': 'Token expired', 'status': 'expired', "success": False}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': 'Invalid token', 'status': 'expired', "success": False}), 401

    except Exception as e:
        return jsonify({"status": False, "message": f"Document could not be viewed: {str(e)}"}), 500


@app.route("/knowledge_base_documents/<path:filename>", methods=["GET"])
def serve_saved_file(filename):
    try:

        file_path = os.path.join(SAVE_FOLDER, filename)
        response = make_response(send_file(file_path))
        response.headers["Content-Disposition"] = f'inline; filename="{filename}"'
        return response

    except FileNotFoundError:
        return "File not found", 404


@app.route("/serve_temp/<path:filename>", methods=["GET"])
def serve_temp_pdf(filename):
    try:
        file_path = os.path.join(TEMP_FOLDER, filename)
        response = make_response(send_file(file_path))
        response.headers["Content-Disposition"] = f'inline; filename="{filename}"'
        return response

    except FileNotFoundError:
        return "File not found", 404


@app.route("/getusage", methods=["GET"])
def usage_details():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'status': 'error', 'message': 'Authorization token is missing'}), 401

        if token in invalid_tokens:
            return jsonify({'status': 'error', 'message': 'Token has been revoked'}), 401

        try:
            decoded_token = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            return jsonify({'status': 'expired', 'message': 'Token has expired', 'success': False}), 401
        except jwt.InvalidTokenError:
            return jsonify({'status': 'expired', 'message': 'Invalid token', 'success': False}), 401

        headers = {
            "xi-api-key": ElevenLabs_api
        }

        response = requests.get("https://api.elevenlabs.io/v1/user/subscription", headers=headers)

        if response.status_code != 200:
            print(f"Failed to fetch usage. Status code: {response.status_code}, Response: {response.text}")
            return jsonify({
                "status": False,
                "message": "Failed to fetch usage data",
                "usage": []
            }), response.status_code

        data = response.json()
        total_chars = data.get("character_limit", 0)
        used_chars = data.get("character_count", 0)
        remaining_chars = total_chars - used_chars

        return jsonify({
            "status": True,
            "message": "Usage fetched successfully",
            "usage": {
                "total": total_chars,
                "used": used_chars,
                "remaining": remaining_chars
            }
        })

    except Exception as e:
        print(f"Error in usage_details: {e}")
        return jsonify({
            "status": False,
            "message": "Internal server error while fetching usage details",
            "error": str(e)
        }), 500


@app.route("/getlanguage", methods=["GET"])
def language_selected():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'status': 'error', 'message': 'Authorization token is missing'}), 401

        if token in invalid_tokens:
            return jsonify({'status': 'error', 'message': 'Token has been revoked'}), 401

        try:
            decoded_token = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            return jsonify({'status': 'expired', 'message': 'Token has expired', 'success': False}), 401
        except jwt.InvalidTokenError:
            return jsonify({'status': 'expired', 'message': 'Invalid token', 'success': False}), 401

        default_language = None
        additional_language = []

        languages_dict = {
            "nl": {"name": "Dutch", "flag": "nl"},
            "fi": {"name": "Finnish", "flag": "fi"},
            "tr": {"name": "Turkish", "flag": "tr"},
            "ru": {"name": "Russian", "flag": "ru"},
            "ta": {"name": "Tamil", "flag": "in"},
            "hr": {"name": "Croatian", "flag": "hr"},
            "ro": {"name": "Romanian", "flag": "ro"},
            "ko": {"name": "Korean", "flag": "kr"},
            "no": {"name": "Norwegian", "flag": "no"},
            "zh": {"name": "Chinese", "flag": "cn"},
            "ja": {"name": "Japanese", "flag": "jp"},
            "hu": {"name": "Hungarian", "flag": "hu"},
            "uk": {"name": "Ukrainian", "flag": "ua"},
            "it": {"name": "Italian", "flag": "it"},
            "sv": {"name": "Swedish", "flag": "se"},
            "id": {"name": "Indonesian", "flag": "id"},
            "ar": {"name": "Arabic", "flag": "sa"},
            "pt-br": {"name": "Portuguese", "flag": "br"},
            "es": {"name": "Spanish", "flag": "es"},
            "hi": {"name": "Hindi", "flag": "in"},
            "el": {"name": "Greek", "flag": "gr"},
            "bg": {"name": "Bulgarian", "flag": "bg"},
            "da": {"name": "Danish", "flag": "dk"},
            "ms": {"name": "Malay", "flag": "my"},
            "vi": {"name": "Vietnamese", "flag": "vn"},
            "pt": {"name": "Portuguese", "flag": "pt"},
            "de": {"name": "German", "flag": "de"},
            "cs": {"name": "Czech", "flag": "cz"},
            "sk": {"name": "Slovak", "flag": "sk"},
            "fr": {"name": "French", "flag": "fr"},
            "pl": {"name": "Polish", "flag": "pl"},
            "en": {"name": "English", "flag": "us"}
        }

        try:
            agent_details = client.conversational_ai.get_agent(agent_id)
            language_presets = agent_details.conversation_config.language_presets

            # change
            # if language_presets:
            try:
                default_language = languages_dict.get(agent_details.conversation_config.agent.language)
            except Exception as e:
                print(f"Error fetching default language: {e}")

            if language_presets:
                for language_code, language_data in language_presets.items():
                    additional_language.append(languages_dict.get(language_code))
            else:
                print("No additional languages found.")
        except Exception as e:
            print(f"Error fetching language details: {e}")

        return jsonify({
            "status": True,
            "message": "Language fetched successfully",
            "language": {
                "default": default_language,
                "additional": additional_language,
            }
        })

    except Exception as e:
        print(f"Error in usage_details: {e}")
        return jsonify({
            "status": False,
            "message": "Internal server error while fetching language details",
            "error": str(e)
        }), 500


@app.route("/updatelanguage", methods=["POST"])
def update_language():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'status': 'error', 'message': 'Authorization token is missing'}), 401

        if token in invalid_tokens:
            return jsonify({'status': 'error', 'message': 'Token has been revoked'}), 401

        try:
            decoded_token = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            return jsonify({'status': 'expired', 'message': 'Token has expired', 'success': False}), 401
        except jwt.InvalidTokenError:
            return jsonify({'status': 'expired', 'message': 'Invalid token', 'success': False}), 401

        with open("agent_language_details.json", "r", encoding="utf-8") as file:
            language_json = json.load(file)

        data = request.get_json()
        if isinstance(data, str):
            data = json.loads(data)

        selected_language = data

        default_language_data = {}
        additional_language_data = {}

        languages_dict = {
            "dutch": "nl", "finnish": "fi", "turkish": "tr", "russian": "ru", "tamil": "ta",
            "croatian": "hr", "romanian": "ro", "korean": "ko", "norwegian": "no", "chinese": "zh",
            "japanese": "ja", "hungarian": "hu", "ukrainian": "uk", "italian": "it", "swedish": "sv",
            "indonesian": "id", "arabic": "ar", "portuguese (brazil)": "pt-br", "spanish": "es",
            "hindi": "hi", "greek": "el", "bulgarian": "bg", "danish": "da", "malay": "ms",
            "vietnamese": "vi", "portuguese": "pt", "german": "de", "czech": "cs", "slovak": "sk",
            "french": "fr", "polish": "pl", "english": "en"
        }

        selected_additional_ids = [
            languages_dict.get(lang["name"].lower())
            for lang in selected_language["language"]["additional"]
            if languages_dict.get(lang["name"].lower())
        ]

        default_name = selected_language["language"]["default"]["name"].lower()
        default_id = languages_dict.get(default_name)

        if selected_additional_ids:
            for selected_language_code in selected_additional_ids:
                language_data = language_json["language_presets"].get(selected_language_code)
                if language_data:
                    additional_language_data[selected_language_code] = language_data
                else:
                    print(f"No data found for code '{selected_language_code}' in the JSON file.")
        else:
            print("No languages selected.")

        if default_id:
            default_language_data = language_json["language_presets"].get(default_id)
            if default_language_data:
                first_message = (
                    default_language_data.get("overrides", {})
                    .get("agent", {})
                    .get("first_message", "")
                )
                print(f"\nDefault Language ID: {default_id}")
                print(f"First Message: {first_message}")
            else:
                print(f"No data found for default language ID '{default_id}' in the JSON.")
        else:
            print("Default language name not found in language dictionary.")

        # client.conversational_ai.update_agent(
        #     agent_id=agent_id,
        #     conversation_config={
        #         "language_presets": additional_language_data,
        #     }
        # )

        client.conversational_ai.update_agent(
            agent_id=agent_id,
            conversation_config={
                "agent": {
                    "language": default_id
                },
                "language_presets": additional_language_data,
            }
        )

        return jsonify({
            "status": True,
            "message": "Language Updated successfully"
        })

    except Exception as e:
        print(f"Error in usage_details: {e}")
        return jsonify({
            "status": False,
            "message": "Internal server error while updating language details",
            "error": str(e)
        }), 500


@app.route('/settings', methods=['POST', 'GET'])
def settings():
    try:
        if request.method == 'POST':
            data = request.json
            user_id = data["UserId"]
            bcm_consultant_url = data["BcmConsultantUrl"]
            bcm_assistant_url = data["BcmAssistantUrl"]
            bcm_champ_url = data["BCMChampUrl"]
            bcm_conversational_ai_url = data["BCMConversationalAIUrl"]
            bcm_consultant_message = data["BcmConsultantMessage"]
            bcm_assistant_message = data["BcmAssistantMessage"]
            bcm_created_date = data["CreatedDate"]
            bcm_last_modified_date = data["LastModifiedDate"]
            with sql.connect("database.db") as con:
                con.row_factory = sql.Row
                cur = con.cursor()
                cur.execute("SELECT * FROM bcmurlsettings WHERE UserId = ?", (user_id,))
                data = cur.fetchall()

                if len(data) > 0:
                    # Update existing record
                    cur.execute("""
                        UPDATE bcmurlsettings
                        SET 
                            BcmConsultantUrl = ?,
                            BcmAssistantUrl = ?,
                            BCMChampUrl = ?,
                            BCMConversationalAIUrl = ?,
                            BcmConsultantMessage = ?,
                            BcmAssistantMessage = ?,
                            CreatedDate = ?,
                            LastModifiedDate = ?
                        WHERE UserId = ?
                    """, (
                        bcm_consultant_url, bcm_assistant_url, bcm_champ_url, bcm_conversational_ai_url,
                        bcm_consultant_message, bcm_assistant_message,
                        bcm_created_date, bcm_last_modified_date, user_id
                    ))
                    return jsonify(message="Data updated successfully", code=1, success=True)

                else:
                    # Insert new record (fixed to match columns and values)
                    cur.execute("""
                        INSERT INTO bcmurlsettings (
                            UserId, BcmConsultantUrl, BcmAssistantUrl, BCMChampUrl, 
                            BCMConversationalAIUrl, BcmConsultantMessage, BcmAssistantMessage, 
                            CreatedDate, LastModifiedDate
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        user_id, bcm_consultant_url, bcm_assistant_url, bcm_champ_url,
                        bcm_conversational_ai_url, bcm_consultant_message,
                        bcm_assistant_message, bcm_created_date, bcm_last_modified_date
                    ))
                    return jsonify(message="Data added successfully", code=1, success=True)

        if request.method == 'GET':
            user_id = request.headers['Userid']
            with sql.connect("database.db") as con:
                con.row_factory = sql.Row
                cur = con.cursor()
                cur.execute("SELECT * FROM bcmurlsettings WHERE UserId=(?)", [(user_id)])
                data = cur.fetchall()
                if len(data) > 0:
                    row = data[0]
                    details = {"bcmConsultantUrl": row["BcmConsultantUrl"], "bcmAssistantUrl": row["BcmAssistantUrl"],
                               "bcmChampUrl": row["BCMChampUrl"],
                               "bcmConversationalAIUrl": row["BCMConversationalAIUrl"],
                               "bcmConsultantMessage": row["BcmConsultantMessage"],
                               "bcmAssistantMessage": row["BcmAssistantMessage"], "CreatedDate": row["CreatedDate"],
                               "LastModifiedDate": row["LastModifiedDate"]}
                    return jsonify(data=details, code=1, success=True)
                else:
                    return jsonify(message="No data found", code=0, success=True)
    except Exception as ex:
        print(ex)
        exc.exception(f"Issue Faced on the settings function... error : {ex}")
        return jsonify({"message": str(ex), 'status': 'error', "success": False}), 500


@app.route('/logout', methods=['POST'])
def logout():
    try:
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'status': 'error', 'message': 'Authorization token is missing'}), 401

        if token in invalid_tokens:
            return jsonify({'status': 'error', 'message': 'Token has been revoked'}), 401

        try:
            decode = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            return jsonify({'status': 'expired', 'message': 'Token has expired', 'success': False}), 401
        except jwt.InvalidTokenError:
            return jsonify({'status': 'expired', 'message': 'Invalid token', 'success': False}), 401
        user_id = decode['user_id']
        if user_id in session_data:
            del session_data[user_id]
        if user_id in user_consultant_instance:
            del user_consultant_instance[user_id]
        if user_id in user_vector_stores:
            del user_vector_stores[user_id]
        return jsonify({'message': 'Logout successful', 'status': 'success'}), 200
    except Exception as ex:
        print(ex)
        exc.exception(f"Issue Faced on the logout function... error : {ex}")
        return jsonify({"message": str(ex), 'status': 'error', "success": False}), 500


# if __name__ == '__main__':
#     # app.run(host="0.0.0.0", port=8970, threaded=True, use_reloader=False)
#     app.run(host="0.0.0.0", port=4000, threaded=True, use_reloader=False)
#
#     # Simple approach using Flask's adhoc SSL context
#     app.run(
#         host="0.0.0.0",
#         port=4000,
#         threaded=True,
#         use_reloader=False,
#         ssl_context='adhoc'  # This creates a temporary self-signed certificate
#     )

# if __name__ == '__main__':
#     # Path to your existing SSL certificates
#     cert_file = "ssl/cert.pem"
#     key_file = "ssl/key.pem"
#
#     # Verify the files exist
#     if not os.path.exists(cert_file) or not os.path.exists(key_file):
#         print(f"Error: SSL certificate files not found!")
#         print(f"Looking for: {cert_file} and {key_file}")
#         exit(1)
#
#     # Method 1: Using ssl_context parameter (recommended)
#     print("Starting HTTPS server on https://localhost:4000")
#     print(f"Using certificate: {cert_file}")
#     print(f"Using key: {key_file}")
#     print("Note: You'll get a security warning in browsers due to self-signed certificate")
#
#     app.run(
#         host="0.0.0.0",
#         port=4000,
#         threaded=True,
#         use_reloader=False,
#         ssl_context=(cert_file, key_file)  # Tuple of (cert_file, key_file)
#     )

if __name__ == '__main__':
    # Path to your existing SSL certificates
    cert_file = "ssl/cert.pem"
    key_file = "ssl/key.pem"
    #
    # # Verify the files exist
    # if not os.path.exists(cert_file) or not os.path.exists(key_file):
    #     print(f"Error: SSL certificate files not found!")
    #     print(f"Looking for: {cert_file} and {key_file}")
    #     exit(1)


    # app.run(
    #     host="0.0.0.0",
    #     port=7777,
    #     debug=True,
    #     # threaded=True,
    #     use_reloader=False,
    #     ssl_context=(cert_file, key_file)
    #
    # # Tuple of (cert_file, key_file)
    # )
    app.run(
        debug=True,
        use_reloader=False,  # <== This is KEY
        ssl_context=(cert_file, key_file),
        host="0.0.0.0",
        port=7878
    )