.login-body {
    /* background: rgb(243, 235, 232);
    background: linear-gradient(100deg, rgba(243, 235, 232, 1) 10%, rgba(245, 231, 224, 1) 19%, rgba(242, 242, 237, 1) 44%, rgba(249, 249, 244, 1) 100%); */
    height: 100vh;
    background-image: url(../img/Login/BG3.png) !important;
    background-repeat: no-repeat;
    background-position: 100%;
    background-size: contain;

}

@media (min-width: 1400px) {
    .login-body {
        background-image: url(../img/Login/BG1.png) !important;
    }
}

.login-hero-content h4 {
    font-size: 3.5rem;
    font-weight: 600;
}

.login-card {
    border: 0;
    border-radius: 2rem;
    background: #F6F3F6;
}

.form-group {
    position: relative;
    margin-bottom: 1rem !important;
}

.input-group {
    color: var(--bs-body-color);
    background-color: transparent;
    border-bottom: 1px solid #C3C3C3;
    border-radius: 0rem;
    align-items: center;
    flex-wrap: inherit;
    height: 38px;
    width: 100%;
}

.input-group:focus {
    color: var(--bs-body-color);
    background-color: transparent;
    border-bottom: 1px solid var(--bs-primary);
}

.input-group:focus-within {
    color: var(--bs-body-color);
    background-color: transparent;
    border-bottom: 1px solid var(--bs-primary);
    border-image: linear-gradient(114deg, rgba(230, 56, 117, 1) 4%, rgba(50, 2, 132, 1) 100%) 1;
}

.input-group-text {
    padding: .375rem 0rem;
    background-color: transparent;
    border: none;
}

.form-control::placeholder {
    color: #838382;
    font-size: var(--bs-body-font-size);
}

.form-control {
    color: var(--bs-gray-700);
    background-color: transparent;
    border: none;

}

.form-control:focus {
    background-color: transparent;
    border: none;
    outline: 0;
    box-shadow: none;
}

.form-select {
    font-size: var(--bs-body-font-size);
    color: var(--bs-gray-700);
    border: none;
}

.form-select:focus {
    border: none;
    outline: 0;
    box-shadow: none;
}


.login-nav .nav-item .nav-link.active {
    background: #000;
    border-radius: 30px;
    color: #fff !important;
    border: 1px solid #000;
}

.login-nav .nav-item:hover .nav-link {
    background: #000;
    color: #fff !important;
}

.login-nav .nav-item .nav-link {
    background: transparent;
    border-radius: 30px;
    border: 1px solid #707070;
    padding: 0.3rem 1rem;
    color: #707070 !important;
    width: 90px;
    text-align: center;
}

.login-title {
    font-size: 3rem;
    color: #000;
    font-weight: 700;
}

.button-bos {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.button-bos:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1; /* Ensures the button appears above other elements during hover */
}


/*End Modal style */