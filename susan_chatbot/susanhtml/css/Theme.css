:root {
    --bs-body-font-size: 13px;
    --bs-14-font-size: 14px;
    --bs-font-family: SF Pro Display;
    --bs-white: #fff;
    --bs-border-radiuss: 1rem;
    --bs-gradient: linear-gradient(180deg, rgb(255 255 255 / 0%), rgb(255 255 255 / 30%));
    --bs-success-rgb: 17, 183, 33;
    --bs-warning-rgb: 222, 62, 15;
}

[class^="bp-"],
[class*=" bp-"] {
    font-size: 18px;
    vertical-align: middle;
}

::-webkit-scrollbar {
    width: 5px;
    height: 5px
}

::-webkit-scrollbar-thumb {
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    border-radius: 10px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

body {
    font-size: var(--bs-body-font-size);
    font-family: var(--bs-font-family) !important;
    /* background-color: #f7f7f7; */
    background: rgb(243, 235, 232);
    background: linear-gradient(100deg, rgba(243, 235, 232, 1) 10%, rgba(245, 231, 224, 1) 19%, rgba(242, 242, 237, 1) 44%, rgba(249, 249, 244, 1) 100%);
}

.dropdown-menu {
    --bs-dropdown-font-size: 13px;
    box-shadow: var(--bs-box-shadow-sm);
    border: none;
}
.bg-secondary-light{
    background-color: #F2F2F2;
}

.dropdown-item.active,
.dropdown-item:active {
    color: #fff !important;
    text-decoration: none;
    background: linear-gradient(90deg, rgba(255, 108, 96, 1) 50%, rgba(255, 190, 70, 1) 100%);
}

.gradient_border {
    background: linear-gradient(white, white) padding-box, linear-gradient(to right, #40e0d0, #adff2f) border-box;
    border: 1px solid transparent;
}

.gradient_text {
    background: linear-gradient(90deg, rgba(255, 108, 96, 1) 50%, rgba(255, 190, 70, 1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 15px;
}

.btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    padding-left: 1rem;
    padding-right: 1rem;
}

.btn .cp-add {
    padding: 8px;
}

.btn-primary {
    background: rgb(230, 56, 117);
    background: linear-gradient(114deg, rgba(230, 56, 117, 1) 4%, rgba(50, 2, 132, 1) 100%);
    border: none;
}

.text-primary {
    color: #ff6c60 !important;
}

.border-primary {
    border-color: #ff6c60 !important;
}

.btn-outline-primary {
    color: #ff6c60;
    background: linear-gradient(90deg, rgba(255, 108, 96, 1) 50%, rgba(255, 190, 70, 1) 100%) border-box;
    border: 1px solid transparent;
    border-radius: 0.375rem !important;
}

.btn-outline-primary:hover {
    color: #fff;
    background: linear-gradient(90deg, #ff6c60 50%, #ffbe46 100%) padding-box, linear-gradient(90deg, #ff6c60 50%, #ffbe46 100%) border-box;
    border: 1px solid transparent;
    border-radius: 0.375rem !important;
}

.form-check-input:focus {
    border-color: #54e36040;
    box-shadow: 0 0 0 .25rem #54e36040;
}

.form-check-input:checked {
    background-color: #ff6c60;
    border-color: #ff6c60;
}

.card-NoDesign {
    --bs-card-border-width: none;
    --bs-card-cap-bg: transparent;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.075) !important;
    border-radius: var(--bs-border-radiuss);
}

.nav-item button {
    color: var(--bs-dark);
}

.nav-link:focus,
.nav-link:hover {
    color: #ff6c60;
}

.nav-item .active {
    /* background: linear-gradient(90deg, #ff6c60 50%, rgba(255, 190, 70, 1) 100%); */
    /* border-radius: 30px; */
    color: #000000 !important;
}

.nav-item i {
    display: none;
}

.nav-item .active i {
    display: inline-block;
}

.fs-14 {
    font-size: var(--bs-14-font-size);
}

.table-sm {
    font-size: 13px;
}

.fs-8 {
    font-size: 12px;
}

.fs-9 {
    font-size: 8px;
}

.fs-xs {
    font-size: 10px;
}

.fs-xxs {
    font-size: 6px;
}

.card-no-design{
    border: 0px;
    border-radius: 2rem;

}

.bg-glass {
    background: #FBFBFC;
    box-shadow: 0 0px 8px 0 rgb(229 229 229);
    backdrop-filter: blur(3.5px);
    -webkit-backdrop-filter: blur(3.5px);
    border-radius: 10px;
    border: 1px solid rgb(255 255 255 / 0%);
}

.card-glass {
    background: rgb(255 255 255);
    /* box-shadow: 0 0px 8px 0 rgb(235 237 245 / 76%); */
    /* backdrop-filter: blur(12px); */
    -webkit-backdrop-filter: blur(12px);
    border-radius: 40px;
    border: 1px solid #fefefe73;
}

.card-glass .card-header {
    background-color: #ffffff54;
    border-radius: 40px 40px 0px 0px;
    border: 0px;
    border-bottom: 1px solid #fff;
    padding: 20px 30px 10px;
}

.card-glass .card-footer {
    border: none;
    background-color: transparent;
}

.text-primary {
    background: #FC7F08;
    background: linear-gradient(to right, #FC7F08 0%, #861096 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

}

.text-priamry-gradient{
    background: #E53775;
background: linear-gradient(to right, #E53775 0%, #B12879 50%, #4F0A81 100%);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
}