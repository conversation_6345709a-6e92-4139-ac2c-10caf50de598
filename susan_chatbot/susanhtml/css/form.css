.form-group {
    position: relative;
    margin-bottom: 1rem !important;
}

.input-group {
    color: var(--bs-body-color);
    background-color: transparent;
    border-bottom: 1px solid var(--bs-gray-300);
    border-radius: 0rem;
    align-items: center;
    flex-wrap: inherit;
    height: 35px;
}

.input-group:focus {
    color: var(--bs-body-color);
    background-color: transparent;
    border-bottom: 1px solid var(--bs-primary);
}

.input-group:focus-within {
    color: var(--bs-body-color);
    background-color: transparent;
    border-bottom: 1px solid var(--bs-primary);
    border-image: linear-gradient(90deg, rgba(255,108,96,1) 50%, rgba(255,190,70,1) 100%) 1;
}

.input-group-text {
    padding: .375rem 0rem;
    background-color: transparent;
    border: none;
}


.invalid-feedback {
    position: absolute;
    text-align: end;
    border-top: 1px solid var(--bs-form-invalid-color);
    margin-top: -1px;
}


.form-control {
    font-size: var(--bs-body-font-size);
    color: var(--bs-gray-700);
    background-color: transparent;
    border: none;
}

.form-control:focus {
    background-color: transparent;
    border: none;
    outline: 0;
    box-shadow: none;
}

.form-select {
    font-size: var(--bs-body-font-size);
    color: var(--bs-gray-700);
    border: none;
    background-color: transparent;
}

.form-select:focus {
    border: none;
    outline: 0;
    box-shadow: none;
    background-color: transparent;
}

.accordion-button {
    padding: .5rem !important;
}

.accordion-button:not(.collapsed) {
    color: currentColor;
    background-color: transparent;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
}

.accordion-header button {
    font-size: 14px;
    font-weight: 500;
    vertical-align: middle;
}

.accordion-body i {
    vertical-align: middle;
}

.accordion-item {
    background: radial-gradient(circle at 100% 100%, #ffffff 0, #ffffff 14px, transparent 14px) 0% 0%/16px 16px no-repeat,
        radial-gradient(circle at 0 100%, #ffffff 0, #ffffff 14px, transparent 14px) 100% 0%/16px 16px no-repeat,
        radial-gradient(circle at 100% 0, #ffffff 0, #ffffff 14px, transparent 14px) 0% 100%/16px 16px no-repeat,
        radial-gradient(circle at 0 0, #ffffff 0, #ffffff 14px, transparent 14px) 100% 100%/16px 16px no-repeat,
        linear-gradient(#ffffff, #ffffff) 50% 50%/calc(100% - 4px) calc(100% - 32px) no-repeat,
        linear-gradient(#ffffff, #ffffff) 50% 50%/calc(100% - 32px) calc(100% - 4px) no-repeat,
        conic-gradient(#e0fba7 0%, #c4f4bb 16.67%, #8fe6e3 33.33%, #a1ebd5 50%, #f1ff9a 66.67%);
    border-radius: 15px !important;
}

.accordion-item.collapsed {
    margin-top: 10px;
    /* Add margin when collapsed */
}

.accordion-button:not(.collapsed)::after {
    background-image: url('/img/bot-vision/minus.svg') !important;

}

.accordion-button::after {
    background-image: url('/img/bot-vision/Plus.svg') !important;
}


.valid {
    color: green;
}

.valid:before {
    position: relative;
    content: "✔";
}

.invalid {
    color: red;
}

.invalid:before {
    position: relative;
    content: "✖";
}

/* configure site list modal design */

:checked + .site_type {
    border-color: #f0e9eb;
    border-top-right-radius: 0;
    transition: .2s;
}

    :checked + .site_type:before {
        content: "✓";
        transform: scale(1);
        font-weight: 800;
    }

.site_type:before {
    background-color:#ff6c60 !important;
    color: #fff;
    content: "\f2bc";
    display: block;
    border-radius: 25% 0 25% 25%;
    border: 0 solid grey;
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    text-align: center;
    transition-duration: .4s;
    transform: scale(0);
}

.site_type {
    padding: 18px;
    display: block;
    position: relative;
    cursor: pointer;
    border: 1px solid #f0e9eb;
    border-radius: 15%;
    transition: .2s;
}

/*End form input Style*/