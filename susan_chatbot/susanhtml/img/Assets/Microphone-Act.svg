<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="32" cy="32" r="25.4658" fill="url(#paint0_linear_0_8)" stroke="white"/>
<g filter="url(#filter0_f_0_8)">
<circle cx="32.001" cy="31.999" r="19.5645" fill="#BB99F4"/>
</g>
<g filter="url(#filter1_f_0_8)">
<circle cx="32.001" cy="31.999" r="19.5645" fill="#FFBCD4"/>
</g>
<g filter="url(#filter2_d_0_8)">
<circle cx="32.0029" cy="31.999" r="18.0547" fill="url(#paint1_linear_0_8)"/>
</g>
<g clip-path="url(#clip0_0_8)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M32.4113 23.1943C34.0759 23.1943 35.4252 24.5437 35.4252 26.2082V31.8054C35.4252 33.47 34.0759 34.8193 32.4113 34.8193C30.7468 34.8193 29.3975 33.47 29.3975 31.8054V26.2082C29.3975 24.5437 30.7468 23.1943 32.4113 23.1943ZM27.3881 31.8193C27.3881 31.488 27.1195 31.2193 26.7881 31.2193C26.4567 31.2193 26.1881 31.488 26.1881 31.8193C26.1881 35.0548 28.6567 37.7139 31.8131 38.0158V39.6943C31.8131 40.0257 32.0817 40.2943 32.4131 40.2943C32.7445 40.2943 33.0131 40.0257 33.0131 39.6943V38.0158C36.1695 37.7139 38.6381 35.0548 38.6381 31.8193C38.6381 31.488 38.3695 31.2193 38.0381 31.2193C37.7067 31.2193 37.4381 31.488 37.4381 31.8193C37.4381 34.5945 35.1882 36.8443 32.4131 36.8443C29.638 36.8443 27.3881 34.5945 27.3881 31.8193Z" fill="white" fill-opacity="0.75"/>
</g>
<defs>
<filter id="filter0_f_0_8" x="0.436523" y="0.43457" width="63.1289" height="63.1289" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6" result="effect1_foregroundBlur_0_8"/>
</filter>
<filter id="filter1_f_0_8" x="0.436523" y="0.43457" width="63.1289" height="63.1289" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6" result="effect1_foregroundBlur_0_8"/>
</filter>
<filter id="filter2_d_0_8" x="13.9482" y="13.9443" width="44.1094" height="44.1094" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_8"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_8" result="shape"/>
</filter>
<linearGradient id="paint0_linear_0_8" x1="32" y1="6.03418" x2="32" y2="57.9658" gradientUnits="userSpaceOnUse">
<stop stop-color="#E4D4E7"/>
<stop offset="1" stop-color="#D1E3F9"/>
</linearGradient>
<linearGradient id="paint1_linear_0_8" x1="43.3202" y1="21.2011" x2="23.9697" y2="45.0437" gradientUnits="userSpaceOnUse">
<stop stop-color="#A17DCB"/>
<stop offset="1" stop-color="#A8ADE7"/>
</linearGradient>
<clipPath id="clip0_0_8">
<rect width="18" height="18" fill="white" transform="translate(23.4131 23.1943)"/>
</clipPath>
</defs>
</svg>
