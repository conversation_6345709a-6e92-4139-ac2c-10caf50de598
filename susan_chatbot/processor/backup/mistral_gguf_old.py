import base64
import json
import re
from io import BytesIO
from processor.logger import trace,exc
from langchain_community.llms import <PERSON>lamaCpp
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml import OxmlElement
from docx.shared import Pt, Inches
from docx.oxml.ns import qn
from docx.shared import Pt
from datetime import datetime
from processor.attached_file_writer import AttachedFileWriter


class BCMPlanner:
    def __init__(self):
        self.llm = LlamaCpp(model_path="models/mistral-7b-instruct-v0.1.Q4_K_M.gguf", n_gpu_layers=-1, n_ctx=4096,
                            verbose=False,
                            max_tokens=1024)
        self.new_doc = Document()
        self.bcm_sections = None
        self.ques = None
        self.sec = None
        self.sec_ans = []
        self.sec_quest = {}
        self.confirmation = None
        self.yes_no = None
        self.llm_questions = None
        self.content = None
        self.final_resp = False
        self.content_approve = False
        self.attached_file_flag = False

    def read_word_to_list(self, file_path):
        try:
            doc = Document(file_path)
            content_doc_list = {"company name": ""}
            for para in doc.paragraphs:
                if para.text != '':
                    content_doc_list[para.text] = ""
            self.bcm_sections = content_doc_list
        except Exception as ex:
            print(ex)

    def generate_section_questions(self, section):
        try:
            # minimal questions
            prompt = f"""<SYS>
    Generate 1-2 specific questions to gather necessary inputs for the following Business Continuity Plan (BCP) section:

    Section Title: {section}
    Section Context: {section}

    Guidelines:
    - Focus on concise, actionable, and relevant questions.
    - Limit the number of questions to a maximum of 2.
    - If the section context is unclear or general, ask broad but important BCP-related questions for that section.
    - Ensure the questions are practical for input gathering and decision-making.
</SYS>"""
            # prompt = f"""<SYS>
            # You are generating input-gathering questions for a Business Continuity Plan (BCP) section. Your task is to create **minimal but highly relevant** questions to obtain the necessary details from the user.
            #
            # ### **Guidelines:**
            # - Focus on **gathering only essential information** required for the section.
            # - **Ask as few questions as possible** while ensuring completeness.
            # - Ensure the questions are **clear, direct, and practical** for decision-making.
            # - If the section context is broad or unclear, generate **general but meaningful BCP-related questions** to guide input collection.
            # - **Do not include numbered lists, bullet points, or excessive questions**.
            # - Keep questions in **natural language** and avoid unnecessary complexity.
            #
            # **Section Title:** {section}
            # **Section Context:** {section}
            # </SYS>"""

            response = self.llm.invoke(prompt).strip()
            list_question = [line.strip() for line in response.split("\n") if line.strip().endswith("?")] or [
                "What details should be added to this section?"]
            dict_question = {question: "" for question in list_question}
            return dict_question
        except Exception as ex:
            print(ex)
            exc.exception(f"Issue identified in generate_section_questions function ... error : {ex}")
            return ["What details should be added to this section?"]

    def generate_section_content(self, section, user_inputs):
        try:
            # D
            response = ""
            attached_content_list = []
            attached_answer = {}
            response_inside_attached_data = None

            for index, (key, value) in enumerate(list(user_inputs.items())):
                if isinstance(value, list):
                    attached_answer[index] = user_inputs.pop(key)
                    self.attached_file_flag = True

            if user_inputs:
                include_contact_table = "contact" in section.lower() or "emergency contact" in section.lower()

                json_string = json.dumps(user_inputs, indent=2)
                parsed_json = json.loads(json_string)

                # concatenated_questions = ""
                # for key in parsed_json.keys():
                #     concatenated_questions += key + "\n"
                #
                # question_value = concatenated_questions.strip()
                #
                # concatenated_input = ""
                # for value in parsed_json.values():
                #     concatenated_input += value + "\n"
                #
                # input_value = concatenated_input.strip()

                questions_list = ", ".join(parsed_json.keys())
                input_value = "\n".join(parsed_json.values()).strip()

                prompt = f"""<SYS>
                    You are a Business Continuity Management (BCM) consultant. Your task is to expand and refine the given user input into a fully developed section of a Business Continuity Plan (BCP). 

                    ### Guidelines:
                    - The user has provided responses to the following topics: {questions_list}.
                    - Use only the provided responses to generate the document. **Do not introduce new topics or add general explanations.**
                    - Ensure coherence, logical flow, and alignment with standard BCP practices.
                    - Maintain a structured response that fits into the Business Continuity Plan document.
                    - **Only use the provided user input. Do not add additional context or assumptions.**

                    ### Formatting Rules:
                    - **Do not include an introduction, questions, examples, summary, or conclusion.**
                    - **Do not use HTML formatting, markdown, bullet points, dictionary formats, JSON, or Q&A formats.**
                    - **The response must be in plain text with continuous paragraphs and no list formatting.**
                    - Avoid redundancy or unnecessary repetition of key points.

                    User Input:
                    {input_value}
                    </SYS>
                    """

                if include_contact_table:
                    prompt += """<SYS> -- Contact Details Table --
        
                    Please format any contact details in a structured table format with these columns:
                    - Contact Name
                    - Role
                    - Contact Number
                    - Email Address
        
                    Instructions:
                    1. Only include actual contacts from the input document
                    2. Format as a markdown table with the headers above
                    3. If no contact information exists, output only: "No contact information available."
                    4. The example below is for reference only - DO NOT include these sample names in your output
        
                    | Contact Name | Role | Contact Number | Email Address |
                    |--------------|------|----------------|---------------|
                    | John Doe | IT | +1234567890 | <EMAIL> |
                    | Jane Smith | HR | +0987654321 | <EMAIL> |
                    </SYS>
                    """

                print(prompt)
                response = self.llm.invoke(prompt).strip()
                # Here you can further process the response to ensure no conclusion or summary is included
                # For example, you might want to strip off the last paragraph if it looks like a conclusion
                paragraphs = response.split('\n\n')
                if len(paragraphs) > 1 and ("conclusion" in paragraphs[-1].lower() or "summary" in paragraphs[-1].lower()):
                    resp = '\n\n'.join(paragraphs[:-1])
                    response = re.sub(r"<.*?>", "", resp)

            if self.attached_file_flag:
                try:
                    json_objects = re.findall(r'\{.*?\}', response, re.DOTALL)
                    if json_objects:
                        for obj in json_objects:
                            try:
                                data = json.loads(obj)
                                if isinstance(data, dict) and data:
                                    attached_content_list.append(list(data.values())[0])
                            except json.JSONDecodeError:
                                print("Skipping invalid JSON:", obj)
                    else:
                        # D
                        response_inside_attached_data = response
                        for line in response.splitlines():
                            attached_content_list.append(line)
                except Exception as ex:
                    print(ex)

                for key, value in sorted(attached_answer.items()):
                    if key == 0:
                        for item in reversed(value):
                            attached_content_list.insert(0, item)
                    else:
                        attached_content_list.extend(value)

                return attached_content_list, response_inside_attached_data
            else:
                return response, response_inside_attached_data

        except Exception as e:
            print(f"Error generating content for section {section}: {str(e)}")
            exc.exception(f"Issue identified in generate_section_content function ... error : {e}")
            return f"[Error generating content for {section}]", None

    def yes_no_confirmation(self, user_input):
        try:
            prompt = f"""You are an AI assistant that helps classify user responses into 'yes' or 'no'. 
            User may enter different words like "go ahead", "proceed", or "stop". 
            Your task is to strictly return either 'yes' or 'no' with no explanation.

            Example Inputs and Outputs:
            - "yes" → "yes"
            - "go ahead" → "yes"
            - "continue" → "yes"
            - "s" → "yes"
            - "y" → "yes"
            - "no" → "no"
            - "stop" → "no"
            - "not now" → "no"
            - "n" → "no"
            - "not need" → "no"
            - "go next" → "no"

            User Input: "{user_input}"
            Output:"""

            llm_response = self.llm.invoke(prompt)  # Assuming you have llama_cpp integrated
            normalized = llm_response.strip().replace('<br>','\n').strip('"').strip("'").lower()
            try:
                normalized = normalized.split()[0].replace('"','')
            except:
                normalized = normalized
            return normalized
        except Exception as ex:
            print(ex)
            exc.exception(f"Issue identified in yes_no_confirmation function ... error : {ex}")

    def bcp_creation(self, question):
        try:
            datatype = "string"
            if question and not self.confirmation and not self.final_resp:
                if self.sec == 'company name':
                    self.add_first_page_title(question)
                elif self.sec == 'company logo':
                    self.add_header_footer(question)
                if self.sec is None:
                    consultant_question = "Hi, I am a BCM Consultant. I am here to help create a BCM plan for your company.\n can you please upload the BCM Template"
                    return consultant_question, datatype
                self.sec_quest[self.sec][self.ques] = question
                self.bcm_sections[self.sec] = question
                self.sec_ans.append(self.sec_quest)
            elif self.final_resp:
                if question != "":
                    self.bcm_sections[self.sec] = question
                    self.new_doc.add_paragraph(self.sec, style='Heading 1')
                    # D
                    if self.attached_file_flag:
                        question_list = question.splitlines()
                        attached_content_writer = AttachedFileWriter(question_list, self.new_doc)
                        attached_content_writer.write_to_doc()
                        self.attached_file_flag = False
                    else:
                        self.new_doc.add_paragraph(question)
                    self.final_resp = False
                    self.confirmation = False
            for section, value in self.bcm_sections.items():
                if value == '':
                    self.sec = section
                    self.sec_quest[self.sec] = {}
                    if section == 'company name':
                        self.ques = "what is the organization name?"
                        self.sec_quest[self.sec][self.ques] = ""
                        return self.ques, datatype
                    elif section == 'company logo':
                        self.ques = "Give me the organization logo?"
                        self.sec_quest[self.sec][self.ques] = ""
                        return self.ques, datatype
                    else:
                        if self.confirmation and not self.yes_no:
                            llm_confirmation = self.yes_no_confirmation(question)
                            if llm_confirmation.lower() == "yes":
                                self.yes_no = True
                                self.llm_questions = self.generate_section_questions(section)
                            elif llm_confirmation.lower() == "no":
                                self.bcm_sections[self.sec] = False
                                self.confirmation = False
                                continue
                            else:
                                return "Please enter 'yes' or 'no'.", datatype
                        if self.yes_no and self.confirmation:
                            if self.content_approve:
                                llm_confirmation = self.yes_no_confirmation(question)
                                if llm_confirmation.lower() == "yes":
                                    self.new_doc.add_paragraph(section, style='Heading 1')
                                    # D
                                    if self.attached_file_flag:
                                        attached_content_writer = AttachedFileWriter(self.content, self.new_doc)
                                        attached_content_writer.write_to_doc()
                                        self.attached_file_flag = False
                                    else:
                                        self.new_doc.add_paragraph(self.content)
                                    self.sec_quest[self.sec][self.ques] = question
                                    self.bcm_sections[self.sec] = self.content
                                    self.sec_ans.append(self.sec_quest)
                                    self.yes_no = False
                                    self.confirmation = False
                                    self.content_approve = False
                                    continue
                                else:
                                    self.final_resp = True
                                    self.yes_no = False
                                    return "Enter your own data", datatype
                            if self.ques in self.llm_questions:
                                self.sec_quest[self.sec][self.ques] = question
                                self.llm_questions[self.ques] = question
                            for ask_ques, llm_ask_value in self.llm_questions.items():
                                if llm_ask_value == "":
                                    self.sec_quest[self.sec][ask_ques] = ""
                                    self.ques = ask_ques
                                    return ask_ques, datatype
                            self.content, response_inside_attached_data = self.generate_section_content(section, self.llm_questions)
                            # D
                            if type(self.content) == list:
                                if response_inside_attached_data:
                                    content_confirm = f"{response_inside_attached_data}\n\nDo you approve the above content? (yes/no)"
                                else:
                                    content_confirm = "Do you approve the above content?(yes/no)"
                            else:
                                content_confirm = self.content + "\n\n Do you approve the above content?(yes/no)"

                            # if isinstance(self.content, list):
                            #     content_text = "\n".join(str(item) for item in self.content if isinstance(item, str))
                            #     content_confirm = f"{content_text}\n\nDo you approve the above content? (yes/no)" if content_text else "Do you approve the above content? (yes/no)"
                            # elif isinstance(self.content, str) and self.content.strip():
                            #     content_confirm = f"{self.content}\n\nDo you approve the above content? (yes/no)"
                            # else:
                            #     content_confirm = "Do you approve the above content? (yes/no)"

                            self.content_approve = True
                            return content_confirm, datatype
                        else:
                            print(f"\nSection: {section}")
                            print("-" * 50)
                            confirmation = f"Do you want this {section} section? (yes/no): "
                            self.confirmation = True
                            return confirmation, datatype

            buffer = BytesIO()
            self.new_doc.save(buffer)
            buffer.seek(0)
            filename = f"output/generated_bcp1_{datetime.now().strftime('%Y%m%d%H%M%S')}.docx"
            with open(filename, "wb") as f:
                f.write(buffer.read())
            with open(filename, "rb") as file:
                encoded_string = base64.b64encode(file.read()).decode("utf-8")
            return encoded_string, "docx"
        except Exception as ex:
            print(ex)
            exc.exception(f"Issue identified in bcp_creation function ... error : {ex}")
            return "BCM plan generated","string"

    def add_first_page_title(self, company_name):
        try:
            table = self.new_doc.add_table(rows=1, cols=1)
            cell = table.cell(0, 0)
            cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            cell.vertical_alignment = 1
            p1 = cell.add_paragraph("Business Continuity Plan")
            p1.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run1 = p1.runs[0]
            run1.bold = True
            run1.font.size = Pt(36)

            p2 = cell.add_paragraph(company_name)
            p2.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run2 = p2.runs[0]
            run2.bold = True
            run2.font.size = Pt(36)

            table.autofit = False
            table.columns[0].width = Inches(6.5)
            table.rows[0].height = Inches(9)
            self.new_doc.add_paragraph()
            self.add_header_footer("models/logo.png")
        except Exception as ex:
            print(ex)
            exc.exception(f"Issue identified in add_first_page_title function ... error : {ex}")

    def add_header_footer(self, header_logo):
        try:
            section = self.new_doc.sections[0]
            header = section.header
            paragraph = header.paragraphs[0]
            run = paragraph.add_run()
            run.add_picture(header_logo, width=Inches(2))
            paragraph.alignment = 2
            footer = section.footer
            footer_paragraph = footer.paragraphs[0]
            footer_paragraph.text = "Copyright © 2025 – All rights reserved with Perpetuuiti               Page Number:"
            page_number_run = footer_paragraph.add_run()
            page_number_field = OxmlElement('w:fldSimple')
            page_number_field.set(qn('w:instr'), 'PAGE')
            page_number_run._r.append(page_number_field)
        except Exception as ex:
            print(ex)
            exc.exception(f"Issue identified in add_header_footer function ... error : {ex}")
