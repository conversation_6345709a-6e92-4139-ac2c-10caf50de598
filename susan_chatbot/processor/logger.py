import logging
from logging.handlers import TimedRotatingFileHandler
import os
root = os.getcwd()
formatter = logging.Formatter(
    '%(levelname)s | %(asctime)s | module: %(module)s | lineno: %(lineno)d |  functName: %(funcName)s | %(message)s ')


def setup_logger(name, log_file, level=logging.DEBUG):
    """To set up as many loggers as you want"""
    logger = logging.getLogger(name)
    logger.setLevel(level)
    fh = TimedRotatingFileHandler(log_file, when='midnight',interval=1, backupCount=7)
    fh.setFormatter(formatter)
    logger.addHandler(fh)
    return logger


trace = setup_logger('info', root + '/logs/traces.log')
exc = setup_logger('exception', root + '/logs/exceptions.log')
