import json
from langchain_community.document_loaders import PyPDFLoader
import time
import torch
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.chains.retrieval import create_retrieval_chain
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_core.prompts import ChatPromptTemplate
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_cohere import Chat<PERSON><PERSON>ere
from processor.logger import trace,exc


device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
embeddings = HuggingFaceEmbeddings(model_name="models/instructor-xl")

with open('config.json','r+')as f:
    config = json.load(f)
    ChatCohere_api = config['ChatCohere_api']
    cohere_api_key = config['cohere_api_key']
    model = config['model']

llm = ChatCohere(
    model=model,
    cohere_api_key=cohere_api_key,
    temperature=0.5,
    citation_options={"mode": "accurate"}
)


class DocumentRetrievalAssistant:
    def __init__(self):
        self.db = None
        self.document_chain = None
        self.file_uploaded = False

    def process_pdf(self, file_path):
        try:
            s = time.time()
            txt_loader = PyPDFLoader(file_path)
            txt_documents = txt_loader.load()
            print("PDF processing time:", time.time() - s)

            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=3048,
                chunk_overlap=90,
                separators=['\n\n', '\n', '(?=>\. )', ' ', '']
            )

            docs = text_splitter.split_documents(txt_documents)
            self.db = FAISS.from_documents(docs, embeddings)
            self.file_uploaded = True

            prompt_template = ChatPromptTemplate.from_template("""    
    You are Susan, an AI assistant developed and fine-tuned by Perpetuuiti for BCM assistance. Your role is to
respond in a direct and conversational manner, like a one-on-one communication, strictly based on the information
from the BCM document.

- If the user greets with "Hi," "Hello," "Thanks," "Hey there," or any similar greeting, respond politely. - For
any other query, provide an answer only based on the BCM document. - If the query is unrelated, inform the user
that your role is strictly to provide BCM-related information and you cannot assist with other topics. 

                    **Context:** {context}  

                    **Question:** {input}  
                    **Answer:**  



Respond in a direct and conversational way, making it feel like a real discussion. Keep answers concise and
accurate, strictly from the BCM document.
                    """)



            self.document_chain = create_stuff_documents_chain(llm, prompt_template)
        except Exception as ex:
            print(ex)
            exc.exception(f"Issue identified in process_pdf function ... error : {ex}")

    def answer_question(self, question):
        try:
            retriever = self.db.as_retriever(search_type="mmr", search_kwargs={'k': 25, 'fetch_k': 50})
            retrieval_chain = create_retrieval_chain(retriever, self.document_chain)
            response = retrieval_chain.invoke({"input": question})
            final_response = response["answer"]
            return final_response
        except Exception as ex:
            print(ex)
            exc.exception(f"Issue identified in answer_question function ... error : {ex}")
