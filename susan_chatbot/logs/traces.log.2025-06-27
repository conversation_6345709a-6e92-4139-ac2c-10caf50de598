INFO | 2025-06-27 09:49:36,277 | module: main | lineno: 288 |  functName: login | login user:dhaya, user_id:761461, email:<EMAIL> 
INFO | 2025-06-27 09:52:44,618 | module: main | lineno: 288 |  functName: login | login user:rocky, user_id:718355, email:<EMAIL> 
INFO | 2025-06-27 10:25:43,545 | module: main | lineno: 288 |  functName: login | login user:rocky, user_id:718355, email:<EMAIL> 
INFO | 2025-06-27 11:17:46,140 | module: main | lineno: 288 |  functName: login | login user:dhaya, user_id:761461, email:<EMAIL> 
INFO | 2025-06-27 14:48:32,494 | module: main | lineno: 288 |  functName: login | login user:rocky, user_id:718355, email:<EMAIL> 
INFO | 2025-06-27 15:01:07,575 | module: main | lineno: 288 |  functName: login | login user:dhaya, user_id:761461, email:<EMAIL> 
INFO | 2025-06-27 15:53:09,392 | module: main | lineno: 288 |  functName: login | login user:rocky, user_id:718355, email:<EMAIL> 
INFO | 2025-06-27 16:55:48,106 | module: main | lineno: 288 |  functName: login | login user:rocky, user_id:718355, email:<EMAIL> 
INFO | 2025-06-27 17:58:50,222 | module: main | lineno: 288 |  functName: login | login user:rocky, user_id:718355, email:<EMAIL> 
INFO | 2025-06-27 17:59:01,158 | module: main | lineno: 288 |  functName: login | login user:dhaya, user_id:761461, email:<EMAIL> 
INFO | 2025-06-27 18:06:01,443 | module: main | lineno: 288 |  functName: login | login user:rocky, user_id:718355, email:<EMAIL> 
