import cv2
import torch
from ultralytics import <PERSON><PERSON><PERSON>

def main(video_path, model_path):
    # Load the YOLOv8 model from a specific path
    model = YOLO(model_path)

    # Open the video file
    cap = cv2.VideoCapture(video_path)

    if not cap.isOpened():
        print("Error opening video file.")
        return

    while True:
        # Read frame-by-frame
        ret, frame = cap.read()
        if not ret:
            break

        # Perform inference
        results = model(frame)

        # Draw bounding boxes
        for result in results:
            boxes = result.boxes.xyxy[0].cpu().numpy()  # Get the bounding boxes
            confidences = result.boxes.conf[0].cpu().numpy()  # Get the confidence scores
            classes = result.boxes.cls[0].cpu().numpy()  # Get the class IDs

            # Ensure boxes, confidences, and classes are iterables
            if boxes.ndim == 2:
                for box, conf, cls in zip(boxes, confidences, classes):
                    if cls == 0:  # Assuming class 0 is 'person' in your model
                        x1, y1, x2, y2 = map(int, box)
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        cv2.putText(frame, f'Person: {conf:.2f}', (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # Display the resulting frame
        cv2.imshow('YOLOv8 Human Detection', frame)

        # Break the loop if 'q' is pressed
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    # Release the capture and close windows
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    video_path = 'video_1.mp4'
    model_path = 'yolov8s.pt'
    main(video_path, model_path)
