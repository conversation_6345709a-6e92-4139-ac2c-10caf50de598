import os
import time
import shutil
import re
import torch
from torch.autograd import Variable
import cv2
import numpy as np
from collections import OrderedDict
from packages import craft_utils
from packages import imgproc
from packages.craft import CRAFT
from text_extraction import ImageProcessor
import logging


# Suppress PaddleOCR logs
class IgnoreWarningsFilter(logging.Filter):
    def filter(self, record):
        return 'angle classifier' not in record.getMessage()


logging.getLogger('ppocr').setLevel(logging.WARNING)
logging.getLogger('ppocr').addFilter(IgnoreWarningsFilter())


def clear_folder(folder_path):
    if os.path.exists(folder_path):
        shutil.rmtree(folder_path)
    os.makedirs(folder_path)


# Function to load the CRAFT model
def load_model(model_path, cuda=False):
    net = CRAFT()
    if cuda:
        net.load_state_dict(copy_state_dict(torch.load(model_path)))
        net = net.cuda()
        net = torch.nn.DataParallel(net)
        torch.backends.cudnn.benchmark = False
    else:
        net.load_state_dict(copy_state_dict(torch.load(model_path, map_location='cpu')))
    net.eval()
    return net


# Function to adjust the model's state_dict keys
def copy_state_dict(state_dict):
    if list(state_dict.keys())[0].startswith("module"):
        start_idx = 1
    else:
        start_idx = 0
    new_state_dict = OrderedDict()
    for k, v in state_dict.items():
        name = ".".join(k.split(".")[start_idx:])
        new_state_dict[name] = v
    return new_state_dict


model_path = './weights/craft_mlt_25k.pth'
net = load_model(model_path, cuda=False)


# Function to check if OCR text format matches vehicle number
def check_ocr_text_format(text):
    pattern = r'^[A-Za-z]{2}.*\d{4}$'
    return re.match(pattern, text) is not None


# Class for detecting number plates in images
class FINDnumberplate:
    def __init__(self, net, result_folder='./result/'):
        self.net = net
        self.result_folder = result_folder
        self.clear_result_folder()

    def clear_result_folder(self):
        if os.path.exists(self.result_folder):
            shutil.rmtree(self.result_folder)
        os.makedirs(self.result_folder)

    def test_net(self, image, text_threshold=0.7, link_threshold=0.4, low_text=0.4, cuda=False, poly=False,
                 mag_ratio=1.5, canvas_size=1280):
        img_resized, target_ratio, size_heatmap = imgproc.resize_aspect_ratio(image, canvas_size,
                                                                              interpolation=cv2.INTER_LINEAR,
                                                                              mag_ratio=mag_ratio)
        ratio_h = ratio_w = 1 / target_ratio

        x = imgproc.normalizeMeanVariance(img_resized)
        x = torch.from_numpy(x).permute(2, 0, 1)  # [h, w, c] to [c, h, w]
        x = Variable(x.unsqueeze(0))  # [c, h, w] to [b, c, h, w]
        if cuda:
            x = x.cuda()

        with torch.no_grad():
            y, _ = self.net(x)

        score_text = y[0, :, :, 0].cpu().data.numpy()
        score_link = y[0, :, :, 1].cpu().data.numpy()

        boxes, polys = craft_utils.getDetBoxes(score_text, score_link, text_threshold, link_threshold, low_text, poly)
        boxes = craft_utils.adjustResultCoordinates(boxes, ratio_w, ratio_h)
        polys = craft_utils.adjustResultCoordinates(polys, ratio_w, ratio_h)
        for k in range(len(polys)):
            if polys[k] is None:
                polys[k] = boxes[k]

        return boxes, polys

    def process_image(self, image):
        bboxes, polys = self.test_net(image)
        cropped_images = []
        for i, poly in enumerate(polys):
            if poly is not None:
                x_min = int(min(poly[:, 0]))
                x_max = int(max(poly[:, 0]))
                y_min = int(min(poly[:, 1]))
                y_max = int(max(poly[:, 1]))

                if x_min < x_max and y_min < y_max:
                    cropped_img = image[y_min:y_max, x_min:x_max]
                    cropped_images.append(cropped_img)

        return cropped_images

    def save_cropped_images(self, cropped_images, subdir):
        for i, cropped_img in enumerate(cropped_images):
            cropped_file = os.path.join(subdir, f"cropped_{i}.jpg")
            try:
                cv2.imwrite(cropped_file, cv2.cvtColor(cropped_img, cv2.COLOR_RGB2BGR))
            except Exception as e:
                print(e)


# Function to extract frames from a video file
def extract_frames(video_path, output_folder):
    os.makedirs(output_folder, exist_ok=True)
    cap = cv2.VideoCapture(video_path)
    fps = int(cap.get(cv2.CAP_PROP_FPS))

    frame_count = 0
    frame_numbers = 0
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        second = frame_count // fps

        if frame_count % fps == 0:
            print(f"Processing frame : {frame_numbers + 1}")
            frame_numbers += 1
            second_folder = os.path.join(output_folder, str(second + 1))
            os.makedirs(second_folder, exist_ok=True)

            # Get image dimensions
            height, width = frame.shape[:2]

            # Define crop boundaries
            top_boundary = 200
            bottom_boundary = height - 250

            # Crop the image using OpenCV
            cropped_img = frame[top_boundary:bottom_boundary, :]

            frame_filename = f"frame_{frame_count}.jpg"
            frame_path = os.path.join(second_folder, frame_filename)

            # Save the cropped image
            cv2.imwrite(frame_path, cropped_img)

        frame_count += 1

    cap.release()
    cv2.destroyAllWindows()


# Function to process frames in the extracted folders
number_plates_folder = 'output_number_plates'
os.makedirs(number_plates_folder, exist_ok=True)


def process_frames_from_folder(output_folder):
    subfolders = sorted([f for f in os.listdir(output_folder) if os.path.isdir(os.path.join(output_folder, f))],
                        key=int)
    number_plates = []

    for subfolder in subfolders:
        subdir = os.path.join(output_folder, subfolder)
        for file in sorted(os.listdir(subdir)):
            file_number = 0
            if file.startswith("frame"):
                file_path = os.path.join(subdir, file)
                image = cv2.imread(file_path)
                resized_image = cv2.resize(image, (1366, 768))
                image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)

                print(f"Processing {file_path}...")
                ocr_processor = FINDnumberplate(net)
                cropped_images = ocr_processor.process_image(image)
                ocr_processor.save_cropped_images(cropped_images, subdir)

                for cropped_image in cropped_images:
                    cropped_image = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2RGB)
                    processor = ImageProcessor()
                    words = processor.extract_text(cropped_image)
                    if words:
                        if (len(words) >= 9) and (words not in number_plates):
                            if check_ocr_text_format(words):
                                number_plates.append(words)
                                for num in number_plates:
                                    print(num)
                                print(f"\033[92mThe Vehicle Number : {words}\033[0m")
                                file_number += 1
                                cv2.putText(image, words, (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 3, (0, 255, 0), 5,
                                            cv2.LINE_AA)

                                base, ext = os.path.splitext(file)
                                number_plate_path = os.path.join(number_plates_folder,
                                                                 f"{base}_{file_number}{ext}")
                                cv2.imwrite(number_plate_path, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
                            else:
                                print(f"Other words : {words}")
                    else:
                        print("No texts in the image")

    print("Process Completed")


# Main execution with time tracking
if __name__ == '__main__':
    video_path = './video/2.mp4'  # Path to your input video file
    output_folder = 'output_frames'  # Main folder to save the frames
    clear_folder(output_folder)
    clear_folder(number_plates_folder)

    # Record the start time
    start_time = time.time()
    print(f"Start Time: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")

    # Extract frames from video
    extract_frames(video_path, output_folder)

    # Process extracted frames
    process_frames_from_folder(output_folder)

    # Record the end time
    end_time = time.time()
    print(f"End Time: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}")
    print(f"Total Time Taken: {end_time - start_time:.2f} seconds")
