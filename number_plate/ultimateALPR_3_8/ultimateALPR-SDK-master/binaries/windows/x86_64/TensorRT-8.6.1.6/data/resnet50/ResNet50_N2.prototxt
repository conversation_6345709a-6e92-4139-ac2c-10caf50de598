name: "ResNet-50"
input: "data"
input_dim: 1
input_dim: 3
input_dim: 224
input_dim: 224

layer {
	bottom: "data"
	top: "conv1"
	name: "conv1"
	type: "Convolution"
	convolution_param {
		num_output: 64
		kernel_size: 7
		pad: 3
		stride: 2
	}
}

layer {
	bottom: "conv1"
	top: "conv1"
	name: "bn_conv1"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "conv1"
	top: "conv1"
	name: "scale_conv1"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "conv1"
	top: "conv1"
	name: "conv1_relu"
	type: "ReLU"
}

layer {
	bottom: "conv1"
	top: "pool1"
	name: "pool1"
	type: "Pooling"
	pooling_param {
		kernel_size: 3
		stride: 2
		pool: MAX
	}
}

layer {
	bottom: "pool1"
	top: "res2a_branch1"
	name: "res2a_branch1"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res2a_branch1"
	top: "res2a_branch1"
	name: "bn2a_branch1"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res2a_branch1"
	top: "res2a_branch1"
	name: "scale2a_branch1"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "pool1"
	top: "res2a_branch2a"
	name: "res2a_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 64
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res2a_branch2a"
	top: "res2a_branch2a"
	name: "bn2a_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res2a_branch2a"
	top: "res2a_branch2a"
	name: "scale2a_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res2a_branch2a"
	top: "res2a_branch2a"
	name: "res2a_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res2a_branch2a"
	top: "res2a_branch2b"
	name: "res2a_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 64
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res2a_branch2b"
	top: "res2a_branch2b"
	name: "bn2a_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res2a_branch2b"
	top: "res2a_branch2b"
	name: "scale2a_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res2a_branch2b"
	top: "res2a_branch2b"
	name: "res2a_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res2a_branch2b"
	top: "res2a_branch2c"
	name: "res2a_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res2a_branch2c"
	top: "res2a_branch2c"
	name: "bn2a_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res2a_branch2c"
	top: "res2a_branch2c"
	name: "scale2a_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res2a_branch1"
	bottom: "res2a_branch2c"
	top: "res2a"
	name: "res2a"
	type: "Eltwise"
}

layer {
	bottom: "res2a"
	top: "res2a"
	name: "res2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res2a"
	top: "res2b_branch2a"
	name: "res2b_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 64
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res2b_branch2a"
	top: "res2b_branch2a"
	name: "bn2b_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res2b_branch2a"
	top: "res2b_branch2a"
	name: "scale2b_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res2b_branch2a"
	top: "res2b_branch2a"
	name: "res2b_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res2b_branch2a"
	top: "res2b_branch2b"
	name: "res2b_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 64
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res2b_branch2b"
	top: "res2b_branch2b"
	name: "bn2b_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res2b_branch2b"
	top: "res2b_branch2b"
	name: "scale2b_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res2b_branch2b"
	top: "res2b_branch2b"
	name: "res2b_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res2b_branch2b"
	top: "res2b_branch2c"
	name: "res2b_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res2b_branch2c"
	top: "res2b_branch2c"
	name: "bn2b_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res2b_branch2c"
	top: "res2b_branch2c"
	name: "scale2b_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res2a"
	bottom: "res2b_branch2c"
	top: "res2b"
	name: "res2b"
	type: "Eltwise"
}

layer {
	bottom: "res2b"
	top: "res2b"
	name: "res2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res2b"
	top: "res2c_branch2a"
	name: "res2c_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 64
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res2c_branch2a"
	top: "res2c_branch2a"
	name: "bn2c_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res2c_branch2a"
	top: "res2c_branch2a"
	name: "scale2c_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res2c_branch2a"
	top: "res2c_branch2a"
	name: "res2c_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res2c_branch2a"
	top: "res2c_branch2b"
	name: "res2c_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 64
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res2c_branch2b"
	top: "res2c_branch2b"
	name: "bn2c_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res2c_branch2b"
	top: "res2c_branch2b"
	name: "scale2c_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res2c_branch2b"
	top: "res2c_branch2b"
	name: "res2c_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res2c_branch2b"
	top: "res2c_branch2c"
	name: "res2c_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res2c_branch2c"
	top: "res2c_branch2c"
	name: "bn2c_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res2c_branch2c"
	top: "res2c_branch2c"
	name: "scale2c_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res2b"
	bottom: "res2c_branch2c"
	top: "res2c"
	name: "res2c"
	type: "Eltwise"
}

layer {
	bottom: "res2c"
	top: "res2c"
	name: "res2c_relu"
	type: "ReLU"
}

layer {
	bottom: "res2c"
	top: "res3a_branch1"
	name: "res3a_branch1"
	type: "Convolution"
	convolution_param {
		num_output: 512
		kernel_size: 1
		pad: 0
		stride: 2
		bias_term: false
	}
}

layer {
	bottom: "res3a_branch1"
	top: "res3a_branch1"
	name: "bn3a_branch1"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3a_branch1"
	top: "res3a_branch1"
	name: "scale3a_branch1"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res2c"
	top: "res3a_branch2a"
	name: "res3a_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 128
		kernel_size: 1
		pad: 0
		stride: 2
		bias_term: false
	}
}

layer {
	bottom: "res3a_branch2a"
	top: "res3a_branch2a"
	name: "bn3a_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3a_branch2a"
	top: "res3a_branch2a"
	name: "scale3a_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3a_branch2a"
	top: "res3a_branch2a"
	name: "res3a_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res3a_branch2a"
	top: "res3a_branch2b"
	name: "res3a_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 128
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res3a_branch2b"
	top: "res3a_branch2b"
	name: "bn3a_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3a_branch2b"
	top: "res3a_branch2b"
	name: "scale3a_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3a_branch2b"
	top: "res3a_branch2b"
	name: "res3a_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res3a_branch2b"
	top: "res3a_branch2c"
	name: "res3a_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 512
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res3a_branch2c"
	top: "res3a_branch2c"
	name: "bn3a_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3a_branch2c"
	top: "res3a_branch2c"
	name: "scale3a_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3a_branch1"
	bottom: "res3a_branch2c"
	top: "res3a"
	name: "res3a"
	type: "Eltwise"
}

layer {
	bottom: "res3a"
	top: "res3a"
	name: "res3a_relu"
	type: "ReLU"
}

layer {
	bottom: "res3a"
	top: "res3b_branch2a"
	name: "res3b_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 128
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res3b_branch2a"
	top: "res3b_branch2a"
	name: "bn3b_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3b_branch2a"
	top: "res3b_branch2a"
	name: "scale3b_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3b_branch2a"
	top: "res3b_branch2a"
	name: "res3b_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res3b_branch2a"
	top: "res3b_branch2b"
	name: "res3b_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 128
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res3b_branch2b"
	top: "res3b_branch2b"
	name: "bn3b_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3b_branch2b"
	top: "res3b_branch2b"
	name: "scale3b_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3b_branch2b"
	top: "res3b_branch2b"
	name: "res3b_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res3b_branch2b"
	top: "res3b_branch2c"
	name: "res3b_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 512
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res3b_branch2c"
	top: "res3b_branch2c"
	name: "bn3b_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3b_branch2c"
	top: "res3b_branch2c"
	name: "scale3b_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3a"
	bottom: "res3b_branch2c"
	top: "res3b"
	name: "res3b"
	type: "Eltwise"
}

layer {
	bottom: "res3b"
	top: "res3b"
	name: "res3b_relu"
	type: "ReLU"
}

layer {
	bottom: "res3b"
	top: "res3c_branch2a"
	name: "res3c_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 128
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res3c_branch2a"
	top: "res3c_branch2a"
	name: "bn3c_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3c_branch2a"
	top: "res3c_branch2a"
	name: "scale3c_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3c_branch2a"
	top: "res3c_branch2a"
	name: "res3c_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res3c_branch2a"
	top: "res3c_branch2b"
	name: "res3c_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 128
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res3c_branch2b"
	top: "res3c_branch2b"
	name: "bn3c_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3c_branch2b"
	top: "res3c_branch2b"
	name: "scale3c_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3c_branch2b"
	top: "res3c_branch2b"
	name: "res3c_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res3c_branch2b"
	top: "res3c_branch2c"
	name: "res3c_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 512
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res3c_branch2c"
	top: "res3c_branch2c"
	name: "bn3c_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3c_branch2c"
	top: "res3c_branch2c"
	name: "scale3c_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3b"
	bottom: "res3c_branch2c"
	top: "res3c"
	name: "res3c"
	type: "Eltwise"
}

layer {
	bottom: "res3c"
	top: "res3c"
	name: "res3c_relu"
	type: "ReLU"
}

layer {
	bottom: "res3c"
	top: "res3d_branch2a"
	name: "res3d_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 128
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res3d_branch2a"
	top: "res3d_branch2a"
	name: "bn3d_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3d_branch2a"
	top: "res3d_branch2a"
	name: "scale3d_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3d_branch2a"
	top: "res3d_branch2a"
	name: "res3d_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res3d_branch2a"
	top: "res3d_branch2b"
	name: "res3d_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 128
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res3d_branch2b"
	top: "res3d_branch2b"
	name: "bn3d_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3d_branch2b"
	top: "res3d_branch2b"
	name: "scale3d_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3d_branch2b"
	top: "res3d_branch2b"
	name: "res3d_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res3d_branch2b"
	top: "res3d_branch2c"
	name: "res3d_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 512
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res3d_branch2c"
	top: "res3d_branch2c"
	name: "bn3d_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res3d_branch2c"
	top: "res3d_branch2c"
	name: "scale3d_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3c"
	bottom: "res3d_branch2c"
	top: "res3d"
	name: "res3d"
	type: "Eltwise"
}

layer {
	bottom: "res3d"
	top: "res3d"
	name: "res3d_relu"
	type: "ReLU"
}

layer {
	bottom: "res3d"
	top: "res4a_branch1"
	name: "res4a_branch1"
	type: "Convolution"
	convolution_param {
		num_output: 1024
		kernel_size: 1
		pad: 0
		stride: 2
		bias_term: false
	}
}

layer {
	bottom: "res4a_branch1"
	top: "res4a_branch1"
	name: "bn4a_branch1"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4a_branch1"
	top: "res4a_branch1"
	name: "scale4a_branch1"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res3d"
	top: "res4a_branch2a"
	name: "res4a_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 1
		pad: 0
		stride: 2
		bias_term: false
	}
}

layer {
	bottom: "res4a_branch2a"
	top: "res4a_branch2a"
	name: "bn4a_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4a_branch2a"
	top: "res4a_branch2a"
	name: "scale4a_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4a_branch2a"
	top: "res4a_branch2a"
	name: "res4a_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res4a_branch2a"
	top: "res4a_branch2b"
	name: "res4a_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4a_branch2b"
	top: "res4a_branch2b"
	name: "bn4a_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4a_branch2b"
	top: "res4a_branch2b"
	name: "scale4a_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4a_branch2b"
	top: "res4a_branch2b"
	name: "res4a_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res4a_branch2b"
	top: "res4a_branch2c"
	name: "res4a_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 1024
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4a_branch2c"
	top: "res4a_branch2c"
	name: "bn4a_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4a_branch2c"
	top: "res4a_branch2c"
	name: "scale4a_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4a_branch1"
	bottom: "res4a_branch2c"
	top: "res4a"
	name: "res4a"
	type: "Eltwise"
}

layer {
	bottom: "res4a"
	top: "res4a"
	name: "res4a_relu"
	type: "ReLU"
}

layer {
	bottom: "res4a"
	top: "res4b_branch2a"
	name: "res4b_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4b_branch2a"
	top: "res4b_branch2a"
	name: "bn4b_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4b_branch2a"
	top: "res4b_branch2a"
	name: "scale4b_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4b_branch2a"
	top: "res4b_branch2a"
	name: "res4b_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res4b_branch2a"
	top: "res4b_branch2b"
	name: "res4b_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4b_branch2b"
	top: "res4b_branch2b"
	name: "bn4b_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4b_branch2b"
	top: "res4b_branch2b"
	name: "scale4b_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4b_branch2b"
	top: "res4b_branch2b"
	name: "res4b_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res4b_branch2b"
	top: "res4b_branch2c"
	name: "res4b_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 1024
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4b_branch2c"
	top: "res4b_branch2c"
	name: "bn4b_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4b_branch2c"
	top: "res4b_branch2c"
	name: "scale4b_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4a"
	bottom: "res4b_branch2c"
	top: "res4b"
	name: "res4b"
	type: "Eltwise"
}

layer {
	bottom: "res4b"
	top: "res4b"
	name: "res4b_relu"
	type: "ReLU"
}

layer {
	bottom: "res4b"
	top: "res4c_branch2a"
	name: "res4c_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4c_branch2a"
	top: "res4c_branch2a"
	name: "bn4c_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4c_branch2a"
	top: "res4c_branch2a"
	name: "scale4c_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4c_branch2a"
	top: "res4c_branch2a"
	name: "res4c_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res4c_branch2a"
	top: "res4c_branch2b"
	name: "res4c_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4c_branch2b"
	top: "res4c_branch2b"
	name: "bn4c_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4c_branch2b"
	top: "res4c_branch2b"
	name: "scale4c_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4c_branch2b"
	top: "res4c_branch2b"
	name: "res4c_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res4c_branch2b"
	top: "res4c_branch2c"
	name: "res4c_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 1024
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4c_branch2c"
	top: "res4c_branch2c"
	name: "bn4c_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4c_branch2c"
	top: "res4c_branch2c"
	name: "scale4c_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4b"
	bottom: "res4c_branch2c"
	top: "res4c"
	name: "res4c"
	type: "Eltwise"
}

layer {
	bottom: "res4c"
	top: "res4c"
	name: "res4c_relu"
	type: "ReLU"
}

layer {
	bottom: "res4c"
	top: "res4d_branch2a"
	name: "res4d_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4d_branch2a"
	top: "res4d_branch2a"
	name: "bn4d_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4d_branch2a"
	top: "res4d_branch2a"
	name: "scale4d_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4d_branch2a"
	top: "res4d_branch2a"
	name: "res4d_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res4d_branch2a"
	top: "res4d_branch2b"
	name: "res4d_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4d_branch2b"
	top: "res4d_branch2b"
	name: "bn4d_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4d_branch2b"
	top: "res4d_branch2b"
	name: "scale4d_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4d_branch2b"
	top: "res4d_branch2b"
	name: "res4d_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res4d_branch2b"
	top: "res4d_branch2c"
	name: "res4d_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 1024
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4d_branch2c"
	top: "res4d_branch2c"
	name: "bn4d_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4d_branch2c"
	top: "res4d_branch2c"
	name: "scale4d_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4c"
	bottom: "res4d_branch2c"
	top: "res4d"
	name: "res4d"
	type: "Eltwise"
}

layer {
	bottom: "res4d"
	top: "res4d"
	name: "res4d_relu"
	type: "ReLU"
}

layer {
	bottom: "res4d"
	top: "res4e_branch2a"
	name: "res4e_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4e_branch2a"
	top: "res4e_branch2a"
	name: "bn4e_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4e_branch2a"
	top: "res4e_branch2a"
	name: "scale4e_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4e_branch2a"
	top: "res4e_branch2a"
	name: "res4e_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res4e_branch2a"
	top: "res4e_branch2b"
	name: "res4e_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4e_branch2b"
	top: "res4e_branch2b"
	name: "bn4e_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4e_branch2b"
	top: "res4e_branch2b"
	name: "scale4e_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4e_branch2b"
	top: "res4e_branch2b"
	name: "res4e_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res4e_branch2b"
	top: "res4e_branch2c"
	name: "res4e_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 1024
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4e_branch2c"
	top: "res4e_branch2c"
	name: "bn4e_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4e_branch2c"
	top: "res4e_branch2c"
	name: "scale4e_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4d"
	bottom: "res4e_branch2c"
	top: "res4e"
	name: "res4e"
	type: "Eltwise"
}

layer {
	bottom: "res4e"
	top: "res4e"
	name: "res4e_relu"
	type: "ReLU"
}

layer {
	bottom: "res4e"
	top: "res4f_branch2a"
	name: "res4f_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4f_branch2a"
	top: "res4f_branch2a"
	name: "bn4f_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4f_branch2a"
	top: "res4f_branch2a"
	name: "scale4f_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4f_branch2a"
	top: "res4f_branch2a"
	name: "res4f_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res4f_branch2a"
	top: "res4f_branch2b"
	name: "res4f_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 256
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4f_branch2b"
	top: "res4f_branch2b"
	name: "bn4f_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4f_branch2b"
	top: "res4f_branch2b"
	name: "scale4f_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4f_branch2b"
	top: "res4f_branch2b"
	name: "res4f_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res4f_branch2b"
	top: "res4f_branch2c"
	name: "res4f_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 1024
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res4f_branch2c"
	top: "res4f_branch2c"
	name: "bn4f_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res4f_branch2c"
	top: "res4f_branch2c"
	name: "scale4f_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4e"
	bottom: "res4f_branch2c"
	top: "res4f"
	name: "res4f"
	type: "Eltwise"
}

layer {
	bottom: "res4f"
	top: "res4f"
	name: "res4f_relu"
	type: "ReLU"
}

layer {
	bottom: "res4f"
	top: "res5a_branch1"
	name: "res5a_branch1"
	type: "Convolution"
	convolution_param {
		num_output: 2048
		kernel_size: 1
		pad: 0
		stride: 2
		bias_term: false
	}
}

layer {
	bottom: "res5a_branch1"
	top: "res5a_branch1"
	name: "bn5a_branch1"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res5a_branch1"
	top: "res5a_branch1"
	name: "scale5a_branch1"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res4f"
	top: "res5a_branch2a"
	name: "res5a_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 512
		kernel_size: 1
		pad: 0
		stride: 2
		bias_term: false
	}
}

layer {
	bottom: "res5a_branch2a"
	top: "res5a_branch2a"
	name: "bn5a_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res5a_branch2a"
	top: "res5a_branch2a"
	name: "scale5a_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res5a_branch2a"
	top: "res5a_branch2a"
	name: "res5a_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res5a_branch2a"
	top: "res5a_branch2b"
	name: "res5a_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 512
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res5a_branch2b"
	top: "res5a_branch2b"
	name: "bn5a_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res5a_branch2b"
	top: "res5a_branch2b"
	name: "scale5a_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res5a_branch2b"
	top: "res5a_branch2b"
	name: "res5a_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res5a_branch2b"
	top: "res5a_branch2c"
	name: "res5a_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 2048
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res5a_branch2c"
	top: "res5a_branch2c"
	name: "bn5a_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res5a_branch2c"
	top: "res5a_branch2c"
	name: "scale5a_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res5a_branch1"
	bottom: "res5a_branch2c"
	top: "res5a"
	name: "res5a"
	type: "Eltwise"
}

layer {
	bottom: "res5a"
	top: "res5a"
	name: "res5a_relu"
	type: "ReLU"
}

layer {
	bottom: "res5a"
	top: "res5b_branch2a"
	name: "res5b_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 512
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res5b_branch2a"
	top: "res5b_branch2a"
	name: "bn5b_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res5b_branch2a"
	top: "res5b_branch2a"
	name: "scale5b_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res5b_branch2a"
	top: "res5b_branch2a"
	name: "res5b_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res5b_branch2a"
	top: "res5b_branch2b"
	name: "res5b_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 512
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res5b_branch2b"
	top: "res5b_branch2b"
	name: "bn5b_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res5b_branch2b"
	top: "res5b_branch2b"
	name: "scale5b_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res5b_branch2b"
	top: "res5b_branch2b"
	name: "res5b_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res5b_branch2b"
	top: "res5b_branch2c"
	name: "res5b_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 2048
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res5b_branch2c"
	top: "res5b_branch2c"
	name: "bn5b_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res5b_branch2c"
	top: "res5b_branch2c"
	name: "scale5b_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res5a"
	bottom: "res5b_branch2c"
	top: "res5b"
	name: "res5b"
	type: "Eltwise"
}

layer {
	bottom: "res5b"
	top: "res5b"
	name: "res5b_relu"
	type: "ReLU"
}

layer {
	bottom: "res5b"
	top: "res5c_branch2a"
	name: "res5c_branch2a"
	type: "Convolution"
	convolution_param {
		num_output: 512
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res5c_branch2a"
	top: "res5c_branch2a"
	name: "bn5c_branch2a"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res5c_branch2a"
	top: "res5c_branch2a"
	name: "scale5c_branch2a"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res5c_branch2a"
	top: "res5c_branch2a"
	name: "res5c_branch2a_relu"
	type: "ReLU"
}

layer {
	bottom: "res5c_branch2a"
	top: "res5c_branch2b"
	name: "res5c_branch2b"
	type: "Convolution"
	convolution_param {
		num_output: 512
		kernel_size: 3
		pad: 1
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res5c_branch2b"
	top: "res5c_branch2b"
	name: "bn5c_branch2b"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res5c_branch2b"
	top: "res5c_branch2b"
	name: "scale5c_branch2b"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res5c_branch2b"
	top: "res5c_branch2b"
	name: "res5c_branch2b_relu"
	type: "ReLU"
}

layer {
	bottom: "res5c_branch2b"
	top: "res5c_branch2c"
	name: "res5c_branch2c"
	type: "Convolution"
	convolution_param {
		num_output: 2048
		kernel_size: 1
		pad: 0
		stride: 1
		bias_term: false
	}
}

layer {
	bottom: "res5c_branch2c"
	top: "res5c_branch2c"
	name: "bn5c_branch2c"
	type: "BatchNorm"
	batch_norm_param {
		use_global_stats: true
	}
}

layer {
	bottom: "res5c_branch2c"
	top: "res5c_branch2c"
	name: "scale5c_branch2c"
	type: "Scale"
	scale_param {
		bias_term: true
	}
}

layer {
	bottom: "res5b"
	bottom: "res5c_branch2c"
	top: "res5c"
	name: "res5c"
	type: "Eltwise"
}

layer {
	bottom: "res5c"
	top: "res5c"
	name: "res5c_relu"
	type: "ReLU"
}

layer {
	bottom: "res5c"
	top: "pool5"
	name: "pool5"
	type: "Pooling"
	pooling_param {
		kernel_size: 7
		stride: 1
		pool: AVE
	}
}

layer {
	bottom: "pool5"
	top: "fc1000"
	name: "fc1000"
	type: "InnerProduct"
	inner_product_param {
		num_output: 1000
	}
}

layer {
	bottom: "fc1000"
	top: "prob"
	name: "prob"
	type: "Softmax"
}
