# GoogleNet Model

## GoogleNet
File: [googlenet.prototxt](https://github.com/BVLC/caffe/blob/master/models/bvlc_googlenet/deploy.prototxt)
GoogleNet is a deep convolution neural network for classification and detection introduced in "Going Deeper with
Convolutions", by <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> [https://arxiv.org/abs/1409.4842](https://arxiv.org/abs/1409.4842)


File: [googlenet.caffemodel](http://dl.caffe.berkeleyvision.org/bvlc_googlenet.caffemodel)
This model is a replication of the model described in the GoogleNet publication with some differences that are
documented in [Caffe github page](https://github.com/BVLC/caffe/tree/master/models/bvlc_googlenet)
