onnx==1.9.0; python_version<"3.8"
onnx==1.12.0; python_version>="3.8"
onnxruntime==1.8.1; python_version<"3.8"
onnxruntime==1.12.1; python_version>="3.8"
Pillow
git+https://github.com/facebookresearch/detectron2.git
git+https://github.com/NVIDIA/TensorRT#subdirectory=tools/onnx-graphsurgeon
cuda-python
pywin32; platform_system == "Windows"
pyyaml
requests
tqdm
numpy==1.18.1; python_version<"3.8" and platform_system == "Windows"
numpy==1.19.4; python_version<"3.8" and platform_system != "Windows"
numpy==1.23.2; python_version>="3.8"
