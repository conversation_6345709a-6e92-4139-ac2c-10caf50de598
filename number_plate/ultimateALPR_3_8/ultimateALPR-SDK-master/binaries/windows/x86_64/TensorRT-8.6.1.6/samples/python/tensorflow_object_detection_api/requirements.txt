onnx==1.10.2; python_version<"3.8"
onnx==1.12.0; python_version>="3.8"
onnxruntime==1.9.0; python_version<"3.8"
onnxruntime==1.12.1; python_version>="3.8"
Pillow
tf2onnx==1.8.1
git+https://github.com/cocodataset/cocoapi.git#subdirectory=PythonAPI; platform_system != "Windows"
pycocotools-windows; platform_system == "Windows"
cuda-python
pywin32; platform_system == "Windows"
pyyaml
requests
tqdm
numpy>=1.19.4
