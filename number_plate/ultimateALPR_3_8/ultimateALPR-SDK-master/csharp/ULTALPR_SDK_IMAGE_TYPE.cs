/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 2.0.9
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

namespace org.doubango.ultimateAlpr.Sdk {

public enum ULTALPR_SDK_IMAGE_TYPE {
  ULTALPR_SDK_IMAGE_TYPE_RGB24,
  ULTALPR_SDK_IMAGE_TYPE_RGBA32,
  ULTALPR_SDK_IMAGE_TYPE_BGRA32,
  ULTALPR_SDK_IMAGE_TYPE_NV12,
  ULTALPR_SDK_IMAGE_TYPE_NV21,
  ULTALPR_SDK_IMAGE_TYPE_YUV420P,
  ULTALPR_SDK_IMAGE_TYPE_YVU420P,
  ULTALPR_SDK_IMAGE_TYPE_YUV422P,
  ULTALPR_SDK_IMAGE_TYPE_YUV444P,
  ULTALPR_SDK_IMAGE_TYPE_Y,
  ULTALPR_SDK_IMAGE_TYPE_BGR24
}

}
