/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 2.0.9
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.doubango.ultimateAlpr.Sdk;

public class UltAlprSdkResult {
  private long swigCPtr;
  protected boolean swigCMemOwn;

  protected UltAlprSdkResult(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(UltAlprSdkResult obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        ultimateAlprSdkJNI.delete_UltAlprSdkResult(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public UltAlprSdkResult() {
    this(ultimateAlprSdkJNI.new_UltAlprSdkResult__SWIG_0(), true);
  }

  public UltAlprSdkResult(int code, String phrase, String json, long numPlates, long numCars) {
    this(ultimateAlprSdkJNI.new_UltAlprSdkResult__SWIG_1(code, phrase, json, numPlates, numCars), true);
  }

  public UltAlprSdkResult(int code, String phrase, String json, long numPlates) {
    this(ultimateAlprSdkJNI.new_UltAlprSdkResult__SWIG_2(code, phrase, json, numPlates), true);
  }

  public UltAlprSdkResult(int code, String phrase, String json) {
    this(ultimateAlprSdkJNI.new_UltAlprSdkResult__SWIG_3(code, phrase, json), true);
  }

  public UltAlprSdkResult(UltAlprSdkResult other) {
    this(ultimateAlprSdkJNI.new_UltAlprSdkResult__SWIG_4(UltAlprSdkResult.getCPtr(other), other), true);
  }

  public int code() {
    return ultimateAlprSdkJNI.UltAlprSdkResult_code(swigCPtr, this);
  }

  public String phrase() {
    return ultimateAlprSdkJNI.UltAlprSdkResult_phrase(swigCPtr, this);
  }

  public String json() {
    return ultimateAlprSdkJNI.UltAlprSdkResult_json(swigCPtr, this);
  }

  public long numPlates() {
    return ultimateAlprSdkJNI.UltAlprSdkResult_numPlates(swigCPtr, this);
  }

  public long numCars() {
    return ultimateAlprSdkJNI.UltAlprSdkResult_numCars(swigCPtr, this);
  }

  public boolean isOK() {
    return ultimateAlprSdkJNI.UltAlprSdkResult_isOK(swigCPtr, this);
  }

}
