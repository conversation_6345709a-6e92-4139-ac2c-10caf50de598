/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 2.0.9
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.doubango.ultimateAlpr.Sdk;

public class UltAlprSdkParallelDeliveryCallback {
  private long swigCPtr;
  protected boolean swigCMemOwn;

  protected UltAlprSdkParallelDeliveryCallback(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(UltAlprSdkParallelDeliveryCallback obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        ultimateAlprSdkJNI.delete_UltAlprSdkParallelDeliveryCallback(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  protected void swigDirectorDisconnect() {
    swigCMemOwn = false;
    delete();
  }

  public void swigReleaseOwnership() {
    swigCMemOwn = false;
    ultimateAlprSdkJNI.UltAlprSdkParallelDeliveryCallback_change_ownership(this, swigCPtr, false);
  }

  public void swigTakeOwnership() {
    swigCMemOwn = true;
    ultimateAlprSdkJNI.UltAlprSdkParallelDeliveryCallback_change_ownership(this, swigCPtr, true);
  }

  protected UltAlprSdkParallelDeliveryCallback() {
    this(ultimateAlprSdkJNI.new_UltAlprSdkParallelDeliveryCallback(), true);
    ultimateAlprSdkJNI.UltAlprSdkParallelDeliveryCallback_director_connect(this, swigCPtr, swigCMemOwn, true);
  }

  public void onNewResult(UltAlprSdkResult newResult) {
    ultimateAlprSdkJNI.UltAlprSdkParallelDeliveryCallback_onNewResult(swigCPtr, this, UltAlprSdkResult.getCPtr(newResult), newResult);
  }

}
