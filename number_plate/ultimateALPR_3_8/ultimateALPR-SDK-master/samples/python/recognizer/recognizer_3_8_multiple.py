# import sys
# import json
# import os.path
# import cv2
# import time
# import numpy as np
# import pytesseract
# import re
# import threading
#
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
#
# sys.path.append(r"E:\d_rtsp\number_plate\ultimateALPR_3_8\ultimateALPR-SDK-master\binaries\windows\x86_64")
#
# import ultimateAlprSdk
# from PIL import Image, ExifTags, ImageOps
#
# TAG = "[PythonRecognizer] "
#
# # Defines the default JSON configuration
# JSON_CONFIG = {
#     "debug_level": "info",
#     "debug_write_input_image_enabled": False,
#     "debug_internal_data_path": ".",
#     "num_threads": -1,
#     "gpgpu_enabled": True,
#     "max_latency": -1,
#     "klass_vcr_gamma": 1.5,
#     "detect_roi": [0, 0, 0, 0],
#     "detect_minscore": 0.1,
#     "car_noplate_detect_min_score": 0.8,
#     "pyramidal_search_enabled": True,
#     "pyramidal_search_sensitivity": 0.28,
#     "pyramidal_search_minscore": 0.3,
#     "pyramidal_search_min_image_size_inpixels": 800,
#     "recogn_rectify_enabled": True,
#     "recogn_minscore": 0.3,
#     "recogn_score_type": "min"
# }
#
# IMAGE_TYPES_MAPPING = {
#     'RGB': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_RGB24,
#     'RGBA': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_RGBA32,
#     'L': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_Y
# }
#
# def crop_and_ocr(image_path, coords, plate_text):
#     # Load the image
#     image = cv2.imread(image_path)
#
#     # Convert coordinates to integer tuples
#     points = np.array(coords, dtype=np.int32).reshape((-1, 2))
#
#     # Find the bounding rectangle of the polygon
#     x, y, w, h = cv2.boundingRect(points)
#
#     # Crop the bounding box from the image
#     cropped_image = image[y:y+h, x:x+w]
#     cv2.imwrite(f"out_{plate_text}.png", cropped_image)
#
#     # Convert the cropped image to PIL format
#     pil_image = Image.fromarray(cv2.cvtColor(cropped_image, cv2.COLOR_BGR2RGB))
#
#     # Perform OCR on the cropped image with PSM 7 (single line of text)
#     start_time = time.time()
#     text = pytesseract.image_to_string(pil_image, config='--psm 7')
#     end_time = time.time()
#
#     # Calculate execution time
#     execution_time = end_time - start_time
#
#     # Clean the OCR result
#     cleaned_result = re.sub(r'[^\w\s]', '', text).strip()
#     last_digit = cleaned_result[-1] if cleaned_result else '0'
#
#     print(f'plate_text: {plate_text}')
#     print(f"tesseract_result: {cleaned_result}")
#     print(f'last_digit: {last_digit}')
#     print(f'NUMBER PLATE: {plate_text[:-1] + last_digit}')
#     print(f'OCR Execution Time: {execution_time:.2f} seconds')
#
# def load_pil_image(path):
#     pil_image = Image.open(path)
#     img_exif = pil_image.getexif()
#     ret = {}
#     orientation = 1
#     try:
#         if img_exif:
#             for tag, value in img_exif.items():
#                 decoded = ExifTags.TAGS.get(tag, tag)
#                 ret[decoded] = value
#             orientation = ret.get("Orientation", 1)
#     except Exception as e:
#         print(TAG + "An exception occurred: {}".format(e))
#
#     if orientation > 1:
#         pil_image = ImageOps.exif_transpose(pil_image)
#
#     if pil_image.mode in IMAGE_TYPES_MAPPING:
#         imageType = IMAGE_TYPES_MAPPING[pil_image.mode]
#     else:
#         raise ValueError(TAG + "Invalid mode: %s" % pil_image.mode)
#
#     return pil_image, imageType
#
# def checkResult(operation, result):
#     if not result.isOK():
#         print(TAG + operation + ": failed -> " + result.phrase())
#         assert False
#     else:
#         result_json = json.loads(result.json())
#         return result_json
#
# def process_plate(image_path, plate_info):
#     plate_text = plate_info.get('text', '')
#     plate_warped_box = plate_info.get('warpedBox', [])
#     if plate_text and plate_warped_box:
#         crop_and_ocr(image_path, plate_warped_box, plate_text)
#
# # Entry point
# if __name__ == "__main__":
#     assets_folder = r"../../../assets"
#     charset = "latin"
#     tokenfile = ""
#     tokendata = ""
#
#     JSON_CONFIG["assets_folder"] = assets_folder
#     JSON_CONFIG["charset"] = charset
#     JSON_CONFIG["car_noplate_detect_enabled"] = False
#     JSON_CONFIG["ienv_enabled"] = False
#     JSON_CONFIG["openvino_enabled"] = True
#     JSON_CONFIG["openvino_device"] = "GPU"
#     JSON_CONFIG["npu_enabled"] = True
#     JSON_CONFIG["klass_lpci_enabled"] = False
#     JSON_CONFIG["klass_vcr_enabled"] = False
#     JSON_CONFIG["klass_vmmr_enabled"] = False
#     JSON_CONFIG["klass_vbsr_enabled"] = False
#     JSON_CONFIG["license_token_file"] = tokenfile
#     JSON_CONFIG["license_token_data"] = tokendata
#
#     checkResult("Init", ultimateAlprSdk.UltAlprSdkEngine_init(json.dumps(JSON_CONFIG)))
#
#     while True:
#         image_path = input("Enter image path: ")
#
#         if not os.path.isfile(image_path):
#             raise OSError(TAG + "File doesn't exist: %s" % image_path)
#
#         image, imageType = load_pil_image(image_path)
#         width, height = image.size
#
#         number_plate_details = checkResult("Process", ultimateAlprSdk.UltAlprSdkEngine_process(
#             imageType,
#             image.tobytes(),
#             width,
#             height,
#             0,
#             1
#         ))
#
#         plates = number_plate_details.get('plates', [])
#         if plates:
#             threads = []
#             for plate_info in plates:
#                 t = threading.Thread(target=process_plate, args=(image_path, plate_info))
#                 t.start()
#                 threads.append(t)
#
#             for t in threads:
#                 t.join()
#         else:
#             print("No plates found.")


import sys
import json
import os.path
import cv2
import time
import numpy as np
import pytesseract
import re
import threading
from queue import Queue

pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

sys.path.append(r"E:\d_rtsp\number_plate\ultimateALPR_3_8\ultimateALPR-SDK-master\binaries\windows\x86_64")

import ultimateAlprSdk
from PIL import Image, ExifTags, ImageOps

TAG = "[PythonRecognizer] "

# Defines the default JSON configuration
JSON_CONFIG = {
    "debug_level": "info",
    "debug_write_input_image_enabled": False,
    "debug_internal_data_path": ".",
    "num_threads": -1,
    "gpgpu_enabled": True,
    "max_latency": -1,
    "klass_vcr_gamma": 1.5,
    "detect_roi": [0, 0, 0, 0],
    "detect_minscore": 0.1,
    "car_noplate_detect_min_score": 0.8,
    "pyramidal_search_enabled": True,
    "pyramidal_search_sensitivity": 0.28,
    "pyramidal_search_minscore": 0.3,
    "pyramidal_search_min_image_size_inpixels": 800,
    "recogn_rectify_enabled": True,
    "recogn_minscore": 0.3,
    "recogn_score_type": "min"
}

IMAGE_TYPES_MAPPING = {
    'RGB': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_RGB24,
    'RGBA': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_RGBA32,
    'L': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_Y
}

def crop_and_ocr(image, coords):
    # Convert coordinates to integer tuples
    points = np.array(coords, dtype=np.int32).reshape((-1, 2))

    # Find the bounding rectangle of the polygon
    x, y, w, h = cv2.boundingRect(points)

    # Crop the bounding box from the image
    cropped_image = image[y:y+h, x:x+w]

    # Convert the cropped image to PIL format
    pil_image = Image.fromarray(cv2.cvtColor(cropped_image, cv2.COLOR_BGR2RGB))


    # Perform OCR on the cropped image with PSM 7 (single line of text)
    start_time = time.time()
    text = pytesseract.image_to_string(pil_image, config='--psm 7')
    end_time = time.time()

    cv2.polylines(image, [points], isClosed=True, color=(0, 0, 255), thickness=2)

    # Calculate execution time
    execution_time = end_time - start_time

    return text, execution_time, (x, y, w, h)

def load_pil_image(path):
    pil_image = Image.open(path)
    img_exif = pil_image.getexif()
    ret = {}
    orientation = 1
    try:
        if img_exif:
            for tag, value in img_exif.items():
                decoded = ExifTags.TAGS.get(tag, tag)
                ret[decoded] = value
            orientation = ret.get("Orientation", 1)
    except Exception as e:
        print(TAG + "An exception occurred: {}".format(e))

    if orientation > 1:
        pil_image = ImageOps.exif_transpose(pil_image)

    if pil_image.mode in IMAGE_TYPES_MAPPING:
        imageType = IMAGE_TYPES_MAPPING[pil_image.mode]
    else:
        raise ValueError(TAG + "Invalid mode: %s" % pil_image.mode)

    return pil_image, imageType


# Check result and extract details
def checkResult(operation, result):
    if not result.isOK():
        print(TAG + operation + ": failed -> " + result.phrase())
        assert False
    else:
        # Parse the JSON string to a dictionary
        result_json = json.loads(result.json())
        return result_json

def process_plate(plate_info, image, results_queue):
    plate_text = plate_info.get('text', '')
    plate_warped_box = plate_info.get('warpedBox', [])

    result, time_taken, bbox = crop_and_ocr(image, plate_warped_box)

    # Clean and extract last digit
    t_result = re.sub(r'[^\w\s]', '', result)
    cleaned_result = t_result.strip()
    last_digit = '0' if not cleaned_result else cleaned_result[-1]

    final_plate_text = plate_text[:-1] + last_digit

    results_queue.put((final_plate_text, bbox))

def draw_results(image, results):
    for plate_text, bbox in results:
        x, y, w, h = bbox
        font_scale = w / 250
        font_thickness = max(int(w / 500), 1)

        # Get text size
        text_size, _ = cv2.getTextSize(plate_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, font_thickness)

        # Create a light yellow background
        bg_color = (0, 255, 255)  # Light yellow in BGR
        text_color = (0, 0, 0)  # Black in BGR

        # Calculate background rectangle coordinates
        bg_w = text_size[0] + 10  # Add some padding
        bg_h = text_size[1] + 10  # Add some padding
        bg_x = x
        bg_y = y - bg_h - 2  # Place background just above the bounding box with 2 pixels gap

        # Ensure the background doesn't go out of the image
        bg_x = max(bg_x, 0)
        bg_y = max(bg_y, 0)

        # Draw the background rectangle
        cv2.rectangle(image, (bg_x, bg_y), (bg_x + bg_w, bg_y + bg_h), bg_color, -1)

        # Calculate text position within the background
        text_x = bg_x + 5  # 5 pixels padding from left
        text_y = bg_y + bg_h - 5  # 5 pixels padding from bottom

        # Display number plate text on the image
        cv2.putText(image, plate_text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX,
                    font_scale, text_color, font_thickness, cv2.LINE_AA)

    return image

# Entry point
if __name__ == "__main__":
    # Specify the image path and assets folder here

    assets_folder = r"../../../assets"
    charset = "latin"  # Specify charset if needed
    tokenfile = ""  # Specify path to license token file if needed
    tokendata = ""  # Specify base64 license token data if needed

    # Update JSON options
    JSON_CONFIG["assets_folder"] = assets_folder
    JSON_CONFIG["charset"] = charset
    JSON_CONFIG["car_noplate_detect_enabled"] = False  # Set to True or False as needed
    JSON_CONFIG["ienv_enabled"] = False  # Set to True or False as needed
    JSON_CONFIG["openvino_enabled"] = True  # Set to True or False as needed
    JSON_CONFIG["openvino_device"] = "GPU"  # Set to desired device
    JSON_CONFIG["npu_enabled"] = True  # Set to True or False as needed
    JSON_CONFIG["klass_lpci_enabled"] = False  # Set to True or False as needed
    JSON_CONFIG["klass_vcr_enabled"] = False  # Set to True or False as needed
    JSON_CONFIG["klass_vmmr_enabled"] = False  # Set to True or False as needed
    JSON_CONFIG["klass_vbsr_enabled"] = False  # Set to True or False as needed
    JSON_CONFIG["license_token_file"] = tokenfile
    JSON_CONFIG["license_token_data"] = tokendata

    checkResult("Init", ultimateAlprSdk.UltAlprSdkEngine_init(json.dumps(JSON_CONFIG)))

    while True:
        image_path = input("Enter image path: ")
        start_time = time.time()

        # Check if image exists
        if not os.path.isfile(image_path):
            raise OSError(TAG + "File doesn't exist: %s" % image_path)

        # Decode the image and extract type
        image, imageType = load_pil_image(image_path)
        width, height = image.size

        number_plate_details = checkResult("Process", ultimateAlprSdk.UltAlprSdkEngine_process(
            imageType,
            image.tobytes(),
            width,
            height,
            0,
            1
        ))

        # Convert PIL image to OpenCV format for drawing
        image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # Extracting plates
        plates = number_plate_details.get('plates', [])
        print(f"Number of plates detected: {len(plates)}")

        if plates:
            threads = []
            results_queue = Queue()

            for plate_info in plates:
                thread = threading.Thread(target=process_plate, args=(plate_info, image_cv, results_queue))
                threads.append(thread)
                thread.start()

            # Wait for all threads to complete
            for thread in threads:
                thread.join()

            # Collect results
            results = []
            while not results_queue.empty():
                results.append(results_queue.get())

            # Draw results on the image
            image_with_plates = draw_results(image_cv, results)

            # Display and save the result
            # cv2.imshow("Number Plates", image_with_plates)
            # cv2.waitKey(0)
            # cv2.destroyAllWindows()

            output_path = 'output_with_multiple_plates.png'
            cv2.imwrite(output_path, image_with_plates)

            print(f'Output saved to {output_path}')

            # Print all detected number plates
            for i, (plate_text, _) in enumerate(results, 1):
                print(f'NUMBER PLATE {i}: {plate_text}')

        else:
            print("No plates found.")
        end_time = time.time()
        print(f"END: {end_time - start_time}")