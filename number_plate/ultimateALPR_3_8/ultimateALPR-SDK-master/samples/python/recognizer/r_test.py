import sys
import json
import cv2
import time
import numpy as np
import re
import threading
from queue import Queue, Empty
from PIL import Image
from paddleocr import PaddleOCR

# Adjust this path to your UltimateALPR SDK installation
sys.path.append(r"E:\d_rtsp\number_plate\ultimateALPR_3_8\ultimateALPR-SDK-master\binaries\windows\x86_64")

import ultimateAlprSdk

TAG = "[PythonRecognizer] "

# Defines the default JSON configuration
JSON_CONFIG = {
    "debug_level": "info",
    "debug_write_input_image_enabled": False,
    "debug_internal_data_path": ".",
    "num_threads": -1,
    "gpgpu_enabled": True,
    "max_latency": -1,
    "klass_vcr_gamma": 1.5,
    "detect_roi": [0, 0, 0, 0],
    "detect_minscore": 0.1,
    "car_noplate_detect_min_score": 0.8,
    "pyramidal_search_enabled": True,
    "pyramidal_search_sensitivity": 0.28,
    "pyramidal_search_minscore": 0.3,
    "pyramidal_search_min_image_size_inpixels": 800,
    "recogn_rectify_enabled": True,
    "recogn_minscore": 0.3,
    "recogn_score_type": "min"
}

IMAGE_TYPES_MAPPING = {
    'RGB': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_RGB24,
    'RGBA': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_RGBA32,
    'L': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_Y
}

# Initialize PaddleOCR with GPU
ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=True)

# ... (rest of the functions remain the same)

def main():
    assets_folder = r"../../../assets"
    charset = "latin"
    tokenfile = ""
    tokendata = ""

    JSON_CONFIG.update({
        "assets_folder": assets_folder,
        "charset": charset,
        "car_noplate_detect_enabled": False,
        "ienv_enabled": False,
        "openvino_enabled": True,
        "openvino_device": "GPU",
        "npu_enabled": True,
        "klass_lpci_enabled": False,
        "klass_vcr_enabled": False,
        "klass_vmmr_enabled": False,
        "klass_vbsr_enabled": False,
        "license_token_file": tokenfile,
        "license_token_data": tokendata
    })

    checkResult("Init", ultimateAlprSdk.UltAlprSdkEngine_init(json.dumps(JSON_CONFIG)))

    # Use 0 for default camera, or provide an RTSP URL for IP camera
    video_source = r"E:\d_rtsp\number_plate\car_test.mp4"  # or "rtsp://admin:Admin123$@***********:554/stream1"
    cap = cv2.VideoCapture(video_source)

    # Set CUDA backend and target for OpenCV
    cap.set(cv2.CAP_PROP_BACKEND, cv2.CAP_OPENCV_CUDA)
    cap.set(cv2.CAP_PROP_CUDA_DEVICE, 0)  # Use the first CUDA device

    input_queue = Queue(maxsize=5)
    output_queue = Queue()

    processing_thread = threading.Thread(target=process_frame_thread, args=(input_queue, output_queue))
    processing_thread.daemon = True
    processing_thread.start()

    last_process_time = time.time()
    last_processed_frame = None
    frame_count = 0
    start_time = time.time()

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        current_time = time.time()
        frame_count += 1

        try:
            last_processed_frame = output_queue.get_nowait()
        except Empty:
            pass

        display_frame = last_processed_frame if last_processed_frame is not None else frame

        original_height, original_width = display_frame.shape[:2]
        new_width = original_width // 2
        new_height = original_height // 2
        display_frame = cv2.resize(display_frame, (new_width, new_height))

        cv2.imshow('Live Stream ALPR', display_frame)

        if current_time - last_process_time >= 0.1:  # Process every 100ms
            if input_queue.qsize() < 5:  # Limit queue size to prevent memory issues
                input_queue.put(frame)
                last_process_time = current_time

        if frame_count % 30 == 0:  # Calculate FPS every 30 frames
            elapsed_time = time.time() - start_time
            fps = frame_count / elapsed_time
            print(f"FPS: {fps:.2f}")

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()

    ultimateAlprSdk.UltAlprSdkEngine_deInit()

if __name__ == "__main__":
    main()

print("Live stream processing completed.")