import os
import cv2
import numpy as np
from paddleocr import PaddleOCR
import re
import logging

# Suppress PaddleOCR logs
logging.getLogger('ppocr').setLevel(logging.WARNING)

class ImageProcessor:
    def __init__(self, lang='en'):
        self.ocr = PaddleOCR(lang=lang)

    def center_image(self, input_image):
        input_height, input_width = input_image.shape[:2]

        output_image = np.ones((200, 200, 3), dtype=np.uint8) * 255

        center_x = (output_image.shape[1] - input_width) // 2
        center_y = (output_image.shape[0] - input_height) // 2

        output_image[center_y:center_y + input_height, center_x:center_x + input_width] = input_image

        return output_image

    def extract_text(self, input_image):
        # centered_image = self.center_image(input_image)
        # cv2.imshow("image", centered_image)
        # cv2.waitKey(0)
        result = self.ocr.ocr(input_image)
        return self._extract_words(result)

    def _extract_words(self, result):
        for sublist in result:
            for item in sublist:
                if isinstance(item, list):
                    return item[1][0]
        return None


# Example usage
if __name__ == "__main__":
    image_path = r'E:\d_rtsp\number_plate\output_images\20240809_152057.png'
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    processor = ImageProcessor()
    words = processor.extract_text(image)
    print(words)
