<?xml version="1.0" encoding="utf-8"?><!--
 Copyright (C) 2016-2019 Doubango AI <https://www.doubango.org>
 License: For non-commercial use only
 Source code: https://github.com/DoubangoTelecom/ultimateALPR-SDK
 WebSite: https://www.doubango.org/webapps/alpr/
-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <org.doubango.ultimateAlpr.common.AlprGLSurfaceView
        android:id="@+id/glSurfaceView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginStart="0dp"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="0dp" />

    <org.doubango.ultimateAlpr.common.AlprPlateView
        android:id="@+id/plateView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginStart="0dp"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="0dp" />

</RelativeLayout>
