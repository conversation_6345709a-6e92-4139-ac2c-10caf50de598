/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 2.0.9
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

namespace org.doubango.ultimateAlpr.Sdk {

using System;
using System.Runtime.InteropServices;

public class UltAlprSdkEngine : IDisposable {
  private HandleRef swigCPtr;
  protected bool swigCMemOwn;

  internal UltAlprSdkEngine(IntPtr cPtr, bool cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = new HandleRef(this, cPtr);
  }

  internal static HandleRef getCPtr(UltAlprSdkEngine obj) {
    return (obj == null) ? new HandleRef(null, IntPtr.Zero) : obj.swigCPtr;
  }

  ~UltAlprSdkEngine() {
    Dispose();
  }

  public virtual void Dispose() {
    lock(this) {
      if (swigCPtr.Handle != IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          ultimateAlprSdkPINVOKE.delete_UltAlprSdkEngine(swigCPtr);
        }
        swigCPtr = new HandleRef(null, IntPtr.Zero);
      }
      GC.SuppressFinalize(this);
    }
  }

  public static UltAlprSdkResult init(string jsonConfig, UltAlprSdkParallelDeliveryCallback parallelDeliveryCallback) {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_init__SWIG_0(jsonConfig, UltAlprSdkParallelDeliveryCallback.getCPtr(parallelDeliveryCallback)), true);
    return ret;
  }

  public static UltAlprSdkResult init(string jsonConfig) {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_init__SWIG_1(jsonConfig), true);
    return ret;
  }

  public static UltAlprSdkResult init() {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_init__SWIG_2(), true);
    return ret;
  }

  public static UltAlprSdkResult deInit() {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_deInit(), true);
    return ret;
  }

  public static UltAlprSdkResult process(ULTALPR_SDK_IMAGE_TYPE imageType, IntPtr imageData, uint imageWidthInSamples, uint imageHeightInSamples, uint imageStrideInSamples, int imageExifOrientation) {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_process__SWIG_0((int)imageType, imageData, imageWidthInSamples, imageHeightInSamples, imageStrideInSamples, imageExifOrientation), true);
    return ret;
  }

  public static UltAlprSdkResult process(ULTALPR_SDK_IMAGE_TYPE imageType, IntPtr imageData, uint imageWidthInSamples, uint imageHeightInSamples, uint imageStrideInSamples) {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_process__SWIG_1((int)imageType, imageData, imageWidthInSamples, imageHeightInSamples, imageStrideInSamples), true);
    return ret;
  }

  public static UltAlprSdkResult process(ULTALPR_SDK_IMAGE_TYPE imageType, IntPtr imageData, uint imageWidthInSamples, uint imageHeightInSamples) {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_process__SWIG_2((int)imageType, imageData, imageWidthInSamples, imageHeightInSamples), true);
    return ret;
  }

  public static UltAlprSdkResult process(ULTALPR_SDK_IMAGE_TYPE imageType, IntPtr yPtr, IntPtr uPtr, IntPtr vPtr, uint widthInSamples, uint heightInSamples, uint yStrideInBytes, uint uStrideInBytes, uint vStrideInBytes, uint uvPixelStrideInBytes, int exifOrientation) {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_process__SWIG_3((int)imageType, yPtr, uPtr, vPtr, widthInSamples, heightInSamples, yStrideInBytes, uStrideInBytes, vStrideInBytes, uvPixelStrideInBytes, exifOrientation), true);
    return ret;
  }

  public static UltAlprSdkResult process(ULTALPR_SDK_IMAGE_TYPE imageType, IntPtr yPtr, IntPtr uPtr, IntPtr vPtr, uint widthInSamples, uint heightInSamples, uint yStrideInBytes, uint uStrideInBytes, uint vStrideInBytes, uint uvPixelStrideInBytes) {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_process__SWIG_4((int)imageType, yPtr, uPtr, vPtr, widthInSamples, heightInSamples, yStrideInBytes, uStrideInBytes, vStrideInBytes, uvPixelStrideInBytes), true);
    return ret;
  }

  public static UltAlprSdkResult process(ULTALPR_SDK_IMAGE_TYPE imageType, IntPtr yPtr, IntPtr uPtr, IntPtr vPtr, uint widthInSamples, uint heightInSamples, uint yStrideInBytes, uint uStrideInBytes, uint vStrideInBytes) {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_process__SWIG_5((int)imageType, yPtr, uPtr, vPtr, widthInSamples, heightInSamples, yStrideInBytes, uStrideInBytes, vStrideInBytes), true);
    return ret;
  }

  public static int exifOrientation(IntPtr jpegMetaDataPtr, uint jpegMetaDataSize) {
    int ret = ultimateAlprSdkPINVOKE.UltAlprSdkEngine_exifOrientation(jpegMetaDataPtr, jpegMetaDataSize);
    return ret;
  }

  public static UltAlprSdkResult requestRuntimeLicenseKey(bool rawInsteadOfJSON) {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_requestRuntimeLicenseKey__SWIG_0(rawInsteadOfJSON), true);
    return ret;
  }

  public static UltAlprSdkResult requestRuntimeLicenseKey() {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_requestRuntimeLicenseKey__SWIG_1(), true);
    return ret;
  }

  public static UltAlprSdkResult warmUp(ULTALPR_SDK_IMAGE_TYPE imageType) {
    UltAlprSdkResult ret = new UltAlprSdkResult(ultimateAlprSdkPINVOKE.UltAlprSdkEngine_warmUp((int)imageType), true);
    return ret;
  }

}

}
