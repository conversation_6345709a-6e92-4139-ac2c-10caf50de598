/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 2.0.9
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

namespace org.doubango.ultimateAlpr.Sdk {

using System;
using System.Runtime.InteropServices;

public class ultimateAlprSdk {
  public static readonly int ULTALPR_SDK_VERSION_MAJOR = ultimateAlprSdkPINVOKE.ULTALPR_SDK_VERSION_MAJOR_get();
  public static readonly int ULTALPR_SDK_VERSION_MINOR = ultimateAlprSdkPINVOKE.ULTALPR_SDK_VERSION_MINOR_get();
  public static readonly int ULTALPR_SDK_VERSION_MICRO = ultimateAlprSdkPINVOKE.ULTALPR_SDK_VERSION_MICRO_get();
}

}
