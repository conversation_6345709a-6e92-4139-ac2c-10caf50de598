Not all files in this folder are required. It depends on the [features](https://www.doubango.org/SDKs/anpr/docs/Features.html) you want to activate.

**This entire folder is useless if your platform/OS doesn't support OpenVINO or you're disabling it.**

| Folder | Requirement |
|-------- | --- |
| ultimateALPR-SDK_detect_main.desktop.openvino.doubango | **Always required when OpenVINO is enabled** |
| ultimateALPR-SDK_detect_pysearch.desktop.openvino.doubango | **Always required when OpenVINO is enabled** |
| ultimateALPR-SDK_klass_lpci.desktop.openvino.doubango | Only if OpenVINO is enabled and you want [License Plate Country Identification (LPCI)](https://www.doubango.org/SDKs/anpr/docs/Features.html#license-plate-country-identification-lpci) |
| ultimateALPR-SDK_klass_vcr.desktop.openvino.doubango | Only if OpenVINO is enabled and you want [Vehicle Color Recognition (VCR)](https://www.doubango.org/SDKs/anpr/docs/Features.html#vehicle-color-recognition-vcr) |
| ultimateALPR-SDK_klass_vmmr.desktop.openvino.doubango | Only if OpenVINO is enabled and you want [Vehicle Make Model Recognition (VMMR)](https://www.doubango.org/SDKs/anpr/docs/Features.html#vehicle-make-model-recognition-vmmr) |
| ultimateALPR-SDK_klass_vbsr.desktop.openvino.doubango | Only if OpenVINO is enabled and you want [Vehicle Body Style Recognition (VBSR)](https://www.doubango.org/SDKs/anpr/docs/Features.html#vehicle-body-style-recognition-vbsr) |
