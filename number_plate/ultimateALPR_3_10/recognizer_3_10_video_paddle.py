import sys
import json
import cv2
import time
import numpy as np
import re
import threading
from queue import Queue, Empty
from PIL import Image
from paddleocr import PaddleOCR
import os

# Build Command -> python ../../../python/setup.py build_ext --inplace -v

# Adjust this path to your UltimateALPR SDK installation
# sys.path.append(r"ultimateALPR-SDK-master\binaries\windows\x86_64")

os.environ['PYTHONUNBUFFERED'] = '1'

current_directory = os.getcwd()

sdk_binaries_path = os.path.join(current_directory, r"ultimateALPR-SDK-master\binaries\windows\x86_64")
sdk_python_path = os.path.join(current_directory, r"ultimateALPR-SDK-master\python")
# current_directory = os.path.dirname(os.path.abspath(__file__))
#
# sdk_binaries_path = os.path.join(current_directory, r"..\..\models\license_plate_model_py_3_10\ultimateALPR-SDK-master\binaries\windows\x86_64")
# sdk_python_path = os.path.join(current_directory, r"..\..\models\license_plate_model_py_3_10\ultimateALPR-SDK-master\python")

os.environ['PATH'] = os.environ['PATH'] + ';' + sdk_binaries_path
os.environ['PYTHONPATH'] = sdk_python_path

sys.path.append(sdk_binaries_path)
sys.path.append(sdk_python_path)

try:
    import ultimateAlprSdk
except ModuleNotFoundError as e:
    print(f"Failed to import licenseplate sdk: {e}")
    sys.exit(1)

TAG = "[PythonRecognizer] "

TAG = "[PythonRecognizer] "

# Defines the default JSON configuration
JSON_CONFIG = {
    "debug_level": "info",
    "debug_write_input_image_enabled": False,
    "debug_internal_data_path": ".",
    "num_threads": -1,
    "gpgpu_enabled": True,
    "max_latency": -1,
    "klass_vcr_gamma": 1.5,
    "detect_roi": [0, 0, 0, 0],
    "detect_minscore": 0.1,
    "car_noplate_detect_min_score": 0.8,
    "pyramidal_search_enabled": True,
    "pyramidal_search_sensitivity": 0.28,
    "pyramidal_search_minscore": 0.3,
    "pyramidal_search_min_image_size_inpixels": 800,
    "recogn_rectify_enabled": True,
    "recogn_minscore": 0.3,
    "recogn_score_type": "min"
}

IMAGE_TYPES_MAPPING = {
    'RGB': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_RGB24,
    'RGBA': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_RGBA32,
    'L': ultimateAlprSdk.ULTALPR_SDK_IMAGE_TYPE_Y
}

# Initialize PaddleOCR
ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False)

def crop_and_ocr(image, coords):
    points = np.array(coords, dtype=np.int32).reshape((-1, 2))
    x, y, w, h = cv2.boundingRect(points)
    cropped_image = image[y:y + h, x:x + w]
    result = ocr.ocr(cropped_image, cls=True)
    text = result[0][0][1][0] if result and result[0] else ''
    cv2.polylines(image, [points], isClosed=True, color=(0, 0, 255), thickness=2)
    return text, (x, y, w, h)

def checkResult(operation, result):
    if not result.isOK():
        print(TAG + operation + ": failed -> " + result.phrase())
        assert False
    else:
        return json.loads(result.json())

def process_plate(plate_info, image, results_queue):
    plate_text = plate_info.get('text', '')
    plate_warped_box = plate_info.get('warpedBox', [])
    result, bbox = crop_and_ocr(image, plate_warped_box)
    t_result = re.sub(r'[^\w\s]', '', result)
    cleaned_result = t_result.strip()
    final_plate_text = cleaned_result if cleaned_result else plate_text
    results_queue.put((final_plate_text, bbox))

def draw_results(image, results):
    for plate_text, bbox in results:
        print(f"Result : {plate_text}")
        x, y, w, h = bbox
        font_scale = w / 50
        font_thickness = max(int(w / 500), 1)
        text_size, _ = cv2.getTextSize(plate_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, font_thickness)
        bg_color = (0, 255, 255)
        text_color = (0, 0, 0)
        bg_w = text_size[0] + 10
        bg_h = text_size[1] + 10
        bg_x = max(x, 0)
        bg_y = max(y - bg_h - 2, 0)
        cv2.rectangle(image, (bg_x, bg_y), (bg_x + bg_w, bg_y + bg_h), bg_color, -1)
        text_x = bg_x + 5
        text_y = bg_y + bg_h - 5
        cv2.putText(image, plate_text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX,
                    font_scale, text_color, font_thickness, cv2.LINE_AA)
    return image

def process_frame(frame):
    height, width = frame.shape[:2]
    pil_image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
    image_type = IMAGE_TYPES_MAPPING[pil_image.mode]
    number_plate_details = checkResult("Process", ultimateAlprSdk.UltAlprSdkEngine_process(
        image_type,
        pil_image.tobytes(),
        width,
        height,
        0,
        1
    ))
    plates = number_plate_details.get('plates', [])
    if plates:
        threads = []
        results_queue = Queue()
        for plate_info in plates:
            thread = threading.Thread(target=process_plate, args=(plate_info, frame, results_queue))
            threads.append(thread)
            thread.start()
        for thread in threads:
            thread.join()
        results = []
        while not results_queue.empty():
            results.append(results_queue.get())
        frame = draw_results(frame, results)
    return frame

def process_frame_thread(input_queue, output_queue):
    while True:
        try:
            frame = input_queue.get(timeout=1.0)
            processed_frame = process_frame(frame)
            output_queue.put(processed_frame)
        except Empty:
            continue
        except Exception as e:
            print(f"Error in processing thread: {e}")
            break

def main():
    assets_folder = r"ultimateALPR-SDK-master/assets"
    charset = "latin"
    tokenfile = ""
    tokendata = ""

    JSON_CONFIG.update({
        "assets_folder": assets_folder,
        "charset": charset,
        "car_noplate_detect_enabled": False,
        "ienv_enabled": False,
        "openvino_enabled": True,
        "openvino_device": "GPU",
        "npu_enabled": True,
        "klass_lpci_enabled": False,
        "klass_vcr_enabled": False,
        "klass_vmmr_enabled": False,
        "klass_vbsr_enabled": False,
        "license_token_file": tokenfile,
        "license_token_data": tokendata
    })

    checkResult("Init", ultimateAlprSdk.UltAlprSdkEngine_init(json.dumps(JSON_CONFIG)))

    # Use 0 for default camera, or provide an RTSP URL for IP camera
    video_source = r"E:\d_rtsp\number_plate\2.mp4"
    cap = cv2.VideoCapture(video_source)

    input_queue = Queue(maxsize=5)
    output_queue = Queue()

    processing_thread = threading.Thread(target=process_frame_thread, args=(input_queue, output_queue))
    processing_thread.daemon = True
    processing_thread.start()

    last_process_time = time.time()
    last_processed_frame = None
    frame_count = 0
    start_time = time.time()

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        current_time = time.time()
        frame_count += 1

        try:
            last_processed_frame = output_queue.get_nowait()
        except Empty:
            pass

        display_frame = last_processed_frame if last_processed_frame is not None else frame

        original_height, original_width = display_frame.shape[:2]
        new_width = original_width // 2
        new_height = original_height // 2
        display_frame = cv2.resize(display_frame, (new_width, new_height))

        cv2.imshow('Live Stream ALPR', display_frame)

        if current_time - last_process_time >= 0.1:  # Process every 100ms
            if input_queue.qsize() < 5:  # Limit queue size to prevent memory issues
                input_queue.put(frame)
                last_process_time = current_time

        if frame_count % 30 == 0:  # Calculate FPS every 30 frames
            elapsed_time = time.time() - start_time
            fps = frame_count / elapsed_time
            print(f"FPS: {fps:.2f}")

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()

    ultimateAlprSdk.UltAlprSdkEngine_deInit()

if __name__ == "__main__":
    main()

print("Live stream processing completed.")