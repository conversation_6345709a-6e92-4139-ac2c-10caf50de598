import cv2
import os


def video_to_frames(video_path, fps=10):
    # Create a folder named "frames" if it doesn't exist
    output_folder = "frames"
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # Open the video file
    video_capture = cv2.VideoCapture(video_path)

    if not video_capture.isOpened():
        print(f"Error: Could not open video {video_path}")
        return

    # Get the original video frame rate
    original_fps = video_capture.get(cv2.CAP_PROP_FPS)
    frame_interval = int(original_fps // fps)

    frame_count = 0
    saved_frame_count = 0

    while video_capture.isOpened():
        ret, frame = video_capture.read()

        if not ret:
            break

        # Save frames based on the frame_interval (to match the desired FPS)
        if frame_count % frame_interval == 0:
            frame_filename = os.path.join(output_folder, f"frame_{saved_frame_count:05d}.jpg")
            cv2.imwrite(frame_filename, frame)
            print(f"Saved: {frame_filename}")
            saved_frame_count += 1

        frame_count += 1

    video_capture.release()
    print("Video processing complete. Frames saved in 'frames' folder.")


if __name__ == "__main__":
    video_path = input("Enter the path to the video file: ")

    # Call the function to extract frames at 10 FPS and save in the "frames" folder
    video_to_frames(video_path, fps=10)
