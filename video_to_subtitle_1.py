from moviepy.editor import VideoFileClip
import whisper

# Load the Whisper model
model = whisper.load_model("base")

# File paths
mp4_file_path = r"VIDEO\old\bot_patrol_1.mp4"
wav_file_path = "audio_sample.wav"
srt_file_path = "transcription.srt"

# Extract audio from the video file
try:
    print("Extracting audio from MP4...")
    video = VideoFileClip(mp4_file_path)
    video.audio.write_audiofile(wav_file_path, codec="pcm_s16le")
    print(f"Audio successfully saved as WAV at: {wav_file_path}")
except Exception as e:
    print(f"An error occurred during audio extraction: {e}")

# Transcribe the audio file and generate SRT
try:
    print("Transcribing the audio file...")
    result = model.transcribe(wav_file_path)

    # Create SRT file
    print("Generating SRT file...")
    with open(srt_file_path, "w", encoding="utf-8") as srt_file:
        for i, segment in enumerate(result["segments"]):
            # Write SRT index
            srt_file.write(f"{i + 1}\n")

            # Calculate start and end times
            start_time = segment["start"]
            end_time = segment["end"]

            # Check if there's a gap to adjust the end time
            if i + 1 < len(result["segments"]):  # If there's a next segment
                next_start_time = result["segments"][i + 1]["start"]
                if next_start_time - end_time > 0.5:  # Gap greater than 1 second
                    end_time += 0.5  # Add 1 second buffer
                else:
                    end_time = next_start_time
            else:
                end_time += 0.5  # Add 1 second for the last segment

            # Convert timestamps to SRT format (hh:mm:ss,ms)
            start_srt = f"{int(start_time // 3600):02}:{int((start_time % 3600) // 60):02}:{int(start_time % 60):02},{int((start_time % 1) * 1000):03}"
            end_srt = f"{int(end_time // 3600):02}:{int((end_time % 3600) // 60):02}:{int(end_time % 60):02},{int((end_time % 1) * 1000):03}"

            # Write timestamps
            srt_file.write(f"{start_srt} --> {end_srt}\n")

            # Write segment text
            srt_file.write(f"{segment['text']}\n\n")

    print(f"SRT file successfully created at: {srt_file_path}")

except Exception as e:
    print(f"An error occurred during transcription or SRT generation: {e}")
