import cv2
import pytesseract
from PIL import Image
from openpyxl import Workbook
import numpy as np

# Path to tesseract.exe (adjust the path according to your Tesseract installation)
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Load the image using OpenCV
image_path = 'pic.jpg'  # Replace with your actual image path
img = cv2.imread(image_path)

# Convert image to HSV color space to detect blue
hsv_img = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

# Define the range for blue color detection (adjust these values for your image)
lower_blue = np.array([100, 150, 0])
upper_blue = np.array([140, 255, 255])

# Create a mask for blue color
blue_mask = cv2.inRange(hsv_img, lower_blue, upper_blue)

# Invert the mask to keep everything except blue
blue_removed = cv2.bitwise_and(img, img, mask=~blue_mask)

# Save the image with blue converted to black
cv2.imwrite('black_text_image.jpg', blue_removed)

# Perform OCR on the black text image (the one without blue)
black_text_img = Image.open('black_text_image.jpg')
extracted_black_text = pytesseract.image_to_string(black_text_img)

# Now process the blue text separately by applying the mask
blue_only_img = cv2.bitwise_and(img, img, mask=blue_mask)

# Convert the remaining blue text image to grayscale and save
gray_blue_only_img = cv2.cvtColor(blue_only_img, cv2.COLOR_BGR2GRAY)
cv2.imwrite('blue_text_image.jpg', gray_blue_only_img)

# Perform OCR on the blue text image
blue_text_img = Image.open('blue_text_image.jpg')
extracted_blue_text = pytesseract.image_to_string(blue_text_img)

# Split the extracted black and blue texts into lines
black_lines = extracted_black_text.split('\n')
blue_lines = extracted_blue_text.split('\n')

# Create a new Excel workbook
wb = Workbook()
ws = wb.active
ws.title = "Extracted Data"

# Writing headers to the Excel file
headers = ["Line Number", "Extracted Text"]
ws.append(headers)

# Write the black text lines to the Excel file
for idx, line in enumerate(black_lines, start=1):
    line = line.strip()  # Clean up leading/trailing whitespace
    if line:  # Only write non-empty lines
        ws.append([idx, line])

# After writing the black text, append a header for the blue text
ws.append([])
ws.append(["Line Number", "Blue Text"])

# Write the blue text lines to the Excel file
for idx, line in enumerate(blue_lines, start=len(black_lines) + 2):  # Continue line numbering after black text
    line = line.strip()  # Clean up leading/trailing whitespace
    if line:  # Only write non-empty lines
        ws.append([idx, line])

# Save the workbook
excel_path = "extracted_text_data_with_blue_and_black.xlsx"
wb.save(excel_path)

print(f"Text extracted and saved to {excel_path}")
