from flask import Flask, render_template, request, jsonify, redirect
import os
from werkzeug.utils import secure_filename
from langchain_community.document_loaders import PyPDFLoader
from langchain_ollama import OllamaEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import Chroma
from langchain.prompts import Chat<PERSON><PERSON>pt<PERSON><PERSON>plate
from langchain_core.output_parsers import Str<PERSON>ut<PERSON>Parser
from langchain_ollama import ChatOllama

from docx import Document as DocxDocument
from langchain_core.documents import Document as LangChainDocument

import ollama
import logging

app = Flask(__name__)

UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
FRAMEWORK_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Bcm_Framework.pdf')
ALLOWED_EXTENSIONS = {'pdf', 'docx'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

MODEL_NAME = "llama3.2"
EMBEDDING_MODEL = "nomic-embed-text"
framework_vectorstore = None
user_vectorstore = None
llm = None
chat_history = []


def initialize_system():
    global framework_vectorstore, llm
    logger.info("\u2699\ufe0f Initializing system...")
    try:
        ollama.pull(EMBEDDING_MODEL)
        llm = ChatOllama(model=MODEL_NAME)

        if not os.path.exists(FRAMEWORK_FILE):
            raise FileNotFoundError(f"Framework file not found at {FRAMEWORK_FILE}")

        framework_vectorstore = build_vectorstore(FRAMEWORK_FILE)
        logger.info(" System initialized")
    except Exception as e:
        logger.error(f" Initialization failed: {str(e)}")
        raise


def load_docx(filepath):
    doc = DocxDocument(filepath)
    text = "\n".join([para.text.strip() for para in doc.paragraphs if para.text.strip()])
    return [LangChainDocument(page_content=text, metadata={"source": filepath})]


def build_vectorstore(filepath):
    logger.info(f"\ud83d\udd0d Processing: {filepath}")
    ext = os.path.splitext(filepath)[1].lower()

    if ext == ".pdf":
        loader = PyPDFLoader(filepath)
        docs = loader.load_and_split()
    elif ext == ".docx":
        docs = load_docx(filepath)
    else:
        raise ValueError("Unsupported file type")

    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
    chunks = text_splitter.split_documents(docs)

    return Chroma.from_documents(
        documents=chunks,
        embedding=OllamaEmbeddings(model=EMBEDDING_MODEL),
        collection_name=os.path.basename(filepath)
    )


def extract_section_titles(filepath):
    import re
    from docx import Document
    doc = Document(filepath)
    sections = {}
    current_title = None

    for para in doc.paragraphs:
        text = para.text.strip()
        if not text:
            continue

        if re.match(r"^[A-Z ]{4,}$", text) or re.match(r"^\d+\. ", text):
            current_title = text
            sections[current_title] = ""
        elif current_title:
            sections[current_title] += " " + text

    return sections

def is_section_valid(text):
    if not text.strip():
        return False
    if text.lower() in ["see above", "refer earlier section"]:
        return False
    if len(set(text.lower().split())) < 5:
        return False
    return True

def get_analysis_chain():
    template = """You are a rigorous BCM compliance expert analyzing a document . 
    The framework document is the single source of truth.

Framework Context: {framework_context}
User Document: {user_document}

Analysis Rules:
1. Perform STRICT semantic matching - reject unrelated content even if labeled as compliance
2. Validate both PRESENCE and QUALITY of required elements
3. Separate actual compliance content from general organizational practices
4. Identify "false positives" where unrelated content is incorrectly labeled as compliance

Required Output Structure:
1. COMPLIANCE STATUS: [Compliant/Partially Compliant/Non-Compliant]
2. MISSING ELEMENTS: List framework requirements not addressed (consider semantic equivalents)
3. FALSE POSITIVES: Content incorrectly labeled as compliance (e.g., wellness as recovery)
4. PARTIAL MATCHES: Sections that address but don't fully meet requirements
5. SPECIFIC RECOMMENDATIONS: Exact changes needed for each gap

Format with clear headings and bullet points. Be ruthless about excluding unrelated content."""
    prompt = ChatPromptTemplate.from_template(template)
    return (
        {
            "framework_context": framework_vectorstore.as_retriever(),
            "user_document": user_vectorstore.as_retriever()
        }
        | prompt
        | llm
        | StrOutputParser()
    )


def get_chat_chain():
    template = """
You are a strict BCM compliance validator.

Instructions:
1. Identify the section/topic asked about in the uploaded user document. If exact match is not found, look for semantic equivalents or related headings.
2. Retrieve and examine content under that heading.
3. Validate it by comparing with the reference framework:
    - Use semantic understanding (e.g., "Recovery Plan" ≈ "Recovery Strategy").
    - Focus on actual content, not just the heading.
4. Then answer these:

----

📌 1. **Section Presence:**
- Is the required section present in any form (exact or synonym)?
- If not, say clearly: "Missing section"

📌 2. **Content Validation:**
- Summarize what's written under the matched section.
- Check if it meets framework expectations. Mention which points are addressed and which are missing.
- Highlight missing framework phrases if applicable.

📌 3. **Irrelevant or Misleading Content:**
- If the section contains non-BCM info (like yoga, birthdays), flag as "irrelevant content under valid title".
- Quote exact lines that are unrelated.

📌 4. **Misplaced Content:**
- If valid content is under a wrong heading, mention:
    "Relevant content found under [other heading], but not labeled correctly."

📌 5. **Final Verdict:**
- Compliant / Partially Compliant / Non-Compliant
- 1-line explanation

----

Use bullet points and structured formatting.

---

Framework Reference:
{framework_context}

User Document Content:
{user_context}

Question:
{question}

Structured Answer:
"""
    prompt = ChatPromptTemplate.from_template(template)

    def format_input(input_data):
        user_docs = user_vectorstore.as_retriever().get_relevant_documents(input_data["question"])
        framework_docs = framework_vectorstore.as_retriever().get_relevant_documents(input_data["question"])

        return {
            "user_context": "\n\n".join([doc.page_content for doc in user_docs]),
            "framework_context": "\n\n".join([doc.page_content for doc in framework_docs]),
            "question": input_data["question"]
        }

    return format_input | prompt | llm | StrOutputParser()


@app.route('/', methods=['GET', 'POST'])
def upload_file():
    global user_vectorstore, chat_history

    if framework_vectorstore is None:
        try:
            initialize_system()
        except Exception as e:
            return render_template('upload.html', error=f"System initialization failed: {str(e)}")

    if request.method == 'POST':
        file = request.files.get('file')
        if not file or file.filename == '':
            return redirect(request.url)

        if file and file.filename.lower().rsplit('.', 1)[1] in ALLOWED_EXTENSIONS:
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            try:
                chat_history = []
                user_vectorstore = build_vectorstore(filepath)
                result = get_analysis_chain().invoke("Analyze BCM compliance")
                return render_template('results.html', result=result, framework=os.path.basename(FRAMEWORK_FILE), filename=filename)
            except Exception as e:
                return render_template('upload.html', error=f"Analysis failed: {str(e)}")
            finally:
                if os.path.exists(filepath):
                    os.remove(filepath)
        else:
            return render_template('upload.html', error="Only PDF or DOCX files are accepted")

    return render_template('upload.html')


@app.route('/chat', methods=['POST'])
def chat():
    global chat_history

    if not user_vectorstore:
        return jsonify({"error": "Please upload a document first"}), 400

    data = request.get_json()
    question = data.get("question", "").strip()

    if not question:
        return jsonify({"error": "Empty question"}), 400

    try:
        chat_chain = get_chat_chain()
        response = chat_chain.invoke({
            "question": question,
            "chat_history": chat_history
        })

        chat_history.append(f"User: {question}")
        chat_history.append(f"Assistant: {response}")
        chat_history = chat_history[-10:]

        return jsonify({"response": response})
    except Exception as e:
        logger.error(f" Chat error: {str(e)}")
        return jsonify({"error": str(e)}), 500


if __name__ == '__main__':
    app.run(debug=True)
