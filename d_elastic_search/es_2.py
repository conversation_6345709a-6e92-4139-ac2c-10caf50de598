from elasticsearch import Elasticsearch

# Initialize Elasticsearch client
es = Elasticsearch([{'host': 'localhost', 'port': 9200}])

# Define the mapping for the index
mapping = {
    "mappings": {
        "properties": {
            "file_name": {
                "type": "text"
            },
            "dominant_color": {
                "type": "object",
                "properties": {
                    "r": { "type": "integer" },
                    "g": { "type": "integer" },
                    "b": { "type": "integer" }
                }
            }
        }
    }
}

# Create the index
def create_index(index_name):
    if not es.indices.exists(index=index_name):
        es.indices.create(index=index_name, body=mapping)
        print(f"Index '{index_name}' created.")
    else:
        print(f"Index '{index_name}' already exists.")

# Usage
index_name = 'images'
create_index(index_name)

from elasticsearch import Elasticsearch
from PIL import Image
import os
from collections import Counter

# Initialize Elasticsearch client
es = Elasticsearch([{'host': 'localhost', 'port': 9200}])


# Function to get dominant color from an image
def get_dominant_color(image_path, num_colors=3):
    image = Image.open(image_path)
    image = image.convert('RGB')
    image = image.resize((150, 150))  # Resize for quicker processing
    pixels = list(image.getdata())
    counter = Counter(pixels)
    most_common_colors = counter.most_common(num_colors)
    return most_common_colors[0][0]  # Returning the most dominant color


# Function to index image color data in Elasticsearch
def index_image_colors(directory):
    for filename in os.listdir(directory):
        if filename.endswith(('.png', '.jpg', '.jpeg')):
            file_path = os.path.join(directory, filename)
            dominant_color = get_dominant_color(file_path)

            # Create a document for Elasticsearch
            doc = {
                'file_name': filename,
                'dominant_color': dominant_color
            }

            # Index the document
            es.index(index='images', doc_type='_doc', body=doc)


# Function to search for images with a particular color
def search_by_color(target_color):
    query = {
        "query": {
            "match": {
                "dominant_color": target_color
            }
        }
    }

    response = es.search(index="images", body=query)
    return response['hits']['hits']


# Example usage
if __name__ == "__main__":
    # Step 1: Index all images in the directory
    image_directory = "/path/to/your/image/directory"
    index_image_colors(image_directory)

    # Step 2: Search for images with a specific color
    target_color = (255, 0, 0)  # RGB for red
    results = search_by_color(target_color)

    # Print the matching images
    for res in results:
        print(f"Found image: {res['_source']['file_name']} with color: {res['_source']['dominant_color']}")

