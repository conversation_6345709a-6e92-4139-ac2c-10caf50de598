import torch
import torch.nn.utils.prune as prune
from ultralytics import YOLO

# Load YOLOv8 model
model_path = r"E:\d_rtsp\d_elastic_search\yo_models\yolov8s.pt"
model = YOLO(model_path)

# Prune convolutional layers
for module_name, module in model.model.named_modules():
    if isinstance(module, torch.nn.Conv2d):
        # Prune 20% of weights in Conv2d layers
        prune.l1_unstructured(module, name='weight', amount=0.2)

# Remove pruning re-parametrizations to make it a standalone model
for module_name, module in model.model.named_modules():
    if isinstance(module, torch.nn.Conv2d):
        prune.remove(module, 'weight')

# Save the pruned model
pruned_model_path = "yolov8s_pruned.pt"
torch.save(model.model.state_dict(), pruned_model_path)
