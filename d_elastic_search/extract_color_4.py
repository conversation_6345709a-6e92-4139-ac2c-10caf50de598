from PIL import Image
import numpy as np
from sklearn.cluster import KMeans
from collections import Counter
import pandas as pd
import math


def load_color_database(csv_path):
    """
    Load and parse the color database from CSV with proper data types.

    Args:
        csv_path (str): Path to the CSV file

    Returns:
        DataFrame: Processed color database
    """
    df = pd.read_csv(csv_path, names=['name', 'hex', 'R', 'G', 'B', 'category'])
    # Convert RGB columns to integers
    df['R'] = pd.to_numeric(df['R'], errors='coerce')
    df['G'] = pd.to_numeric(df['G'], errors='coerce')
    df['B'] = pd.to_numeric(df['B'], errors='coerce')
    # Drop any rows with invalid RGB values
    df = df.dropna(subset=['R', 'G', 'B'])
    return df


def calculate_color_distance(color1, color2):
    """
    Calculate the Euclidean distance between two RGB colors.

    Args:
        color1 (tuple): First RGB color
        color2 (tuple): Second RGB color

    Returns:
        float: Color distance
    """
    r1, g1, b1 = map(float, color1)
    r2, g2, b2 = map(float, color2)
    return math.sqrt((r1 - r2) ** 2 + (g1 - g2) ** 2 + (b1 - b2) ** 2)


def find_closest_color(rgb_color, color_db):
    """
    Find the closest matching color in the database.

    Args:
        rgb_color (tuple): RGB color to match
        color_db (DataFrame): Color database

    Returns:
        tuple: (color_name, hex_code, distance)
    """
    min_distance = float('inf')
    closest_color = None

    for _, row in color_db.iterrows():
        db_color = (row['R'], row['G'], row['B'])
        distance = calculate_color_distance(rgb_color, db_color)

        if distance < min_distance:
            min_distance = distance
            closest_color = (row['name'], row['hex'], distance, row['category'])

    return closest_color


def get_top_colors(image_path, color_db, num_colors=5):
    """
    Extract dominant colors from an image and match them with the database.

    Args:
        image_path (str): Path to the image file
        color_db (DataFrame): Color database
        num_colors (int): Number of dominant colors to extract

    Returns:
        list: List of tuples containing color information
    """
    # Open and convert image to RGB
    img = Image.open(image_path)
    img = img.convert('RGB')

    # Resize image to speed up processing
    img = img.resize((150, 150))

    # Convert image to numpy array
    img_array = np.array(img)
    pixels = img_array.reshape(-1, 3)

    # Apply KMeans clustering
    kmeans = KMeans(n_clusters=num_colors, random_state=42)
    kmeans.fit(pixels)

    # Get the colors
    colors = kmeans.cluster_centers_
    colors = colors.round().astype(int)

    # Calculate color frequencies
    labels = kmeans.labels_
    color_counts = Counter(labels)

    # Sort colors by frequency
    sorted_colors = [colors[label] for label, count in color_counts.most_common(num_colors)]

    # Match colors with database
    result = []
    for color in sorted_colors:
        rgb = tuple(map(int, color))
        hex_code = '#{:02x}{:02x}{:02x}'.format(rgb[0], rgb[1], rgb[2])

        # Find exact match or closest color
        closest_color = find_closest_color(rgb, color_db)

        result.append({
            'rgb': rgb,
            'hex': hex_code,
            'matched_name': closest_color[0],
            'matched_hex': closest_color[1],
            'distance': closest_color[2],
            'common_name': closest_color[3]
        })

    return result


def print_colors(colors):
    """
    Print the colors in a formatted way.

    Args:
        colors (list): List of color information dictionaries
    """
    print("Top 5 Dominant Colors:")
    print("-" * 60)
    for i, color in enumerate(colors, 1):
        print(f"{i}. Extracted Color:")
        print(f"   RGB: {color['rgb']}")
        print(f"   HEX: {color['hex']}")
        print(f"   Match HEX: {color['matched_hex']}")
        print(f"   Color Distance: {color['distance']:.2f}")
        print(f"   Closest Match: {color['matched_name']}")
        print(f"   Common Name: {color['common_name']}")
        print("-" * 60)


# Example usage
if __name__ == "__main__":
    # Replace with your paths
    image_path = 'test_1.png'
    csv_path = 'colors_with_common_names.csv'

    # Load color database
    color_db = load_color_database(csv_path)

    # Get and print colors
    top_colors = get_top_colors(image_path, color_db)
    print_colors(top_colors)