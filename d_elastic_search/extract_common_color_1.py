import pandas as pd

# Load the CSV file to examine its contents
file_path = 'colors.csv'
colors_df = pd.read_csv(file_path)

# Display the first few rows to understand its structure
colors_df.head()

import cv2
import numpy as np

# Define color boundaries in HSV format for the common color names
COLOR_BOUNDS = {
    "blue": ((100, 150, 50), (140, 255, 255)),
    "violet": ((130, 100, 50), (160, 255, 255)),
    "pink": ((160, 100, 50), (180, 255, 255)),
    "red": ((0, 100, 50), (10, 255, 255)),
    "green": ((40, 50, 50), (80, 255, 255)),
    "black": ((0, 0, 0), (180, 255, 50)),
    "light blue": ((90, 50, 50), (120, 255, 255)),
    "white": ((0, 0, 200), (180, 20, 255)),
    "yellow": ((20, 100, 100), (30, 255, 255)),
    "orange": ((10, 100, 100), (20, 255, 255)),
    "gray": ((0, 0, 50), (180, 50, 200)),
    "brown": ((10, 100, 50), (20, 255, 150))
}

# Function to classify color based on predefined HSV bounds
def classify_color(hsv_color):
    for color_name, (lower, upper) in COLOR_BOUNDS.items():
        lower_bound = np.array(lower, dtype=np.uint8)
        upper_bound = np.array(upper, dtype=np.uint8)
        if cv2.inRange(np.uint8([[hsv_color]]), lower_bound, upper_bound):
            return color_name
    return "Unknown"

# Function to convert RGB to HSV
def rgb_to_hsv(r, g, b):
    color_rgb = np.uint8([[[r, g, b]]])
    color_hsv = cv2.cvtColor(color_rgb, cv2.COLOR_RGB2HSV)
    return color_hsv[0][0]

# Apply color classification to each row in the DataFrame
colors_df['common_color'] = colors_df.apply(lambda row: classify_color(rgb_to_hsv(row['R'], row['G'], row['B'])), axis=1)

# Display the DataFrame with the new common_color column
colors_df[['color', 'code', 'common_color']].head(20)

# Save the DataFrame with the categorized common color names to a new CSV file
output_path = 'colors_with_common_names.csv'
colors_df.to_csv(output_path, index=False)

