import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
import cv2


# Function to extract dominant colors
def extract_dominant_colors(image_path, n_colors=5):
    # Read the image
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)  # Convert BGR to RGB

    # Resize the image to speed up processing
    r, c = image.shape[:2]
    out_r = 500  # Resize image height to 500px
    new_image = cv2.resize(image, (int(out_r * float(c) / r), out_r))

    # Reshape image to a 2D array of pixels
    pixels = new_image.reshape((-1, 3))

    # Perform KMeans clustering to find dominant colors with a fixed random state for consistency
    kmeans = KMeans(n_clusters=n_colors, random_state=42)  # Set random_state for consistent results
    kmeans.fit(pixels)

    # Get the cluster centers (dominant colors)
    dominant_colors = np.asarray(kmeans.cluster_centers_, dtype='uint8')

    # Return dominant colors
    return dominant_colors, new_image


# Function to plot the image and dominant colors
def plot_dominant_colors(image_path, dominant_colors, new_image):
    # Plot original image and resized image
    plt.figure(figsize=(14, 6))

    plt.subplot(121)
    plt.title("Original Image")
    plt.imshow(new_image)
    plt.axis('off')

    # Plot dominant colors as bars
    plt.subplot(122)
    plt.title("Dominant Colors")
    plt.axis('off')
    plt.imshow([dominant_colors])  # Show colors as horizontal bars

    plt.show()


# Main script
image_path = r"o_1.png"  # Replace with your image path
dominant_colors, resized_image = extract_dominant_colors(image_path, n_colors=3)

# Output the dominant colors
print("Dominant Colors (RGB):", dominant_colors)

# Plot the image and dominant colors
plot_dominant_colors(image_path, dominant_colors, resized_image)
