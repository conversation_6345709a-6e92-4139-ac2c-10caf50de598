from PIL import Image, ImageDraw
import numpy as np
from sklearn.cluster import KMeans
from collections import Counter
import pandas as pd
import math
import os
import time


def load_color_database(csv_path):
    """
    Load and parse the color database from CSV with proper data types.

    Args:
        csv_path (str): Path to the CSV file

    Returns:
        DataFrame: Processed color database
    """
    df = pd.read_csv(csv_path, names=['name', 'hex', 'R', 'G', 'B', 'category'])
    # Convert RGB columns to integers
    df['R'] = pd.to_numeric(df['R'], errors='coerce')
    df['G'] = pd.to_numeric(df['G'], errors='coerce')
    df['B'] = pd.to_numeric(df['B'], errors='coerce')
    # Drop any rows with invalid RGB values
    df = df.dropna(subset=['R', 'G', 'B'])
    return df


def calculate_color_distance(color1, color2):
    """
    Calculate the Euclidean distance between two RGB colors.

    Args:
        color1 (tuple): First RGB color
        color2 (tuple): Second RGB color

    Returns:
        float: Color distance
    """
    r1, g1, b1 = map(float, color1)
    r2, g2, b2 = map(float, color2)
    return math.sqrt((r1 - r2) ** 2 + (g1 - g2) ** 2 + (b1 - b2) ** 2)


def find_closest_color(rgb_color, color_db):
    """
    Find the closest matching color in the database.

    Args:
        rgb_color (tuple): RGB color to match
        color_db (DataFrame): Color database

    Returns:
        tuple: (color_name, hex_code, distance)
    """
    min_distance = float('inf')
    closest_color = None

    for _, row in color_db.iterrows():
        db_color = (row['R'], row['G'], row['B'])
        distance = calculate_color_distance(rgb_color, db_color)

        if distance < min_distance:
            min_distance = distance
            closest_color = (row['name'], row['hex'], distance, row['category'])

    return closest_color


def get_top_colors(image_path, color_db, num_colors=5):
    """
    Extract dominant colors from an image and match them with the database.

    Args:
        image_path (str): Path to the image file
        color_db (DataFrame): Color database
        num_colors (int): Number of dominant colors to extract

    Returns:
        list: List of tuples containing color information
    """
    # Open and convert image to RGB
    img = Image.open(image_path)
    img = img.convert('RGB')

    # Resize image to speed up processing
    img = img.resize((150, 150))

    # Convert image to numpy array
    img_array = np.array(img)
    pixels = img_array.reshape(-1, 3)

    # Apply KMeans clustering
    kmeans = KMeans(n_clusters=num_colors, random_state=42)
    kmeans.fit(pixels)

    # Get the colors
    colors = kmeans.cluster_centers_
    colors = colors.round().astype(int)

    # Calculate color frequencies
    labels = kmeans.labels_
    color_counts = Counter(labels)

    # Sort colors by frequency
    sorted_colors = [colors[label] for label, count in color_counts.most_common(num_colors)]

    # Match colors with database
    result = []
    for color in sorted_colors:
        rgb = tuple(map(int, color))
        hex_code = '#{:02x}{:02x}{:02x}'.format(rgb[0], rgb[1], rgb[2])

        # Find exact match or closest color
        closest_color = find_closest_color(rgb, color_db)

        result.append({
            'rgb': rgb,
            'hex': hex_code,
            'matched_name': closest_color[0],
            'matched_hex': closest_color[1],
            'distance': closest_color[2],
            'common_name': closest_color[3]
        })

    return result


def print_colors(colors):
    """
    Print the colors in a formatted way.

    Args:
        colors (list): List of color information dictionaries
    """
    print("Top 5 Dominant Colors:")
    print("-" * 60)
    for i, color in enumerate(colors, 1):
        print(f"{i}. Extracted Color:")
        print(f"   RGB: {color['rgb']}")
        print(f"   HEX: {color['hex']}")
        print(f"   Match HEX: {color['matched_hex']}")
        print(f"   Color Distance: {color['distance']:.2f}")
        print(f"   Closest Match: {color['matched_name']}")
        print(f"   Common Name: {color['common_name']}")
        print("-" * 60)


# [Previous functions remain the same up to get_top_colors()]

def create_color_palette_image(original_image, colors, output_path):
    """
    Create a visual representation of the image with its color palette.

    Args:
        original_image (PIL.Image): Original image
        colors (list): List of color information dictionaries
        output_path (str): Path to save the output image
    """
    # Calculate dimensions
    palette_height = 100
    margin = 20

    # Resize original image to a reasonable height while maintaining aspect ratio
    target_height = 400
    aspect_ratio = original_image.size[0] / original_image.size[1]
    target_width = int(target_height * aspect_ratio)
    resized_image = original_image.resize((target_width, target_height))

    # Create new image with white background
    total_width = target_width
    total_height = target_height + palette_height + 3 * margin

    result_image = Image.new('RGB', (total_width, total_height), 'white')

    # Paste original image
    result_image.paste(resized_image, (0, 0))

    # Create color palette
    num_colors = len(colors)
    color_width = target_width // num_colors

    draw = ImageDraw.Draw(result_image)

    # Draw color rectangles
    for i, color in enumerate(colors):
        x1 = i * color_width
        y1 = target_height + 2 * margin
        x2 = (i + 1) * color_width
        y2 = y1 + palette_height

        # Draw color rectangle
        draw.rectangle([x1, y1, x2, y2], fill=color['rgb'])

        # Draw color information
        text_y = y2 + 5
        hex_color = color['hex']
        common_name = color['common_name']

        # Make text color black or white depending on background color brightness
        r, g, b = color['rgb']
        brightness = (r * 299 + g * 587 + b * 114) / 1000
        text_color = 'black' if brightness > 128 else 'white'

        # Draw color hex and name
        draw.text((x1 + 5, y1 + 5), hex_color, fill=text_color)
        draw.text((x1 + 5, y1 + 25), common_name, fill=text_color)

    # Save the result
    result_image.save(output_path)
    print(f"Palette image saved as {output_path}")


def process_image(image_path, csv_path, output_path):
    """
    Process an image and create a color palette visualization.

    Args:
        image_path (str): Path to input image
        csv_path (str): Path to color database CSV
        output_path (str): Path to save output image
    """
    # Load color database
    color_db = load_color_database(csv_path)

    # Get original image for visualization
    original_image = Image.open(image_path)

    # Get top colors
    top_colors = get_top_colors(image_path, color_db)

    # Create and save palette visualization
    create_color_palette_image(original_image, top_colors, output_path)

    # Print color information
    print_colors(top_colors)


if __name__ == "__main__":
    # Replace with your paths
    image_path = r"E:\d_rtsp\d_elastic_search\2_['black', 'gray']_segment.png"
    filename = os.path.splitext(os.path.basename(image_path))[0]
    csv_path = 'color_codes.csv'
    output_path = f'{filename}_palette.png'

    start_time = time.time()
    process_image(image_path, csv_path, output_path)
    end_time = time.time()

    execution_time = end_time - start_time
    print(f"Execution time: {execution_time:.2f} seconds")





