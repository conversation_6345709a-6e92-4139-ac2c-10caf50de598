import cv2
import numpy as np
import os


def segment_object(image_path):

    image = cv2.imread(image_path)
    height, width = image.shape[:2]
    rect = (1, 1, width, height)  # Define the rectangle for GrabCut
    if image is None:
        raise ValueError("Image not found or invalid path provided.")

    # Initialize the mask and models
    mask = np.zeros(image.shape[:2], np.uint8)
    bg_model = np.zeros((1, 65), np.float64)
    fg_model = np.zeros((1, 65), np.float64)

    # Apply the GrabCut algorithm
    cv2.grabCut(image, mask, rect, bg_model, fg_model, 5, cv2.GC_INIT_WITH_RECT)

    # Convert the mask to binary
    mask = np.where((mask == 2) | (mask == 0), 0, 1).astype('uint8')
    segmented = image * mask[:, :, np.newaxis]

    return segmented


# Example usage
if __name__ == "__main__":
    input_image_path = r"C:\Users\<USER>\Downloads\croped_image\2_['black', 'gray'].png"
    filename = os.path.splitext(os.path.basename(input_image_path))[0]
    segmented_image = segment_object(input_image_path)

    # Display the segmented image
    cv2.imshow('Segmented Object', segmented_image)
    cv2.imwrite(f"{filename}_segment.png", segmented_image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
