from PIL import Image, ImageDraw

def rgb_to_hex(rgb):
    return '#{:02x}{:02x}{:02x}'.format(*rgb)

def get_colors(image_path, numcolors=10, resize=150):
    try:
        # Load and resize image to speed up processing
        img = Image.open(image_path)
        img = img.copy()
        img.thumbnail((resize, resize))

        # Reduce to palette
        paletted = img.convert('P', palette=Image.ADAPTIVE, colors=numcolors)

        # Find dominant colors
        palette = paletted.getpalette()
        color_counts = sorted(paletted.getcolors(), reverse=True)
        colors = []
        for i in range(min(numcolors, len(color_counts))):
            palette_index = color_counts[i][1]
            dominant_color = palette[palette_index*3:palette_index*3+3]
            colors.append(tuple(dominant_color))

        # Convert colors to RGB and Hex format
        rgb_hex_colors = [(color, rgb_to_hex(color)) for color in colors]
        return rgb_hex_colors

    except Exception as e:
        print(f"Error processing image: {e}")
        return []

def save_palette(colors, swatchsize=20, outfile="palette.png"):
    num_colors = len(colors)
    palette = Image.new('RGB', (swatchsize*num_colors, swatchsize))
    draw = ImageDraw.Draw(palette)

    posx = 0
    for color, hex_code in colors:
        draw.rectangle([posx, 0, posx+swatchsize, swatchsize], fill=color)
        posx = posx + swatchsize

    del draw
    palette.save(outfile, "PNG")
    print(f"Palette saved to {outfile}")

# Example usage
image_path = "o_1_segment.png"
output_file = "palette.png"
colors = get_colors(image_path, numcolors=5)
print("Dominant Colors (RGB, HEX):", colors)
save_palette(colors, outfile=output_file)
