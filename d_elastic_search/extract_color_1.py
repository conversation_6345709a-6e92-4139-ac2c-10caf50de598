import numpy as np
from PIL import Image
from sklearn.cluster import KMeans

# Define color ranges
COLOR_RANGES = {
    'red': ((120, 0, 0), (255, 60, 60)),
    'green': ((0, 120, 0), (60, 255, 60)),
    'blue': ((0, 0, 120), (60, 60, 255)),
    'yellow': ((120, 120, 0), (255, 255, 60)),
    'magenta': ((120, 0, 120), (255, 60, 255)),
    'cyan': ((0, 120, 120), (60, 255, 255)),
    'orange': ((120, 60, 0), (255, 180, 60)),
    'purple': ((60, 0, 60), (180, 60, 180)),
    'brown': ((60, 30, 0), (180, 90, 60)),
    'pink': ((180, 60, 120), (255, 180, 210)),
    'gray': ((60, 60, 60), (180, 180, 180)),
    'black': ((0, 0, 0), (60, 60, 60)),
    'white': ((180, 180, 180), (255, 255, 255))
}


def get_color_name(rgb):
    r, g, b = rgb
    for color_name, ((r1, g1, b1), (r2, g2, b2)) in COLOR_RANGES.items():
        if r1 <= r <= r2 and g1 <= g <= g2 and b1 <= b <= b2:
            return color_name
    return "unknown"


def get_top_colors(image_path, num_colors=3):
    image = Image.open(image_path)
    image = image.convert('RGB')
    image = image.resize((150, 150))
    image_array = np.array(image)
    pixels = image_array.reshape(-1, 3)

    kmeans = KMeans(n_clusters=num_colors, random_state=42)
    kmeans.fit(pixels)

    colors = kmeans.cluster_centers_
    colors = colors.astype(int)

    labels = kmeans.labels_
    color_frequency = np.bincount(labels)
    sorted_indices = np.argsort(color_frequency)[::-1]
    top_colors = colors[sorted_indices]

    return [tuple(color) for color in top_colors]


# Example usage
image_path = 'trip_segment.png'
top_3_colors = get_top_colors(image_path)

print("Top 3 dominant colors:")
for i, color in enumerate(top_3_colors, 1):
    hex_color = '#{:02x}{:02x}{:02x}'.format(*color)
    color_name = get_color_name(color)
    print(f"{i}. RGB{color} - Hex: {hex_color} - Name: {color_name}")