import cv2
import numpy as np
import torch
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
from ultralytics import YOLO
import pandas as pd
from typing import List, Tuple, Optional
import logging
from pathlib import Path
import colorsys

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class YOLOColorAnalyzer:
    def __init__(self, model_path: str, colors_csv_path: Optional[str] = None):
        """
        Initialize YOLO model and color analyzer.

        Parameters
        ----------
        model_path : str
            Path to YOLOv8 segmentation model
        colors_csv_path : str, optional
            Path to CSV file containing reference color names
        """
        self.model = YOLO(model_path)
        self.colors_df = self._load_colors(colors_csv_path) if colors_csv_path else None

    @staticmethod
    def _load_colors(colors_csv_path: str) -> pd.DataFrame:
        """Load color reference data if available."""
        return pd.read_csv(colors_csv_path)

    def segment_object(self, image_path: str, class_id: int = 0) -> Tuple[np.ndarray, np.ndarray]:
        """
        Segment specific object from image using YOLOv8.

        Parameters
        ----------
        image_path : str
            Path to input image
        class_id : int
            Class ID to segment (default 0 for person)

        Returns
        -------
        Tuple[np.ndarray, np.ndarray]
            Segmented image and binary mask
        """
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image from {image_path}")

        # Get model predictions
        results = self.model(image)

        # Check if any objects were detected
        if len(results[0].masks) == 0:
            raise ValueError(f"No objects of class {class_id} detected in the image")

        # Get mask for the specified class
        masks = results[0].masks.data
        mask = masks[0].cpu().numpy()  # Get first instance of the class

        # Resize mask to match image dimensions
        mask_resized = cv2.resize(mask, (image.shape[1], image.shape[0]))
        binary_mask = (mask_resized * 255).astype(np.uint8)

        # Apply mask to get segmented image
        segmented_image = cv2.bitwise_and(image, image, mask=binary_mask)

        return segmented_image, binary_mask

    def extract_dominant_colors(self,
                                image: np.ndarray,
                                n_colors: int = 5,
                                min_pixel_count: int = 100) -> Tuple[np.ndarray, List[float]]:
        """
        Extract dominant colors from image using KMeans clustering.

        Parameters
        ----------
        image : np.ndarray
            Input image
        n_colors : int
            Number of dominant colors to extract
        min_pixel_count : int
            Minimum number of pixels to consider a color cluster

        Returns
        -------
        Tuple[np.ndarray, List[float]]
            Dominant colors and their percentages
        """
        # Convert to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Remove black background (assuming segmented image)
        valid_pixels = image_rgb.reshape(-1, 3)
        valid_pixels = valid_pixels[~np.all(valid_pixels == 0, axis=1)]

        if len(valid_pixels) < min_pixel_count:
            raise ValueError("Not enough valid pixels in the segmented region")

        # Perform KMeans clustering
        kmeans = KMeans(n_clusters=n_colors, random_state=42)
        kmeans.fit(valid_pixels)

        # Get colors and their percentages
        colors = kmeans.cluster_centers_.astype(np.uint8)
        labels = kmeans.labels_

        # Calculate percentage of each color
        percentages = [sum(labels == i) / len(labels) * 100 for i in range(n_colors)]

        # Sort colors by percentage
        sorted_indices = np.argsort(percentages)[::-1]
        sorted_colors = colors[sorted_indices]
        sorted_percentages = [percentages[i] for i in sorted_indices]

        return sorted_colors, sorted_percentages

    def name_colors(self, colors: np.ndarray) -> List[dict]:
        """
        Find closest matching color names from reference data.

        Parameters
        ----------
        colors : np.ndarray
            Array of RGB colors

        Returns
        -------
        List[dict]
            Color information including name, hex code, and RGB values
        """
        if self.colors_df is None:
            return [{'rgb': f'RGB{tuple(color)}'} for color in colors]

        results = []
        for color in colors:
            # Calculate color distances
            distances = np.sqrt(
                (self.colors_df['R'] - color[0]) ** 2 +
                (self.colors_df['G'] - color[1]) ** 2 +
                (self.colors_df['B'] - color[2]) ** 2
            )

            closest_idx = distances.argmin()
            matched_color = self.colors_df.iloc[closest_idx]

            results.append({
                'color_name': matched_color['color'],
                'common_name' : matched_color['common_color'],
                'hex_code': matched_color['code'],
                'rgb': f'RGB{tuple(color)}'
            })

        return results

    def visualize_results(self,
                          original_image: np.ndarray,
                          segmented_image: np.ndarray,
                          colors: np.ndarray,
                          percentages: List[float],
                          save_path: Optional[str] = None):
        """
        Visualize the segmentation and color analysis results.
        """
        plt.figure(figsize=(15, 5))

        # Original image
        plt.subplot(131)
        plt.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
        plt.title("Original Image")
        plt.axis('off')

        # Segmented image
        plt.subplot(132)
        plt.imshow(cv2.cvtColor(segmented_image, cv2.COLOR_BGR2RGB))
        plt.title("Segmented Object")
        plt.axis('off')

        # Color palette
        plt.subplot(133)
        for idx, (color, percentage) in enumerate(zip(colors, percentages)):
            plt.bar(idx, 1, color=color / 255, alpha=0.8)
            plt.text(idx, 0.5, f'{percentage:.1f}%',
                     ha='center', va='center',
                     color='black' if np.mean(color) > 127 else 'white')
        plt.title("Dominant Colors")
        plt.axis('off')

        if save_path:
            plt.savefig(save_path)
        plt.show()


def main():
    # Initialize analyzer
    analyzer = YOLOColorAnalyzer(
        model_path="yolov8s-seg.pt",
        colors_csv_path="colors_with_common_names.csv"  # Optional
    )

    # Process image
    image_path = "y_segment.png"
    original_image = cv2.imread(image_path)

    try:
        # Segment object
        segmented_image, mask = analyzer.segment_object(image_path)
        cv2.imwrite("y_segment.png", segmented_image)

        # Extract colors
        dominant_colors, color_percentages = analyzer.extract_dominant_colors(
            segmented_image,
            n_colors=10
        )

        # Get color names if reference data is available
        color_info = analyzer.name_colors(dominant_colors)

        # Print results
        print("\nDominant Colors Analysis:")
        print("=" * 50)
        for info, percentage in zip(color_info, color_percentages):
            print(f"Color: {info.get('color_name', 'N/A')}")
            print(f"Common_name: {info.get('common_name', 'N/A')}")
            print(f"RGB: {info['rgb']}")
            print(f"Percentage: {percentage:.1f}%")
            if 'hex_code' in info:
                print(f"Hex: {info['hex_code']}")
            print("-" * 50)

        # Visualize results
        analyzer.visualize_results(
            original_image,
            segmented_image,
            dominant_colors,
            color_percentages,
            save_path="color_analysis_results.png"
        )

    except Exception as e:
        logger.error(f"Error during analysis: {str(e)}")
        raise


if __name__ == "__main__":
    main()




