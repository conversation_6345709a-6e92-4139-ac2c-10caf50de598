from elasticsearch import Elasticsearch

# Create an Elasticsearch client
es = Elasticsearch("http://localhost:9200")

# Create an index with mappings
def create_index():
    index_name = "products"
    mappings = {
        "mappings": {
            "properties": {
                "product_id": {"type": "keyword"},
                "product_name": {"type": "text"},
                "colors": {"type": "keyword"}
            }
        }
    }
    es.indices.create(index=index_name, body=mappings, ignore=400)

# Indexing a product
def index_product(product_id, product_name, colors):
    index_name = "products"
    document = {
        "product_id": product_id,
        "product_name": product_name,
        "colors": colors
    }
    es.index(index=index_name, id=product_id, body=document)

# Searching for a specific color
def search_by_color(color):
    index_name = "products"
    query = {
        "query": {
            "term": {
                "colors": color
            }
        }
    }
    response = es.search(index=index_name, body=query)
    return response['hits']['hits']

# Example usage
create_index()
index_product("1", "T-shirt", ["red", "blue", "green"])
index_product("2", "Hat", ["blue", "yellow"])
index_product("3", "Sneakers", ["red", "white"])

# Search for products
results = search_by_color("red")
print("Products with color 'red':", results)

# Multi-color search
results = search_by_multiple_colors(["red", "blue"])
print("Products with colors 'red' or 'blue':", results)

# Aggregation
aggregated_results = aggregate_by_color()
print("Aggregated product counts by color:", aggregated_results)
