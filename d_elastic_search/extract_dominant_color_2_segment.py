import cv2
import numpy as np
import torch
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
from ultralytics import YOLO

# Load YOLOv8 segmentation model
model = YOLO("yo_models/yolov8s-seg.pt")

# Load image
image_path = r"E:\d_rtsp\d_elastic_search\color_extractor\image_files\6_['black', 'gray'].png"
image = cv2.imread(image_path)

# Perform inference to detect objects and get segmentation masks
results = model.predict(image)

# Get the mask for the person (YOLOv8 segmentation returns masks directly)
person_class_id = 0  # Person class is typically class 0 in COCO
masks = results[0].masks.data  # All masks for detected objects

# Get the mask for the first person detected (you can loop through if there are multiple people)
person_mask = masks[0].cpu().numpy()

# Ensure the mask has the same dimensions as the image
person_mask_resized = cv2.resize(person_mask, (image.shape[1], image.shape[0]))

# Convert the mask to binary (0 or 255 values) as OpenCV expects
binary_mask = (person_mask_resized * 255).astype(np.uint8)

# Ensure the mask is single-channel
if len(binary_mask.shape) > 2:
    binary_mask = binary_mask[:, :, 0]

# Apply the person mask to segment the person in the image
segmented_image = cv2.bitwise_and(image, image, mask=binary_mask)
cv2.imwrite("test_4a.jpeg", segmented_image)

def extract_dominant_colors(image_path, n_colors=5):

    image = cv2.cvtColor(image_path, cv2.COLOR_BGR2RGB)  # Convert BGR to RGB

    # Resize the image to speed up processing
    r, c = image.shape[:2]
    out_r = 500  # Resize image height to 500px
    new_image = cv2.resize(image, (int(out_r * float(c) / r), out_r))

    # Reshape image to a 2D array of pixels
    pixels = new_image.reshape((-1, 3))

    # Perform KMeans clustering to find dominant colors with a fixed random state for consistency
    kmeans = KMeans(n_clusters=n_colors, random_state=42)  # Set random_state for consistent results
    kmeans.fit(pixels)

    # Get the cluster centers (dominant colors)
    dominant_colors = np.asarray(kmeans.cluster_centers_, dtype='uint8')

    # Return dominant colors
    return dominant_colors

# Extract dominant colors from the segmented person region
dominant_colors = extract_dominant_colors(segmented_image)

# Output the dominant colors and display the result
print("Dominant Colors (RGB):", dominant_colors)

# Visualize the segmented person and dominant colors
plt.figure(figsize=(10, 5))

# Display segmented person image
plt.subplot(121)
plt.imshow(cv2.cvtColor(segmented_image, cv2.COLOR_BGR2RGB))
plt.title("Segmented Person")
plt.axis('off')

# Display the dominant colors as a color bar
plt.subplot(122)
plt.imshow([dominant_colors])
plt.title("Dominant Colors")
plt.axis('off')

plt.show()
