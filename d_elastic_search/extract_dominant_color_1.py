import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
import cv2

# Define common colors with their HSV ranges
common_colors_hsv = {
    'white': (np.array([0, 0, 200], np.uint8), np.array([180, 20, 255], np.uint8)),
    'red': (np.array([0, 50, 50], np.uint8), np.array([10, 255, 255], np.uint8)),
    'black': (np.array([0, 0, 0], np.uint8), np.array([180, 255, 30], np.uint8)),
    'blue': (np.array([100, 150, 0], np.uint8), np.array([140, 255, 255], np.uint8)),
    'yellow': (np.array([20, 100, 100], np.uint8), np.array([30, 255, 255], np.uint8)),
    'purple': (np.array([140, 50, 50], np.uint8), np.array([160, 255, 255], np.uint8)),
    'pink': (np.array([160, 50, 50], np.uint8), np.array([170, 255, 255], np.uint8)),
    'green': (np.array([40, 50, 50], np.uint8), np.array([80, 255, 255], np.uint8)),
    'orange': (np.array([10, 100, 100], np.uint8), np.array([20, 255, 255], np.uint8)),
    'gray': (np.array([0, 0, 50], np.uint8), np.array([180, 20, 200], np.uint8)),
    'brown': (np.array([10, 50, 50], np.uint8), np.array([20, 255, 200], np.uint8)),
    'lightblue': (np.array([90, 50, 70], np.uint8), np.array([170, 255, 255], np.uint8)),
}


# Function to find the nearest common color using HSV ranges
def get_nearest_color_hsv(rgb_color):
    # Convert RGB to HSV
    hsv_color = cv2.cvtColor(np.uint8([[rgb_color]]), cv2.COLOR_RGB2HSV)[0][0]

    for color_name, (lower, upper) in common_colors_hsv.items():
        if np.all(lower <= hsv_color) and np.all(hsv_color <= upper):
            return color_name

    return 'unknown'  # Return 'unknown' if no match is found


# Function to extract dominant colors
def extract_dominant_colors(image_path, n_colors=5):
    # Read the image
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)  # Convert BGR to RGB

    # Resize the image to speed up processing
    r, c = image.shape[:2]
    out_r = 500  # Resize image height to 500px
    new_image = cv2.resize(image, (int(out_r * float(c) / r), out_r))

    # Reshape image to a 2D array of pixels
    pixels = new_image.reshape((-1, 3))

    # Perform KMeans clustering to find dominant colors
    kmeans = KMeans(n_clusters=n_colors, random_state=42)  # Set random_state for consistent results
    kmeans.fit(pixels)

    # Get the cluster centers (dominant colors)
    dominant_colors = np.asarray(kmeans.cluster_centers_, dtype='uint8')

    # Map each dominant color to the nearest common color
    common_color_labels = [get_nearest_color_hsv(color) for color in dominant_colors]

    return dominant_colors, common_color_labels, new_image


# Function to plot the image and dominant colors
def plot_dominant_colors(image_path, dominant_colors, common_color_labels, new_image):
    # Plot original image and resized image
    plt.figure(figsize=(14, 6))

    plt.subplot(121)
    plt.title("Original Image")
    plt.imshow(new_image)
    plt.axis('off')

    # Plot dominant colors as bars
    plt.subplot(122)
    plt.title("Dominant Colors")
    plt.axis('off')
    plt.imshow([dominant_colors])  # Show colors as horizontal barsl

    # Print the common color names below the bar
    for i, color_label in enumerate(common_color_labels):
        plt.text(i, -0.1, color_label, fontsize=12, color='black', ha='center', va='center')

    plt.show()


# Main script
image_path = r"trip_segment.png"  # Replace with your image path
dominant_colors, common_color_labels, resized_image = extract_dominant_colors(image_path, n_colors=5)

# Output the dominant colors and their common names
print("Dominant Colors (RGB):", dominant_colors)
print("Mapped Common Colors:", common_color_labels)

# Plot the image and dominant colors
plot_dominant_colors(image_path, dominant_colors, common_color_labels, resized_image)
