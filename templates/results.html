<!DOCTYPE html>
<html>
<head>
    <title>BCM Compliance Report</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; line-height: 1.6; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .document-info { background: #e8f4fc; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .result-box {
            background: #f8f9fa;
            padding: 25px;
            border-left: 4px solid #3498db;
            white-space: pre-wrap;
            font-family: monospace;
            line-height: 1.5;
            margin-bottom: 30px;
        }
        .chat-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .chat-messages {
            height: 300px;
            overflow-y: auto;
            margin-bottom: 15px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 5px;
        }
        .user-message {
            background: #e3f2fd;
            margin-left: 20%;
        }
        .bot-message {
            background: #f1f1f1;
            margin-right: 20%;
        }
        .chat-input {
            display: flex;
            gap: 10px;
        }
        #chat-input {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        #send-btn {
            padding: 10px 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        #send-btn:hover { background: #2980b9; }
        .btn {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
            transition: background 0.3s;
        }
        .btn:hover { background: #2980b9; }
        .timestamp {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BCM Compliance Analysis Report</h1>
        <div class="document-info">
            <div>Reference Framework: <strong>{{ framework }}</strong></div>
            <div>Analyzed Document: <strong>{{ filename }}</strong></div>
            <div class="timestamp">Report generated on: <span id="datetime"></span></div>
        </div>
    </div>

    <div class="result-box">
        {{ result | safe }}
    </div>

    <div class="chat-container">
        <h2>Document Chat Assistant</h2>
        <p>Ask questions about your document or ISO 22301 compliance:</p>

        <div class="chat-messages" id="chat-messages">
            <!-- Messages will appear here -->
        </div>

        <div class="chat-input">
            <input type="text" id="chat-input" placeholder="Type your question here...">
            <button id="send-btn">Send</button>
        </div>
    </div>

    <a href="/" class="btn">Analyze Another Document</a>

    <script>
        // Add current date and time
        const now = new Date();
        document.getElementById('datetime').textContent = now.toLocaleString();

        // Chat functionality
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');
        const chatMessages = document.getElementById('chat-messages');

        function addMessage(sender, message) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message');
            messageDiv.classList.add(sender === 'user' ? 'user-message' : 'bot-message');
            messageDiv.innerHTML = `<strong>${sender === 'user' ? 'You' : 'Assistant'}:</strong><br>${message}`;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        async function sendMessage() {
            const question = chatInput.value.trim();
            if (!question) return;

            addMessage('user', question);
            chatInput.value = '';

            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ question })
                });

                const data = await response.json();
                if (data.error) {
                    addMessage('bot', `Error: ${data.error}`);
                } else {
                    addMessage('bot', data.response);
                }
            } catch (error) {
                addMessage('bot', `Error: Could not connect to the server`);
                console.error('Error:', error);
            }
        }

        sendBtn.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });
    </script>
</body>
</html>