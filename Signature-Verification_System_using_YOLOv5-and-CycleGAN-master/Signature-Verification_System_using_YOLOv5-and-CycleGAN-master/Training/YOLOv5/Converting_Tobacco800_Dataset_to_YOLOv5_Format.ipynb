#%% md
# Importing Libraries
#%%
import os, sys, random, shutil
import xml.etree.ElementTree as ET
from glob import glob
import pandas as pd
from shutil import copyfile
import pandas as pd
from sklearn import preprocessing, model_selection
import matplotlib.pyplot as plt
%matplotlib inline
from matplotlib import patches
import numpy as np
#%% md
Refer [this blog](https://towardsai.net/p/computer-vision/yolo-v5-object-detection-on-a-custom-dataset) for more information. Its an excellent resource.  
Tobacco 800 dataset could be downloaded from [here](http://tc11.cvc.uab.es/datasets/Tobacco800_1)
#%% md
# Extracting information from Tobacco-800 XML files
#%%
df = []
cnt = 0

# refer the xml files to understand its structure and revist this code block.
annotations = sorted(glob('tobacco_data_zhugy/groundtruth/*.xml'))
for file in annotations:
    myroot = ET.parse(file).getroot()
    # image filename is changed aah97e00-page02_1.tif -> 0.tif, so the previous filename is collected here.
    prev_filename = myroot[0].attrib['src']
    filename = str(cnt) + '.tif' # new filename based on the count (0.tif, 1.tif etc)
    page_height, page_width =  myroot[0][0].attrib['height'], myroot[0][0].attrib['width']
    
    row = []
    # An image might have multiple items (zones) (logos and signs), so iterate through each zones
    for zone in myroot[0][0]:
        category = zone.attrib['gedi_type'] # type of zone (DLLogo/ DLSignature)
        id = zone.attrib['id']
        x, y = zone.attrib['col'], zone.attrib['row'] # x, y coordinate
        w, h = zone.attrib['width'], zone.attrib['height'] # width and height of bbox
        
        # Signature have Authors, represeting whose signature it is
        if category == 'DLSignature':
            AuthorID = zone.attrib['AuthorID']
            Overlapped = zone.attrib['Overlapped']
        else:
            # Logos don't have authors.
            AuthorID, Overlapped = ('NA', 'NA')
        row = [prev_filename, filename, page_height, page_width, AuthorID, Overlapped, category, id, x, y, w, h]
        df.append(row)
    cnt += 1
#%% md
**Saving the information to Dataframe**
#%%
data = pd.DataFrame(df, columns=['prev_filename', 'filename', 'page_height', 'page_width', 'AuthorID', 'Overlapped', 'category', 'id', 'x', 'y', 'width', 'height'])
#%%
test = data[['page_height', 'page_width']]
test.max(), test.min()
#%% md
**Scaling the image to reduce training time**  
To save on training time, resize the images to a maximum height and width of 640 and 480. While resizing the image, the bounding box cordinates also changes. This code computes how much each image is shrinken and updates the bounding box coordinates appropriately.
#%%
BASE_DIR = 'tobacco_data_zhugy/pages/'
SAVE_PATH = 'tobacco_data_zhugy/scaled'
os.mkdir(SAVE_PATH)

def scale_image(df):
    df_new = []
    filename = df.prev_filename
    X, Y, W, H = map(int, df.x), map(int, df.y), map(int, df.width), map(int, df.height)
    for file, x, y, w, h in zip(filename, X, Y, W, H):
        image_path = BASE_DIR + file
        img = cv2.imread(image_path, 1)
        page_height, page_width = img.shape[:2]
        max_height = 640
        max_width = 480
        
        # computes the scaling factor
        if max_height < page_height or max_width < page_width:
            scaling_factor = max_height / float(page_height)
            if max_width/float(page_width) < scaling_factor:
                scaling_factor = max_width / float(page_width)
            # scale the image with the scaling factor
            img = cv2.resize(img, None, fx=scaling_factor, fy=scaling_factor, interpolation=cv2.INTER_AREA)
        jpg_filename = file[:-4] + '.jpg'
        new_file_path = SAVE_PATH + jpg_filename
        cv2.imwrite(new_file_path, img) # write the scales image
        
        # save new page height and width
        page_height, page_width = page_height*scaling_factor, page_width*scaling_factor
        # compute new x, y, w, h coordinates after scaling
        x, y, w, h= int(x*scaling_factor), int(y*scaling_factor), int(w*scaling_factor), int(h*scaling_factor)
        row = [jpg_filename, x, y, w, h, page_height, page_width]
        df_new.append(row)
    return df_new
scaled_data = scale_image(data)
#%% md
**Adding the information regarding the scaling to the df**
#%%
scaled_data = list(zip(*scaled_data))

data['new_filename'] = scaled_data[0]
data['x_scaled'] = scaled_data[1]
data['y_scaled'] = scaled_data[2]
data['w_scaled'] = scaled_data[3]
data['h_scaled'] = scaled_data[4]
data['page_height_scaled'] = scaled_data[5]
data['page_width_scaled'] = scaled_data[6]
data.head(10)
#%% md
**Testing the scaled image**
#%%
img = cv2.imread('tobacco_data_zhugy/scaled/agw39d00.jpg')

img = cv2.rectangle(img, (90, 348), (90+120, 348+26), (255, 0, 0), 1)
plt.figure(figsize=(6, 6))
plt.imshow(img)
#%% md
**Saving to CSV file**
#%%
data[['prev_filename', 'new_filename', 'filename', 'page_height', 'page_width', 'page_height_scaled', 'page_width_scaled', 'AuthorID', 'Overlapped', 'category', 'id', 'x', 'y', 'width', 'height', 'x_scaled', 'y_scaled', 'w_scaled', 'h_scaled']].to_csv('tobacco_data_zhugy/tobacco_cleaned.csv', index=False)
data.head(3)
#%% md
# Converting data to YOLOv5 format
#%%
def x_center(df):
  return int(df.x_scaled + (df.w_scaled/2))
def y_center(df):
  return int(df.y_scaled + (df.h_scaled/2))

def w_norm(df, col):
  return df[col]/df['page_width_scaled']
def h_norm(df, col):
  return df[col]/df['page_height_scaled']

df = pd.read_csv('tobacco_data_zhugy/tobacco_cleaned.csv')

le = preprocessing.LabelEncoder()
le.fit(df['category'])
print(le.classes_)
labels = le.transform(df['category'])
df['labels'] = labels


df['x_center'] = df.apply(x_center, axis=1)
df['y_center'] = df.apply(y_center, axis=1)

df['x_center_norm'] = df.apply(w_norm, col='x_center',axis=1)
df['width_norm'] = df.apply(w_norm, col='w_scaled', axis=1)

df['y_center_norm'] = df.apply(h_norm, col='y_center',axis=1)
df['height_norm'] = df.apply(h_norm, col='h_scaled',axis=1)

df.head(1)
#%% md
# Moving images to train and valid folders
#%%
df_train, df_valid = model_selection.train_test_split(df, test_size=0.1, random_state=13, shuffle=True)
print(df_train.shape, df_valid.shape)
#%% md
**Creating relevant directories**
#%%
os.mkdir('tobacco_data_zhugy/tobacco_yolo_format/')
os.mkdir('tobacco_data_zhugy/tobacco_yolo_format/images/')
os.mkdir('tobacco_data_zhugy/tobacco_yolo_format/images/train/')
os.mkdir('tobacco_data_zhugy/tobacco_yolo_format/images/valid/')

os.mkdir('tobacco_data_zhugy/tobacco_yolo_format/labels/')
os.mkdir('tobacco_data_zhugy/tobacco_yolo_format/labels/train/')
os.mkdir('tobacco_data_zhugy/tobacco_yolo_format/labels/valid/')
#%% md
**Segregating images and labels to train and valid**
#%%
def segregate_data(df, img_path, label_path, train_img_path, train_label_path):
  filenames = []
  for filename in df.filename:
    filenames.append(filename)
  filenames = set(filenames)
  
  for filename in filenames:
    yolo_list = []

    for _,row in df[df.filename == filename].iterrows():
      yolo_list.append([row.labels, row.x_center_norm, row.y_center_norm, row.width_norm, row.height_norm])

    yolo_list = np.array(yolo_list)
    txt_filename = os.path.join(train_label_path,str(row.new_filename.split('.')[0])+".txt")
    # Save the .img & .txt files to the corresponding train and validation folders
    np.savetxt(txt_filename, yolo_list, fmt=["%d", "%f", "%f", "%f", "%f"])
    shutil.copyfile(os.path.join(img_path,row.new_filename), os.path.join(train_img_path,row.new_filename))
 
# Apply function
src_img_path = "tobacco_data_zhugy/scaled/"
src_label_path = "tobacco_data_zhugy/groundtruth/"

train_img_path = "tobacco_data_zhugy/tobacco_yolo_format/images/train"
train_label_path = "tobacco_data_zhugy/tobacco_yolo_format/labels/train"

valid_img_path = "tobacco_data_zhugy/tobacco_yolo_format/images/valid"
valid_label_path = "tobacco_data_zhugy/tobacco_yolo_format/labels/valid"

segregate_data(df_train, src_img_path, src_label_path, train_img_path, train_label_path)
segregate_data(df_valid, src_img_path, src_label_path, valid_img_path, valid_label_path)

print("No. of Training images", len(os.listdir('tobacco_data_zhugy/tobacco_yolo_format/images/train')))
print("No. of Training labels", len(os.listdir('tobacco_data_zhugy/tobacco_yolo_format/labels/train')))

print("No. of valid images", len(os.listdir('tobacco_data_zhugy/tobacco_yolo_format/images/valid')))
print("No. of valid labels", len(os.listdir('tobacco_data_zhugy/tobacco_yolo_format/labels/valid')))
#%% md
**Deleting ipython checkpoints**
#%%
try:
  shutil.rmtree('tobacco_data_zhugy/tobacco_yolo_format/images/train/.ipynb_checkpoints')
except FileNotFoundError:
  pass

try:
  shutil.rmtree('tobacco_data_zhugy/tobacco_yolo_format/images/valid/.ipynb_checkpoints')
except FileNotFoundError:
  pass

try:
  shutil.rmtree('tobacco_data_zhugy/tobacco_yolo_format/labels/train/.ipynb_checkpoints')
except FileNotFoundError:
  pass

try:
  shutil.rmtree('tobacco_data_zhugy/tobacco_yolo_format/labels/valid/.ipynb_checkpoints')
except FileNotFoundError:
  pass
