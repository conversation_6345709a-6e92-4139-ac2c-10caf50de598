{"Network": [{"categoryName": "Network", "workflowCategoryBaseChildViewListVms": [{"name": "F5 Load Balancer", "workflowCategoryChildViewListVms": [{"name": "F5 LTM", "actionLists": [{"actionName": "ExecuteForceEnableNodeStateOfVirtualServer"}, {"actionName": "ExecuteForceDisableNodeStateOfVirtualServer"}, {"actionName": "ExecuteEnableLTMVirtualServer"}, {"actionName": "ExecuteDisableLTMVirtualServer"}, {"actionName": "CheckLTMVirtualServerState"}]}, {"name": "F5 Cloud Service", "actionLists": [{"actionName": "CheckF5LoadBalancerDefaultRoutePoolsNameExist"}, {"actionName": "CheckF5LoadBalancerNameExistandEnabled"}, {"actionName": "CheckAPITokenExpiryDate"}, {"actionName": "ExecuteMigrateF5LoadBalancerDefaultRoutePoolsNameChange"}]}]}, {"name": "NS1_DNS", "workflowCategoryChildViewListVms": [{"name": "NS1_DNS", "actionLists": [{"actionName": "ModifyNS1DNSRecordsATypeMultipleIPAddress"}, {"actionName": "ModifyNS1DNSRecordCNAMEType"}, {"actionName": "ModifyNS1DNSRecordATypeSingleIPAddress"}, {"actionName": "CheckNS1DNSRecordCNAMEType"}, {"actionName": "CheckNS1DNSRecordATypeSingleIPAddress"}, {"actionName": "CheckNS1DNSRecordATypeMultipleIPAddress"}]}]}, {"name": "InfoBlox", "workflowCategoryChildViewListVms": [{"name": "InfoBlox", "actionLists": [{"actionName": "INFOBLOX_QueryDNS"}, {"actionName": "INFOBLOX_ModifyDNS"}, {"actionName": "INFOBLOX_DeleteDNS"}, {"actionName": "INFOBLOX_DNSQueryStatisticsView"}, {"actionName": "INFOBLOX_AddDNS"}, {"actionName": "Delete_CName_Record"}, {"actionName": "ExecuteEnableDisableDNSRecordmapping"}, {"actionName": "CheckDNSRecordStatus"}, {"actionName": "CheckIfDNSRecordExistInViewandZone"}, {"actionName": "CheckifDSNZoneExistInDNSview"}, {"actionName": "CheckDNSViewExist"}, {"actionName": "ModifyCNameRecord"}, {"actionName": "AddCNameRecord"}]}]}, {"name": "Routers", "workflowCategoryChildViewListVms": [{"name": "Routers", "actionLists": [{"actionName": "Check<PERSON>lan"}, {"actionName": "CheckStaticRoute"}]}]}, {"name": "FireWall", "workflowCategoryChildViewListVms": [{"name": "FireWall", "actionLists": [{"actionName": "UpdateFirewallPolicy"}]}]}, {"name": "<PERSON><PERSON><PERSON>", "workflowCategoryChildViewListVms": [{"name": "OpenShift", "actionLists": [{"actionName": "OpenShiftMonitoring"}, {"actionName": "ExecuteScaleUpDownPodsDeploymentsCount"}, {"actionName": "CheckPodDeploymentsCountwithAllReadyStatus"}, {"actionName": "ExecuteScaleUpDownPodsReplicaSetsCount"}, {"actionName": "CheckPodReplicaSetsCountwithAllReadyStatus"}, {"actionName": "ExecuteScaleUpDownPodsStatefulSetsCount"}, {"actionName": "CheckPodStatefulSetsCountwithAllReadyStatus"}, {"actionName": "ExecuteScaleUpDownMachineSetsMachinesCount"}, {"actionName": "CheckOpenShiftMachineSetMachineCount"}, {"actionName": "ExecuteVirtualMachineStop"}, {"actionName": "ExecuteVirtualMachineStart"}, {"actionName": "CheckSpecificVirtualMachineStatus"}, {"actionName": "checkpoddepployment_test"}]}]}, {"name": "Switches", "workflowCategoryChildViewListVms": [{"name": "Switches", "actionLists": [{"actionName": "UpdateAirGap_ActionIM"}, {"actionName": "EnablePort_ActionIM"}, {"actionName": "VerifyPortEnable_ActionIM"}, {"actionName": "VerifyPortDisable_ActionIM"}, {"actionName": "DisablePort_ActionIM"}, {"actionName": "Nexus_AddRoute"}]}]}]}]}