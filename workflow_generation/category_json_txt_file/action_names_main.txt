ExecuteCheckPowerShellCommand,
VerifyInstalledSSH,
UIAutomationWithPassword,
UIAutomation,
UACDisable,
TestSRMConnectivity,
StopMSCluster,
Start_ScheduledTask_Job,
StartMSCluster,
KillUnixProcess,
IsClusterOnline,
IsClusterOffline,
InstallOpenSSH_New,
InstallOpenSSH,
FolderCompare,
FileCompare,
ExecuteScriptWithUserPassword,
ExecuteScriptWithPassword,
ExecuteScriptWithEnvPassword,
ExecuteScriptAlongPassword,
ExecuteScript,
ExecuteSSHSingleSession,
ExecuteOSZFSCommand,
ExecuteOSCommandWithPassword,
ExecuteLocalProcess,
ExecuteCheckOSCommandWithPassword,
CompareFileORFolder,
ClusterRemoveDependency,
ClusterAddDependency,
ChangeNodeIP,
ApplicationServiceMonitor,
Add_User_Administrator_Group,
ExecutePowerShellCommand,
EnableFolderTargetReferralNamespace,
DisableFolderTargetReferralNamespace,
CheckFolderTargetReferralNamespaceStatus,
Wait,
winkillProcessByName,
winIsFileExist,
WinRenameFile,
WinChangeFileText,
WaitForPing,
StopScheduledTask,
StartScheduledTask,
ShutdownRemoteServer,
ShutDownServer,
SCP,
ReplicateStandByCtrlFile,
ReplicateDataSyncFoldersWin,
ReplicateDataSyncFileWin,
ReplaceTxtInFile,
ReplaceFile,
RmvNTAUTHORITYSystmAcntFmDNSRcrdWithAlwOrDny,
RmveCmptrAcuntFrmDNSRcdWthAloOrDnyPrmsson,
RebootRemoteServer,
R_Replication,
ModifyDNSRecordValueATypewithoutTTL,
ModifyDNSRecordValueATypewithTTL,
MSSQLServerInstalled_SSMS_Install,
MSSQLServerInstalled,
ExecuteWindowsProcess,
ExecuteSymcliCommand,
ExecuteSSH,
ExecuteOSCmd,
ExecuteCheckSymcliCommand,
ExecuteBatchFile,
EnableScheduledTask,
DisableScheduledTask,
DeleteDNS,
DataSyncReplicateFile,
DataSyncRedoArchiveFolder,
DataSyncFolders,
DataSyncArchiveFolder,
DNSServerSetTTLValueforDNSSOA,
CheckStringValueExistInFile,
CheckServiceStatus,
CheckScheduledTaskStatus,
CheckFileExist,
CheckDNSRecordwithoutTTLValueAType,
CheckDNSRecordwithTTLvalueAType,
ChangeServiceStartMode,
ChangeDNS,
BatchFileExecution,
ApplicationStop,
ApplicationStart,
AddnewrecordTxtFile,
AddNTAUTHORITYSystmAcuntToDNSRcrdWthDenyPrmsn,
AddNTAUTHORITYSystmAcuntTDNSRcrdWthAlowPrmsn,
AddDNS,
AddCmptrAccountToDNSRecodWthDenyPermission,
AddCmptrAccountToDNSRecdWthAllowPrmsion,
WinIsServiceRunning,
WinIsServiceDown,
StopWindowsService,
StopService,
StartService,
StartWindowsService,
EnableNetscalerLoadBalancingServer,
DisableNetScalerLoadBalancing,
CheckNetScalerLoadBalancerStatus,
VerifyDNSCNAMERecord,
TransferOrSeizureFSMORole,
RemoveSPNFromActiveDirectoryComputer,
ModifyCNAMEwithTTL,
ModifyArecordwithTTL,
DCServer,
DNSVerify,
DNSModify,
CheckActiveDirectoryReplicationSyncAllStatus,
CheckActiveDirectoryReplicationStatus,
CheckADForestFSMORole,
CheckADDomainFSMORole,
ChangeDNSGlueRecord,
AddSPNToActiveDirectoryComputer,
ActiveDirectoryAddUser,
AD_CheckReplicationSyncAllStatus,
ADReplicationStatus,
Win2k8VerifyClusterResourceState,
Win2k8StopClusterResource,
Win2k8StartClusterResource,
OnLine,
OffLine,
Import,
DiskRescan,
DiskOnlinewithDriveLetter,
CheckDiskStatus,
MakeDiskReadWrite,
MakeDiskOnline,
MakeDiskOffline,
CheckDriveLetterafterDiskOnline,
CheckDiskDetailsUsingLUN_WWN_Number,
ChangeDiskDriveLetter,
Execute5250TransactionCountCheckPopup,
Execute5250Process,
AS400ExecuteMonitor,
CheckClientLPARStatus,
CheckClientBootDiskMapping,
ActivateClientLPAR,
WaitForLPARState,
ShutDownClientLPAR,
CheckEthernetStatus,
VaryOnVolumeGroups,
VaryOffVolumeGroups,
UnmountVolumeGroups,
UnmountVGS,
UnmountVG,
UnmountNFSVolume,
ReplicateStandByTraceFile,
ReplicateDataSyncFoldersPosix,
ReplicateDataSyncFoldersDR,
ReplicateDataSyncFilePosix,
RemoveHDisk,
RMDEVSVolumeGroups,
MountVolumeGroups,
MountVGS,
MountVG,
MountNFSVolume,
KillProcess,
HPUnmountVGS,
HPUnmountVGParallel,
HPUXVGChangeDeactive,
HPUXVGChangeActive,
HPUXUnmount,
HPUXMount,
HPUXIsVGActive,
HPUXImportVGToMapFile,
HPUXExportVGToMapFile,
HPMountVGS,
HPMountVGParallel,
ExportVolumeGroups,
ExecuteNohup,
ExecuteCPSL,
Enable_SLB_Real_Services,
Disable_SLB_Real_Services,
Check_SLB_Real_ServiceName_Status,
AIXMount,
AIXDisMount,
AIXChangeFileText,
ExecuteCPL,
ExecuteOSCommand,
ExecuteCheckOSCommand,
ReplicateRSyncFoldersPosix,
ReplicateRSyncFilePosix,
Ora_Rsync_ReplicateStandByControlFile,
Ora_Rsync_ReplicateStandByTraceFile,
ExecuteCheckXCLICommand,
VerifystatusofVM,
Verify_FailedOverWaitingCompletion_Status,
VerifyState,
VerifyReverseRepliConnectionconfig,
VerifyReplicationMode,
VerifyReplicationState,
VerifyPreparedForFailoverstatus,
VerifyHyperVVMVLANIDConnectedStatus,
VerifyHyperVVMDisConnectedStatus,
VerifyHyperVVMConnectedStatus,
VerifyForwardRepliConnectionconfig,
StartVM,
StartFailover,
SingleNetClsterHyprVVMIPAddressWthMacAddress,
ShutdownVM,
SetVLANIDToVirtualSwitchOnHyperVVM,
HyperVResumeReplication,
FailsOverReplicaVM,
DisconnectionToHyperVVM,
ConnectionToVirtualSwitchOnHyperVVM,
Cluster_VerifyPrimaryReplicationMode,
ChangeReplicationmode,
ChangeDNSIP,
ChngeClsterHyprVVMIPAddressWthOutMacAddress,
ChangeClusterHyperVVMIPAddressWithMacAddress,
Cancel_FailOver_Cluster_HyperVVM,
SingleNetClsterHyprVVMIPAddressWthuyMacAdres,
HyperV_Monitoring_Action,
RmoveClstrResorceDpndncyFrmDpndntResorce,
AddClusterResourceDependencyToDpndentResorce,
VerifyClusterResourceDpndncyInDependentRsorce,
RemoveClusterResourceFromClusterOwnerGroup,
AddClusterResourceToClusterOwnerGroup,
VerifyDiskStatusOnClusterSharedVolume,
VerifyClstrSharedVolumeDiskinFailOverClstr,
RemoveVirtualSharingHardDiskDuplicateVM,
RemoveVirtualSharingHardDisk,
EnableVirtualHardDiskSharingOptnDuplicateVM,
EnableVirtualHardDiskSharingOption,
DisableVirtualHardDiskSharingOptnDuplcteVM,
DisableVirtualHardDiskSharingOption,
ChkVirtualHardDskSharingOptnStatusDuplcteVM,
CheckVirtualHardDiskSharingOptionStatus,
AddNewVirtualHardDiskDuplicateVM,
AddNewVirtualHardDisk,
HyperV_Monitoring,
VerifyPowerOffStatusOfReplicaPR,
VerifyRunningStatusOfPrimaryDR,
StartPrimaryHyperVVMDR,
VerifyReplicaReplicationModePR,
VerifyHyperVReplicationStatePR,
VerifyPrimaryReplicationMode,
VerifyHyperVReplicationState,
RepModeToPrimaryRepAndStartRep,
VerifyFailOverWaitComStatusRep,
FailsOverReplicaOnTargetHostDR,
VerifyPreparedFailoverStatusPR,
StartFailoverPrimaryRepPending,
ShutdownPrimaryHyperVOff,
ShutdownPrimaryHyperV,
VerifyReverseRepConnectionConfigurationDR,
VerifyForwardRepConnectionConfigurationPR,
VerifyPrimaryReplicationModePR,
VerifyReplicaReplicationModeDR,
VerifyHyperVReplicationStateDR,
VirtualSwitchVerifyExistStatus,
VerifyReverseReplicationConnection,
VerifyForwardReplicationConnection,
VerifyFailedOverWaitingCompletionStatus,
VMNetworkAdapter_Verify_Status,
VMNetworkAdapterVerifyVLANIDStatus,
StopVM,
StartVMFailOver,
SingleNetHyperVVMIPAddressWithOutMacAddress,
SingleNetHyperVVMIPAddressWithMacAddress,
SetVMReverseReplication,
SetVLANIDToVirtualSwitch,
SetNetworkAdapterConnection,
PrepareVMFailOver,
ExecuteCheckHyperVCommand,
DisconnectNetworkAdapterConnection,
CheckVMState,
CheckVMReplicationState,
CheckVMReplicationMode,
CheckHyperVReplication,
ChangeHyperVVMIPAddressWithOutMacAddress,
ChangeHyperVVMIPAddressWithMacAddress,
Cancel_FailOver_Standalone_HyperVVM,
VerifyTargetLocalAvlabltyZoneforLeapRcvryPln,
VerifySourceFailedAvlbltyZoneforLeapRcvryPln,
VerifyNutanixLeapRecoveryPlanName,
Nutanix_AssignIPAddressToNetworkInterfaceCard,
NutanixLeapRecoveryPlnValdteRcvryPlnTrgtSte,
NutanixLeapRecoveryPlnUnplandFailovrTrgtSite,
NutanixLeapRecoveryPlanPlanedFailovrTargtSite,
ChkRecoveryPointEntityVMdsnotexstAtTargtLclAZ,
ChkRecoveryPointEntityVMexistAtSourceFailedAZ,
CheckIfNetworkAdapterExistUsingNICIPAddress,
CheckIfNetworkAdapterExstUsingMACAddress,
AssignDNSIPAddressToWindowsServerrandomly,
AssignDNSIPAddressToWndwsSrvrUsingMACAddress,
AssignDNSIPAddressToWndwServrUsigIPAddress,
AddNetworkInterfaceCardToVM,
VerifyIfNoReplIsPendingUnderPDForInactiveSite,
VerifyIfNoReplIsPendingUnderPDForActiveSite,
MigrateProtectionDomain,
ExecuteVMPowerOn,
ExecuteVMPowerOff,
Check_VM_State,
CheckVMExistInCGUnderPDForInactiveSite,
CheckVMExistInCGUnderPDForActiveSite,
CheckProtectionDomainStatus,
CheckCGExistInPDForInactiveSite,
CheckCGExistInPDForActiveSite,
UnmountDataStore,
UnmounFromAllEsxiHost,
UnRegisterVM,
UnRegisterDATASTORE,
StopVSphereHAAgentService,
StartVSphereHAAgentService,
RescanHBAToFindNewStorageLUN,
RegisterVMWithoutESXiHost,
RegisterVM,
ReScanVMFSforStorageLUN,
PowerOnVM,
PowerOnDATASTORE,
PowerOffVM,
PowerOffDATASTORE,
MountDatastoreOnAllEsxiHost,
MountDataStore,
DeAttachLUN,
CheckDataStoreCanBeUnmount,
AttachLunOnMultipleESXiHost,
AttachLUN,
vAPP_UpdateFailoverNetworkSettings,
vAPP_SetReverseReplication,
vAPP_PowerOn,
vAPP_PowerOff,
vAPP_ExecuteFailOver,
vAPP_Delete,
vAPP_CheckReplicationState,
vAPP_CheckRecoveryState,
vAPP_CheckOverallHealth,
vAPP_CheckFailoverNetworkSettingsSrcTgt,
vAPP_CheckFailoverNetworkSettings,
VCAV_ExecutevAPPSync,
RecoverPointforVMExecuteRecoverProduction,
RecoverPointforVMRecoveryActivitiesRecoverProduction,
RecoverPointforVMTestCopyForRecoverProductionStopActivity,
RecoverPointforVMTestCopyForRecoverProduction,
RecoverPointforVMExecuteFailoverRoleChange,
RecoverPointforVMRecoveryActivitiesFailoverRoleChange,
RecoverPointforVMTestCopyForFailoverStopActivity,
RecoverPointforVMTestCopyForFailover,
RecoverPointforVMTestCopyStopActivity,
RecoverPointforVMTestCopy,
CheckConsistencyGroupTransferStatusActive,
CheckConsistencyGroupRecoveryActivitiesStatus,
CheckConsistencyGroupProdReplicavRPAclusterDetails,
VirtualizationRPforVMReplication_Monitor,
ExecuteVPGRollbackBeforeMoveCommit,
ExecuteVPGMove_CommitPolicyNONE,
ExecuteVPGMoveCommitwithoutReverseProtection,
ExecuteVPGMoveCommitwithReverseProtection,
ExecuteVPGFailoverwithoutCommitAndSDSourceVM,
CheckVPGStateVolumeInitialSync,
CheckVPGStateMovingBeforeCommit,
ExecuteVPGVPGFailoverCommitwithReverseProtection,
ExecuteVPGSTOPFAILOVERTEST,
ExecuteVPGRollbackBeforeFailoverCommit,
CheckVPGProtectionStsMeetingSLAandStateNone,
ExecuteVPGFailoverwithoutCommit,
ExecuteVPGFAILOVERTEST,
CheckVPGStateDeltaSyncing,
CheckVPGRecoverySiteDR,
CheckVPGProtectionStateFailingOverRollingBack,
CheckVPGProtectionStateFailingOverBeforeCom,
CheckVPGProtectionStatusFailingover,
RP4VM_FailoverUsingPredefinedNetwork,
RP4VM_EnableLatestImage,
RP4VM_StartReplicaTransfer,
AddAdditionalDisk,
VerifyVolumeMount,
VerifyUnmountStatus,
VerifyStorageLun,
VerifyMountStatus,
VerifyDisk,
VerifyDeviceAssociatedToDisk,
VerifyAttachStatus,
VcenterVMPowerOn,
VcenterVMPowerOff,
VcenterVMCheckRunning,
VcenterRemoveVM,
VcenterRemoveSnapshot,
VcenterProvisionVM,
VcenterPowerOnVM,
VcenterPowerOffVM,
VcenterExecuteVMCommand,
VcenterCreateLinkedClone,
VcenterCheckVMToolStatus,
VcenterCheckVMPowerState,
VcenterCheckVMExist,
VMUnmount,
VMUnRegister,
VMRemoveSnapShot,
VMRegisterMachine,
VMMount,
VMIsAvailable,
VMCreateSnapShot,
VMCheckRunning,
UpdateWindowsHostName,
UpdateVMNetworkInfo,
RescanAllAdaptors,
ReplicateVirtualMachine,
RemoveVM,
RemoveLunFromESXI,
RemoveGuestVMSystemFromDomain,
RemoveAdditionalDisk,
PowerOnMachine,
PowerOffMachine,
NepAppVMUnmount,
MountUsingVMFSUUID,
JoinGuestVMSystemToDomain,
IsMachineRunning,
ExecuteVmWarePowerCLICommand,
ExecutePowerCLIScript,
ExecuteNCheOpVmWarePowerCLICommand,
ExecuteCheckVMCommand,
DetachLunFromESXI,
CreateNewVm,
CreateLinkedClone,
CheckVMToolStatus,
CheckVMExist,
ChangeGuestVMHostName,
AttachLuns,
SRMReProtectRecoveryPlan,
SRMPerformPlannedMigrationFailover,
SRMPerformDisasterRecoveryFailover,
SRMInitiateRecoveryPlan,
SRMExecuteTestRecoveryPlan,
SRMExecuteCleanupRecoveryPlan,
SRMCheckRecoveryPlanState,
SRMCheckProtectionGroupState,
ReProductRecoveryPlan,
CheckRecoveryPlanState,
SRM_Monitor_Action,
VerifyTargetLocalAvlabltyZoneforLeapRcvryPln,
VerifySourceFailedAvlbltyZoneforLeapRcvryPln,
VerifyNutanixLeapRecoveryPlanName,
Nutanix_AssignIPAddressToNetworkInterfaceCard,
NutanixLeapRecoveryPlnValdteRcvryPlnTrgtSte,
NutanixLeapRecoveryPlnUnplandFailovrTrgtSite,
NutanixLeapRecoveryPlanPlanedFailovrTargtSite,
ChkRecoveryPointEntityVMdsnotexstAtTargtLclAZ,
ChkRecoveryPointEntityVMexistAtSourceFailedAZ,
CheckIfNetworkAdapterExistUsingNICIPAddress,
CheckIfNetworkAdapterExstUsingMACAddress,
AssignDNSIPAddressToWindowsServerrandomly,
AssignDNSIPAddressToWndwsSrvrUsingMACAddress,
AssignDNSIPAddressToWndwServrUsigIPAddress,
AddNetworkInterfaceCardToVM,
VerifyIfNoReplIsPendingUnderPDForInactiveSite,
VerifyIfNoReplIsPendingUnderPDForActiveSite,
MigrateProtectionDomain,
ExecuteVMPowerOn,
ExecuteVMPowerOff,
Check_VM_State,
CheckVMExistInCGUnderPDForInactiveSite,
CheckVMExistInCGUnderPDForActiveSite,
CheckProtectionDomainStatus,
CheckCGExistInPDForInactiveSite,
CheckCGExistInPDForActiveSite,
VrfyIfNoReplicationPendingForPDInActiveSite,
VerifyIfNoReplicationPendingForPD_ActiveSite,
ProtectionDomainActivationFailoverFromDR,
ChkVMExistInConsistencyGrpUndrPDInActveSite,
ChkVMExistInConsistencyGrpUndrPDActveSte,
CheckProtectionDomainStatusForSite,
ChkConsistncyGrpMembrStatusUndrPDInActveSte,
ChkConsistencyGrpMembrStatsUndrPDActvSte,
AcknowledgenResolveRcntInformativeAlrtForPD,
VerifyDiskStatusOnClusterSharedVolume,
VerifyClstrSharedVolumeDiskinFailOverClstr,
RemoveVirtualSharingHardDiskDuplicateVM,
RemoveVirtualSharingHardDisk,
EnableVirtualHardDiskSharingOptnDuplicateVM,
EnableVirtualHardDiskSharingOption,
DisableVirtualHardDiskSharingOptnDuplcteVM,
DisableVirtualHardDiskSharingOption,
ChkVirtualHardDskSharingOptnStatusDuplcteVM,
CheckVirtualHardDiskSharingOptionStatus,
AddNewVirtualHardDiskDuplicateVM,
AddNewVirtualHardDisk,
HyperV_Monitoring,
VerifyPowerOffStatusOfReplicaPR,
VerifyRunningStatusOfPrimaryDR,
StartPrimaryHyperVVMDR,
VerifyReplicaReplicationModePR,
VerifyHyperVReplicationStatePR,
VerifyPrimaryReplicationMode,
VerifyHyperVReplicationState,
RepModeToPrimaryRepAndStartRep,
VerifyFailOverWaitComStatusRep,
FailsOverReplicaOnTargetHostDR,
VerifyPreparedFailoverStatusPR,
StartFailoverPrimaryRepPending,
ShutdownPrimaryHyperVOff,
ShutdownPrimaryHyperV,
VerifyReverseRepConnectionConfigurationDR,
VerifyForwardRepConnectionConfigurationPR,
VerifyPrimaryReplicationModePR,
VerifyReplicaReplicationModeDR,
VerifyHyperVReplicationStateDR,
VirtualSwitchVerifyExistStatus,
VerifyReverseReplicationConnection,
VerifyForwardReplicationConnection,
VerifyFailedOverWaitingCompletionStatus,
VMNetworkAdapter_Verify_Status,
VMNetworkAdapterVerifyVLANIDStatus,
StopVM,
StartVMFailOver,
SingleNetHyperVVMIPAddressWithOutMacAddress,
SingleNetHyperVVMIPAddressWithMacAddress,
SetVMReverseReplication,
SetVLANIDToVirtualSwitch,
SetNetworkAdapterConnection,
PrepareVMFailOver,
ExecuteCheckHyperVCommand,
DisconnectNetworkAdapterConnection,
CheckVMState,
CheckVMReplicationState,
CheckVMReplicationMode,
CheckHyperVReplication,
ChangeHyperVVMIPAddressWithOutMacAddress,
ChangeHyperVVMIPAddressWithMacAddress,
Cancel_FailOver_Standalone_HyperVVM,
ShutDownZone,
DetachZone,
CheckZoneUp,
CheckZoneStatus,
CheckZoneDown,
BootZone,
AttachZone,
VerifyLDOMPowerON,
VerifyLDOMPowerOFF,
LDOMUnbindDomain,
LDOMStopDomain,
LDOMStartDomain,
LDOMRemoveDomain,
LDOMBindDomain,
LDOMADDMemory,
LDOMADDDomain,
LDOMADDCPU,
AIXLPARPOWERON,
AIXLPARPOWEROFF,
AIXLPARCHECKPOWERON,
AIXLPARCHECKPOWEROFF,
Redis_CheckEmptyArray_DR,
Redis_CheckEmptyArray_PR,
Redis_VerifySingleTableRowCount_DR,
Redis_VerifySingleTableRowCount_PR,
Postgres_StartService,
CheckContent,
AddContentRecovery,
CheckRecoveryFilePrimary,
VerifyRecoveryDone,
RestartService,
CreateTriggerFile,
CheckRecoveryFile,
Postgres_StopService,
VerifyRecoveryStatus,
VerifyServerRuuning,
VerifyPostGresSQL_TriggerFileNameAndPath,
VerifyPostGresSQL_DR_ReplicationStatus,
VerifyPostGresSQL_PR_ReplicationStatus,
VerifyPostGresSQL_DatabaseClusterState,
VerifyPostGresSQLXLogLSNMatching,
StartPostgreSQLServer,
RestartPostgreSQLServer,
ExecuteDRFailingOverToStandbyServer,
VerifyDRTriggerFilePath,
VerifyPRDBClusterShutdown,
StopPostgreSQLServer,
CreateRecoveryConfAtPR,
Verify_PRAndDRWalLSNMatches,
Verify_DatabaseClusterStatus,
ExecutePostgresSQLCommand,
ClusterChangeOver,
PromoteClusterState,
VerifyReplicationStatusDR,
VerifyClusterStatus,
VerifyPostgresServiceStatus,
VerifyCurrentWalLocation,
VerifyPostgreSQLRecoveryStatus,
VerifyReplicationStatusPR,
VerifyClusterState,
PostgresMonitoring,
PG_Recovery_Sed,
StartPostgresSQLService,
PG_RecoveryCat,
StopPostgreSQLService,
PG_RewindSynchronizeTwoCluster,
PG_RecoveryConf_MV,
pg_rewindtest,
CreateStandByFileInSecondarySer,
CustomActionTest,
PowerShellTest,
TestAction,
StartSybaseServer,
VerifySybaseServerIsUp,
VerifySybaseServerIsDown,
SybaseShutDownDB,
SybaseCheckPointDB,
ReplicateSybaseBcpTable,
DumpTransactionLogFilewithStandbyAccess,
SybaseOnlineDBForStandbyAccess,
SybaseOnlineDB,
LoadTransactionLogFile,
ExecuteSybaseISQLCommand,
GenerateSybaseLastTransactionLog,
ExecuteSybaseBcpInCommand,
ExecuteSybaseBcpOutCommand,
VerifyDBStatus,
VerifyBackupServerStatus,
VerifySybaseDataServerStatus,
CopySybaseLastTransactionLog,
ExecuteCheckSybaseISQLcommand,
SybaseReplicationResumeConnection,
SybaseStartReplicationAgentOnPrimary,
SybaseReplicationCheckRoleSwitchStatus,
SybaseReplicationSwitchOverStatus,
SybasePrimaryToStandby,
VerifySybaseReplicationAgentISDown,
SybaseStopReplicationAgentOnPrimary,
VerifySybaseStandbyDBStatus,
VerifySybasePrimaryDBStatus,
test_command_Copy9011,
TestWW_Copy8328,
VerifyDatabaseStatu_Copy3563,
LSSQL_RstreLastBckupLogwthNoRcvryWithPrchck_Copy1761,
LSSQL_EnableLogshippingwithTargetDBRestoring_Copy2143,
LSSQL_RestoreLastlogwithStandbyDR_Copy1751,
LSSQL_RestoreLastlogwithRecoveryDR_Copy6317,
LSSQL_ValidateBackupwithPrimarySecondary_Copy8706,
LSSQL_CopyTailLogBackupDR_Copy4404,
LSSQL_VerifyPrimarybackuptransactionlogExist_Copy4304,
LSSQLExecuteSecondaryLogShipping_Copy6983,
LSSQLExecutePrimaryLogShippingReverse_Copy6024,
VerifyJobNameAssociatedWithLogShipping_Copy9987,
VerifySourceAndDestBackupDirectory_Copy626,
VerifyBackupDirectoryandSharePath_Copy2758,
Execute2DRSitePrimaryLogShipping_Copy3220,
LSSQLRestoreLastBackupLogWithRecovery_Copy8040,
Execute3SiteSecondaryLogShipping_Copy7757,
Execute3DRSitePrimaryLogShipping_Copy2643,
RestoreLogWithStandby_Copy1265,
Verify3DRSiteLogFileSequence_Copy9302,
LSSQLMakeDBWritableLastRestoredFileFailOver_Copy1308,
LSSQLExecuteSecondaryLogShippingJobSchedule_Copy8777,
LSSQLExecutePrimaryLogShippingJobSchedule_Copy504,
SQLNLSCheckDatabaseMode_Copy5504,
SQLNLSCheckDatabaseState_Copy5936,
VerifyDatabaseMirroringStatus_Copy8342,
MirroringFailOver_Copy1092,
KillMSSQLProcessByName_Copy4082,
ILSSQLRemoveRestoreJob_Copy4676,
ILSSQLRemoveCopyJob_Copy8019,
AttachDB_Copy9781,
SetDBOptionSingleUser_Copy2035,
DBOffline_Copy5634,
LSSQLMigrateRoles_Copy5841,
ILSSQLMigrateLogins_Copy6748,
ILSSQLExecuteSecondaryLogShipping_Copy8481,
ILSSQLExecutePrimaryLogShipping_Copy1013,
ILSSQLRemoveBackUpJob_Copy8703,
ILSSQLRunBackUpJob_Copy1312,
LinuxDisMount_Copy8956,
LinuxMount_Copy1579,
ILSSQLMigrateRoles_Copy6694,
LSSQLMigrateLogins_Copy2225,
DetachSQLDatabase_Copy6169,
AttachSQLDatabase_Copy4977,
test_NLS_Copy697,
LSSQL_RemoveSecondaryJob_Copy5188,
LSSQL_Check_DbEntry_OnPrimaryServer_Exist_Copy6825,
LSSQL_Check_DBEntry_OnSecondaryServer_Exist_Copy3771,
LSSQL_Check_PrimaryLogshipping_Exist_Copy1013,
LSSQLCheckPrimarySecondaryLogshippingExist_Copy153,
LSSQLRestoreSecondaryLogShipping_Copy6109,
LSSQLRestorePrimaryLogShipping_Copy8024,
LSSQLRestoreLastBackUpLogWithNoRecovery_Copy5534,
LSSQLSetDBMultiUserAccessMode_Copy3724,
LSSQLGenerateLastBackUpLog_Copy8879,
LSSQLKillAllSession_Copy6006,
MSSQL_NLS_Monitoring_Copy5990,
FixOrphanUsers_Copy144,
ILSSQLSetDRDBInMultiUserAccessMode_Copy7365,
LSSQLUpdatingRestoreJobWithDRIPAddress_Copy7250,
ILSSQLUpdatingRestoreJobWithDRIPAddress_Copy3921,
LSSQLUpdatingCopyJobWithDRIPAddress_Copy1697,
ILSSQLUpdatingCopyJobWithDRIPAddress_Copy7043,
ILSSQLUpdatingBackupJobWithPRIPAddress_Copy6952,
LSSQLUpdatingBackupJobWithPRIPAddress_Copy5431,
LSSQLRemovePrimarySecondaryLogShipping_Copy3349,
ILSSQLRemovePrimaryLogShipping_Copy9087,
LSSQLRemoveSecondaryLogShipping_Copy6454,
ILSSQLRemoveSecondaryLogShipping_Copy9524,
LSSQLRemovePrimaryLogShipping_Copy7851,
ILSSQLRemovePrimarySecondaryLogShipping_Copy3414,
LSSQLRestoreDatabaseWithRecovery_Copy8869,
ILSSQLRestoreDatabaseWithRecovery_Copy3308,
ILSSQLSetDBSingleUserAccessModeDR_Copy2666,
ILSSQLKillSessionDR_Copy1484,
ILSSQLSetDBMultiUserAccessModePrimary_Copy6753,
LSSQLVerifyDBSingleUserAccessMode_Copy3803,
ILSSQLVerifyPRDBSingleUserAccessMode_Copy4681,
LSSQLSetDBSingleUserAccessMode_Copy9415,
ILSSQLSetDBSingleUserAccessModePrimary_Copy4551,
ImportLoginRolesOnDRServer_Copy5070,
ImportLoginsOnDRServer_Copy2279,
ExportLoginRolesFromProductionServer_Copy951,
ExportLoginFromProductionServer_Copy8248,
ILSSQLKillSessionPrimary_Copy3833,
LSSQLFixOrphanUsers_Copy4095,
LSSQLEnableJob_Copy978,
LSSQLGenerateLastBackUpLogWithNoRecovery_Copy9108,
LSSQLKillDBSessionWithTimeOut_Copy8578,
LSSQLRunJob_Copy7046,
LSSQLDisableJob_Copy903,
VerifyLogFileSequence_Copy2486,
RunRestoreJob_Copy5153,
RunCopyJob_Copy7219,
DisableRestoreJob_Copy9208,
DisableCopyJob_Copy5245,
DisableBackUpJob_Copy5566,
BackupJobStatus_Copy9896,
TransactionLogShippingState_Copy5868,
DatabaseAccessMode_Copy5213,
DatabaseRecoveryModel_Copy1968,
VerifyDatabaseRecoveryMode_Copy4754,
VerifyUpdateabilityState_DR_Copy9100,
VerifyUpdateabilityState_PR_Copy8009,
VerifyDatabaseStatus_Copy2448,
ExecuteMSSQLCommand,
ExecuteCheckMSSQLCommand,
CompareSQLServerTableRowCountDR,
CompareSQLServerTableRowCountPR,
UnjoinedDBfromAvailabilityGroup,
RestoreDatabasewithRecoveryDROnly,
RemovePrimaryDatabase,
ModifyAGMode,
JoinSecondaryDBtoAvailabilityGroup,
CheckDBisJoinedorUnjoined,
AddPrimaryDatabasetoAvailabiityGroup,
TestallAvailabilityReplicasHealthProd,
TestallAvailabilityReplicasHealthDR,
SynAlwaysOnFailoverWithSpecificSecondary,
SynAlwaysOnFailoverWithRandomSecondary,
SuspendReplicationAllDBinAvailabilityGrup,
ResumeReplication,
ManualPlannedFailover,
ForceFullyFailover,
CheckSuspendedState,
CheckResumed,
CheckAvailabilityGroupStatusProd,
CheckAvailabilityGroupStatusEnableDisable,
CheckAvailabilityGroupStatusDR,
CheckAllowAllConnectionsProdDR,
CheckAllDatabasesState,
ASynAlwaysOnFailoverWithSpecificSecondary,
ASynAlwaysOnFailoverWithRandomSecondary,
CheckSqlServerRunningStates,
CheckDBRoleAndMirrorState,
ExecuteDBClusterChangeOver,
MSSQL_Mirroring_Monitoring,
ChangeClusterNodeWeightMultiple,
ChangeClusterNodeWeightSingle,
CheckClusterNodeWeightMultiple,
CheckClusterNodeWeightSingle,
ClusterNameAssociatewithGroup,
SqlDSMsSqlRestoreLastLog,
SqlDSMigrateServerRolesDR,
SqlDSMSSqlRestoreLog,
ScanFileFromApplicationServer,
SQLDSMigrateServerRolesPR,
SQLDSMigrateLoggingPR,
SQLDSMigrateLoggingDR,
SQLDSMSSQLRestoreDBWithRecovery,
SQLDSMSSQLKillProcessSec,
SQLDSMSSQLKillProcessPrim,
SQLDSMSSQLGenerateLastLogFC,
SQLDSMSSQLDBOption,
SQLDSDatSyncSQL2000,
MoveCSVFileToDBServer,
FileReplicationApplicationToDBServer,
DownloadResponseFileFromProductionServer,
CreateCSVFileFromRESP,
ApplicationDataSync,
MSSQL_NLS_Monitoring,
FixOrphanUsers,
ILSSQLSetDRDBInMultiUserAccessMode,
LSSQLUpdatingRestoreJobWithDRIPAddress,
ILSSQLUpdatingRestoreJobWithDRIPAddress,
LSSQLUpdatingCopyJobWithDRIPAddress,
ILSSQLUpdatingCopyJobWithDRIPAddress,
ILSSQLUpdatingBackupJobWithPRIPAddress,
LSSQLUpdatingBackupJobWithPRIPAddress,
LSSQLRemovePrimarySecondaryLogShipping,
ILSSQLRemovePrimaryLogShipping,
LSSQLRemoveSecondaryLogShipping,
ILSSQLRemoveSecondaryLogShipping,
LSSQLRemovePrimaryLogShipping,
ILSSQLRemovePrimarySecondaryLogShipping,
LSSQLRestoreDatabaseWithRecovery,
ILSSQLRestoreDatabaseWithRecovery,
ILSSQLSetDBSingleUserAccessModeDR,
ILSSQLKillSessionDR,
ILSSQLSetDBMultiUserAccessModePrimary,
LSSQLVerifyDBSingleUserAccessMode,
ILSSQLVerifyPRDBSingleUserAccessMode,
LSSQLSetDBSingleUserAccessMode,
ILSSQLSetDBSingleUserAccessModePrimary,
ImportLoginRolesOnDRServer,
ImportLoginsOnDRServer,
ExportLoginRolesFromProductionServer,
ExportLoginFromProductionServer,
ILSSQLKillSessionPrimary,
LSSQLFixOrphanUsers,
LSSQLEnableJob,
LSSQLGenerateLastBackUpLogWithNoRecovery,
LSSQLKillDBSessionWithTimeOut,
LSSQLRunJob,
LSSQLDisableJob,
VerifyLogFileSequence,
RunRestoreJob,
RunCopyJob,
DisableRestoreJob,
DisableCopyJob,
DisableBackUpJob,
BackupJobStatus,
TransactionLogShippingState,
DatabaseAccessMode,
DatabaseRecoveryModel,
LSSQLRestoreSecondaryLogShipping,
LSSQLRestorePrimaryLogShipping,
LSSQLGenerateLastBackUpLog,
LSSQLKillAllSession,
LSSQLRestoreLastBackUpLogWithNoRecovery,
LSSQLSetDBMultiUserAccessMode,
LSSQL_RemoveSecondaryJob,
LSSQL_Check_DbEntry_OnPrimaryServer_Exist,
LSSQL_Check_DBEntry_OnSecondaryServer_Exist,
LSSQL_Check_PrimaryLogshipping_Exist,
LSSQLCheckPrimarySecondaryLogshippingExist,
VerifyDatabaseRecoveryMode,
VerifyUpdateabilityState_DR,
VerifyUpdateabilityState_PR,
VerifyDatabaseStatus,
test_NLS,
LSSQL_RstreLastBckupLogwthNoRcvryWithPrchck,
LSSQL_EnableLogshippingwithTargetDBRestoring,
LSSQL_RestoreLastlogwithStandbyDR,
LSSQL_RestoreLastlogwithRecoveryDR,
LSSQL_ValidateBackupwithPrimarySecondary,
LSSQL_CopyTailLogBackupDR,
LSSQL_VerifyPrimarybackuptransactionlogExist,
LSSQLExecuteSecondaryLogShipping,
LSSQLExecutePrimaryLogShippingReverse,
VerifyJobNameAssociatedWithLogShipping,
VerifySourceAndDestBackupDirectory,
VerifyBackupDirectoryandSharePath,
Execute2DRSitePrimaryLogShipping,
LSSQLRestoreLastBackupLogWithRecovery,
Execute3SiteSecondaryLogShipping,
Execute3DRSitePrimaryLogShipping,
RestoreLogWithStandby,
Verify3DRSiteLogFileSequence,
LSSQLMakeDBWritableLastRestoredFileFailOver,
LSSQLExecuteSecondaryLogShippingJobSchedule,
LSSQLExecutePrimaryLogShippingJobSchedule,
SQLNLSCheckDatabaseMode,
SQLNLSCheckDatabaseState,
VerifyDatabaseMirroringStatus,
MirroringFailOver,
KillMSSQLProcessByName,
ILSSQLRemoveRestoreJob,
ILSSQLRemoveCopyJob,
AttachDB,
SetDBOptionSingleUser,
DBOffline,
LSSQLMigrateRoles,
ILSSQLMigrateLogins,
ILSSQLExecuteSecondaryLogShipping,
ILSSQLExecutePrimaryLogShipping,
ILSSQLRemoveBackUpJob,
ILSSQLRunBackUpJob,
LinuxDisMount,
LinuxMount,
ILSSQLMigrateRoles,
LSSQLMigrateLogins,
DetachSQLDatabase,
AttachSQLDatabase,
LSSQL_Verify_DbEntry_OnPrimaryServer_Exist,
LSSQL_Verify_DBEntry_OnSecondaryServer_Exist,
LSSQL_Verify_PrimaryLogshipping_Exist,
LSSQLVerifyPrimarySecondaryLogshippingExist,
SQLNLS_CheckDatabaseMode,
ChangeAvailabilityMode,
ExecuteAlwaysOnNormalFailOver,
PreFlightCheckDatabaseSyncState,
AlwaysON_Montioring,
ResumeDataMovement,
ExecuteAlwaysOnForceFailOver,
CheckAvailabilityMode,
CheckRole,
CheckPriorityState_Custom,
CheckHealthStatusUP,
MongoDBMonitor,
CheckReplicationLagStatus,
SetPriorityOfTheReplicaSet,
CheckMangoDBPrimaryState,
CheckSecondaryState,
DB2TakeOverHADR,
DB2Start,
DB2DeActivateDBStart,
DB2ActivateDB,
DB2SwitchBackDB,
DB2SwitchOverDB,
DB2IsDBUp,
DB2IsHADRActive,
DB2StopHADR,
DB2StartHADRPrimary,
DB2DeActivateDBTerminate,
DB2StartHADRStandBy,
DB2DeactivateDatabase,
DB2Terminate,
DB2IsDatabaseActive,
DB2UnquiesceDatabase,
DB2IsDatabaseQuiesced,
DB2QuiesceDatabase,
DB2VerifyLogGap,
DB2VerifyLogPosition,
DB2IsHADRStatePEER,
DB2IsHADRRoleStandby,
DB2IsHADRRolePrimary,
DB2IsDatabaseStandby,
DB2IsDatabasePrimary,
DB2HADR_Monitoring,
VerifystatusofVM,
Verify_FailedOverWaitingCompletion_Status,
VerifyState,
VerifyReverseRepliConnectionconfig,
VerifyReplicationMode,
VerifyReplicationState,
VerifyPreparedForFailoverstatus,
VerifyHyperVVMVLANIDConnectedStatus,
VerifyHyperVVMDisConnectedStatus,
VerifyHyperVVMConnectedStatus,
VerifyForwardRepliConnectionconfig,
StartVM,
StartFailover,
SingleNetClsterHyprVVMIPAddressWthMacAddress,
ShutdownVM,
SetVLANIDToVirtualSwitchOnHyperVVM,
HyperVResumeReplication,
FailsOverReplicaVM,
DisconnectionToHyperVVM,
ConnectionToVirtualSwitchOnHyperVVM,
Cluster_VerifyPrimaryReplicationMode,
ChangeReplicationmode,
ChangeDNSIP,
ChngeClsterHyprVVMIPAddressWthOutMacAddress,
ChangeClusterHyperVVMIPAddressWithMacAddress,
Cancel_FailOver_Cluster_HyperVVM,
SingleNetClsterHyprVVMIPAddressWthuyMacAdres,
HyperV_Monitoring_Action,
RmoveClstrResorceDpndncyFrmDpndntResorce,
AddClusterResourceDependencyToDpndentResorce,
VerifyClusterResourceDpndncyInDependentRsorce,
RemoveClusterResourceFromClusterOwnerGroup,
AddClusterResourceToClusterOwnerGroup,
VrfyMasterLogFileAndPostiononMstrSlaveServer,
StopSlave,
Slave_SQLRunningStatus,
SlaveIO_State,
SlaveIORunning_Status,
Relay_MasterLogFile,
Read_MasterLogPosition,
MySQLSlaveStatus,
MySQLServicestatus,
MasterRelayMasterlogFile,
MasterReadMasterlogPosition,
Master_LogFile,
ExecuteMysqlDBCommand,
ExecuteCheckMysqlDBCommand,
ExecMasterLogPosition,
ConnectStatus,
ChangeMASTERToMasterHostAndLogFile,
ChangeMasterToMasterHost,
SetGlobalReadOnlyOFF,
SetGlobalReadOnlyON,
FlushLogs,
FlushTables,
VerifyReadWriteOnMasterInstance,
MakeMasterInstanceToReadWrite,
ShowMasterStatus,
StartSlaveInMaster,
CheckSlaveStoppedStatus,
MySQLMonitoring,
StopReplicationInSlaveServer,
MakeMasterInstanceReadOnly,
VerifyReadOnlyOnSlave,
VerifyRepUserConnectivityFromSlaveToMaster,
VerifyReadOnlyOnMaster,
VerifySQLLogBinSetToONMaster,
VerifyReadOnlyOnMasterInstance,
VerifySQLLogPositionMasterAndSlaveServer,
RecoverStandbyDB,
PreShutDB,
PRPreShutRedoCtrlScript,
MountStandbyDatabase,
CreateControlScript,
CreateControlFileFromTraceFile,
BackupRedoControlFile,
AddTempTableSpace,
ASMRestoreStandbyControlFile,
ASMRecoverDatabase,
ASMOpenDatabase,
ASMCreateStandbyControlFile,
StartUp_NoMount,
StartUpMount,
ShutDownStandbyDB,
ShutDownPrimaryDB,
ShutownDRDB,
ReplicateRSyncFoldersPosix,
ReplicateRSyncFilePosix,
Ora_Rsync_ReplicateStandByControlFile,
Ora_Rsync_ReplicateStandByTraceFile,
UpdateDROperationStatus,
VerifyDatabaseState,
MaxDB_StartDatabase,
MaxDB_StopDatabase,
ExecuteCheckCommand,
DGBValidateDB,
DGBSwitchOver,
DGBFailOver,
DGBConvertSnapshotStandby,
DGBConvertPhysicalStandby,
DGBConfigurationIsOK,
OracleDG12C_StartRecovery,
OracleDG12C_StartDB,
OracleDG12C_ShutdownDB,
OracleDG12C_MountDB,
OracleDG12CDGVerifySwitchover,
OracleDG12CDGSwitchover,
StopNodeManagerService,
StopManagedServer,
StopHTTPServer,
StopAdminServer,
StartNodeManagerService,
StartManagedServer,
StartHTTPServer,
StartAdminServer,
VerifyDBModeAfterRevertSnapshot,
VerifyDBRoleAfterRevertSnapshot,
VerifyDBRoleBeforReverteSnapshot,
VerifyDBRoleBeforSnapshot,
CheckFlashBackOff,
ConvertSnapToPhysicalStandby,
VerifyDBRoleAfterSnapshot,
VerifyDBModeAfterSnapshot,
VerifyCurrentScn,
ConvertSnapshotStandby,
VerifyDBFlashBackRetentionTarget,
VerifyDBRecoveryFileDestSize,
VerifyDBRecoveryFileDest,
VerifyDataGuardStatus,
VerifyDBModeBeforSnapshot,
VerifyDBModeBeforRevertSnapshot,
DGWinVerifyMaxSequenceNumber,
DGDatabaseOpen,
DGShutDownPrimary,
DGConnector,
ExecuteSqlCommand,
WhileCheck,
DGMountStandby,
DGJobStatus,
Test_Chatbot,
TestAction_RoleandMode,
srvctl_StartInstance_OOpen,
ODG_Monitor_Action,
AlterDataBaseSwitchOver,
VerifyAlterDBSO,
srvctl_StopDatabase,
srvctl_StartDatabase,
srvctl_StopInstance,
srvctl_StartInstance_OMount,
CheckOpenMode,
AlterDBMount,
StartUpNoMount,
SwitchOverStatus,
ExecuteCheckSqlCommand,
DGSwitchStandByToPrimary,
DGSwitchPrimaryToStandBy,
DGSwitchLogFile,
DGRecoverStandBy,
DGCheckUserStatus,
DGVerifyMaxSequenceNumber,
DGVerifyDBModeAndRole,
DGSwitchLogFileCurrent,
JobStatusInPR,
RecoveryStartInNewDR,
ShutDownDR,
MountNewDRDBStandBy,
ShutDownPrimary,
VerifyArchiveLogSequence,
VerifySwitchoverStatus,
OracleRAC_Monitoring_Action,
OracleDataGuardAPI_SwitchDB,
OracleDataGuardAPICheckDBSyncStatus,
VerifyLogSequence,
SwitchShutPrimaryDB,
SwitchShutPRDBWin_DS,
SwitchShutPRDBWin,
StopOracleListener,
StopListenerWithPassword,
StartUpMountForce,
StartOracleListener,
StartListenerWithPassword,
StartDatabaseNoMount,
StartDatabaseMount,
StartDatabase,
StartDataBaseReadOnly,
StartDBStandByWin_DS,
StartDBStandByWin,
StartDBStandBy,
StartDBReadWriteWin_DS,
StartDBReadWriteWin,
StartDBReadWrite,
ShutStandByDBWin_DS,
ShutDownDataBase,
ShutDB,
RestoreStandbyControlFile,
ReplicateStandByControlFile,
Recover_Stand_By_Database_DS,
RecoverStandbyDatabaseFile,
RecoverDatabase,
PreShutRedoCtrlScript,
OpenDatabase,
KillOracleSessions,
IsCheckPointCountOne,
GenerateRedoCtrlBKPScriptWin_DS,
GenerateRedoCtrlBKPScriptWin,
FlashBackToRestorePoint,
ExecuteSqlScriptWithPassword,
ExecuteSqlScriptWithEnvPassword,
ExecuteRedoCtrlBKPWin_DS,
ExecuteRedoCtrlBKPWin,
ExecuteDBCommand,
DropRestorePoint,
Database_Switch_Log_DS,
CreateTempFileTableSpace,
CreateTempFile,
CreateStandbyControlFile,
CreateRestorePoint,
CreateControlFileScript,
CreateControlFileByScript,
CopyRedoCtrFile,
CompareTableCountPR,
CompareTableCountDR,
CheckNoLoggingOperation,
CheckFlashBackOn,
BackUpControlFile,
AlterDatabaseOpen,
ApplyIncrementalLogs,
AlterSystemSetLogArchiveDestEnable,
AlterSystemSetLogArchiveDestDefer,
AlterSystemLog,
AlterStbyDBtoMaxPerformance,
AlterDatabaseMount,
AlterDatabaseFlashBackOn,
AlterDatabaseFlashBackOff,
AlterDatabaseConvertPhysicalStby,
AlterDatabaseActiveStby,
AlterDBRecoverManagedStbyDBCancel,
ActiveDatabaseReadWrite,
ExecuteForceEnableNodeStateOfVirtualServer,
ExecuteForceDisableNodeStateOfVirtualServer,
ExecuteEnableLTMVirtualServer,
ExecuteDisableLTMVirtualServer,
CheckLTMVirtualServerState,
CheckF5LoadBalancerDefaultRoutePoolsNameExist,
CheckF5LoadBalancerNameExistandEnabled,
CheckAPITokenExpiryDate,
ExecuteMigrateF5LoadBalancerDefaultRoutePoolsNameChange,
ModifyNS1DNSRecordsATypeMultipleIPAddress,
ModifyNS1DNSRecordCNAMEType,
ModifyNS1DNSRecordATypeSingleIPAddress,
CheckNS1DNSRecordCNAMEType,
CheckNS1DNSRecordATypeSingleIPAddress,
CheckNS1DNSRecordATypeMultipleIPAddress,
INFOBLOX_QueryDNS,
INFOBLOX_ModifyDNS,
INFOBLOX_DeleteDNS,
INFOBLOX_DNSQueryStatisticsView,
INFOBLOX_AddDNS,
Delete_CName_Record,
ExecuteEnableDisableDNSRecordmapping,
CheckDNSRecordStatus,
CheckIfDNSRecordExistInViewandZone,
CheckifDSNZoneExistInDNSview,
CheckDNSViewExist,
ModifyCNameRecord,
AddCNameRecord,
CheckVlan,
CheckStaticRoute,
UpdateFirewallPolicy,
OpenShiftMonitoring,
ExecuteScaleUpDownPodsDeploymentsCount,
CheckPodDeploymentsCountwithAllReadyStatus,
ExecuteScaleUpDownPodsReplicaSetsCount,
CheckPodReplicaSetsCountwithAllReadyStatus,
ExecuteScaleUpDownPodsStatefulSetsCount,
CheckPodStatefulSetsCountwithAllReadyStatus,
ExecuteScaleUpDownMachineSetsMachinesCount,
CheckOpenShiftMachineSetMachineCount,
ExecuteVirtualMachineStop,
ExecuteVirtualMachineStart,
CheckSpecificVirtualMachineStatus,
checkpoddepployment_test,
UpdateAirGap_ActionIM,
EnablePort_ActionIM,
VerifyPortEnable_ActionIM,
VerifyPortDisable_ActionIM,
DisablePort_ActionIM,
Nexus_AddRoute,
Unmount_Volume,
Mount_Volume,
VerifyLunsStatus,
SnapMirror_Volume_Restrict,
SnapMirror_Volume_Online,
SnapMirror_Update,
SnapMirror_Resync,
SnapMirror_Check_Vol_Status,
SnapMirror_Break,
InitialiseSnapMirror,
VerifySnapMirrorStatus,
NetApp_SnapMirror_Monitor,
SyncReplicationandVerify,
SyncReplication,
DeleteMtree,
CreateMtreeReplicationPair,
CreateMtree,
CheckmtreeReplicationPairStatus,
VerifyProtectStatus,
UnprotectSite,
SwitchSite,
SplitStatus,
ProtectSite,
Isolate,
HaltCG,
EnableStar,
DisconnectSite,
DisableStar,
ConnectSite,
PPDM_Backup,
CrossvCenterVMMigration,
CheckvCenterExistOnthePPDMServerAssetSource,
RestoreVMToAlternateLocationMultiple_New,
VMInstantAccessRestore,
RestoreVMToAlternateLocation_Multiple_New,
RestoreVMToAlternateLocation_Multiple,
RestoreVMToAlternateLocation_Proxy,
RestoreVMToAlternateLocation_New,
RestoreVMToAlternateLocation,
RestoreVirtualMachineToNewLocation,
RestoreVirtualMachineToOriginalLocation,
ReplicatingAllBackupsForSpecificClient,
ReplicatingAllBackupsForSpecificDomain,
RestoreVirtualMachineFromHistoryToNewLocation,
RestoreVirtualMachineFrmHistoryToOriginalLoca,
CheckifDomainExistAfterReplication,
ReplicateBackup,
CheckifClientExists,
CheckifClientExistAfterReplication,
CheckVirtual_MachineLatestBackupImageExist,
ChkVirtualMachineLatestBackupExistAftrReplica,
CheckVMExistAndProtect_New,
CheckVMExistAndProtect,
CheckReplicationActivityStatus,
ChkLatstBakupImgExstOfTrgtVMForRstrOprtn,
CheckIfTargetVMExistForRestore,
CheckIfTargetVCenterNameExistForRestore,
CheckIfTargetExistForRestoreOperation,
CheckIfDomainsNameExist,
AddvCenterToPPDMAssetSource,
AddCredentialsToPPDMTypeVCENTER,
VMInstantAccessRestore_Multiple,
Execute_SyncCopy,
Execute_SecureCopy_Analyze,
Execute_SecureCopy,
Execute_Recovery_Check,
Execute_Policy_Application_Recovery,
Execute_Copy_Analyze,
Check_LatestCopyAvailableForPerticularPolicy,
Check_LastAnalysisStatus,
CheckRecovery_LatestSelected_CheckStatus,
EMC_SWITCHOVERDG,
EMC_SWITCHBACKDG,
EMC_SWAPPERSONALITY,
EMC_SPLITDEVICEGROUP,
EMC_SETASYNCMODE,
EMC_SETACPDEVICEMODE,
EMC_RESUMEDEVICEGROUP,
EMC_ISSPLIT,
EMC_ISDGRESENT,
EMC_ISDGCONSISTENT,
EMC_FAILOVERDEVICEGROUP,
EMC_ESTABLISHDEVICEGROUP,
EMC_ENABLEDEVICEGROUP,
EMC_DISABLEDEVICEGROUP,
EMC_CHECKDGTRACKSZERO,
DisableGroupImageAccess,
FailoverProtection,
EnableGroupImageAccess,
FailoverProtectionGroup_set,
VerifyLoggedAccess,
VerifyGroupStatistics,
VerifyGroupState,
VerifyDirectAccess,
VerifyDataTransfer,
StartGroupDataTransfer,
SetProductionCopy,
PauseGroupDataTransfer,
IsGroupSync,
SyncPolicyAllowWrites,
RunSyncJob,
ModifySyncPolicySchedule,
FailoverSyncPolicy,
DiskGlobalMirrorMonitor,
UpdateTargetStorageGroup,
SwitchOver,
PerformDataSynchronization,
PauseGlobalMirror,
IsCGFormed,
SwitchBack,
ResumeGlobalMirror,
ExecuteStorageCommand,
ExecuteDS_WaitForNoFlashCopy,
ExecuteDS_PauseGMIR,
ExecuteDS_Make_Flash,
ExecuteDS_MakeSession,
ExecuteDS_MakeGMIR,
ExecuteDS_MKFlash,
ExecuteDS_FailOverPPRC,
ExecuteDS_FailBackPPRC,
ExecuteDS_CommitFlash,
ExecuteDS_Check_NoFlashCopy,
ExecuteDS_CheckFlashZeroTracks,
ExecuteDS_ChangeSessionRemove,
ExecuteDS_ChangeSessionAdd,
ExecuteDS_ChVolGroupRemove,
ExecuteDS_ChVolGrpAdd,
ExecuteDSCommand,
ExecuteCheckDSCommand,
ExecuteDS_WaitForFlashTracksZero,
ExecuteDS_SetFlashRevertible,
ExecuteDS_RevertFlash,
ExecuteDS_ReverseFlash,
ExecuteDS_ResyncFlash,
ExecuteDS_RemoveGMIR,
ExecuteDS_RMFlash,
ExecuteDS_PausePPRC,
CheckStorageMailboxPath,
CheckDSTracks_LSPPRC,
ExecuteDS_ResumePPRC,
ExecuteDS_ResumeGMIR,
Hitachi_PairSwap,
Hitachi_Pairdisplay_DR,
Hitachi_Pairdisplay_PR,
SwapApplicationNodeReplication,
ResumeApplicationNodeReplication,
PauseApplicationNodeReplication,
ChecktheReplicationPausedState,
ChecktheReplicationState_OK,
ChecktheReplicationSwappedState,
HURMonitor,
Hitachi_Pairsplit,
Hitachi_PairReSync,
Hitachi_IsVolSuspended,
Hitachi_Horcctakeover,
HP3PAR_Storage_Monitoring,
StopRCopyGroup,
SetRCopyGroupRestore,
SetRCopyGroupFailOver,
ExecuteSyncRCopy,
CheckRCopyGroupSyncStatus,
SyncReplication_Huawei,
SwapReplicaRole,
SplitReplication,
EnableSecondaryResourceAccess_ReadWrite,
EnableSecondaryResourceAccess_ReadOnly,
CheckSecondaryResourceAccess_ReadWrite,
CheckSecondaryResourceAccess_ReadOnly,
CheckReplicationRunningStatus,
CheckLUNSplitState,
CheckConsistencyGroupTimeValue,
ChkConsistncyGroupStatusSynchronizationBgns,
CheckConsistencyGroupReplicaRole,
ChangeConsistencyGroupTimeValue,
PromotePod,
DemotePod,
ReplicationDatalag,
PodReplicationDirection,
PodPromotionStatus,
PodReplicationStatus,
UnMountFS,
StopVxVMDG,
StartVxVMDG,
MountFS,
IsVxVMDGVolumeEnable,
IsVxVMDGVolumeDisable,
IsFSUnMounted,
IsFSMounted,
ImportVxVMDG,
DeportVxVMDG,
CheckDGAllVolEnabled,
CheckDGAllVolDisabled,
RenameFolder,
RenameFile,
CopyFolder,
CopyFile,
Stop,
Start,
Resume,
Pause,
Cancle,
StopGoldenGateGroup,
StartGoldenGateGroup,
CheckGoldenGateGroupStatus_IsRunning,
CheckGoldenGateGroupRBASync,
GoldenGateReplication,
Stop_Rsync_Replication,
Execute_RsyncJob,
Verify_Sync_Status,
Verify_Connectivity,
RSyncReplication,
RsyncAppMonitor,
RSyncAppReplication,
Robocopy_Monitoring,
Robocopy_Replication,
ExecuteFailOver,
VerifyFailOver,
CheckStatusExecuteStart,
VerifyJobStart,
ExecuteFailback,
VerifyFailBack,
ExecuteRestoreJob,
VerifyJobRestoreStatus,
VerifyJobStatus,
VerifyJobReverse,
ExecuteReverse,
DTVerifyReplicationStatus,
DTVerifyReplicationQueue,
DTVerifyJobName,
DTStopJob,
DTStartJob,
DTJobRestore,
DTJobPerformFailOver,
DTJobPerformFailBack,
DTJobCheckNotIsInError,
DTJobCheckMirrorState,
DTJobCheckMirrorPermillage,
DTJobCheckMirrorBytesRemaining,
DTJobCheckIsInError,
DTJobCheckHighLevelState,
DTJobCheckHealth,
DTJobCheckDiskQueueBytes,
DTJobCheckCanStop,
DTJobCheckCanStart,
DTJobCheckCanRestore,
DTJobCheckCanFailOver,
DTJobCheckCanFailBack,
CheckStatusExecuteReverse,
ExecuteVCSResourceONLINE,
ExecuteVCSResourceOFFLINE,
CheckVCSResourceState,
VVR_CheckServiceGroupStateOfClusterNode,
VVR_CheckServiceGroupNodeShowingOffline,
VVR_CheckServiceGroupClusterNodeShowingOnline,
VVRSG_CheckIfStringValueExistInFile,
VVRSG_ExecuteBatchFile,
VVRSGExecuteONLINEAnyOneOfServerGroup,
VVRSGExecuteOFFLINEAnyOneOfServerGroup,
VVRSG_CheckStatusOfServiceGroupClusterName,
VVR_CheckLinkStateUpToDateStatus,
VVR_CheckLinkStateInReplicationStatus,
VVR_SwitchNodeStateInCluster,
VVR_GetServiceGroupStateOnSpecifiedNode,
VrfyTstRplctionHealthPasdStatsExcptDBAvlblty,
VerifyServerComponentActiveStateforallCmpnent,
VerifyReplayQueueStatusforAllMailboxDB,
VerifyPrimaryActiveManagerStateatPR,
VerifyMailboxDBMountedStatusatPR_SiteLevel,
VerifyMailboxDBMountedStatusatPR_ServerLevel,
VerifyMailboxDBHealthyStatusatDR_SiteLevel,
VerifyMailboxDBHealthyStatusatDR_ServerLevel,
VrfyDBCopyActvatnPolcySetingfrMalboxServr,
VrfyContentIndexStateasHealthyforallMailboxDB,
StopDAGatPrimarySiteWithoutConfigurationOnly,
StopDAGatPrimarySiteWithConfigurationOnly,
StartDAG,
SetDBCopyAutoActivationPolicy,
SendeReceiveConnector,
RestoreDAG,
RedirectMailboxServerMsgQueuetoOtherServer,
MovePrimaryActiveManagertoTarget,
MoveDatabaseWithoutSkip,
MoveDatabaseWithSkip,
MoveActiveMailboxDatabasewithSkipOptions,
Mount_DataBase,
ExchDAGVerifyWitnessServerAndPathConfigured,
ExchDAGVerifyAltrnteWitnesServerAdDirectory,
ExchDAGSetWitnessServerAndDirectory,
ExchDAGSetAlternateWitnessServerAndDirectory,
EnableSendConnector,
EnableMailboxDatabaseCircularLogging,
DismountDatabase,
DisableSendConnector,
DisableMailboxDatabaseCircularLogging,
CheckTransportSeverQueueStatus,
CheckSiteLevelMailboxAllServerStatusunderDAG,
ChkMailboxdatabaseBackupFullIncrementalStatus,
CheckMailboxServerMessageQueueCount,
CheckMailboxDatabaseCopyStatus,
CheckDatabaseCopyAutoActivationPolicy,
CheckAllMailboxDBMountedStatus,
VerifyPassiveMBXReplayQueueLength,
VerifyPassiveMBXDBCopyStatus,
VerifyPassiveMBXDBCopyErrorMessage,
VerifyPassiveMBXDBCopyContent,
VerifyPassiveMBXCopyQueueLength,
VerifyDAGStatusOnline,
VerifyActiveMBXDBCopyStatus,
VerifyActiveMBXDBCopyErrorMessage,
VerifyActiveMBXDBCopyContent,
SuspendWhenReadyToCompleteModeStatus,
SuspendWhenReadyToCompleteMode,
StoppedMailBoxServers,
StartedMailBoxServers,
ResumeMoveMailBoxReq,
RemoveUserMailBoxMoveReqIfExist,
MoveActiveMailboxDatabase,
GetMoveReqStatus,
GetMoveReqStatics,
CheckUserMoveReqCanBeCreated,
CheckUserMailBoxExist,
SetSCRPrerequisite,
SCRStatusWithCopyQueueLength,
ResumeSCR,
MoveMailboxConfiguration,
MountDatabase,
GetMailboxListCountBeforeSwitch,
GetMailboxListCount,
EnableSCR,
DismountMailboxdatabase,
DisableSCRRestorelogs,
CreateStorageGroup,
CompareNewSGMailboxPath,
ChecktargetDBStatus,
CheckTargetDBFileStatus,
AllowFileRestoreToMailboxDB,
VerifyDAGParameterValue,
StopDAGOnMailBoxServer,
StopDAGActiveDirSiteConfigOnly,
StartDAGOnMailBoxServer,
SetPreferredADServer,
SetDatabaseAvailabilityGroup,
RestoreDAGActiveDirSite,
MoveClusterGroup,
Move_ActiveMailBoxDatabase,
CheckMailBoxDBStatusParameterValue,
VerifyReplicationServiceonPAM,
VerifyMailboxServerEntries,
StopDatabaseAvailabilityGroupOnMailboxServer,
StopDatabaseAvailabilityGroup,
SetPublicFolderDatabase,
SetDatabaseCopyAutoActivationPolicyBlocked,
SetDatabaseCopyAutoActivationPolicy,
RestoreDatabaseAvailabilityGroup,
MoveMailboxDatabaseToDRServer,
MoveDefaultofflineAddressBook,
Execute_PowerShellCommand,
ExchDAG_VerifyStarteddMailBoxServer,
VerifyCurntSiteNameAssociatedWthMailbxServr,
ExchDAG_VerifyCheckandStopclusterService,
ExchDAG_StopDAGAtPRMailboxServer,
ExchDAG_StartDAGAtPRMailboxServer,
ExchDAG_SetDAGToSeedALLChanges,
ResumeMailBoxDatabaseCopy,
MountMailboxDatabaseActivePRMailboxServer,
ExchDAG_CheckReplicationHealthonMailboxServer,
CheckPrimaryAlternateFileShareWitnessInUse,
CheckReplicationHealthStatus,
CheckMailboxServerRoleServicesStatus,
CheckHealthyMailboxDatabaseDetails,
CheckDatabaseMountedStatus,
CheckDAGMembershipStatus,
StopIISWebsite,
StopApplicationPool,
StartIISWebsite,
StartApplicationPool,
HitHTTPUrl,
HitCheckHTTPUrl,
ExecuteCheckRestAPICommand,
NoRelication,
DRReady,
DROperationType,
WaitForWorkflowAction,
WaitForParallelAction,
DisplayAlert,
Stop_WorkflowRTO,
Start_WorkflowRTO,
CyberRecoveryExecuteCopyAnalyze,
CyberRecoveryExecuteSecureCopy,
PPRExecuteRecoveryApplicationPPDM,
CR_ChkiflatestcopyAvailableparticularpolcy,
CyberRecovery_CheckStatus,
CyberRecoveryExecuteSecureCopyAnalyze,
PPDM_ExecuteProtectedSingleVM,
PPDM_VMCopyRestoreToAlternetLocationVer1,
PPDMCheckifMSSQLDBBackupcopyexistVer1_2,
PPDM_1CheckVMexistprotectedVer4,
PPDMExecuteMultipleUnProtectedVMBackup,
PPDMExecuteSingleUnProtectedVMBackup,
PPDMRstorVMtoAltrntlocwtlatstBackupcopy,
PPDMRstrMSSQLDBTOALTRNATEMltplDBtoDfultVer1_1,
DELLEMCPPDMMSSQLBRestorMultipleDBVer1_1,
PPDMExecuteProtectedMultipleVM,
RemoveProbeHealthfromLoadBalancer,
RemoveLoadBalancerRuleFromLoadBalancer,
RmveLodBalancrInboudNATRuleFromLoadBalancer,
RemoveLoadBalancerFromVM,
AddProbeHealthToLoadBalancer,
AddLoadBalancerToVM,
AddLoadBalancerRuleToLoadBalancer,
AddLoadBalancerInboundNATRuleToLoadBalancer,
ExecuteReverseReplicationAzureToOnPrim,
ExecuteReprotectOnPrimToAzure,
ExecuteReprotectOnPremVMwareToAzure,
ExecuteReprotectAzureToOnPrim,
ExecutePlannedFailoverCommitOnPrimToAzure,
ExecutePlannedFailoverAzureToOnPrim,
ExecuteCommitFailover,
CheckReplicatedVMProtectionState,
CheckAllowedOperationAzureToPrim,
TCL_Cloud_VM_Instance_PowerON,
TCL_Cloud_VM_Instance_PowerOFF,
Check_TCL_Cloud_VM_InstanceStatus,
Rackware_StartWaveinRMMServer,
Rackware_ExecuteWaveFailOver,
Rackware_ExecuteWaveFailBack,
Rackware_CheckWaveStatus,
Rackware_CheckDRPolicyStatus,
HostSyncWhenTargetExistWithoutWait,
HostSyncWhenTargetExistWithWait,
CrteWavWtHstSetOCIAutoprovsonparamtrs,
CreateHostSyncAutoTrgtntAvlStaticIPWthoutWait,
CreateHostSyncAutoTargetntAvlStatcIPWthWait,
CreateHostSyncAutoTargetntAvlDynmcIPWithoutWt,
OracleCloudVMInstanceAction,
DeattachALLNSGsfromInstanceVNIC,
CreateNSGandAddSecurityRule,
CheckifNSGAttachedtoInstanceVNIC,
CheckOracleCloudVMInstanceStatus,
AddNewNSGtoInstanceVNICReplaceExistingNSGs,
AddNewNSGtoInstanceVNICAppendtoExistingNSGs,
SoftLayer_UpgradeVirtualMachineMemory,
SoftLayer_UpgradeVirtualMachineCPU,
SoftLayer_UpgradeVirtualMachineByIds,
SoftLayer_UpgradeVirtualGuest,
SoftLayer_ProvisionVirtualMachineByIds,
SoftLayer_ProvisionVirtualGuest,
SoftLayer_PowerONVirtualGuest,
SoftLayer_PowerOFFVirtualGuest,
SoftLayer_CheckVirtualGuestPowerON,
SoftLayer_CheckVirtualGuestPowerOFF,
RemoveRoutingRule,
RmvePathbsdRulfrmBckendPoolRul,
ChckListenerwithAssociatedRule,
ChkApplictionGatwyRulPathexist,
ChkApplcationGatwyOpratnlState,
AddRoutingRule,
AddListenerHTTPType,
AdAppltnGtewyBckendPoolRulPath,
OCI_DeleteDNSRecordAType,
OCI_CheckifDNSRecordExistAType,
OCI_CheckifDNSRecordDoesNotExistAType,
OCI_CheckExistingTTLValueDNSRecordAType,
OCI_AddDNSRecordAType,
AzureMySQLExecuteServiceStopStart,
AzureMySQLCheckServiceStartStopStatus,
ExecutePromoteMySQLReplicaServer,
ExecuteCreateMySQLReplicaServer,
ExecuteDeleteMySQLStandaloneServer,
ExecuteDeleteMySQLReplicationReplicaServer,
AzureMysqlMonitoring,
ExecuteDeleteMySQLReplicationSourceServer,
CheckMySQLServerAvailableStatus,
CheckAzureMySQLServerRole,
CheckAzureMySQLServerExistintheRegion,
AzurePostgreSQLExecuteServiceStopStart,
AzurePostgreSQLCheckServiceStopStart,
AzurePostgreSqlMonitoring,
StpVrtulMchneScaleAllInstances,
StpSpcficVrtulMchnSclStInstnce,
StopMySqlReplication,
StrtVrtulMchneScaleAlInstances,
StartSpcfcVrtulMchnScleStInstn,
RemoveSecurityRuletoNSG,
ExecuteUnplannedFailover,
ExctUnplandFailOvrOnPrmToAzure,
ExecuteRe_Protect,
ExecutePlannedFailover,
ExecutePlannedFailOverAzure,
ExecuteForceFailOverAzure,
ExecuteFailoverAzureCosmosDB,
ExecuteCommitUnplannedFailover,
EnableDisableATMEndpoint,
DissociateNSGtoVM,
DissociateNSGtoNetwrkInterface,
DeleteReplicationSourceServer,
DeleteReplicationReplicaServer,
CreateReplicationAZMySql,
ChkVrtualMachineScaleSetStatus,
ChkUnplandFailovrCompltdStatus,
ChkUnplandFailovrComitedStatus,
CkSpcfcVrtulMchneScleSetStatus,
CheckRe_ProtectProtectionState,
CheckRe_Protect,
CheckPublicIPAddress,
CheckPlannedFailoverStatus,
CheckNSGNameExist,
CheckNSGNameAssociatetoVM,
CheckEnableDisableATMEndpoint,
CheckCommitFailOver,
CheckAzureWriteLocationCosmos,
CheckAzureSQLDBRole,
CheckAzureReplicationState,
CheckAzureReadLocationCosmos,
ChkAzDBActProvsningStatsCosmos,
CheckAllowedOperation,
ChkAZMySQLSrvrAvailableStatus,
CheckAZMySQLRole,
Azure_TestFailOver,
Azure_CleanUpFailOver,
AssociatePublicIPAddress,
AssociateNSGtoVMSpcfyNtwrkCard,
AssociateNSGtoVMDfaltNtwrkCard,
AssociateNSGtoNetworkInterface,
AsignNSGtoVMWthRplaceExstngNSG,
AddSecurityRuletoNSG,
VCDVerifyStatus,
VCDPowerOn,
VCDPowerOff,
StopAzureVM,
StartAzureVM,
CheckAzureVMStatus,
CheckAzureVMPowerState,
ChangeAzureVMSize,
Azure_MSSQLPass_Monitoring,
ExecuteSQLDBForcedFailover,
ExecuteSQLDBPlannedFailover,
CheckAZReplicationState,
CheckAZSQLDBPaaSRole,
CheckStorageReplicationDataLagWithInput,
SetStorageAccountReplication,
ExecuteStorageAccountFailover,
CheckStorageAccountType,
Azurestoragereplication_Monitoring,
ExecuteCommitFailoverASR,
ExecuteUnPlannedFailoverASR,
ExecuteReprotectASR,
ExecuteTestFailoverCleanUpASR,
CheckASRTestFailoverStatus,
ExecuteTestFailover,
CheckASRTestFailoverCleanupStatus,
CheckUnplannedFailoverStatus,
CheckCommitFailoverStatus,
CheckASRReProtectStatus,
CheckASRVMProtectionStatus,
RubrikMountVirtualMachine,
MigrateDataStoreAfterMount,
Stop_EC2_Instance,
Start_EC2_Instance,
Modify_EC2_Instance_Size,
Is_EC2_Instance_UP,
Is_EC2_Instance_Down,
Check_EC2_Instance_State,
Upload_To_S3_Bucket,
Upload_Files_To_S3_Bucket,
Download_From_S3_Bucket,
Download_Files_From_S3_Bucket,
CyberRecoveryExecuteCopyAnalyze,
CyberRecoveryExecuteSecureCopy,
PPRExecuteRecoveryApplicationPPDM,
CR_ChkiflatestcopyAvailableparticularpolcy,
CyberRecovery_CheckStatus,
CyberRecoveryExecuteSecureCopyAnalyze,
PPDM_ExecuteProtectedSingleVM,
PPDM_VMCopyRestoreToAlternetLocationVer1,
PPDMCheckifMSSQLDBBackupcopyexistVer1_2,
PPDM_1CheckVMexistprotectedVer4,
PPDMExecuteMultipleUnProtectedVMBackup,
PPDMExecuteSingleUnProtectedVMBackup,
PPDMRstorVMtoAltrntlocwtlatstBackupcopy,
PPDMRstrMSSQLDBTOALTRNATEMltplDBtoDfultVer1_1,
DELLEMCPPDMMSSQLBRestorMultipleDBVer1_1,
PPDMExecuteProtectedMultipleVM,
VBVMWareReplicaFailOverPlanned,
VBRVMwareReplicaUndoFailOver,
VBRVMWareReplicaUndoFailback,
VBRVMWareReplicaFailOverPermanent,
VBRVMWareReplicaFailOverDROnly,
VBRVMWareReplicaFAILBACKToOriginalVM,
FailOverReplicationJob,
FailBackReplicationJob,
FailBackCommitReplicationJob,
CreateReplicationJobUseProdVMState,
CreateReplicationJobFromBackupFiles,
CheckReplicationJobLastState,
CheckReplicationJobLastResult,
CheckReplicationJob,
CheckReplicaMonitoringStatus,
eBDROperations,
UpdateAnAsset,
UnmanageAnAsset,
RemoveAssetInMaintenanceMode,
RefreshAnAsset,
RebootAsset,
PutAssetInMaintenanceMode,
PowerOnAsset,
PowerOffAsset,
ManageAnAsset,
ExecutePlan,
ExecuteOpsCenterCommand,
ExecuteCheckOpsCenterCommand,
HPPowerONBladeServer,
HPPowerOFFBladeServer,
HPCheckPowerStatusOn,
HPCheckPowerStatusOff,
PowerOnOffStatus,
PowerOn,
PowerOff,
MSSQLlinkstatus,
ExecuteTxnQueCheck,
ExecuteStatusNodeCheck,
Execute_SAF_COUNT_CHECK,
ExecuteDRNetCheckStatusState,
ExecuteDRNetCheckRecords,
ExcuteDRNetCheckDistributorFileMapStatusQueue,
ExecuteDRNetCheckCollectorFileMapMode,
ExecuteDRNetCheckAuditModeStatus,
ExecuteBase24Command,
ExecuteBase24CheckCommand,