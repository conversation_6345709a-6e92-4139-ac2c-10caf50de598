ExecuteCheckPowerShellCommand,
VerifyInstalledSSH,
UIAutomationWithPassword,
UIAutomation,
UACDisable,
TestSRMConnectivity,
StopMSCluster,
Start_ScheduledTask_Job,
StartMSCluster,
KillUnixProcess,
IsClusterOnline,
IsClusterOffline,
InstallOpenSSH_New,
InstallOpenSSH,
FolderCompare,
FileCompare,
ExecuteScriptWithUserPassword,
ExecuteScriptWithPassword,
ExecuteScriptWithEnvPassword,
ExecuteScriptAlongPassword,
ExecuteScript,
ExecuteSSHSingleSession,
ExecuteOSZFSCommand,
ExecuteOSCommandWithPassword,
ExecuteLocalProcess,
ExecuteCheckOSCommandWithPassword,
CompareFileORFolder,
ClusterRemoveDependency,
ClusterAddDependency,
ChangeNodeIP,
ApplicationServiceMonitor,
Add_User_Administrator_Group,
ExecutePowerShellCommand,
EnableFolderTargetReferralNamespace,
DisableFolderTargetReferralNamespace,
CheckFolderTargetReferralNamespaceStatus,
Wait,
winkillProcessByName,
winIsFileExist,
WinRenameFile,
WinChangeFileText,
WaitForPing,
StopScheduledTask,
StartScheduledTask,
ShutdownRemoteServer,
ShutDownServer,
SCP,
ReplicateStandByCtrlFile,
ReplicateDataSyncFoldersWin,
ReplicateDataSyncFileWin,
ReplaceTxtInFile,
ReplaceFile,
RmvNTAUTHORITYSystmAcntFmDNSRcrdWithAlwOrDny,
RmveCmptrAcuntFrmDNSRcdWthAloOrDnyPrmsson,
RebootRemoteServer,
R_Replication,
ModifyDNSRecordValueATypewithoutTTL,
ModifyDNSRecordValueATypewithTTL,
MSSQLServerInstalled_SSMS_Install,
MSSQLServerInstalled,
ExecuteWindowsProcess,
ExecuteSymcliCommand,
ExecuteSSH,
ExecuteOSCmd,
ExecuteCheckSymcliCommand,
ExecuteBatchFile,
EnableScheduledTask,
DisableScheduledTask,
DeleteDNS,
DataSyncReplicateFile,
DataSyncRedoArchiveFolder,
DataSyncFolders,
DataSyncArchiveFolder,
DNSServerSetTTLValueforDNSSOA,
CheckStringValueExistInFile,
CheckServiceStatus,
CheckScheduledTaskStatus,
CheckFileExist,
CheckDNSRecordwithoutTTLValueAType,
CheckDNSRecordwithTTLvalueAType,
ChangeServiceStartMode,
ChangeDNS,
BatchFileExecution,
ApplicationStop,
ApplicationStart,
AddnewrecordTxtFile,
AddNTAUTHORITYSystmAcuntToDNSRcrdWthDenyPrmsn,
AddNTAUTHORITYSystmAcuntTDNSRcrdWthAlowPrmsn,
AddDNS,
AddCmptrAccountToDNSRecodWthDenyPermission,
AddCmptrAccountToDNSRecdWthAllowPrmsion,
WinIsServiceRunning,
WinIsServiceDown,
StopWindowsService,
StopService,
StartService,
StartWindowsService,
EnableNetscalerLoadBalancingServer,
DisableNetScalerLoadBalancing,
CheckNetScalerLoadBalancerStatus,
VerifyDNSCNAMERecord,
TransferOrSeizureFSMORole,
RemoveSPNFromActiveDirectoryComputer,
ModifyCNAMEwithTTL,
ModifyArecordwithTTL,
DCServer,
DNSVerify,
DNSModify,
CheckActiveDirectoryReplicationSyncAllStatus,
CheckActiveDirectoryReplicationStatus,
CheckADForestFSMORole,
CheckADDomainFSMORole,
ChangeDNSGlueRecord,
AddSPNToActiveDirectoryComputer,
ActiveDirectoryAddUser,
AD_CheckReplicationSyncAllStatus,
ADReplicationStatus,
Win2k8VerifyClusterResourceState,
Win2k8StopClusterResource,
Win2k8StartClusterResource,
OnLine,
OffLine,
Import,
DiskRescan,
DiskOnlinewithDriveLetter,
CheckDiskStatus,
MakeDiskReadWrite,
MakeDiskOnline,
MakeDiskOffline,
CheckDriveLetterafterDiskOnline,
CheckDiskDetailsUsingLUN_WWN_Number,
ChangeDiskDriveLetter,
Execute5250TransactionCountCheckPopup,
Execute5250Process,
AS400ExecuteMonitor,
CheckClientLPARStatus,
CheckClientBootDiskMapping,
ActivateClientLPAR,
WaitForLPARState,
ShutDownClientLPAR,
CheckEthernetStatus,
VaryOnVolumeGroups,
VaryOffVolumeGroups,
UnmountVolumeGroups,
UnmountVGS,
UnmountVG,
UnmountNFSVolume,
ReplicateStandByTraceFile,
ReplicateDataSyncFoldersPosix,
ReplicateDataSyncFoldersDR,
ReplicateDataSyncFilePosix,
RemoveHDisk,
RMDEVSVolumeGroups,
MountVolumeGroups,
MountVGS,
MountVG,
MountNFSVolume,
KillProcess,
HPUnmountVGS,
HPUnmountVGParallel,
HPUXVGChangeDeactive,
HPUXVGChangeActive,
HPUXUnmount,
HPUXMount,
HPUXIsVGActive,
HPUXImportVGToMapFile,
HPUXExportVGToMapFile,
HPMountVGS,
HPMountVGParallel,
ExportVolumeGroups,
ExecuteNohup,
ExecuteCPSL,
Enable_SLB_Real_Services,
Disable_SLB_Real_Services,
Check_SLB_Real_ServiceName_Status,
AIXMount,
AIXDisMount,
AIXChangeFileText,
ExecuteCPL,
ExecuteOSCommand,
ExecuteCheckOSCommand,
ReplicateRSyncFoldersPosix,
ReplicateRSyncFilePosix,
Ora_Rsync_ReplicateStandByControlFile,
Ora_Rsync_ReplicateStandByTraceFile,
ExecuteCheckXCLICommand,
VerifystatusofVM,
Verify_FailedOverWaitingCompletion_Status,
VerifyState,
VerifyReverseRepliConnectionconfig,
VerifyReplicationMode,
VerifyReplicationState,
VerifyPreparedForFailoverstatus,
VerifyHyperVVMVLANIDConnectedStatus,
VerifyHyperVVMDisConnectedStatus,
VerifyHyperVVMConnectedStatus,
VerifyForwardRepliConnectionconfig,
StartVM,
StartFailover,
SingleNetClsterHyprVVMIPAddressWthMacAddress,
ShutdownVM,
SetVLANIDToVirtualSwitchOnHyperVVM,
HyperVResumeReplication,
FailsOverReplicaVM,
DisconnectionToHyperVVM,
ConnectionToVirtualSwitchOnHyperVVM,
Cluster_VerifyPrimaryReplicationMode,
ChangeReplicationmode,
ChangeDNSIP,
ChngeClsterHyprVVMIPAddressWthOutMacAddress,
ChangeClusterHyperVVMIPAddressWithMacAddress,
Cancel_FailOver_Cluster_HyperVVM,
SingleNetClsterHyprVVMIPAddressWthuyMacAdres,
HyperV_Monitoring_Action,
RmoveClstrResorceDpndncyFrmDpndntResorce,
AddClusterResourceDependencyToDpndentResorce,
VerifyClusterResourceDpndncyInDependentRsorce,
RemoveClusterResourceFromClusterOwnerGroup,
AddClusterResourceToClusterOwnerGroup,
VerifyDiskStatusOnClusterSharedVolume,
VerifyClstrSharedVolumeDiskinFailOverClstr,
RemoveVirtualSharingHardDiskDuplicateVM,
RemoveVirtualSharingHardDisk,
EnableVirtualHardDiskSharingOptnDuplicateVM,
EnableVirtualHardDiskSharingOption,
DisableVirtualHardDiskSharingOptnDuplcteVM,
DisableVirtualHardDiskSharingOption,
ChkVirtualHardDskSharingOptnStatusDuplcteVM,
CheckVirtualHardDiskSharingOptionStatus,
AddNewVirtualHardDiskDuplicateVM,
AddNewVirtualHardDisk,
HyperV_Monitoring,
VerifyPowerOffStatusOfReplicaPR,
VerifyRunningStatusOfPrimaryDR,
StartPrimaryHyperVVMDR,
VerifyReplicaReplicationModePR,
VerifyHyperVReplicationStatePR,
VerifyPrimaryReplicationMode,
VerifyHyperVReplicationState,
RepModeToPrimaryRepAndStartRep,
VerifyFailOverWaitComStatusRep,
FailsOverReplicaOnTargetHostDR,
VerifyPreparedFailoverStatusPR,
StartFailoverPrimaryRepPending,
ShutdownPrimaryHyperVOff,
ShutdownPrimaryHyperV,
VerifyReverseRepConnectionConfigurationDR,
VerifyForwardRepConnectionConfigurationPR,
VerifyPrimaryReplicationModePR,
VerifyReplicaReplicationModeDR,
VerifyHyperVReplicationStateDR,
VirtualSwitchVerifyExistStatus,
VerifyReverseReplicationConnection,
VerifyForwardReplicationConnection,
VerifyFailedOverWaitingCompletionStatus,
VMNetworkAdapter_Verify_Status,
VMNetworkAdapterVerifyVLANIDStatus,
StopVM,
StartVMFailOver,
SingleNetHyperVVMIPAddressWithOutMacAddress,
SingleNetHyperVVMIPAddressWithMacAddress,
SetVMReverseReplication,
SetVLANIDToVirtualSwitch,
SetNetworkAdapterConnection,
PrepareVMFailOver,
ExecuteCheckHyperVCommand,
DisconnectNetworkAdapterConnection,
CheckVMState,
CheckVMReplicationState,
CheckVMReplicationMode,
CheckHyperVReplication,
ChangeHyperVVMIPAddressWithOutMacAddress,
ChangeHyperVVMIPAddressWithMacAddress,
Cancel_FailOver_Standalone_HyperVVM,
VerifyTargetLocalAvlabltyZoneforLeapRcvryPln,
VerifySourceFailedAvlbltyZoneforLeapRcvryPln,
VerifyNutanixLeapRecoveryPlanName,
Nutanix_AssignIPAddressToNetworkInterfaceCard,
NutanixLeapRecoveryPlnValdteRcvryPlnTrgtSte,
NutanixLeapRecoveryPlnUnplandFailovrTrgtSite,
NutanixLeapRecoveryPlanPlanedFailovrTargtSite,
ChkRecoveryPointEntityVMdsnotexstAtTargtLclAZ,
ChkRecoveryPointEntityVMexistAtSourceFailedAZ,
CheckIfNetworkAdapterExistUsingNICIPAddress,
CheckIfNetworkAdapterExstUsingMACAddress,
AssignDNSIPAddressToWindowsServerrandomly,
AssignDNSIPAddressToWndwsSrvrUsingMACAddress,
AssignDNSIPAddressToWndwServrUsigIPAddress,
AddNetworkInterfaceCardToVM,
VerifyIfNoReplIsPendingUnderPDForInactiveSite,
VerifyIfNoReplIsPendingUnderPDForActiveSite,
MigrateProtectionDomain,
ExecuteVMPowerOn,
ExecuteVMPowerOff,
Check_VM_State,
CheckVMExistInCGUnderPDForInactiveSite,
CheckVMExistInCGUnderPDForActiveSite,
CheckProtectionDomainStatus,
CheckCGExistInPDForInactiveSite,
CheckCGExistInPDForActiveSite,
UnmountDataStore,
UnmounFromAllEsxiHost,
UnRegisterVM,
UnRegisterDATASTORE,
StopVSphereHAAgentService,
StartVSphereHAAgentService,
RescanHBAToFindNewStorageLUN,
RegisterVMWithoutESXiHost,
RegisterVM,
ReScanVMFSforStorageLUN,
PowerOnVM,
PowerOnDATASTORE,
PowerOffVM,
PowerOffDATASTORE,
MountDatastoreOnAllEsxiHost,
MountDataStore,
DeAttachLUN,
CheckDataStoreCanBeUnmount,
AttachLunOnMultipleESXiHost,
AttachLUN,
vAPP_UpdateFailoverNetworkSettings,
vAPP_SetReverseReplication,
vAPP_PowerOn,
vAPP_PowerOff,
vAPP_ExecuteFailOver,
vAPP_Delete,
vAPP_CheckReplicationState,
vAPP_CheckRecoveryState,
vAPP_CheckOverallHealth,
vAPP_CheckFailoverNetworkSettingsSrcTgt,
vAPP_CheckFailoverNetworkSettings,
VCAV_ExecutevAPPSync,
RecoverPointforVMExecuteRecoverProduction,
RecoverPointforVMRecoveryActivitiesRecoverProduction,
RecoverPointforVMTestCopyForRecoverProductionStopActivity,
RecoverPointforVMTestCopyForRecoverProduction,
RecoverPointforVMExecuteFailoverRoleChange,
RecoverPointforVMRecoveryActivitiesFailoverRoleChange,
RecoverPointforVMTestCopyForFailoverStopActivity,
RecoverPointforVMTestCopyForFailover,
RecoverPointforVMTestCopyStopActivity,
RecoverPointforVMTestCopy,
CheckConsistencyGroupTransferStatusActive,
CheckConsistencyGroupRecoveryActivitiesStatus,
CheckConsistencyGroupProdReplicavRPAclusterDetails,
VirtualizationRPforVMReplication_Monitor,
ExecuteVPGRollbackBeforeMoveCommit,
ExecuteVPGMove_CommitPolicyNONE,
ExecuteVPGMoveCommitwithoutReverseProtection,
ExecuteVPGMoveCommitwithReverseProtection,
ExecuteVPGFailoverwithoutCommitAndSDSourceVM,
CheckVPGStateVolumeInitialSync,
CheckVPGStateMovingBeforeCommit,
ExecuteVPGVPGFailoverCommitwithReverseProtection,
ExecuteVPGSTOPFAILOVERTEST,
ExecuteVPGRollbackBeforeFailoverCommit,
CheckVPGProtectionStsMeetingSLAandStateNone,
ExecuteVPGFailoverwithoutCommit,
ExecuteVPGFAILOVERTEST,
CheckVPGStateDeltaSyncing,
CheckVPGRecoverySiteDR,
CheckVPGProtectionStateFailingOverRollingBack,
CheckVPGProtectionStateFailingOverBeforeCom,
CheckVPGProtectionStatusFailingover,
RP4VM_FailoverUsingPredefinedNetwork,
RP4VM_EnableLatestImage,
RP4VM_StartReplicaTransfer,
AddAdditionalDisk,
VerifyVolumeMount,
VerifyUnmountStatus,
VerifyStorageLun,
VerifyMountStatus,
VerifyDisk,
VerifyDeviceAssociatedToDisk,
VerifyAttachStatus,
VcenterVMPowerOn,
VcenterVMPowerOff,
VcenterVMCheckRunning,
VcenterRemoveVM,
VcenterRemoveSnapshot,
VcenterProvisionVM,
VcenterPowerOnVM,
VcenterPowerOffVM,
VcenterExecuteVMCommand,
VcenterCreateLinkedClone,
VcenterCheckVMToolStatus,
VcenterCheckVMPowerState,
VcenterCheckVMExist,
VMUnmount,
VMUnRegister,
VMRemoveSnapShot,
VMRegisterMachine,
VMMount,
VMIsAvailable,
VMCreateSnapShot,
VMCheckRunning,
UpdateWindowsHostName,
UpdateVMNetworkInfo,
RescanAllAdaptors,
ReplicateVirtualMachine,
RemoveVM,
RemoveLunFromESXI,
RemoveGuestVMSystemFromDomain,
RemoveAdditionalDisk,
PowerOnMachine,
PowerOffMachine,
NepAppVMUnmount,
MountUsingVMFSUUID,
JoinGuestVMSystemToDomain,
IsMachineRunning,
ExecuteVmWarePowerCLICommand,
ExecutePowerCLIScript,
ExecuteNCheOpVmWarePowerCLICommand,
ExecuteCheckVMCommand,
DetachLunFromESXI,
CreateNewVm,
CreateLinkedClone,
CheckVMToolStatus,
CheckVMExist,
ChangeGuestVMHostName,
AttachLuns,
SRMReProtectRecoveryPlan,
SRMPerformPlannedMigrationFailover,
SRMPerformDisasterRecoveryFailover,
SRMInitiateRecoveryPlan,
SRMExecuteTestRecoveryPlan,
SRMExecuteCleanupRecoveryPlan,
SRMCheckRecoveryPlanState,
SRMCheckProtectionGroupState,
ReProductRecoveryPlan,
CheckRecoveryPlanState,
SRM_Monitor_Action,
VerifyTargetLocalAvlabltyZoneforLeapRcvryPln,
VerifySourceFailedAvlbltyZoneforLeapRcvryPln,
VerifyNutanixLeapRecoveryPlanName,
Nutanix_AssignIPAddressToNetworkInterfaceCard,
NutanixLeapRecoveryPlnValdteRcvryPlnTrgtSte,
NutanixLeapRecoveryPlnUnplandFailovrTrgtSite,
NutanixLeapRecoveryPlanPlanedFailovrTargtSite,
ChkRecoveryPointEntityVMdsnotexstAtTargtLclAZ,
ChkRecoveryPointEntityVMexistAtSourceFailedAZ,
CheckIfNetworkAdapterExistUsingNICIPAddress,
CheckIfNetworkAdapterExstUsingMACAddress,
AssignDNSIPAddressToWindowsServerrandomly,
AssignDNSIPAddressToWndwsSrvrUsingMACAddress,
AssignDNSIPAddressToWndwServrUsigIPAddress,
AddNetworkInterfaceCardToVM,
VerifyIfNoReplIsPendingUnderPDForInactiveSite,
VerifyIfNoReplIsPendingUnderPDForActiveSite,
MigrateProtectionDomain,
ExecuteVMPowerOn,
ExecuteVMPowerOff,
Check_VM_State,
CheckVMExistInCGUnderPDForInactiveSite,
CheckVMExistInCGUnderPDForActiveSite,
CheckProtectionDomainStatus,
CheckCGExistInPDForInactiveSite,
CheckCGExistInPDForActiveSite,
VrfyIfNoReplicationPendingForPDInActiveSite,
VerifyIfNoReplicationPendingForPD_ActiveSite,
ProtectionDomainActivationFailoverFromDR,
ChkVMExistInConsistencyGrpUndrPDInActveSite,
ChkVMExistInConsistencyGrpUndrPDActveSte,
CheckProtectionDomainStatusForSite,
ChkConsistncyGrpMembrStatusUndrPDInActveSte,
ChkConsistencyGrpMembrStatsUndrPDActvSte,
AcknowledgenResolveRcntInformativeAlrtForPD,
VerifyDiskStatusOnClusterSharedVolume,
VerifyClstrSharedVolumeDiskinFailOverClstr,
RemoveVirtualSharingHardDiskDuplicateVM,
RemoveVirtualSharingHardDisk,
EnableVirtualHardDiskSharingOptnDuplicateVM,
EnableVirtualHardDiskSharingOption,
DisableVirtualHardDiskSharingOptnDuplcteVM,
DisableVirtualHardDiskSharingOption,
ChkVirtualHardDskSharingOptnStatusDuplcteVM,
CheckVirtualHardDiskSharingOptionStatus,
AddNewVirtualHardDiskDuplicateVM,
AddNewVirtualHardDisk,
HyperV_Monitoring,
VerifyPowerOffStatusOfReplicaPR,
VerifyRunningStatusOfPrimaryDR,
StartPrimaryHyperVVMDR,
VerifyReplicaReplicationModePR,
VerifyHyperVReplicationStatePR,
VerifyPrimaryReplicationMode,
VerifyHyperVReplicationState,
RepModeToPrimaryRepAndStartRep,
VerifyFailOverWaitComStatusRep,
FailsOverReplicaOnTargetHostDR,
VerifyPreparedFailoverStatusPR,
StartFailoverPrimaryRepPending,
ShutdownPrimaryHyperVOff,
ShutdownPrimaryHyperV,
VerifyReverseRepConnectionConfigurationDR,
VerifyForwardRepConnectionConfigurationPR,
VerifyPrimaryReplicationModePR,
VerifyReplicaReplicationModeDR,
VerifyHyperVReplicationStateDR,
VirtualSwitchVerifyExistStatus,
VerifyReverseReplicationConnection,
VerifyForwardReplicationConnection,
VerifyFailedOverWaitingCompletionStatus,
VMNetworkAdapter_Verify_Status,
VMNetworkAdapterVerifyVLANIDStatus,
StopVM,
StartVMFailOver,
SingleNetHyperVVMIPAddressWithOutMacAddress,
SingleNetHyperVVMIPAddressWithMacAddress,
SetVMReverseReplication,
SetVLANIDToVirtualSwitch,
SetNetworkAdapterConnection,
PrepareVMFailOver,
ExecuteCheckHyperVCommand,
DisconnectNetworkAdapterConnection,
CheckVMState,
CheckVMReplicationState,
CheckVMReplicationMode,
CheckHyperVReplication,
ChangeHyperVVMIPAddressWithOutMacAddress,
ChangeHyperVVMIPAddressWithMacAddress,
Cancel_FailOver_Standalone_HyperVVM,
ShutDownZone,
DetachZone,
CheckZoneUp,
CheckZoneStatus,
CheckZoneDown,
BootZone,
AttachZone,
VerifyLDOMPowerON,
VerifyLDOMPowerOFF,
LDOMUnbindDomain,
LDOMStopDomain,
LDOMStartDomain,
LDOMRemoveDomain,
LDOMBindDomain,
LDOMADDMemory,
LDOMADDDomain,
LDOMADDCPU,
AIXLPARPOWERON,
AIXLPARPOWEROFF,
AIXLPARCHECKPOWERON,
AIXLPARCHECKPOWEROFF,
