{"App": ["StopIISWebsite", "CheckApplicationGatewayRulePathexist", "AddApplicationGatewayBackendPoolRulePath", "CheckApplicationGatewayRulePathexist", "RemovePathbasedRulefromBackendPoolRule", "RemovePathbasedRulefromBackendPoolRule", "CheckListenerwithAssociatedRule", "AddListenerHTTPType", "CheckListenerwithAssociatedRule", "AddRoutingRule", "RemoveRoutingRule", "CheckApplicationGatewayOperationalState"], "Database": ["VerifyDataGuardStatus", "CheckAzureWriteLocationCosmos", "CheckAzureReadLocationCosmos", "CheckAzureDBAccountProvisioningStatusCosmos", "ExecuteFailoverAzureCosmosDB"], "Network": ["ModifyNS1DNSRecordsATypeMultipleIPAddress"]}