# import requests
#
# api_key = "***************************************************"
# agent_id = "0TPD5bYDLlxqV1NQh8B5"
# document_id = "qzYf6SLzOjIikC7nL0cr"
#
# url = f"https://api.elevenlabs.io/v1/convai/agents/{agent_id}/knowledge-base"
#
# headers = {
#     "accept": "application/json",
#     "content-type": "application/json",
#     "xi-api-key": api_key
# }
#
# payload = {
#     "document_ids": [document_id]
# }
#
# response = requests.post(url, json=payload, headers=headers)
#
# if response.status_code == 200:
#     print("✅ Document successfully attached to the agent.")
# else:
#     print(f"❌ Failed to attach document. Status: {response.status_code} - {response.text}")


from elevenlabs import ElevenLabs

client = ElevenLabs(api_key="***************************************************")

response = client.conversational_ai.knowledge_base.documents.get_content(
    documentation_id="qzYf6SLzOjIikC7nL0cr"
)

print(response)

