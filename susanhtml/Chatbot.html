<!doctype html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="short/cut icon" href="img/Assets/Chat-Logo.svg" />
    <title>Susan.Ai</title>

    <!-- 5.0 Bootstrap CSS -->
    <link href="library/bootstrap/css/bootstrap.min.css" rel="stylesheet" />

    <!--Table Css-->
    <!-- <link href="library/dataTables/css/dataTables.bootstrap5.min.css" rel="stylesheet"> -->

    <!--Font Css-->
    <link href="fonts/San-Francisco-Pro/SFPro-Font.css" rel="stylesheet" />
    <link href="fonts/Cp-icons/cp-icon.css" rel="stylesheet" />

    <!--Custom Css-->
    <link href="css/Style.css" rel="stylesheet" />
    <link href="css/chatbot.css" rel="stylesheet" />
    <link href="css/form.css" rel="stylesheet" />
    <link href="css/Theme.css" rel="stylesheet" />
   <style>
    .card-title {
        font-size: 14px;
    }

    .call-record-card h6 {
        font-size: 13px
    }

    .circle {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        vertical-align: middle;
        margin-right: 2px
    }

    .form-check-input {
        font-size: 15px;
    }

    .knowledgebase .form-check-input:checked {
        background-color: #47cf73;
        border-color: #47cf73;
    }

    .modal.right .modal-dialog {
        position: fixed;
        right: 0;
        top: 0;
        margin: 0;
        height: 100%;
    }

    .modal.right .modal-content {
        height: 100%;
        overflow-y: auto;
        border-radius: 0;
    }

   
</style>
</head>

<body>
    <div class="container-fluid px-0">
        <header class="sticky-top bg-glass">
            <nav class="navbar">
                <div class="container-fluid">
                    <div class="brand-logo img-fuild" style="margin-left: 12px;"><img src="img/Assets/Header-Logo.svg"
                            class="img-fluid">
                    </div>

                    <ul class="nav justify-content-end gap-3 align-items-center">
                        <!-- <li class="nav-item"><img role="button" src="img/Assets/Header-Message.svg" class="img-fluid " -->
                                <!-- width="20"> -->
                        <!-- </li> -->
                        <!-- <li class="nav-item"><img role="button" src="img/Assets/Notification.svg" class="img-fluid " -->
                                <!-- width="20"></li> -->
                        <!-- <li class="nav-item"><img role="button" src="img/Assets/Header-History.svg" class="img-fluid " -->
                                <!-- width="20"></li> -->
                        <!-- <li class="nav-item vr"></li> -->
                        <li class="nav-item"><img role="button" src="img/Logo/Perpetuutit-Logo.svg" class="img-fluid ">
                        </li>

                    </ul>
                </div>
            </nav>
        </header>
        <div class="Layout-Container">

            <div class="Body-Content">
                <div class="me-3">
                    <div class="row">
                        <div class="col-2">
                            <div class="card card-glass z-3 card-bg-gradient">
                                <div class="card-body mt-3 pt-1 py-2 chatbot-chat-history"
                                    style="height: calc(100vh - 228px); overflow: auto;">
                                    <div>
                                        <div class="mt-2 mb-4">
                                            <div class="text-center">
                                                <img src="img/Logo/Profile.svg" class="img-fluid rounded-circle mb-2"
                                                    width="60" />
                                                <!-- <h6>Mohammed Sabeel</h6> -->
                                                <h6 class="loginName">Admin</h6>
                                            </div>
                                        </div>
                                        <div class="sidebar-nav">
                                            <ul class="nav flex-column gap-1">
                                                <li class="nav-item  aiChatNavbar button-bos" navstatus="aiChat">
                                                    <a href="#" class="active"><img role="button"
                                                            src="img/Assets/Chat-Ai.svg" class="img-fluid" id="aiChat" width="20">
                                                        <span>AI Chat</span></a>
                                                </li>
												<li class="nav-item aiChatNavbar" navstatus="aiConversational">
                                               <a href="#" class="">
                                            <img role="button"
 src="img/Icon-Final/Conversational_Ai.svg" class="img-fluid" id="aiConversational" width="20">
        <span>Conversational AI </span>
    </a>
</li>
<li class="nav-item aiChatNavbar" navstatus="aiTracking">
    <a href="#" class="">
        <img role="button"
             src="img/call-track-icons/Call_Tracking_Black.svg" class="img-fluid" id="aiTracking" width="20">
        <span>Call Tracking </span>
    </a>
</li>
												
												
                                                <!-- <li class="nav-item aiChatNavbar button-bos" navstatus="aiExam"> -->
                                                    <!-- <a href="#"><img role="button" src="img/Assets/Dashboard.svg" -->
                                                            <!-- class="img-fluid" width="20" id="aiExam"> -->
                                                        <!-- <span>BCM Champ</span></a> -->
                                                <!-- </li> -->
                                                <!-- <li class="nav-item"> -->
                                                    <!-- <a href="#"><img role="button" src="img/Assets/Settings.svg" -->
                                                            <!-- class="img-fluid" width="20"> -->
                                                        <!-- <span>Settings</span></a> -->
                                                <!-- </li> -->
                                                <!-- <li class="nav-item"> -->
                                                    <!-- <a href="#"><img role="button" src="img/Assets/Help.svg" -->
                                                            <!-- class="img-fluid" width="20"> -->
                                                        <!-- <span>Help</span></a> -->
                                                <!-- </li> -->
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer text-center">
                                    <div class="py-3 button-bos" role="button" id="logout">
                                        <a style="text-decoration:none" class="text-dark">
                                            <img src="img/Assets/Sign-Out.svg" class="img-fluid mb-2" width="30">
                                            <p class="mb-0 fw-semibold fs-6">Signout</p>
                                        </a>   
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col px-0">
                            <div class="row g-2" id="SusanAIChat">


                                <div class="col">
                                    <div class="card card-glass " style="background-color: #FCFCFD;height:calc(100vh - 115px);">
                                        <div class="card-body mt-3 pt-1 py-2 chatbot-body"
                                             id="chatbot-message">
                                            <div class="welcome-message p-2" id=" welcome-message">
                                                <div>
                                                    <div class="text-center chat-welcome-title">

                                                        <img src="img/Assets/Chat-Top-Logo.svg"
                                                            class="img-fluid mb-2" />

                                                        <h3 class="text-priamry-gradient">Welcome to Susan.Ai</h3>
														 <h5 class="text-priamry-gradient bcmStatus d-none"></h5>
                                                        <p class="fs-7 mb-0 text-secondary ">Transform your ideas with CV
                                                            Ai
                                                            chatbot, designed to deliver endless possibilities and
                                                            intelligent
                                                            solutions</p>

                                                    </div>
                                                    <div class="mt-3 BcmCard">
                                                        <div class="row  g-lg-5 g-sm-2">
                                                            <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 ">
                                                                <div class="card card-no-design card-bg-gradient h-100 shadow-sm button-bos">
                                                                    <div class="card-body">
                                                                        <div
                                                                            class="d-flex justify-content-start align-items-start py-3">
                                                                            <span
                                                                                class="p-2 shadow-sm  rounded-4 bg-white"><img
                                                                                    class="img-fluid"
                                                                                    src="img/Assets/Ai-Assistant.svg"
                                                                                    width="40" /></span>
                                                                        </div>
                                                                        <h6 class="bcmtext">BCM Consultant</h6>
                                                                        <p class="text-secondary mb-0">Assign Susan to analyze and intake your Organaization Details
                                                                        </p>
                                                                    </div>
                                                                    <div class="card-footer text-end">
                                                                        <img src="img/Assets/Card-Chat-Send.svg"
                                                                            class="img-fluid consultantStatus " role="button" status="BCMConsultant"/>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 ">
                                                                <div class="card card-no-design card-bg-gradient h-100 shadow-sm button-bos">
                                                                    <div class="card-body">
                                                                        <div
                                                                            class="d-flex justify-content-start align-items-start py-3">
                                                                            <span
                                                                                class="p-2 shadow-sm rounded-4  bg-white"><img
                                                                                    class="img-fluid"
                                                                                    src="img/Assets/BCM-Consultant.svg"
                                                                                    width="40" /></span>
                                                                        </div>
                                                                        <h6 class="bcmtext">BCM Assistant</h6>
                                                                        <p class="text-secondary">Get Susan as assistant to make your BCM search easier</p>
                                                                    </div>
                                                                    <div class="card-footer text-end">
                                                                        <img src="img/Assets/Card-Chat-Send.svg"
                                                                            class="img-fluid consultantStatus" role="button" status="BCMAssistant"/>
                                                                    </div>
                                                                </div>
                                                            </div>


                                                        </div>
                                                    </div>
													<div class="mt-3 Bcmbutton d-none">
      <label role="button" class="btn btn-primary my-3 UploadFile mx-auto button-bos"  style="width:fit-content;">
    <i class="cp-upload fs-6 me-1 align-middle"></i>Upload
    <input type="file" class="d-none import" id="import" />
    </label>
     <div class="p-2 mb-0 rounded-3 border border-light-primary">
    <div class="d-flex">
    <div class="w-100 text-start fw-semibold px-2">
    <span id="ExcelName"></span>
    </div>
    <div class="flex-shrink-1 d-flex gap-2 w-25 justify-content-end px-2">
    <div class="fw-medium fs-14" id="AITextFlow"></div>
    <div class="spinner-border spinner-border-sm d-none chatTextLoader" role="status">
    <span class="sr-only"></span>
    </div>
    <i class="cp-Delete fs-6 text-danger d-none" role="button"></i>
    </div>
    </div>
    <div class="d-flex">
    <div class="p-2 pb-1 w-100">
    <div class="progress" role="progressbar" aria-label="Animated striped example" style="height:8px;" id="progress-container">
    <div role="progressbar" class="progress-bar progress-bar-striped progress-bar-animated" id="progress-bar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%;background-color: #6383FE;">
    <span class="visually-hidden">60%</span>
    </div>
    </div>
    </div>
    <div class="flex-shrink-1 fw-semibold progressbarTotal px-2">0%</div>
    </div>
    </div>
 </div>
													
                                                </div>

                                            </div>
                                            <div>
                                                <div class="message-container">
                                                    <div class="d-none">

                                                        <!-- User Message -->
                                                        <div>
                                                            <div class="user-side">
                                                                <div>
                                                                    <img src="img/Icon-Final/Profile.svg"
                                                                        class="img-fluid" width="30">
                                                                </div>
                                                                <div class="auto-w message user">
                                                                    <div>
                                                                        <p class="chat-question">Susan, there is heavy
                                                                            rain and
                                                                            flooding in California. What should I do
                                                                            now?

                                                                        </p>
                                                                    </div>
                                                                    <span class="chat-time "><small>12:24
                                                                            AM</small></span>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div >
                                                            <div class="admin-side">
                                                                <div>
                                                                    <img src="img/Assets/Chat-Logo.svg"
                                                                        class="img-fluid" width="30">
                                                                </div>
                                                                <div class="auto-w">
                                                                    <div class="message admin">
                                                                        <div>
                                                                            <p class="chat-question">Based on the
                                                                                business
                                                                                continuity plan you provided for
                                                                                flooding in
                                                                                California, the
                                                                                recommended actions are:
                                                                            </p>
                                                                            <p>1. If California is impacted, then work
                                                                                can be
                                                                                managed by Arizona office or California
                                                                                employees can work from home.</p>
                                                                            <p>2. Similarly, if Arizona is impacted,
                                                                                then the
                                                                                California office can manage work or
                                                                                California
                                                                                employees can work from home</p>
                                                                            <p>Therefore, depending on the location of
                                                                                your
                                                                                business and the extent of flooding, you
                                                                                should
                                                                                consider implementing either remote work
                                                                                arrangements or outsourcing to a backup
                                                                                location
                                                                                in order to maintain business continuity
                                                                                during
                                                                                this crisis situation.</p>
                                                                        </div>
                                                                        <div
                                                                            class="d-flex align-items-center justify-content-end gap-1">
                                                                            <span class="chat-time"><small>12:25
                                                                                    AM</small></span>
                                                                            <span><img src="img/Icon-Final/Dislike.svg"
                                                                                    class="img-fluid" /></span>
                                                                            <span><img src="img/Icon-Final/Like.svg"
                                                                                    class="img-fluid" /></span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="chatbot-suggestion-btn">
                                                                        <button class="btn btn-suggestion"><img
                                                                                class="img-fluid pe-1"
                                                                                src="img/Assets/Report-Black.svg" />Reports</button>
                                                                        <button class="btn btn-suggestion"><img
                                                                                class="img-fluid pe-1"
                                                                                src="img/Assets/Regenarate.svg" />Regenrate</button>
                                                                        <button class="btn btn-suggestion"><img
                                                                                class="img-fluid pe-1"
                                                                                src="img/Assets/Know-More.svg" />Know
                                                                            More</button>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </div>
                                                        <div class="user-side">
                                                            <div>
                                                                <img src="img/Icon-Final/Profile.svg" class="img-fluid"
                                                                    width="30">
                                                            </div>
                                                            <div class="auto-w message user">
                                                                <div>
                                                                    <p class="chat-question">Hi Emo, Reports
                                                                    </p>
                                                                </div>
                                                                <span class="chat-time "><small>12:27 AM</small></span>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-footer chatFooter d-none chatend">
                                            <div>
                                                <div
                                                    class="d-flex gap-2 align-items-center flex-column chat-input-border">

                                                    <div class="chat-input-container">

                                                       <textarea  id="user-message"  class="chat-input" style="height:0px"
                                                            placeholder="Can you help me with a _" autocomplete="off" disabled></textarea>

                                                         <div class="d-flex gap-2 align-content-center justify-content-end chatField ms-auto" style="max-width:85px;width:85px">
      <img src="img/assets/Text-Chat-Send.svg" title="Send" class="img-fluid button-bos"
           id="send-button" width="25" role="button" />
      <span class="backField">
          <img src="img/assets/Back.svg" title="Back" class="img-fluid button-bos"
               width="20" role="button" />
      </span>

      <span class="uploadDocument" role="button">
          <label>
              <img src="img/assets/pin.svg"
                   class="img-fluid uploadPin button-bos" title="Upload" width="20" />
              <input type="file" class="d-none importCommon" data-status="uploadDocument" id="importCommon" />
          </label>

      </span>
  </div>
                                                    </div>

                                                   
                                                </div>

                                            </div>

                                        </div>
                                    </div>
                                </div>
                               
                            </div>
<div class="row g-2 d-none" id="conversationalAI">

    <div class="col  px-lg-0 px-xl-0 px-md-0 px-sm-2">

        <div class="card card-glass " style="background-color: #FCFCFD; height:calc(100vh - 100px);">

            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-2 col-sm-12 border-end">
                        <div class="conversion-ai-tabs p-2 d-flex flex-column gap-5">
                            <div class="card-header p-0"><h5 class="page-Title">Conversational AI</h5></div>
                            <div class="d-flex align-items-start">
                                <div class="nav flex-column nav-pills me-3 gap-3" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                     <!-- <button class="nav-link active" id="v-pills-Agents-tab" data-bs-toggle="pill" data-bs-target="#v-pills-Agents" type="button" role="tab" aria-controls="v-pills-Agents" aria-selected="true">Agents</button>  -->
                                    <button class="nav-link" id="v-pills-Knowledge-tab" data-bs-toggle="pill" data-bs-target="#v-pills-Knowledge" type="button" role="tab" aria-controls="v-pills-Knowledge" aria-selected="false">Knowledge Base</button>
                                       <!-- <button class="nav-link" id="v-pills-Phone-tab" data-bs-toggle="pill" data-bs-target="#v-pills-Phone" type="button" role="tab" aria-controls="v-pills-Phone" aria-selected="false">Phone Numbers</button> -->
                                    <!-- <button class="nav-link" id="v-pills-History-tab" data-bs-toggle="pill" data-bs-target="#v-pills-History" type="button" role="tab" aria-controls="v-pills-History" aria-selected="false">Call History</button> -->
                                    <!-- <button class="nav-link" id="v-pills-Conversational-tab" data-bs-toggle="pill" data-bs-target="#v-pills-Conversational" type="button" role="tab" aria-controls="v-pills-Conversational" aria-selected="false">Conversational Setiings</button>  -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col" style="height: calc(100vh - 140px);overflow:auto">
                        <div class="px-2">
                            <div class="tab-content" id="v-pills-tabContent">
                                <div class="tab-pane fade show active" id="v-pills-Agents" role="tabpanel" aria-labelledby="v-pills-Agents-tab" tabindex="0">
                                    <div id="blank" class="d-none">
                                        <div class="row">
                                            <div class="col-md-5 col-lg-5 col-xl-5 col-xxl-3 col-sm-12">
                                                <div class="d-flex align-items-center gap-3 mb-2">
                                                    <h6 class="mb-0">AI Agents</h6>
                                                    <button class="btn btn-sm btn-outline-secondary">Playground</button>
                                                    <button class="btn btn-sm btn-primary">Add Agent</button>
                                                </div>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <input type="search" class="form-control" placeholder="Search agents name or ID" />
                                                    </div>
                                                </div>
                                            </div>
                                            <hr />

                                            <div class="col-12">
                                                <div class="row row-cols-lg-3 row-cols-md-3 row-cols-xl-3 row-cols-xxl-4 g-4">
                                                    <div class="col">
                                                        <div class="card conversion-card">
                                                            <div class="card-body">
                                                                  <!-- <div class="mb-3"> -->
                                                                    <!-- <img src="img/agents/blank-template.png" class="img-fluid" width="49" height="49" /> -->
                                                                <!-- </div>  -->
                                                                <h6 class="card-title">Blank Template</h6>
                                                                <p class="card-text">
                                                                    Start with a blank template
                                                                    and customize your agent
                                                                    to suit your needs.
                                                                </p>
                                                            </div>
                                                            <div class="card-footer d-flex justify-content-between align-items-center conversion-footer">
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="card conversion-card">
                                                            <div class="card-body">
                                                                  <!-- <div class="mb-3"> -->
                                                                    <!-- <img src="img/agents/Support-Agent.png" class="img-fluid" width="49" height="49" /> -->
                                                                <!-- </div>  -->
                                                                <h6 class="card-title">Support Agent</h6>
                                                                <p class="card-text">
                                                                    Start with a blank template
                                                                    and customize your agent
                                                                    to suit your needs.
                                                                </p>
                                                            </div>
                                                            <div class="card-footer d-flex justify-content-between align-items-center conversion-footer">
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="card conversion-card">
                                                            <div class="card-body">
                                                                   <!-- <div class="mb-3"> -->
                                                                    <!-- <img src="img/agents/Travel-Guide.png" class="img-fluid" width="49" height="49" /> -->
                                                                <!-- </div> -->
                                                                <h6 class="card-title">Travel Guide</h6>
                                                                <p class="card-text">
                                                                    Start with a blank template
                                                                    and customize your agent
                                                                    to suit your needs.
                                                                </p>
                                                            </div>
                                                            <div class="card-footer d-flex justify-content-between align-items-center conversion-footer">
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="card conversion-card">
                                                            <div class="card-body">
                                                                <!-- <div class="mb-3"> -->
                                                                    <!-- <img src="img/agents/Finance-Agent.png" class="img-fluid" width="49" height="49" /> -->
                                                                <!-- </div> -->
                                                                <h6 class="card-title">Finance Agent</h6>
                                                                <p class="card-text">
                                                                    Start with a blank template
                                                                    and customize your agent
                                                                    to suit your needs.
                                                                </p>
                                                            </div>
                                                            <div class="card-footer d-flex justify-content-between align-items-center conversion-footer">
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>



                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-none">
                                        <div class="row">
                                            <div class="col-md-5 col-lg-5 col-xl-5 col-xxl-3 col-sm-12">
                                                <div class="d-flex align-items-center gap-3 mb-2">
                                                    <h6 class="mb-0">AI Agents</h6>
                                                    <button class="btn btn-sm btn-outline-secondary">Playground</button>
                                                    <button class="btn btn-sm btn-primary">Add Agent</button>
                                                </div>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <input type="search" class="form-control" placeholder="Search agents name or ID" />
                                                    </div>
                                                </div>


                                                <div class="d-flex flex-column gap-3 align-items-center">
                                                    <div class="card conversion-card text-start">
                                                        <div class="card-body">
                                                            <div class="mb-3 d-flex justify-content-between align-items-center">
                                                                    <!-- <div class="d-flex align-items-center gap-2"> -->
                                                                    <!-- <img src="img/agents/blank-template.png" class="img-fluid" width="15" height="15" /> -->
                                                                    <!-- <h6 class="card-title mb-0">Blank Template</h6> -->
                                                                <!-- </div>  -->
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                            </div>
                                                            <p class="card-text">
                                                                Start with a blank template
                                                                and customize your agent
                                                                to suit your needs.
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div class="card conversion-card text-start">
                                                        <div class="card-body">
                                                            <div class="mb-3 d-flex justify-content-between align-items-center">
                                                                  <!-- <div class="d-flex align-items-center gap-2"> -->
                                                                    <!-- <img src="img/agents/Support-Agent.png" class="img-fluid" width="15" height="15" /> -->
                                                                    <!-- <h6 class="card-title mb-0">Support Agent</h6> -->
                                                                <!-- </div>  -->
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                            </div>
                                                            <p class="card-text">
                                                                Start with a blank template
                                                                and customize your agent
                                                                to suit your needs.
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div class="card conversion-card text-start">
                                                        <div class="card-body">
                                                            <div class="mb-3 d-flex justify-content-between align-items-center">
                                                                  <!-- <div class="d-flex align-items-center gap-2"> -->
                                                                    <!-- <img src="img/agents/Travel-Guide.png" class="img-fluid" width="15" height="15" /> -->
                                                                    <!-- <h6 class="card-title mb-0">Travel Guide</h6> -->
                                                                <!-- </div>  -->
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                            </div>
                                                            <p class="card-text">
                                                                Start with a blank template
                                                                and customize your agent
                                                                to suit your needs.
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div class="card conversion-card text-start">
                                                        <div class="card-body">
                                                            <div class="mb-3 d-flex justify-content-between align-items-center">
                                                                  <!-- <div class="d-flex align-items-center gap-2"> -->
                                                                    <!-- <img src="img/agents/Finance-Agent.png" class="img-fluid" width="15" height="15" /> -->
                                                                    <!-- <h6 class="card-title mb-0">Finance Agent</h6> -->
                                                                <!-- </div>  -->
                                                                <div class="mt-auto d-inline-flex gap-2 rounded-pill align-items-center p-1 pe-2 bg-gray">
                                                                    <img src="https://storage.googleapis.com/eleven-public-cdn/images/voices/purple-cyan.jpg" class="rounded-pill" style="width:.875rem;height:.875rem">
                                                                    <p class="mb-0">Any</p>
                                                                </div>
                                                            </div>
                                                            <p class="card-text">
                                                                Start with a blank template
                                                                and customize your agent
                                                                to suit your needs.
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="col">
                                                <div class="mb-2">
                                                    <h6 class="mb-1">Support Agent</h6>
                                                    <p class="mb-1 text-muted">ID 0002345</p>
                                                    <div class="conversion-ai-tabs">
                                                        <ul class="nav nav-pills mb-3 gap-3" id="pills-tab" role="tablist">
                                                            <li class="nav-item" role="presentation">
                                                                <button class="nav-link active" id="pills-agentsconfigure-tab" data-bs-toggle="pill" data-bs-target="#pills-agentsconfigure" type="button" role="tab" aria-controls="pills-agentsconfigure" aria-selected="true">Agents</button>
                                                            </li>
                                                            <li class="nav-item" role="presentation">
                                                                <button class="nav-link" id="pills-Voice-tab" data-bs-toggle="pill" data-bs-target="#pills-Voice" type="button" role="tab" aria-controls="pills-Voice" aria-selected="false">Voice</button>
                                                            </li>
                                                            <li class="nav-item" role="presentation">
                                                                <button class="nav-link" id="pills-Analyisis-tab" data-bs-toggle="pill" data-bs-target="#pills-Analyisis" type="button" role="tab" aria-controls="pills-Analyisis" aria-selected="false">Analyisis</button>
                                                            </li>
                                                            <li class="nav-item" role="presentation">
                                                                <button class="nav-link" id="pills-Secuirty-tab" data-bs-toggle="pill" data-bs-target="#pills-Secuirty" type="button" role="tab" aria-controls="pills-Secuirty" aria-selected="false">Secuirty</button>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="">
                                                    <div class="tab-content" id="pills-tabContents">
                                                        <div class="tab-pane fade show active" id="pills-agentsconfigure" role="tabpanel" aria-labelledby="pills-agentsconfigure-tab" tabindex="0">

                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Agent Language</h6>
                                                                            <p>Choose the default language the agent will commuincate in,</p>
                                                                        </div>
                                                                        <div>
                                                                            <select class="form-select border" aria-label="Large select example">
                                                                                <option value="1">English</option>
                                                                                <option value="2">Tamil</option>
                                                                                <option value="3">Hindi</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Additional Languages</h6>
                                                                            <p>Specify additional languages which callers can choose from.</p>
                                                                        </div>
                                                                        <div>
                                                                            <select class="form-select border" aria-label="Large select example">
                                                                                <option value="1">English</option>
                                                                                <option value="2">Tamil</option>
                                                                                <option value="3">Hindi</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div>
                                                                        <h6 class="card-title">First message</h6>
                                                                        <p>The first message the agent will say. If empty, the agent will wait for the user to start the conversation.</p>
                                                                    </div>
                                                                    <div>
                                                                        <textarea class="form-control" placeholder="e.g. Hello, how can I help you today?"></textarea>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div>
                                                                        <h6 class="card-title">System prompt</h6>
                                                                        <p>The system prompt is used to determine the persona of the agent and the context of the conversation.</p>
                                                                    </div>
                                                                    <div>
                                                                        <textarea class="form-control" placeholder="e.g. You are a German language teacher named Laura."></textarea>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div>
                                                                        <h6 class="card-title">Dynamic Variables</h6>
                                                                        <p>Variables like {{user_name}} in your prompts and first message will be replaced with actual values when the conversation starts. <a href="#">Learn more</a></p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">LLM</h6>
                                                                            <p>
                                                                                Select which provider and model to use for the LLM.
                                                                                If your chosen LLM is not available at the moment or something goes wrong, we will redirect the conversation to another LLM.
                                                                                Currently, the LLM cost is covered by us. In the future, this cost will be passed onto you.
                                                                            </p>
                                                                        </div>
                                                                        <div>
                                                                            <select class="form-select border" aria-label="Large select example">
                                                                                <option value="1">Chatgpt</option>
                                                                                <option value="2">Gemini</option>
                                                                                <option value="3">Deepseek</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div>
                                                                        <h6 class="card-title">Temperature</h6>
                                                                        <p>Temperature is a parameter that controls the creativity or randomness of the responses generated by the LLM.</p>
                                                                    </div>
                                                                    <div>

                                                                        <input type="range" class="form-range" id="customRange1">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between gap-3">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Limit token usage</h6>
                                                                            <p>
                                                                                Configure the maximum number of tokens that the LLM can predict. A limit will be applied if the value is greater than 0.
                                                                            </p>
                                                                        </div>
                                                                        <div>
                                                                            <input class="form-control" type="number" placeholder="0" />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between gap-3 mb-3">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Knowledge base</h6>
                                                                            <p>
                                                                                Provide the LLM with domain-specific information to help it answer questions more accurately.
                                                                            </p>
                                                                        </div>
                                                                        <div>
                                                                            <input class="form-control" type="file" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="d-flex align-items-center justify-content-between gap-3">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Use RAG</h6>
                                                                            <p>
                                                                                Retrieval-Augmented Generation (RAG) increases the agent's maximum Knowledge Base size. The agent will have access to relevant pieces of attached Knowledge Base during answer generation.
                                                                            </p>
                                                                        </div>
                                                                        <div>
                                                                            <div class="form-check form-switch">
                                                                                <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault">
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between gap-3">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Tools</h6>
                                                                            <p>
                                                                                Provide the agent with tools it can use to help users.
                                                                            </p>
                                                                        </div>
                                                                        <div>
                                                                            <div class="dropdown">
                                                                                <button class="btn btn-primary btn-sm" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                    Add Tool
                                                                                </button>
                                                                                <ul class="dropdown-menu shadow-sm">
                                                                                    <li><a class="dropdown-item" href="#">Custom Tool</a></li>
                                                                                    <li><a class="dropdown-item" href="#">Language Detection</a></li>

                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between gap-3">
                                                                        <div class="w-50">
                                                                            <h6 class="card-title">Workspace Secrets</h6>
                                                                            <p>
                                                                                Create and manage secure secrets that can be accessed across your workspace
                                                                            </p>
                                                                        </div>

                                                                        <button class="btn btn-primary btn-sm" type="button">
                                                                            Add secret
                                                                        </button>

                                                                    </div>

                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="tab-pane fade" id="pills-Voice" role="tabpanel" aria-labelledby="pills-Voice-tab" tabindex="0">

                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Voice</h6>
                                                                            <p>Select the ElevenLabs voice you want to use for the agent.</p>
                                                                        </div>
                                                                        <div>
                                                                            <select class="form-select border" aria-label="Large select example">
                                                                                <option value="1">Eric</option>
                                                                                <option value="2">River</option>
                                                                                <option value="3">Ram</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Use Flash</h6>
                                                                            <p>Flash is our new recommended model for low latency use cases. For more comparison between Turbo and Flash, <a href="#">refer here</a>.</p>
                                                                            <p>Your agent will use Turbo v2.</p>
                                                                        </div>
                                                                        <div>
                                                                            <div class="form-check form-switch">
                                                                                <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault">
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">TTS output format</h6>
                                                                            <p>Select the output format you want to use for ElevenLabs text to speech.</p>
                                                                        </div>
                                                                        <div>
                                                                            <select class="form-select border" aria-label="Large select example">
                                                                                <option value="1">PCM 8000 Hz</option>
                                                                                <option value="2">PCM 16000 Hz</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Pronunciation Dictionaries</h6>
                                                                            <p>Lexicon dictionary files will apply pronunciation replacements to agent responses. Currently, the phoneme function of the pronunciation dictionaries only works with the Turbo v2 model, while the alias function works with all models.</p>
                                                                        </div>
                                                                        <!-- <div> -->
                                                                            <!-- <label for="dictionary"></label> -->
                                                                        <!-- </div>  -->
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div>
                                                                        <h6 class="card-title">Optimize streaming latency</h6>
                                                                        <p>Configure latency optimizations for the speech generation. Latency can be optimized at the cost of quality.</p>
                                                                    </div>
                                                                    <div>

                                                                        <input type="range" class="form-range" id="customRange1">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div>
                                                                        <h6 class="card-title">Stability</h6>
                                                                        <p>Higher values will make speech more consistent, but it can also make it sound monotone. Lower values will make speech sound more expressive, but may lead to instabilities.</p>
                                                                    </div>
                                                                    <div>

                                                                        <input type="range" class="form-range" id="customRange1">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div>
                                                                        <h6 class="card-title">Speed</h6>
                                                                        <p>Controls the speed of the generated speech. Values below 1.0 will slow down the speech, while values above 1.0 will speed it up. Extreme values may affect the quality of the generated speech.</p>
                                                                    </div>
                                                                    <div>

                                                                        <input type="range" class="form-range" id="customRange1">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div>
                                                                        <h6 class="card-title">Similarity</h6>
                                                                        <p>Higher values will boost the overall clarity and consistency of the voice. Very high values may lead to artifacts. Adjusting this value to find the right balance is recommended.</p>
                                                                    </div>
                                                                    <div>

                                                                        <input type="range" class="form-range " id="customRange1">
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>
                                                        <div class="tab-pane fade" id="pills-Analyisis" role="tabpanel" aria-labelledby="pills-Analyisis-tab" tabindex="0">
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between gap-3">
                                                                        <div class="w-50">
                                                                            <h6 class="card-title">Evaluation criteria</h6>
                                                                            <p>
                                                                                Define custom criteria to evaluate conversations against. You can find the evaluation results for each conversation in the history tab.
                                                                            </p>
                                                                        </div>

                                                                        <button class="btn btn-primary btn-sm" type="button">
                                                                            Add criteria
                                                                        </button>

                                                                    </div>

                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between gap-3">
                                                                        <div class="w-50">
                                                                            <h6 class="card-title">Data collection</h6>
                                                                            <p>
                                                                                Define custom data specifications to extract from conversation transcripts. You can find the evaluation results for each conversation in the history tab.
                                                                            </p>
                                                                        </div>

                                                                        <button class="btn btn-primary btn-sm" type="button">
                                                                            Add Item
                                                                        </button>

                                                                    </div>

                                                                </div>
                                                            </div>

                                                        </div>
                                                        <div class="tab-pane fade" id="pills-Secuirty" role="tabpanel" aria-labelledby="pills-Secuirty-tab" tabindex="0">
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Enable authentication</h6>
                                                                            <p>Require users to authenticate before connecting to the agent.</p>
                                                                        </div>
                                                                        <div>
                                                                            <div class="form-check form-switch">
                                                                                <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault">
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Allowlist</h6>
                                                                            <p>Specify the hosts that will be allowed to connect to this agent.</p>
                                                                        </div>
                                                                        <div>
                                                                            <button class="btn btn-primary btn-sm" type="button">
                                                                                Add host
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <h6 class="card-title">Enable overrides</h6>
                                                                    <p>Choose which parts of the config can be overridden by the client at the start of the conversation.</p>
                                                                    <ul class="list-group">
                                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                            <label class="form-lable" for="Agentlanguage">Agent language</label>
                                                                            <div class="form-check form-switch">
                                                                                <input class="form-check-input" type="checkbox" role="switch" id="Agentlanguage">
                                                                            </div>
                                                                        </li>
                                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                            <label class="form-lable" for="Firstmessage">First message</label>
                                                                            <div class="form-check form-switch">
                                                                                <input class="form-check-input" type="checkbox" role="switch" id="Firstmessage">
                                                                            </div>
                                                                        </li>
                                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                            <label class="form-lable" for="SystemPrompt">System Prompt</label>
                                                                            <div class="form-check form-switch">
                                                                                <input class="form-check-input" type="checkbox" role="switch" id="SystemPrompt">
                                                                            </div>
                                                                        </li>
                                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                            <label class="form-lable" for="Voice">Voice</label>
                                                                            <div class="form-check form-switch">
                                                                                <input class="form-check-input" type="checkbox" role="switch" id="Voice">
                                                                            </div>
                                                                        </li>
                                                                    </ul>

                                                                </div>
                                                            </div>
                                                            <div class="card conversion-card bg-gray-card">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between">
                                                                        <div class="w-75">
                                                                            <h6 class="card-title">Fetch initiation client data from webhook</h6>
                                                                            <p>If enabled, the conversation initiation client data will be fetched from the webhook defined in the settings when receiving Twilio calls.</p>
                                                                        </div>
                                                                        <div>
                                                                            <div class="form-check form-switch">
                                                                                <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault">
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                        </div>


                                    </div>
                                </div>
                                <div class="tab-pane fade" id="v-pills-Knowledge" role="tabpanel" aria-labelledby="v-pills-Knowledge-tab" tabindex="0">

                                    <div>
                                        <h6 class="mb-1">Support Agent</h6>
                                        <p>Configure Workspace wide settings for Conversational AI</p>
                                    </div>
                                    <div class="d-flex gap-2 my-4">
                                         <!-- <button type="button" class="knowleadge-btns" data-bs-toggle="modal" data-bs-target="#URLModal"> -->
                                            <!-- <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe w-4 h-4"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg> -->
                                            <!-- Add URL -->
                                        <!-- </button> -->
                                        <button type="button" class="knowleadge-btns" data-bs-toggle="modal" data-bs-target="#FileModal">
                                            <img class="img-fluid pe-1"
                                                 src="img/assets/Add_Files.svg" style="width:35px" />
                                            Add Files
                                        </button>
                                           <!-- <button type="button" class="knowleadge-btns" data-bs-toggle="modal" data-bs-target="#TextModal"> -->
                                            <!-- <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe w-4 h-4"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg> -->
                                            <!-- Create Text -->
                                        <!-- </button>  -->
                                    </div>
                                    <div>
                                        <div class="form-group">
                                            <div class="input-group">
                                                <input type="search" class="form-control" id="SearchKnowledgeBase" placeholder="Search Knowledge Base">
                                            </div>
                                        </div>
                                        <div style="height: calc(100vh - 365px);overflow: auto;" id="divknowledgebase">
                                            <table class="table align-middle  knowledgebase">
                                                <thead class="position-sticky top-0 z-3s bg-white">
                                                    <tr>
                                                        <th scope="col" role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal">Name</th>
                                                            <!-- <th scope="col">Created by</th>  -->
                                                        <th scope="col">Last updated</th>
                                                        <th scope="col">Connect</th>
                                                        <th scope="col">Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="SupportAgentList">
                                                  
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                </div>
                                <div class="tab-pane fade" id="v-pills-disabled" role="tabpanel" aria-labelledby="v-pills-disabled-tab" tabindex="0">...</div>
                                <div class="tab-pane fade" id="v-pills-messages" role="tabpanel" aria-labelledby="v-pills-messages-tab" tabindex="0">...</div>
                                <div class="tab-pane fade" id="v-pills-settings" role="tabpanel" aria-labelledby="v-pills-settings-tab" tabindex="0">...</div>
                            </div>
                        </div>

                    </div>
                </div>

            </div>

        </div>

    </div>

</div>


<div class="row g-2 d-none" id="CallTracking">
    <div class="col">
	
        <div class="card card-glass " style="background-color: #FCFCFD; height:calc(100vh - 95px);">
            <div class="card-header pt-1 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><img class="img-fluid me-1" src="img/call-track-icons/Call_Tracking_Black.svg" />Call Tracking</h6>
                    <div class="input-group" style="width:150px">
                        <span class="input-group-text"><img class="img-fluid me-1" src="img/call-track-icons/solar_calendar-broken.svg" /></span>
                        <input type="date" id="susanDateTime" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="card-body py-2">

                <div class="row h-100">
                    <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-7 d-grid h-100" style="grid-template-rows: max-content;">
                        <div class="card card-no-design call-track-card shadow-sm mb-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="d-flex align-items-start gap-2">
                                        <div class="d-flex justify-content-start align-items-start">
                                            <img class="img-fluid" src="img/assets/Chat-Top-Logo.svg">
                                        </div>
                                        <div>
                                            <h5 class="text-priamry-gradient">Susan.Ai<span id="susanPhoneNo" class="d-none " style="font-size: 15px;margin-left: 8px; "></span></h5>
                                            <p class="mb-0">
                                                Youll be able to collaborate and quickly
                                            </p>
                                            <p class="mb-0">
                                                get a sense of what's happening at work.
                                            </p>
                                        </div>

                                    </div>
                                    <div>
                                        <button class="btn btn-light bg-white btn-sm rounded-5 py-2 px-3">View License Details<span role="button"><img class="img-fluid ms-2" src="img/call-track-icons/Gradient_Arrow.svg"></span></button>
                                    </div>
                                </div>


                            </div>
                            <div class="card-footer">
                                <div class="row row-cols-4 g-md-2 g-lg-2 g-xxl-3">
                                    <div class="col">
                                        <div class="d-flex align-items-start gap-2  bg-white p-2 rounded-4 shadow-sm">
                                            <div class="d-flex justify-content-start align-items-start">
                                                <span class="p-2 shadow-sm rounded-4 bg-white">
                                                    <img class="img-fluid" src="img/call-track-icons/total_calls.svg">
                                                </span>
                                            </div>
                                            <div>
                                                <p class="mb-0 fs-7">Total Calls</p>
                                                <p class="mb-0">
                                                    <span class="fw-semibold" id="TotalCall">0</span><span class="mx-2"><img class="img-fluid" src="img/call-track-icons/Up_Arrow.svg"></span><span id="TotalCallPercent">0%</span>
                                                </p>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="d-flex align-items-start gap-2  bg-white p-2 rounded-4 shadow-sm">
                                            <div class="d-flex justify-content-start align-items-start">
                                                <span class="p-2 shadow-sm rounded-4 bg-white">
                                                    <img class="img-fluid" src="img/call-track-icons/Answered_Calls.svg">
                                                </span>
                                            </div>
                                            <div>
                                                <p class="mb-0 fs-7">Answered Calls</p>
                                                <p class="mb-0">
                                                    <span class="fw-semibold" id="AnsweredCall">0</span><span class="mx-2"><img class="img-fluid" src="img/call-track-icons/Up_Arrow.svg"></span> <span id="AnsweredPercent">0%</span>
                                                </p>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="d-flex align-items-start gap-2  bg-white p-2 rounded-4 shadow-sm">
                                            <div class="d-flex justify-content-start align-items-start">
                                                <span class="p-2 shadow-sm rounded-4 bg-white">
                                                    <img class="img-fluid" src="img/call-track-icons/Missed_Calls.svg">
                                                </span>
                                            </div>
                                            <div>
                                                <p class="mb-0 fs-7">Failed Calls</p>
                                                <p class="mb-0">
                                                    <span class="fw-semibold" id="MissedCall">0</span><span class="mx-2"><img class="img-fluid" src="img/call-track-icons/Up_Arrow.svg"></span>
                                                    <span id="MissedPercent">0%</span>
                                                </p>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="d-flex align-items-start gap-2  bg-white p-2 rounded-4 shadow-sm">
                                            <div class="d-flex justify-content-start align-items-start">
                                                <span class="p-2 shadow-sm rounded-4 bg-white">
                                                    <img class="img-fluid" src="img/call-track-icons/Average_Duration.svg">
                                                </span>
                                            </div>
                                            <div>
                                                <p class="mb-0 fs-7">Avg.Duration</p>
                                                <p class="mb-0">
                                                    <span class="fw-semibold" id="AvgDuration">0</span><span class="mx-2"><img class="img-fluid" src="img/call-track-icons/Down_Arrow.svg"></span> <span id="AvgDurationPercent">0%</span>
                                                </p>
                                            </div>

                                        </div>
                                    </div>
                                </div>


                            </div>
                        </div>
                        <div class="card card-no-design shadow-sm">
                            <div class="card-header d-flex align-items-center justify-content-between pt-2">
                                <div>
                                    <h6 class="mb-0 card-title">
                                        <span class="me-1"><img class="img-fluid" src="img/call-track-icons/Call_Recordings.svg"></span>
                                        Call History - <span class="text-warning gradient" id="SelectedDate"></span>
                                    </h6>
                                </div>
                                <div class="d-flex align-items-center gap-2">
                                    <div class="bg-white rounded-pill shadow-sm px-2">
                                        <div class="input-group border-0">
                                            <span class="input-group-text"><img class="img-fluid" src="img/call-track-icons/Search-icon.svg" width="20"></span>
                                            <input type="search" id="CallHistorySearch" class="form-control" placeholder="Search..." />
                                        </div>
                                    </div>
                                   
                                    <span class="bg-white rounded-pill shadow-sm p-2">
                                        <span role="button">
                                            <img class="img-fluid" src="img/call-track-icons/solar_export-broken.svg" width="20">
                                        </span>

                                    </span>
                                </div>
                            </div>
                            <div class="card-body pt-0" style="height: calc(50vh - 58px);overflow: auto;">
                                <div id="callHistoryTable">
                                    <table class="table table-hover align-middle Call-Feeds-Table" id="callTable">
                                        <thead style="background:#F7F7F7" class="position-sticky top-0 z-3">
                                            <tr>
                                                <th>Date Time</th>
                                                <th>Duration</th>
                                                <th>Agent Name</th>
                                                <th>Mobile No</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="CallTrackingTable">
                                           
                                        </tbody>
                                    </table>
                                </div>

                            </div>

                        </div>

                    </div>
                    <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-5 d-grid h-100" style="grid-template-rows: max-content;">
                        <div class="card card-no-design shadow-sm mb-2">
                            <div class="card-header d-flex align-items-center justify-content-between px-3 pt-2">
                                <div>
                                    <h6 class="mb-0 card-title"><span class="me-1"><img class="img-fluid" src="img/call-track-icons/Call_Recordings.svg"></span>Call Recordings</h6>
                                </div>
                                <div class="d-flex align-items-center gap-2  w-50 justify-content-end flex-fill">
                                    <div class="bg-white rounded-pill shadow-sm px-2 w-50">
                                        <div class="input-group border-0">
                                            <span class="input-group-text"><img class="img-fluid" src="img/call-track-icons/Search-icon.svg" width="20"></span>
                                            <input type="search" class="form-control" placeholder="Search..." />
                                        </div>
                                    </div>
                                 
                                </div>
                            </div>
                            <div class="card-body call-record-card" style="height: calc(50vh - 145px);overflow: auto;">

                                <div class="card card-no-design  shadow-sm mb-3">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6>Call with Sabeel</h6>
                                                <p class="mb-0 fs-8">
                                                    Files shared of Pune HR Details
                                                </p>
                                            </div>
                                            <div>
                                                <img class="img-fluid" src="img/call-track-icons/Docs.svg" width="40">
                                            </div>
                                        </div>
                                        <div class="mt-2 d-flex justify-content-between align-items-center">
                                            <div class="w-75 d-flex justify-content-between align-items-center">
                                                <div class="w-75 d-flex align-items-center gap-2">
                                                    <span><img class="img-fluid" src="img/call-track-icons/PDF.svg"></span>
                                                    <span><img class="img-fluid" src="img/call-track-icons/IMG.svg"></span>
                                                    <span><img class="img-fluid" src="img/call-track-icons/PPT.svg"></span>
                                                </div>
                                            </div>
                                            <div>
                                                <span class="success-circle success me-2"><img class="img-fluid" src="img/call-track-icons/Completed.svg"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card card-no-design  shadow-sm mb-0">
                                    <div class="card-body" id="conversationChat">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6>Call with Selvam</h6>
                                                <p class="mb-0 fs-8">
                                                    Enquired and discussed
                                                    about today s task!
                                                </p>
                                            </div>
                                            <div>
                                                <img class="img-fluid" src="img/call-track-icons/Task.svg" width="40">
                                            </div>
                                        </div>
                                        <div class="mt-2 d-flex justify-content-between align-items-center">
                                            <div class="p-2 shadow-sm bg-white w-75 d-flex justify-content-between align-items-center" style="border-radius:8px">
                                                <div>
                                                    <h6>Market Research</h6>
                                                    <p class="mb-0 fs-8">
                                                        10:00 AM
                                                    </p>
                                                </div>
                                                <span role="button" class="px-3 py-1 rounded-4" style="background:#EDE8FF;color:#673FE3">Done</span>
                                            </div>
                                            <div>
                                                <span class="success-circle error me-2"><img class="img-fluid" src="img/call-track-icons/Failure.svg"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>


                        <div class="card card-no-design shadow-sm">
                            <div class="card-header d-flex align-items-center justify-content-between px-3 pt-3">
                                <div>
                                    <h6 class="mb-0 card-title"><span class="me-1"><img class="img-fluid" src="img/call-track-icons/Call_Analytics.svg"></span>Call Analytics</h6>
                                </div>
                                <div class="d-flex align-items-center gap-3">
                                    <span class="d-none" id="Totalchart"><span class="circle total"></span>Total</span>
                                    <span class="d-none" id="answeredchart"><span class="circle answered"></span>Answered</span>
                                    <span class="d-none" id="missedchart"><span class="circle missed"></span>Failed</span>
                                </div>
                            </div>
                            <div class="card-body pt-0 ">
                                <div id="Call-Analytics-Chart" class="h-100"></div>

                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>
    </div>
	 <elevenlabs-convai agent-id="agent_01jvpxq8nefx19k87cv34gy388"></elevenlabs-convai>
</div>

                        </div>
                    </div>

                </div>
            </div>
          
        </div>
    </div>




<!--Add URL Modal -->
<div class="modal fade" id="URLModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5">Add URL</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">URL</label>
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="https://example.com">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Add URL</button>
            </div>
        </div>
    </div>
</div>




<!--Add Connected Modal -->
<div class="modal fade" id="ConnectModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="width: 75%;">
            <div class="modal-header align-items-center justify-content-center">
                <img src="img/Icon-Final/Connected.svg" class="img-fluid" style="margin-top: -100px" />
            </div>
            <div class="modal-body  d-flex align-items-center justify-content-center">
                <div>
                    <p class="fw-semibold">Are You Sure Want To <span class="text-priamry-gradient" id="connectMessage">Connect ?</span></p>
                    <div class=" d-flex align-items-center gap-2">
                         <div class="spinner-border text-info d-none" id="ConnectFileSpinner" role="status">
  <span class="visually-hidden">Loading...</span>
</div>
                        <button class="btn btn-sm btn-secondary rounded-pill flex-fill" id="connectCancel" data-bs-dismiss="modal" >Cancel</button>
                        <button class="btn btn-sm btn-primary rounded-pill flex-fill" id='ConnectStatusState'>Connect</button>
                       
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!--Add Delete Modal -->
<div class="modal fade" id="DeleteModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="width: 75%;">
            <div class="modal-header align-items-center justify-content-center">
                <img src="img/Icon-Final/Delete.svg" class="img-fluid" style="margin-top: -100px" />
            </div>
            <div class="modal-body  d-flex align-items-center justify-content-center">
                <div>
                    <p class="fw-semibold">Are You Sure Want To Delete<span class="text-priamry-gradient"> PDf ?</span></p>
                    <div class=" d-flex align-items-center gap-2">
                        <button class="btn btn-sm btn-secondary rounded-pill flex-fill" id="supportDocCancel" data-bs-dismiss="modal">Cancel</button>
                        <button class="btn btn-sm btn-primary rounded-pill flex-fill" id="supportDocsDelete">Delete</button>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!--Add File Modal -->
<div class="modal fade" id="FileModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5">Add Files</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">File</label>
                    <div class="input-group">
                        <input type="file" class="form-control SupportUploadFile">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="spinner-border text-info d-none" id="addFileSpinner" role="status">
  <span class="visually-hidden">Loading...</span>
</div>
                <button type="button" class="btn btn-secondary" id="fileClose" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary AddFile">Add File</button>
            </div>
        </div>
    </div>
</div>



<!--Add Text Modal -->
<div class="modal fade" id="TextModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5">Create Text</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Text Name</label>
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Enter a name for your text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Text Content</label>
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Enter your text content here">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Create Text</button>
            </div>
        </div>
    </div>
</div>




<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRight" aria-labelledby="offcanvasRightLabel" style="border-radius: 25px 0px 0px 25px;">
    <div class="offcanvas-header align-items-start">
        <div>
            <h5 class="offcanvas-title fs-6 mb-2"><span class="me-1"><img class="img-fluid" src="img/call-track-icons/Call_Logs.svg"></span>Call Logs</h5>
            <div class="d-flex align-items-center gap-2">
                <p class="mb-0 callerName"></p><span class="text-priamry-gradient callDate"></span>
            </div>
        </div>
    
            <div class="d-flex flex-fill">

                <!-- <span class="bg-white rounded-pill shadow-sm p-2"> <span role="button"> <img class="img-fluid" src="img/call-track-icons/solar_export-broken.svg" width="20"> </span> </span> -->
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
      
    </div>
    <div class="offcanvas-body" id="conversationchatlog">
    </div>
</div>

<div class='toast-container position-absolute p-3 top-0 end-0' style="z-index: 999999;">
    <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' style='background-color:rgb(255 255 255);border:0' aria-atomic='true'>
        <div class='d-flex'>
            <div class='toast-body'>
                <span id="alertClass">
                    <i class='cp-check toast_icon iconClass'></i>
                </span>
                <span id="message">

                </span>
            </div>
            <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
        </div>
    </div>
</div>


<div class="offcanvas offcanvas-end w-50"  tabindex="-1" aria-labelledby="ViewModal" id="ViewModal">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="offcanvasWithBothOptionsLabel">View Content</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body" >
    <div class="spinner-border spinner-border-sm d-none chatViewLoader" role="status">
                                            <span class="sr-only"></span>
                                        </div>
      <!-- <div id="pdfViewer" style="width: 100%; height: 600px;"></div>  -->
        <iframe id="ViewShowContent" style="height:100%;width:98%"></iframe>
    </div>
</div>




    <!-- Widget Script -->
   <script src="js/susanCall.js"></script>

<!-- <script src="//www.amcharts.com/lib/4/core.js"></script> -->
<!-- <script src="//www.amcharts.com/lib/4/charts.js"></script> -->
<!-- <script src="//www.amcharts.com/lib/4/themes/animated.js"></script> -->

    <script src="library/jquery/jquery.min.js"></script>
    <script src="library/bootstrap/js/bootstrap.bundle.min.js"></script>
	<script src="library/amcharts/core.js"></script>
<script src="library/amcharts/charts.js"></script>
<script src="library/amcharts/animated.js"></script>
    <script src="js/chatFunction.js"></script>
	<script src="configurejson.js"></script>
</body>

</html>

<!-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> -->
<!-- <script src="//www.amcharts.com/lib/4/core.js"></script> -->
<!-- <script src="//www.amcharts.com/lib/4/charts.js"></script> -->
<!-- <script src="//www.amcharts.com/lib/4/themes/animated.js"></script> -->
