import simpleaudio as sa

def play_alert_sound(file_path):
    try:
        # Load the .wav file
        wave_obj = sa.WaveObject.from_wave_file(file_path)
        # Play the sound
        play_obj = wave_obj.play()
        # Wait for the sound to finish playing
        play_obj.wait_done()
    except Exception as e:
        print(f"Error playing sound: {e}")

for _ in range(4):
    play_alert_sound('.\sound\warning_new.wav')

