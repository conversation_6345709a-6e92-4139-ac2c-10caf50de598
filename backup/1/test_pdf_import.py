#!/usr/bin/env python3
"""Test PDF import"""

print("Testing PDF imports...")

try:
    from PyPDF2 import PdfReader
    print("✅ PyPDF2 import successful")
    PyPDF2_available = True
except ImportError as e:
    print(f"❌ PyPDF2 import failed: {e}")
    PyPDF2_available = False

try:
    from pypdf import PdfReader as PdfReader2
    print("✅ pypdf import successful")
    pypdf_available = True
except ImportError as e:
    print(f"❌ pypdf import failed: {e}")
    pypdf_available = False

if PyPDF2_available:
    print("Using PyPDF2")
elif pypdf_available:
    print("Using pypdf")
else:
    print("No PDF library available")
