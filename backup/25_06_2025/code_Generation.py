import os
import re
import gradio as gr
from langchain.document_loaders import Text<PERSON>oader
from langchain.docstore.document import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.vectorstores import FAISS
from langchain.llms import HuggingFacePipeline
from langchain.chains import RetrievalQ<PERSON>
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline

class CPSLCodeGenerator:
    """
    A class to encapsulate the new CPSL code-generation chatbot logic.
    """

    def __init__(self, data_file="code_generation_files/CPSL_Documentation.txt"):
        try:
            # Make sure file is present
            if not os.path.exists(data_file):
                raise FileNotFoundError(
                    f"Data file '{data_file}' not found in the directory: {os.getcwd()}"
                )

            # 1) Load documents
            doc_loader = TextLoader(data_file)
            docs = doc_loader.load()

            # 2) Split documents
            text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=100)
            split_docs = text_splitter.split_documents(docs)

            # 3) Create embeddings and vector DB
            embeddings = HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")
            vectordb = FAISS.from_documents(split_docs, embeddings)

            # 4) Set up the model & pipeline
            model_name = "code_generation_files/model/Y1"
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForCausalLM.from_pretrained(
                model_name,
                device_map="auto",
                torch_dtype="auto"
            )

            qa_pipeline = pipeline(
                "text-generation",
                model=model,
                tokenizer=tokenizer,
                max_new_tokens=500,  # Limit on output tokens
                pad_token_id=tokenizer.eos_token_id
            )

            llm = HuggingFacePipeline(pipeline=qa_pipeline)

            # 5) Create QA Chain
            retriever = vectordb.as_retriever(search_kwargs={"k": 5})
            self.qa_chain = RetrievalQA.from_chain_type(
                retriever=retriever,
                chain_type="stuff",
                llm=llm,
                return_source_documents=False
            )
        except Exception as e:
            print(e)

    def _preprocess_query(self, query: str) -> str:
        """
        Convert queries containing 'script' or 'code' into a specialized request.
        """
        if "script" in query.lower() or "code" in query.lower():
            return f"Write a CPSL script: {query}"
        return query

    @staticmethod
    def extract_code_block_alone_for_answer(script):
        lines = script.splitlines()
        code_lines = []
        capture = False

        for line in lines:
            if line.startswith("R_Connect"):
                capture = True

            if capture:
                if line.strip() == "":
                    break
                code_lines.append(line)

        return "\n".join(code_lines)

    @staticmethod
    def extract_code_block_alone(script):
        lines = script.splitlines()
        code_lines = []
        capture = False

        for line in lines:
            if line.startswith("R_Connect"):
                capture = True

            if capture:
                code_lines.append(line)

        return "\n".join(code_lines)


    def _clean_response(self, response: dict) -> str:

        result = response.get("result", "")
        try:
            # Try to extract code block first
            code_match = re.search(r"```(.*?)```", result, re.DOTALL)
            if code_match:
                code_text = code_match.group(1).strip()
                code_result = self.extract_code_block_alone(code_text)
                return code_result
        except Exception as ex:
            print(ex)

        # Fallback to extracting text after "Answer:"
        if "Answer:" in result:
            code_text = result.split("Answer:")[1].strip()
            code_result = self.extract_code_block_alone_for_answer(code_text)
            return code_result

        return result.strip()

    def generate_response(self, user_input: str) -> str:
        """
        Main entry point to get a response from the QA chain.
        """
        processed_query = self._preprocess_query(user_input)
        raw_response = self.qa_chain.invoke({"query": processed_query})
        code_result = self._clean_response(raw_response)
        return code_result

    def create_interface(self) -> gr.Blocks:
        with gr.Blocks() as interface:
            # Add custom CSS for styling
            gr.HTML("""
                <style>
                .message-wrap {
                    font-size: 18px !important;
                }
                .message pre {
                    background-color: #282c34 !important;
                    border-radius: 8px !important;
                    padding: 15px !important;
                }
                .message code {
                    color: #ff9800 !important;  /* Orange color for code */
                    font-size: 16px !important;
                    font-family: 'Monaco', 'Courier New', monospace !important;
                }
                .contain {
                    font-size: 18px !important;
                }
                </style>
            """)

            gr.Markdown("## CPSL Code Generation Chatbot")

            with gr.Row():
                with gr.Column(scale=1):
                    user_input = gr.Textbox(
                        label="Your Message",
                        placeholder="Type your message here...",
                        scale=2
                    )
                    send_button = gr.Button("Send")
                with gr.Column(scale=1):
                    chat_history = gr.Chatbot(
                        label="CPSL Chat Output",
                        height=500,
                        bubble_full_width=False,
                        elem_classes="chat-container"
                    )


            def interact(user_message, history):
                bot_reply = self.generate_response(user_message)
                if "```" not in bot_reply:
                    bot_reply = f"```\n{bot_reply}\n```"
                history.append((user_message, bot_reply))
                return history, history

            user_input.submit(
                fn=interact,
                inputs=[user_input, chat_history],
                outputs=[chat_history, chat_history]
            )
            send_button.click(
                fn=interact,
                inputs=[user_input, chat_history],
                outputs=[chat_history, chat_history]
            )
            return interface



