<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>3D Income vs Expenses Overlap Chart</title>
  <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
  <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
  <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
</head>
<body>
  <div id="chartdiv" style="width: 100%; height: 500px;"></div>

  <script>
    am4core.ready(function () {
      am4core.useTheme(am4themes_animated);

      // Chart instance
      var chart = am4core.create("chartdiv", am4charts.XYChart3D);

      chart.data = [
        { country: "USA", income: 30, expenses: 20 },
        { country: "UK", income: 15, expenses: 10 },
        { country: "Japan", income: 35, expenses: 30 },
        { country: "Germany", income: 25, expenses: 35 }
      ];

      // X Axis
      var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
      categoryAxis.dataFields.category = "country";
      categoryAxis.renderer.minGridDistance = 60;

      // Y Axis
      var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());

      // Reusable base pattern (will be cloned)
      var pattern = new am4core.LinePattern();
      pattern.width = 6;
      pattern.height = 6;
      pattern.strokeWidth = 1.5;
      pattern.stroke = am4core.color("#000");
      pattern.rotation = 45;

      // Front bar: Income
      var series1 = chart.series.push(new am4charts.ColumnSeries3D());
      series1.dataFields.valueY = "income";
      series1.dataFields.categoryX = "country";
      series1.name = "Income";
      series1.columns.template.width = am4core.percent(50);
      series1.columns.template.zIndex = 2; // front

      series1.columns.template.adapter.add("fill", function (_, target) {
        const { income, expenses } = target.dataItem.dataContext;
        return income < expenses ? am4core.color("green") : am4core.color("red");
      });

      // Back bar: Expenses
      var series2 = chart.series.push(new am4charts.ColumnSeries3D());
      series2.dataFields.valueY = "expenses";
      series2.dataFields.categoryX = "country";
      series2.name = "Expenses";
      series2.columns.template.width = am4core.percent(50);
      series2.columns.template.dx = 20; // offset back bar rightward
      series2.columns.template.zIndex = 1; // back layer
      series2.columns.template.fillOpacity = 1;

      series2.columns.template.adapter.add("fill", function (_, target) {
        const { income, expenses } = target.dataItem.dataContext;

        if (expenses < income) {
          return am4core.color("green");
        } else {
          // clone the pattern to avoid re-use bug
          var localPattern = pattern.clone();
          chart.patterns.push(localPattern);
          return localPattern;
        }
      });

      series2.columns.template.adapter.add("strokeOpacity", () => 0);

      // Optional legend
      chart.legend = new am4charts.Legend();
    });
  </script>
</body>
</html>
