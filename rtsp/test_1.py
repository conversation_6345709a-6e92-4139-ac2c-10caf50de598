import cv2

rtsp_url = "rtsp://admin:Admin123$@10.11.25.64:554/stream1"
cap = cv2.VideoCapture(rtsp_url)

if not cap.isOpened():
    print("Cannot open RTSP stream.")
    exit()

# Get native resolution
width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
print(f"Native resolution: {width}x{height}")

# Use the same resolution when writing
fourcc = cv2.VideoWriter_fourcc(*'mp4v')
out = cv2.VideoWriter("clear_frames_output.mp4", fourcc, 30.0, (width, height))

while True:
    ret, frame = cap.read()
    if not ret:
        print("Failed to grab frame.")
        break

    # Write unmodified frame
    out.write(frame)

    # Optional: show the frame
    cv2.imshow("RTSP Stream", frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
out.release()
cv2.destroyAllWindows()
