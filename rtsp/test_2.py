# import cv2
#
# # RTSP stream URL
# rtsp_url = "rtsp://admin:Admin123$@10.11.25.64:554/stream1"
#
# # Open RTSP stream
# cap = cv2.VideoCapture(rtsp_url)
#
# # Check if opened successfully
# if not cap.isOpened():
#     print("Cannot open RTSP stream.")
#     exit()
#
# # Get original resolution
# width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
# height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
# fps = cap.get(cv2.CAP_PROP_FPS) or 25  # Fallback if FPS is not available
#
# # Define codec and output file
# fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # Use 'XVID' or 'H264' if needed
# out = cv2.VideoWriter("output.mp4", fourcc, fps, (width, height))
#
# while True:
#     ret, frame = cap.read()
#     if not ret:
#         print("Stream ended or failed.")
#         break
#
#     out.write(frame)  # Write raw frame
#
#     # Optional: show frame
#     cv2.imshow("RTSP", frame)
#     if cv2.waitKey(1) & 0xFF == ord('q'):
#         break
#
# # Release everything
# cap.release()
# out.release()
# cv2.destroyAllWindows()




import cv2

# Load super resolution model
sr = cv2.dnn_superres.DnnSuperResImpl_create()
sr.readModel("ESPCN_x4.pb")  # Download from OpenCV's GitHub models
sr.setModel("espcn", 4)  # 4x upscaling

# Read one frame from RTSP
cap = cv2.VideoCapture("rtsp://admin:Admin123$@10.11.25.64:554/stream1")
ret, frame = cap.read()
cap.release()

if ret:
    # Enhance using super resolution
    result = sr.upsample(frame)

    # Save or display
    cv2.imwrite("enhanced_frame.jpg", result)
    cv2.imshow("Enhanced", result)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
else:
    print("Failed to capture frame.")
