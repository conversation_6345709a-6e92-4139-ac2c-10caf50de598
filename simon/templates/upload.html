<!DOCTYPE html>
<html>
<head>
    <title>BCM Document Analyzer</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #2c3e50; }
        .upload-box {
            border: 2px dashed #3498db;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .upload-box.highlight {
            background: #f0f7ff;
            border-color: #2980b9;
        }
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .btn:hover { background: #2980b9; }
        .btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }
        .error { color: #e74c3c; margin-top: 10px; }
        #file-info {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
        }
        #file-info .filename {
            font-weight: bold;
            color: #2c3e50;
        }
        .file-requirements {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>BCM Document Analyzer</h1>
    <p>Upload your Business Continuity Management (BCM) document for ISO 22301 compliance analysis</p>

    <div class="file-requirements">
        <p>Requirements:</p>
        <ul>
            <li>PDF format only</li>
            <li>Maximum file size: 10MB</li>
        </ul>
    </div>

    {% if error %}
        <div class="error">Error: {{ error }}</div>
    {% endif %}

    <form method="POST" enctype="multipart/form-data" onsubmit="showLoading()">
        <div class="upload-box" id="drop-area">
            <input type="file" name="file" id="file-input" accept=".pdf" required style="display: none;">
            <label for="file-input" style="cursor: pointer;">
                <div>Click to select a PDF file</div>
                <div>or drag and drop it here</div>
            </label>
            <div id="file-info"></div>
        </div>
        <button type="submit" class="btn" id="submit-btn">
            <span id="btn-text">Analyze Document</span>
            <span id="loading" style="display:none;"> ⏳ Processing...</span>
        </button>
    </form>

    <script>
        const dropArea = document.getElementById('drop-area');
        const fileInput = document.getElementById('file-input');
        const fileInfo = document.getElementById('file-info');
        const submitBtn = document.getElementById('submit-btn');

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropArea.classList.add('highlight');
        }

        function unhighlight() {
            dropArea.classList.remove('highlight');
        }

        // Handle dropped files
        dropArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length) {
                fileInput.files = files;
                updateFileInfo(files[0]);
            }
        }

        // Handle selected files
        fileInput.addEventListener('change', function() {
            if (this.files.length) {
                updateFileInfo(this.files[0]);
            }
        });

        function updateFileInfo(file) {
            if (file.size > 10 * 1024 * 1024) { // 10MB
                fileInfo.innerHTML = `<span class="error">File too large (max 10MB)</span>`;
                submitBtn.disabled = true;
                return;
            }

            if (file.type !== 'application/pdf') {
                fileInfo.innerHTML = `<span class="error">Only PDF files are allowed</span>`;
                submitBtn.disabled = true;
                return;
            }

            fileInfo.innerHTML = `Selected: <span class="filename">${file.name}</span> (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
            submitBtn.disabled = false;
        }

        function showLoading() {
            document.getElementById('btn-text').style.display = 'none';
            document.getElementById('loading').style.display = 'inline';
        }
    </script>
</body>
</html>