INFO | 2025-07-01 15:33:42,790 | module: analytics_search | lineno: 29 |  functName: <module> | Successfully connected to Elasticsearch. 
INFO | 2025-07-01 15:33:42,804 | module: analytics_search | lineno: 42 |  functName: <module> | Index 'patrol_elastic' already exists. 
INFO | 2025-07-01 15:34:41,054 | module: audioplaybackbg | lineno: 74 |  functName: <module> | Loading the sound alarm .. 
INFO | 2025-07-01 15:34:44,469 | module: __DatabaseLayer__ | lineno: 177 |  functName: get_camera_by_user | get camera details by user 
INFO | 2025-07-01 15:34:44,813 | module: main | lineno: 273 |  functName: <module> | ==================== getting system information =============================== 
INFO | 2025-07-01 15:34:47,623 | module: main | lineno: 283 |  functName: <module> | Windows 
INFO | 2025-07-01 15:34:47,624 | module: main | lineno: 284 |  functName: <module> | OS Name: b'Microsoft Windows 10 Pro' 
INFO | 2025-07-01 15:34:47,625 | module: main | lineno: 285 |  functName: <module> | OS Version: 10.0.19045 19045 
INFO | 2025-07-01 15:34:47,628 | module: main | lineno: 286 |  functName: <module> | CPU: Intel(R) Core(TM) i5-6500T CPU @ 2.50GHz 
INFO | 2025-07-01 15:34:47,629 | module: main | lineno: 287 |  functName: <module> | RAM: 15.9110107421875 GB 
INFO | 2025-07-01 15:34:47,638 | module: main | lineno: 297 |  functName: <module> | =================== completed on getting system information ========================= 
INFO | 2025-07-01 15:34:47,639 | module: main | lineno: 302 |  functName: <module> | ==================== Loading the models =============================== 
INFO | 2025-07-01 15:35:48,641 | module: main | lineno: 369 |  functName: <module> | =================== completed on loading the global models ========================= 
INFO | 2025-07-01 15:35:50,628 | module: main | lineno: 380 |  functName: reset_stream_details | Getting users streaming details 
INFO | 2025-07-01 15:35:50,629 | module: __DatabaseLayer__ | lineno: 237 |  functName: get_user_stream_details | get stream details for users 
INFO | 2025-07-01 15:36:19,449 | module: main | lineno: 3502 |  functName: get | Rendering login page 
INFO | 2025-07-01 15:36:42,481 | module: main | lineno: 3510 |  functName: post | Checking Username and password 
INFO | 2025-07-01 15:36:42,485 | module: __DatabaseLayer__ | lineno: 143 |  functName: get_login_details | get login details 
INFO | 2025-07-01 15:36:51,935 | module: main | lineno: 3510 |  functName: post | Checking Username and password 
INFO | 2025-07-01 15:36:51,937 | module: __DatabaseLayer__ | lineno: 143 |  functName: get_login_details | get login details 
INFO | 2025-07-01 15:36:52,781 | module: main | lineno: 3596 |  functName: post | Camera Details View 
INFO | 2025-07-01 15:36:52,791 | module: __DatabaseLayer__ | lineno: 177 |  functName: get_camera_by_user | get camera details by user 
