import random
import time
from fastapi import <PERSON><PERSON><PERSON>, Request
from twilio.twiml.voice_response import VoiceResponse, Gather
from fastapi.responses import PlainTextResponse
import requests
import json
from elevenlabs.client import ElevenLabs
from elevenlabs import play, save, stream, Voice, VoiceSettings

import json
import re
from bs4 import BeautifulSoup

app = FastAPI()

# Initialize the ElevenLabs client with your API key
client = ElevenLabs(api_key="***************************************************")


def generate_speech(text: str, voice: str = "Brian"):
    """Generate speech using ElevenLabs API and return the audio."""
    audio = client.generate(text=text, voice=voice)
    return audio


@app.post("/answer")
async def answer_call():
    """Respond to incoming phone calls with a brief message and gather voice input."""
    resp = VoiceResponse()

    # Use ElevenLabs to generate speech and play the audio
    audio = generate_speech("<PERSON>, I am <PERSON>, your AI assistant here to support you with Perpetuuit<PERSON>'s BCM.",
                            voice="<PERSON>")
    play(audio)

    gather = Gather(input='speech', language="en-IN", action='/handle_input', method='POST', timeout=10,
                    speech_model='googlev2_telephony', speech_timeout='auto')
    gather.say("How can I assist you today?")
    resp.append(gather)

    return PlainTextResponse(str(resp))


@app.post("/handle_input")
async def handle_input(request: Request):
    """Handle the user's voice input."""
    user_input = (await request.form()).get('SpeechResult', None)
    resp = VoiceResponse()

    if user_input:
        user_input = user_input.lower()
        print(user_input)

        body_content = {
            "token": "ohveeyvpek1kp4aggn812zbp83t2mqfu",
            "action": "",
            "UserId": "193",
            "q": user_input,
            "nlumodel": "20190516_095554",
            "agentname": "Continuity_vault",
            "userName": "Mahesh"
        }
        response = requests.post('https://e7f0-210-18-183-42.ngrok-free.app/av3arapp/new_chat_response',
                                 json=body_content)

        if response:
            result = json.loads(response.text)

            clean_text = ''
            process_names_string = ''

            for item in result['response']:
                html = item['data']['botsays'][0]['value']

                if '<table' in html:
                    soup = BeautifulSoup(html, 'html.parser')
                    rows = soup.find_all('tr')[1:]  # Skip header row
                    names = []
                    for row in rows:
                        cols = row.find_all('td')
                        if len(cols) >= 2:
                            names.append(cols[1].get_text(strip=True))

                    if names:
                        if len(names) == 1:
                            process_names_string = names[0]
                        else:
                            process_names_string = ', '.join(names[:-1]) + ' and ' + names[-1]
                else:
                    clean_text = re.sub(r'<.*?>', '', html).strip()

            data = clean_text + ". " + process_names_string
            print(data)

            # Use ElevenLabs to generate speech for the response
            audio = generate_speech(data)
            play(audio)

            time.sleep(1)
            rand_quest = ['Do you have any other questions?', 'Is there anything else you did like to ask?',
                          'Any other concerns you did like to address?', 'Is there anything more I can help you with?',
                          'Do you need assistance with anything else?', 'Would you like to ask anything else?']
            data1 = random.choice(rand_quest)

            # Use ElevenLabs to generate speech for the follow-up question
            audio = generate_speech(data1)
            play(audio)

        gather = Gather(input='speech', language="en-IN", action='/handle_input', method='POST', timeout=10,
                        speech_model='googlev2_telephony', speech_timeout='auto')
        resp.append(gather)
    else:
        resp.say("Sorry, I couldn't hear your response. Please try again.")
        resp.redirect('/answer')

    return PlainTextResponse(str(resp))


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8090)
