import random
import time
import re
import os
from datetime import datetime
from bs4 import BeautifulSoup
from fastapi import FastAPI, Request
from fastapi.responses import PlainTextResponse
from twilio.twiml.voice_response import VoiceResponse, Gather
from elevenlabs.client import ElevenLabs
import requests
import json

app = FastAPI()

# ElevenLabs setup
client = ElevenLabs(api_key="***************************************************")  # replace with your key
human_voice = "Brian"

# Public base URL (ngrok or similar)
NGROK_BASE_URL = "https://07fc-210-18-183-42.ngrok-free.app "

# Create audio folder
os.makedirs("static", exist_ok=True)

def generate_audio(text: str, voice: str = human_voice) -> str:
    """Generate audio from ElevenLabs and return public URL"""
    audio_stream = client.generate(text=text, voice=voice, stream=True)
    audio_bytes = b''.join(chunk for chunk in audio_stream)

    timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")
    filename = f"audio_{timestamp}.mp3"
    filepath = os.path.join("static", filename)

    with open(filepath, "wb") as f:
        f.write(audio_bytes)

    return f"{NGROK_BASE_URL}/static/{filename}"


@app.post("/answer")
async def answer_call(request: Request):
    resp = VoiceResponse()

    greeting = "Hi, I am Susan, your AI assistant here to support you with Perpetuuiti's BCM. How can I assist you today?"
    audio_url = generate_audio(greeting)

    if audio_url:
        resp.play(audio_url)
    else:
        resp.say("Audio generation failed.")

    gather = Gather(input='speech', language="en-IN", action='/handle_input', method='POST', timeout=10,
                    speech_model='googlev2_telephony', speech_timeout='auto')
    resp.append(gather)

    return PlainTextResponse(str(resp))


@app.post("/handle_input")
async def handle_input(request: Request):
    user_input = (await request.form()).get('SpeechResult', None)
    resp = VoiceResponse()

    if user_input:
        user_input = user_input.lower()
        print("User:", user_input)

        body_content = {
            "token": "us1mxgkdvrzzt33nk2rxixf1epvvbemq",
            "action": "",
            "UserId": "193",
            "q": user_input,
            "nlumodel": "20190516_095554",
            "agentname": "Continuity_vault",
            "userName": "Mahesh"
        }

        chatbot_start = time.time()
        response = requests.post('https://2b3f-210-18-183-42.ngrok-free.app/av3arapp/new_chat_response', json=body_content)
        chatbot_end = time.time()
        print(f"Chatbot Response Time: {chatbot_end - chatbot_start:.2f}s")

        result = json.loads(response.text)
        clean_text = ''
        process_names_string = ''

        for item in result['response']:
            html = item['data']['botsays'][0]['value']

            if '<table' in html:
                soup = BeautifulSoup(html, 'html.parser')
                rows = soup.find_all('tr')[1:]
                names = [row.find_all('td')[1].text.strip() for row in rows if len(row.find_all('td')) >= 2]

                if names:
                    process_names_string = ', '.join(names[:-1]) + ' and ' + names[-1] if len(names) > 1 else names[0]
            else:
                clean_text = re.sub(r'<.*?>', '', html).strip()

        final_text = f"{clean_text}. {process_names_string}".strip()
        print("Bot:", final_text)

        bot_audio_url = generate_audio(final_text)
        if bot_audio_url:
            resp.play(bot_audio_url)
        else:
            resp.say("Audio generation failed.")

        time.sleep(1)
        follow_up = random.choice([
            "Do you have any other questions?",
            "Is there anything else you would like to ask?",
            "Any other concerns you would like to address?",
            "Is there anything more I can help you with?",
            "Do you need assistance with anything else?",
            "Would you like to ask anything else?"
        ])

        follow_audio_url = generate_audio(follow_up)
        if follow_audio_url:
            resp.play(follow_audio_url)

        gather = Gather(input='speech', language="en-IN", action='/handle_input', method='POST', timeout=10,
                        speech_model='googlev2_telephony', speech_timeout='auto')
        resp.append(gather)

    else:
        retry_text = "Sorry, I couldn't hear your response. Please try again."
        retry_url = generate_audio(retry_text)
        resp.play(retry_url)
        resp.redirect('/answer')

    return PlainTextResponse(str(resp))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8090)
