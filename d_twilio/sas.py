import re
import requests
import pandas as pd
import urllib3
from bs4 import BeautifulSoup

# Suppress only the single InsecureRequestWarning
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Load Excel sheet
excel_path = 'cv_dataset.xlsx'  # Replace with the actual Excel file path
df = pd.read_excel(excel_path)

# API endpoint and constant body params
chatbot_url = 'https://10.11.20.58:8080/av3arapp/new_chat_response'
base_payload = {
    "token": "1myczd1ed8b3muli3ewzbyoy252yqydw",
    "action": "",
    "UserId": "193",
    "nlumodel": "20190516_095554",
    "agentname": "Continuity_vault",
    "userName": "Mahesh"
}

def extract_response_content(api_response_json):
    try:
        clean_text = ''
        process_names_string = ''
        for item in api_response_json.get('response', []):
            html = item['data']['botsays'][0]['value']
            if '<table' in html:
                soup = BeautifulSoup(html, 'html.parser')
                rows = soup.find_all('tr')[1:]  # skip header
                names = [
                    row.find_all('td')[1].get_text(strip=True)
                    for row in rows if len(row.find_all('td')) >= 2
                ]
                if names:
                    process_names_string = ', '.join(names[:-1]) + ' and ' + names[-1] if len(names) > 1 else names[0]
            else:
                clean_text = re.sub(r'<.*?>', '', html).strip()
        final_text = clean_text
        if process_names_string:
            final_text += ". " + process_names_string
        return final_text
    except Exception as e:
        return f"Processing Error: {e}"


responses = []
for question in df['question']:
    payload = base_payload.copy()
    payload['q'] = question
    try:
        response = requests.post(chatbot_url, json=payload, verify=False)
        if response.status_code == 200:
            json_data = response.json()
            extracted = extract_response_content(json_data)
            responses.append(extracted)
        else:
            responses.append(f"API Error {response.status_code}")
    except Exception as e:
        responses.append(f"Request Failed: {e}")

# Add to dataframe
df['Response'] = responses

# Save output
output_path = 'updated_excel_with_clean_responses.xlsx'
df.to_excel(output_path, index=False)
print(f"Done. Responses saved to: {output_path}")
