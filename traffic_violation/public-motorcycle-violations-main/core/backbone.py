import tensorflow as tf
from tensorflow.keras.layers import Concatenate
import core.common as common


def darknet53(input_data):
    input_data = common.convolutional(input_data, (3, 3, 3, 32))
    input_data = common.convolutional(input_data, (3, 3, 32, 64), downsample=True)

    for i in range(1):
        input_data = common.residual_block(input_data, 64, 32, 64)

    input_data = common.convolutional(input_data, (3, 3, 64, 128), downsample=True)

    for i in range(2):
        input_data = common.residual_block(input_data, 128, 64, 128)

    input_data = common.convolutional(input_data, (3, 3, 128, 256), downsample=True)

    for i in range(8):
        input_data = common.residual_block(input_data, 256, 128, 256)

    route_1 = input_data
    input_data = common.convolutional(input_data, (3, 3, 256, 512), downsample=True)

    for i in range(8):
        input_data = common.residual_block(input_data, 512, 256, 512)

    route_2 = input_data
    input_data = common.convolutional(input_data, (3, 3, 512, 1024), downsample=True)

    for i in range(4):
        input_data = common.residual_block(input_data, 1024, 512, 1024)

    return route_1, route_2, input_data


def cspdarknet53(input_data):
    input_data = common.convolutional(input_data, (3, 3, 3, 32), activate_type="mish")
    input_data = common.convolutional(input_data, (3, 3, 32, 64), downsample=True, activate_type="mish")

    route = input_data
    route = common.convolutional(route, (1, 1, 64, 64), activate_type="mish")
    input_data = common.convolutional(input_data, (1, 1, 64, 64), activate_type="mish")

    for i in range(1):
        input_data = common.residual_block(input_data, 64, 32, 64, activate_type="mish")

    input_data = common.convolutional(input_data, (1, 1, 64, 64), activate_type="mish")

    # Replace tf.concat with Keras' Concatenate layer
    input_data = Concatenate(axis=-1)([input_data, route])

    input_data = common.convolutional(input_data, (1, 1, 128, 64), activate_type="mish")
    input_data = common.convolutional(input_data, (3, 3, 64, 128), downsample=True, activate_type="mish")

    route = input_data
    route = common.convolutional(route, (1, 1, 128, 64), activate_type="mish")
    input_data = common.convolutional(input_data, (1, 1, 128, 64), activate_type="mish")

    for i in range(2):
        input_data = common.residual_block(input_data, 64, 64, 64, activate_type="mish")

    input_data = common.convolutional(input_data, (1, 1, 64, 64), activate_type="mish")
    input_data = Concatenate(axis=-1)([input_data, route])

    input_data = common.convolutional(input_data, (1, 1, 128, 128), activate_type="mish")
    input_data = common.convolutional(input_data, (3, 3, 128, 256), downsample=True, activate_type="mish")

    route = input_data
    route = common.convolutional(route, (1, 1, 256, 128), activate_type="mish")
    input_data = common.convolutional(input_data, (1, 1, 256, 128), activate_type="mish")

    for i in range(8):
        input_data = common.residual_block(input_data, 128, 128, 128, activate_type="mish")

    input_data = common.convolutional(input_data, (1, 1, 128, 128), activate_type="mish")
    input_data = Concatenate(axis=-1)([input_data, route])

    input_data = common.convolutional(input_data, (1, 1, 256, 256), activate_type="mish")
    route_1 = input_data

    input_data = common.convolutional(input_data, (3, 3, 256, 512), downsample=True, activate_type="mish")

    route = input_data
    route = common.convolutional(route, (1, 1, 512, 256), activate_type="mish")
    input_data = common.convolutional(input_data, (1, 1, 512, 256), activate_type="mish")

    for i in range(8):
        input_data = common.residual_block(input_data, 256, 256, 256, activate_type="mish")

    input_data = common.convolutional(input_data, (1, 1, 256, 256), activate_type="mish")
    input_data = Concatenate(axis=-1)([input_data, route])

    input_data = common.convolutional(input_data, (1, 1, 512, 512), activate_type="mish")
    route_2 = input_data

    input_data = common.convolutional(input_data, (3, 3, 512, 1024), downsample=True, activate_type="mish")

    route = input_data
    route = common.convolutional(route, (1, 1, 1024, 512), activate_type="mish")
    input_data = common.convolutional(input_data, (1, 1, 1024, 512), activate_type="mish")

    for i in range(4):
        input_data = common.residual_block(input_data, 512, 512, 512, activate_type="mish")

    input_data = common.convolutional(input_data, (1, 1, 512, 512), activate_type="mish")
    input_data = Concatenate(axis=-1)([input_data, route])

    input_data = common.convolutional(input_data, (1, 1, 1024, 1024), activate_type="mish")
    input_data = common.convolutional(input_data, (1, 1, 1024, 512))
    input_data = common.convolutional(input_data, (3, 3, 512, 1024))
    input_data = common.convolutional(input_data, (1, 1, 1024, 512))

    input_data = Concatenate(axis=-1)([
        tf.nn.max_pool(input_data, ksize=13, padding='SAME', strides=1),
        tf.nn.max_pool(input_data, ksize=9, padding='SAME', strides=1),
        tf.nn.max_pool(input_data, ksize=5, padding='SAME', strides=1),
        input_data
    ])

    input_data = common.convolutional(input_data, (1, 1, 2048, 512))
    input_data = common.convolutional(input_data, (3, 3, 512, 1024))
    input_data = common.convolutional(input_data, (1, 1, 1024, 512))

    return route_1, route_2, input_data
