#%%
import xml.etree.ElementTree as ET
import glob
import ntpath
import os
dest_folder = r'C:\Users\<USER>\Desktop\vehicle_detection\data\test'

for i in glob.glob(r'C:\Users\<USER>\Desktop\vehicle_detection\data\annotations\XML\val/*'):
    tree = ET.parse(i)
    root = tree.getroot()
    bb = {}
    dims = []
    check = 0

    for size in root.findall('size'):
        width = int(size.find('width').text)
        dims.append(width)
        height = int(size.find('height').text)
        dims.append(height)

    for obj in root.findall('object'):
        anomaly = obj.find('name').text
        if anomaly == 'motorcycle' :
            for bndbox in obj.findall('bndbox'):
                bb['xmin'] = int(bndbox.find('xmin').text)
                bb['xmax'] = int(bndbox.find('xmax').text)
                bb['ymin'] = int(bndbox.find('ymin').text)
                bb['ymax'] = int(bndbox.find('ymax').text)
                
            width = bb['xmax'] - bb['xmin']
            w_m = round(width/dims[0], 6)
            height = bb['ymax'] - bb['ymin']
            h_m = round(height/dims[1], 6)
            
            
            x = (bb['xmin'] + bb['xmax'])/2
            x = round(x/dims[0], 6)
            y = (bb['ymin'] + bb['ymax'])/2
            y = round(y/dims[1], 6)
            
            filename = ntpath.basename(i)
            filename = filename.split('.')[0] + '.txt'
        
            file_path = os.path.join(dest_folder, filename)
            with open(file_path,'a') as f:
                f.write(str(int(0))+' '+str(x)+' '+str(y)+' '+str(w_m)+' '+str(h_m)+ '\n')
#%%
