# import cv2
# import numpy as np
# from skimage import measure, morphology
# from skimage.measure import regionprops
# import matplotlib.pyplot as plt
#
#
# def detect_and_box_black_components(source_image):
#     # Convert the image to binary (black regions are 0, white regions are 255)
#     img = cv2.threshold(source_image, 127, 255, cv2.THRESH_BINARY_INV)[1]  # Invert to make black components '1'
#
#     # Perform connected component analysis
#     blobs_labels = measure.label(img, background=0)  # Label the connected components in the binary image
#
#     # Convert to color image to draw colored bounding boxes
#     output_image = cv2.cvtColor(source_image, cv2.COLOR_GRAY2BGR)
#
#     counter = 1
#     # Analyze the connected components and draw bounding boxes for components with width < 200
#     for region in regionprops(blobs_labels):
#         # Extract the bounding box coordinates
#         minr, minc, maxr, maxc = region.bbox
#         width = maxc - minc
#         height = maxr - minr
#
#         # Only draw a bounding box if the width is less than 200 pixels
#         if width < 400 and height < 400:
#             # cv2.rectangle(input_imag_1, (minc, minr), (maxc, maxr), (0, 255, 0), 2)  # Green box
#             minr = max(0, minr - 20)  # Ensure it does not go below 0
#             minc = max(0, minc - 20)
#             maxr = min(source_image.shape[0], maxr + 20)  # Ensure it does not exceed image dimensions
#             maxc = min(source_image.shape[1], maxc + 20)
#
#             # Draw the bounding box with extra width and height
#             cv2.rectangle(original_image, (minc, minr), (maxc, maxr), (0, 255, 0), 2)  # Green box
#             # Crop the region within the bounding box
#             cropped_image = original_image[minr:maxr, minc:maxc]
#
#             # Save the cropped region as a separate image
#             cropped_filename = f"signature_{counter}.png"
#             cv2.imwrite(cropped_filename, cropped_image)
#             print(f"Cropped region saved as {cropped_filename}")
#
#             counter += 1
#
#     # Save and display the result
#     cv2.imwrite('black_components_bounding_boxes.png', original_image)
#
#     # Show the result using matplotlib
#     plt.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
#     plt.title('Black Components with Bounding Boxes (Width < 200)')
#     plt.axis('off')
#     plt.show()
#
#
# # Main function to process the image
# if __name__ == "__main__":
#     # Load the input image (grayscale)
#     input_image = cv2.imread('signature_extracted.png', 0)  # Replace 'pic.jpg' with your image path
#     original_image = cv2.imread('pic.jpg')  # Replace 'pic.jpg' with your image path
#
#     # Detect black connected components and draw bounding boxes with width < 200
#     detect_and_box_black_components(input_image)

import re
def extract_ip(rtsp_url):
    try:
        # Regular expression to find the IP address in the RTSP URL
        match = re.search(r'(\b(?:\d{1,3}\.){3}\d{1,3}\b)', rtsp_url)
        if match:
            return match.group(1)
        return None
    except Exception as e:
        print(e)

rtsp_ip = extract_ip("rtsp://admin:Admin123@192.638.2.95:554/stream1")
print(rtsp_ip)