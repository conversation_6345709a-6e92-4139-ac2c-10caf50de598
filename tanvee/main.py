from docx import Document
import ollama
import re
import time

def extract_text_from_docx(path):
    """Extract non-empty text from a Word (.docx) document."""
    try:
        doc = Document(path)
        return "\n".join([para.text for para in doc.paragraphs if para.text.strip()])
    except Exception as e:
        print(f"Error reading {path}: {e}")
        return ""

# Step 1: Extract both documents
company_bcm_text = extract_text_from_docx("Code for Document Evaluation/bcm_test.docx")
iso_bcm_text = extract_text_from_docx("Code for Document Evaluation/Standard_BCM_Plan_ISO_22301_short.docx")

try:
    client = ollama.Client(host='http://***********:11434')

    # Step 2: Initialize prompt with expert-level system message
    base_messages = [
        {
            "role": "system",
            "content": (
                "You are a helpful assistant with expertise in business continuity planning and ISO 22301 compliance. "
                "You are given two business continuity management (BCM) documents:\n\n"
                "- One is a standard-compliant BCM framework document (based on ISO 22301)\n"
                "- The other is a company's BCM implementation plan.\n\n"
                "Your job is to compare the two documents and identify all topics and subtopics that are:\n"
                "1. Mentioned in the ISO standard-based framework but\n"
                "2. Missing, partially covered, or not explicitly addressed in the company’s BCM plan.\n\n"
                "Important:\n"
                "- Compare each topic and subtopic listed in the ISO document section-by-section.\n"
                "- Consider governance, risk management, business impact analysis, testing, communication, plan maintenance, recovery, annexes, and metadata.\n"
                "- Include any topics from annexes, contact directories, training calendars, checklists, legal sections, or communication protocols that are missing or underdeveloped.\n"                "- If a topic is fully missing, list it as missing.\n\n"
                "At the end, output a full list of missing topics with numbering."
            )
        },
        {
            "role": "system",
            "content": f"ISO Standard BCM Document:\n\n{iso_bcm_text}"
        },
        {
            "role": "system",
            "content": f"Company BCM Plan Document:\n\n{company_bcm_text}"
        },
        {
            "role": "user",
            "content": "List all topics and subtopics from the ISO standard that are not present in the company’s BCM plan. Give the list with numbers."
        }
    ]

    all_missing_topics = set()
    max_iterations = 5
    iteration = 1

    while iteration <= max_iterations:
        print(f"\n=== Iteration {iteration} ===")
        response = client.chat(model="llama3.2:latest", messages=base_messages)
        content = response['message']['content']
        print(content)

        # Extract new topics (assumes numbered list format)
        current_topics = set(re.findall(r'\d+\.\s+(.*)', content))

        if not current_topics:
            print("No new topics found or format unrecognized. Stopping iteration.")
            break

        # Add new findings to cumulative topic set
        all_missing_topics.update(current_topics)

        # Prepare the verification question with cumulative list
        follow_up = (
            "Please re-evaluate the following cumulative list of topics identified as missing in the company’s BCM plan. "
            "If any of these are fully or partially addressed, remove them. If missing, mark clearly:\n\n"
        )
        follow_up += "\n".join(f"- {topic}" for topic in sorted(all_missing_topics))

        # Append current assistant response and new user prompt to conversation history
        base_messages.append({"role": "assistant", "content": content})
        base_messages.append({"role": "user", "content": follow_up})

        iteration += 1
        time.sleep(1)

    print("\n=== Final Refined Output ===")
    print(base_messages[-2]['content'])

except Exception as e:
    print(f"Unexpected error: {e}")