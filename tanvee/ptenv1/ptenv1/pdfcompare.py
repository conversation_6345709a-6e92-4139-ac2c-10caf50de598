import fitz  # PyMuPDF
import ollama

def extract_text_from_pdf(path):
    try:
        with fitz.open(path) as doc:
            return "\n".join(page.get_text() for page in doc)
    except Exception as e:
        print(f"Error reading {path}: {e}")
        return ""

# Step 1: Extract both PDF documents
bcm_text = extract_text_from_pdf(r"C:\Users\<USER>\Desktop\perpetuuiti\1bcm_plan.pdf")
framework_text = extract_text_from_pdf(r"C:\Users\<USER>\Desktop\perpetuuiti\1source_framework.pdf")

# Step 2: Use llama3.2 or gemma2 to compare content
try:
    client = ollama.Client(host='http://localhost:11434')

    messages = [
        {
            "role": "system",
            "content": "You are an assistant that compares two documents. The first one lists topics that must be covered. The second one provides the content for the list of topics."
        },
        {
            "role": "system",
            "content": f"The source framework is:\n\n{framework_text}"
        },
        {
            "role": "system",
            "content": f"The plan document is:\n\n{bcm_text}"
        },
        {
            "role": "user",
            "content": "List the topics in the first document that are not mentioned in the second document."
        }
    ]

    response = client.chat(model="llama3.2:latest", messages=messages)
    print(response['message']['content'])

except Exception as e:
    print(f"Unexpected error: {e}")
