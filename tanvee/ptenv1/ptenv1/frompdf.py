import fitz  # PyMuPDF
import ollama

# Step 1: Load and extract text from a PDF file
def extract_text_from_pdf(pdf_path):
    text = ""
    try:
        doc = fitz.open(pdf_path)
        for page in doc:
            text += page.get_text()
        return text
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return ""

# Step 2: Ask user a question
pdf_path = r"C:\Users\<USER>\Desktop\bee.pdf.pdf"  # Replace with your actual PDF path
context = extract_text_from_pdf(pdf_path)
user_question = input("Enter your question about the PDF: ")

# Step 3: Prepare and send the query to Ollama
try:
    client = ollama.Client(host='http://localhost:11434')

    # Build chat messages with system context
    response = client.chat(
        model='llama3.2:latest',
        messages=[
            {"role": "system", "content": "Answer the user's questions based only on the PDF content provided."},
            {"role": "user", "content": f"The PDF content is:\n\n{context[:8000]}..."},  # truncate to 8k chars for token limit
            {"role": "user", "content": user_question}
        ]
    )

    print("\nAnswer:")
    print(response['message']['content'])

except Exception as e:
    print(f"Unexpected error: {e}")
