import cv2
import numpy as np
from skimage import measure, morphology
from skimage.measure import label, regionprops
from skimage.color import label2rgb
import matplotlib.pyplot as plt

def extract_signature(source_image):
    # Convert the source image to binary
    img = cv2.threshold(source_image, 127, 255, cv2.THRESH_BINARY)[1]

    # Perform connected component analysis
    blobs = img > img.mean()
    blobs_labels = measure.label(blobs, background=1)
    image_label_overlay = label2rgb(blobs_labels, image=img)

    # Initialize variables to track the biggest component and total area
    the_biggest_component = 0
    total_area = 0
    counter = 0
    average = 0.0

    # Analyze the connected components
    for region in regionprops(blobs_labels):
        if region.area > 10:
            total_area += region.area
            counter += 1
        # Track the biggest connected component
        if region.area >= 250:
            if region.area > the_biggest_component:
                the_biggest_component = region.area

    # Calculate the average size of connected components
    average = total_area / counter if counter != 0 else 0
    print(f"The biggest component: {the_biggest_component}")
    print(f"Average area: {average}")

    # Experimental-based ratio to remove smaller objects
    a4_constant = (((average / 84.0) * 250.0) + 100) * 1.5
    print(f"a4_constant: {a4_constant}")

    # Remove small objects based on the calculated threshold
    cleaned_blobs = morphology.remove_small_objects(blobs_labels, a4_constant)

    # Save the intermediate result for debugging purposes
    plt.imsave('pre_version.png', cleaned_blobs)

    # Load the saved intermediate image for further processing
    img = cv2.imread('pre_version.png', 0)

    # Apply OTSU thresholding to ensure binary format
    img = cv2.threshold(img, 0, 255, cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU)[1]

    # Return the processed image (signature extracted)
    return img

# Main function to process the image
if __name__ == "__main__":
    # Load the input image (grayscale)
    input_image = cv2.imread('pic.jpg', 0)  # Replace 'input_signature.jpg' with your image path

    # Extract the signature from the image
    signature_image = extract_signature(input_image)

    # Save the extracted signature
    cv2.imwrite('signature_extracted.png', signature_image)  # Save the output image with signature extracted
    print("Signature extraction completed and saved as 'signature_extracted.png'")
